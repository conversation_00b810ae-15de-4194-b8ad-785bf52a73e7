{"timestamp": "2025-05-21T14:40:59.634Z", "tests": {"API-01": {"name": "<PERSON><PERSON>", "status": "Fail", "details": {"statusCode": 500, "response": {"error": "request to http://localhost:3001/api/admin/diagnostics/auth-check failed, reason: "}}}, "API-02": {"name": "Expired Token Test", "status": "Fail", "details": {"statusCode": 500, "response": {"error": "request to http://localhost:3001/api/admin/diagnostics/auth-check failed, reason: "}}}, "API-03": {"name": "No Token Test", "status": "Fail", "details": {"statusCode": 500, "response": {"error": "request to http://localhost:3001/api/admin/diagnostics/auth-check failed, reason: "}}}, "API-04": {"name": "<PERSON><PERSON> for Customer Data", "status": "Fail", "details": {"statusCode": 500, "responseType": "object"}}, "API-06": {"name": "<PERSON><PERSON> for Settings", "status": "Fail", "details": {"statusCode": 500, "responseType": "object"}}, "X-Auth-Token": {"name": "<PERSON>-<PERSON><PERSON>-<PERSON><PERSON> Test", "status": "Fail", "details": {"statusCode": 500, "response": {"error": "request to http://localhost:3001/api/admin/diagnostics/auth-check failed, reason: "}}}}}