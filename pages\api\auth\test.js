/**
 * Authentication Test Endpoint
 * 
 * This endpoint serves as a diagnostic tool to verify that authentication
 * is working correctly in both development and production environments.
 */

import { withAuth } from '@/lib/api-auth';
import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase';

async function handler(req, res) {
  try {
    // Basic auth test - we know it passed if we got here
    // (the withAuth middleware would have blocked if auth failed)
      const { user, role } = req.auth;
      // Additional tests to verify database connection
    const adminClient = getAdminClient();
    if (!adminClient) {
      return res.status(500).json({
        success: false,
        message: 'Failed to initialize admin client',
        environment: process.env.NODE_ENV || 'unknown'
      });
    }
    
    const { data, error } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', user.id)
      .single();
      
    if (error) {
      return res.status(500).json({
        success: false,
        message: 'Authentication passed but database connection failed',
        errorDetails: error.message,
        errorCode: error.code,
        environment: process.env.NODE_ENV || 'unknown'
      });
    }
    
    // Check if the role in the database matches the role from auth
    const roleMatches = data?.role === role;
    
    return res.status(200).json({
      success: true,
      message: 'Authentication is working correctly',
      details: {
        user: {
          id: user.id,
          email: user.email
        },
        role: role,
        roleMatches: roleMatches,
        environment: process.env.NODE_ENV || 'unknown',
        timestamp: new Date().toISOString()
      }
    });
  } catch (err) {
    console.error('Error in auth test endpoint:', err);
    res.status(500).json({
      success: false,
      message: 'Internal server error during authentication test',
      error: err.message,
      environment: process.env.NODE_ENV || 'unknown'
    });
  }
}

// Use our authentication middleware with admin access requirement
export default withAuth(handler, { adminOnly: true, staffAllowed: true });
