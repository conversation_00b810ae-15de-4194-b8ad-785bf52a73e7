# Authentication Troubleshooting

This document provides solutions for common authentication issues in the Ocean Soul Sparkles application.

## Common Issues and Solutions

### 1. "Unauthorized access" Error

**Symptoms:**
- API requests return 401 Unauthorized status
- Error message: "Unauthorized access" or "Missing or invalid Authorization header"
- User is redirected to login page unexpectedly

**Possible Causes:**
- Token is missing or invalid
- Token has expired
- Token is not being sent correctly in the request

**Solutions:**

1. **Check if user is logged in:**
   ```javascript
   const { user, loading } = useAuth()
   console.log('User:', user, 'Loading:', loading)
   ```

2. **Verify token in browser storage:**
   - Open browser developer tools
   - Go to Application > Storage > Local Storage
   - Check for `oss_auth_token` or `sb-ndlgbcsbidyhxbpqzgqp-auth-token`
   - Verify that the token exists and is not expired

3. **Check request headers:**
   - Open browser developer tools
   - Go to Network tab
   - Find the failing API request
   - Check that the Authorization header is present and correctly formatted
   - Format should be: `Authorization: Bearer <token>`

4. **Force re-authentication:**
   - Sign out and sign in again
   - This will generate a new token

5. **Clear browser storage:**
   - Open browser developer tools
   - Go to Application > Storage > Local Storage
   - Clear all storage for the domain
   - Refresh the page and log in again

### 2. "Session retrieval timed out" Error

**Symptoms:**
- Error message: "Session retrieval timed out"
- Authentication state is inconsistent
- User is logged out on page refresh

**Possible Causes:**
- Network issues preventing token refresh
- Supabase client configuration issues
- Race condition in authentication state management

**Solutions:**

1. **Check network connectivity:**
   - Verify that the application can connect to Supabase
   - Check for network errors in the browser console

2. **Increase timeout settings:**
   - Update the Supabase client configuration in `lib/supabase.js`:
     ```javascript
     export const supabase = createClient(supabaseUrl, supabaseKey, {
       auth: {
         persistSession: true,
         autoRefreshToken: true,
         storageKey: 'oss_auth_token',
         detectSessionInUrl: false,
         flowType: 'implicit',
         sessionTimeout: 60000, // Increase timeout to 60 seconds
       }
     })
     ```

3. **Implement retry logic:**
   - Add retry logic to the AuthProvider component:
     ```javascript
     const initializeAuth = async (retries = 3) => {
       try {
         const { data, error } = await supabase.auth.getSession()
         // Handle session data
       } catch (error) {
         if (retries > 0) {
           console.log(`Auth initialization failed, retrying... (${retries} attempts left)`)
           setTimeout(() => initializeAuth(retries - 1), 1000)
         } else {
           console.error('Auth initialization failed after multiple attempts:', error)
           setLoading(false)
         }
       }
     }
     ```

### 3. "Insufficient permissions" Error

**Symptoms:**
- API requests return 403 Forbidden status
- Error message: "Insufficient permissions"
- User can log in but cannot access certain features

**Possible Causes:**
- User does not have the required role
- Role information is not being retrieved correctly
- Database permissions are not set correctly

**Solutions:**

1. **Check user role:**
   ```javascript
   const { user, role } = useAuth()
   console.log('User:', user, 'Role:', role)
   ```

2. **Verify role in database:**
   - Check the `user_roles` table in the database
   - Ensure the user has the correct role assigned

3. **Update user role:**
   - Use the admin panel to update the user's role
   - Or update directly in the database:
     ```sql
     UPDATE user_roles
     SET role = 'admin'
     WHERE id = '<user_id>';
     ```

4. **Check role-based access control:**
   - Verify that the API endpoint is using the correct role check
   - Ensure the `withAdminAuth` middleware is being used correctly

### 4. Cross-Origin Request Issues

**Symptoms:**
- CORS errors in browser console
- API requests fail from certain origins
- Authentication works in some environments but not others

**Possible Causes:**
- CORS configuration is too restrictive
- Authentication headers are not being included in cross-origin requests
- Preflight requests are not handling authentication correctly

**Solutions:**

1. **Update CORS configuration:**
   - Check the CORS configuration in `next.config.js`
   - Ensure that all required origins are allowed

2. **Use X-Auth-Token header:**
   - For cross-origin requests, use the X-Auth-Token header instead of Authorization
   - Update the fetch call:
     ```javascript
     fetch('/api/admin/customers', {
       headers: {
         'X-Auth-Token': token
       }
     })
     ```

3. **Enable credentials in fetch requests:**
   - Add credentials: 'include' to fetch requests:
     ```javascript
     fetch('/api/admin/customers', {
       headers: {
         'Authorization': `Bearer ${token}`
       },
       credentials: 'include'
     })
     ```

### 5. Token Refresh Issues

**Symptoms:**
- User is logged out unexpectedly
- Authentication state is lost after some time
- Token refresh requests fail

**Possible Causes:**
- Token refresh is not configured correctly
- Network issues during token refresh
- Browser storage issues

**Solutions:**

1. **Check token refresh configuration:**
   - Verify that autoRefreshToken is enabled in the Supabase client:
     ```javascript
     export const supabase = createClient(supabaseUrl, supabaseKey, {
       auth: {
         persistSession: true,
         autoRefreshToken: true,
         // Other options...
       }
     })
     ```

2. **Monitor token refresh events:**
   - Use the auth-test-helper.js script to monitor token refresh:
     ```javascript
     // In browser console
     authTest.monitorTokenRefresh(300000) // Monitor for 5 minutes
     ```

3. **Implement manual token refresh:**
   - Add a manual token refresh function:
     ```javascript
     const refreshToken = async () => {
       try {
         const { data, error } = await supabase.auth.refreshSession()
         if (error) throw error
         return data
       } catch (error) {
         console.error('Error refreshing token:', error)
         return null
       }
     }
     ```

### 6. Browser Compatibility Issues

**Symptoms:**
- Authentication works in some browsers but not others
- Safari-specific issues with token storage or refresh
- Mobile browser authentication issues

**Possible Causes:**
- Browser-specific storage limitations
- Different handling of cookies and local storage
- Stricter security policies in certain browsers

**Solutions:**

1. **Use consistent storage mechanism:**
   - Configure Supabase to use a specific storage mechanism:
     ```javascript
     export const supabase = createClient(supabaseUrl, supabaseKey, {
       auth: {
         persistSession: true,
         autoRefreshToken: true,
         storageKey: 'oss_auth_token',
         storage: window.localStorage, // Explicitly use localStorage
       }
     })
     ```

2. **Implement browser detection:**
   - Add browser detection to apply browser-specific fixes:
     ```javascript
     const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
     
     if (isSafari) {
       // Apply Safari-specific fixes
     }
     ```

3. **Test across browsers:**
   - Use the browser-based test interface to test authentication in different browsers
   - Document browser-specific issues and solutions

## Debugging Tools

### 1. Authentication Test Helper

The `auth-test-helper.js` script provides browser-based utilities for testing authentication:

```javascript
// In browser console
authTest.checkAuth() // Check current authentication state
authTest.extractToken() // Extract current token
authTest.testSessionPersistence() // Test session persistence
authTest.monitorTokenRefresh(300000) // Monitor token refresh for 5 minutes
authTest.runAllTests() // Run all browser-based tests
```

### 2. API Authentication Tests

The `run-auth-tests.js` script tests API authentication:

```bash
node scripts/run-auth-tests.js --token=<your_auth_token>
```

### 3. Authentication Diagnostics Page

The `/admin/diagnostics/auth-test` page provides a UI for testing authentication:

- Run authentication tests
- Test session persistence
- Monitor token refresh
- View authentication information

## Logging and Monitoring

For persistent authentication issues, enable detailed logging:

1. **Enable debug logging in Supabase client:**
   ```javascript
   export const supabase = createClient(supabaseUrl, supabaseKey, {
     auth: {
       persistSession: true,
       autoRefreshToken: true,
       debug: true, // Enable debug logging
     }
   })
   ```

2. **Add logging to authentication middleware:**
   ```javascript
   export const authenticateAdmin = async (req, res, next) => {
     const requestId = crypto.randomUUID()
     console.log(`[${requestId}] Authentication request received`)
     
     try {
       // Extract token
       const authHeader = req.headers.authorization
       console.log(`[${requestId}] Auth header: ${authHeader ? 'Present' : 'Missing'}`)
       
       // Rest of the middleware...
     } catch (error) {
       console.error(`[${requestId}] Authentication error:`, error)
       return res.status(500).json({
         error: 'Internal Server Error',
         message: 'Authentication process failed',
         requestId
       })
     }
   }
   ```

3. **Monitor authentication events:**
   - Set up monitoring for authentication failures
   - Track token refresh events
   - Monitor session timeouts
