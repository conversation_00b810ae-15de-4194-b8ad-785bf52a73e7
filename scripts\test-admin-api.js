#!/usr/bin/env node

/**
 * Admin API Functionality Test Script
 * 
 * This script tests the admin panel API endpoints to verify:
 * 1. Customer API endpoints work correctly
 * 2. Booking API endpoints work correctly  
 * 3. Product API endpoints work correctly
 * 4. Authentication is working properly
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAdminAPI() {
  console.log('🧪 Starting Admin API Functionality Tests...\n');
  
  const results = {
    customerAPI: { status: '❌', details: [] },
    bookingAPI: { status: '❌', details: [] },
    productAPI: { status: '❌', details: [] },
    dataIntegrity: { status: '❌', details: [] }
  };

  try {
    // 1. Test Customer API
    console.log('👥 Testing Customer API...');
    
    // Test customer list fetch
    const { data: customers, error: customerError } = await supabase
      .from('customers')
      .select('id, name, email, phone, created_at, updated_at')
      .limit(5);

    if (customerError) {
      results.customerAPI.details.push(`Failed to fetch customers: ${customerError.message}`);
    } else {
      results.customerAPI.status = '✅';
      results.customerAPI.details.push(`Successfully fetched ${customers.length} customers`);
      
      // Test customer search
      if (customers.length > 0) {
        const searchTerm = customers[0].name.split(' ')[0]; // Use first name for search
        const { data: searchResults, error: searchError } = await supabase
          .from('customers')
          .select('id, name, email')
          .ilike('name', `%${searchTerm}%`)
          .limit(3);

        if (!searchError && searchResults.length > 0) {
          results.customerAPI.details.push(`Customer search working (found ${searchResults.length} results for "${searchTerm}")`);
        }
      }
    }

    // 2. Test Booking API
    console.log('📅 Testing Booking API...');
    
    const { data: bookings, error: bookingError } = await supabase
      .from('bookings')
      .select(`
        id,
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        customers:customer_id (name, email),
        services:service_id (name, color)
      `)
      .limit(5);

    if (bookingError) {
      results.bookingAPI.details.push(`Failed to fetch bookings: ${bookingError.message}`);
    } else {
      results.bookingAPI.status = '✅';
      results.bookingAPI.details.push(`Successfully fetched ${bookings.length} bookings with relationships`);
      
      // Check if relationships are working
      const bookingsWithCustomers = bookings.filter(b => b.customers);
      const bookingsWithServices = bookings.filter(b => b.services);
      
      results.bookingAPI.details.push(`${bookingsWithCustomers.length}/${bookings.length} bookings have customer data`);
      results.bookingAPI.details.push(`${bookingsWithServices.length}/${bookings.length} bookings have service data`);
    }

    // 3. Test Product API
    console.log('🛍️ Testing Product API...');
    
    const { data: products, error: productError } = await supabase
      .from('products')
      .select('id, name, price, category, status, stock, created_at')
      .limit(5);

    if (productError) {
      results.productAPI.details.push(`Failed to fetch products: ${productError.message}`);
    } else {
      results.productAPI.status = '✅';
      results.productAPI.details.push(`Successfully fetched ${products.length} products`);
      
      // Check product data quality
      const productsWithPrices = products.filter(p => p.price && p.price > 0);
      const activeProducts = products.filter(p => p.status === 'active');
      
      results.productAPI.details.push(`${productsWithPrices.length}/${products.length} products have valid prices`);
      results.productAPI.details.push(`${activeProducts.length}/${products.length} products are active`);
    }

    // 4. Test Data Integrity
    console.log('🔗 Testing Data Integrity...');
    
    // Test customer-booking relationships
    const { data: customerBookingTest, error: relationError } = await supabase
      .from('customers')
      .select(`
        id,
        name,
        bookings:bookings(count)
      `)
      .limit(5);

    if (relationError) {
      results.dataIntegrity.details.push(`Failed to test customer-booking relationships: ${relationError.message}`);
    } else {
      results.dataIntegrity.status = '✅';
      results.dataIntegrity.details.push('Customer-booking relationships working correctly');
    }

    // Test for orphaned records
    const { data: orphanedBookings, error: orphanError } = await supabase
      .from('bookings')
      .select('id, customer_id')
      .is('customers.id', null);

    if (!orphanError) {
      if (orphanedBookings && orphanedBookings.length === 0) {
        results.dataIntegrity.details.push('No orphaned booking records found');
      } else {
        results.dataIntegrity.details.push(`Warning: ${orphanedBookings?.length || 0} orphaned booking records found`);
      }
    }

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    return false;
  }

  // Print Results
  console.log('\n📋 API FUNCTIONALITY TEST RESULTS');
  console.log('==================================');
  console.log(`${results.customerAPI.status} Customer API:`);
  results.customerAPI.details.forEach(detail => console.log(`   • ${detail}`));
  
  console.log(`${results.bookingAPI.status} Booking API:`);
  results.bookingAPI.details.forEach(detail => console.log(`   • ${detail}`));
  
  console.log(`${results.productAPI.status} Product API:`);
  results.productAPI.details.forEach(detail => console.log(`   • ${detail}`));
  
  console.log(`${results.dataIntegrity.status} Data Integrity:`);
  results.dataIntegrity.details.forEach(detail => console.log(`   • ${detail}`));

  const overallStatus = [
    results.customerAPI.status,
    results.bookingAPI.status,
    results.productAPI.status,
    results.dataIntegrity.status
  ].every(status => status === '✅') ? '✅ ALL TESTS PASSED' : '⚠️ SOME TESTS FAILED';

  console.log(`\n🎯 Overall API Status: ${overallStatus}`);
  
  return overallStatus.includes('PASSED');
}

// Run API tests
if (require.main === module) {
  testAdminAPI()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ API test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testAdminAPI };
