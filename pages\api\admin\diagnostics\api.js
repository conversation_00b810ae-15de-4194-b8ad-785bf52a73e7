import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for API diagnostics
 * This endpoint checks the API endpoints
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] API diagnostics endpoint called: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // For diagnostics endpoints, we'll use a more lenient authentication approach
  // This allows the diagnostics page to work even when auth is having issues
  let authResult;
  try {
    // Authenticate request using our robust auth module
    authResult = await authenticateAdminRequest(req);
    const { authorized, error, user, role } = authResult;

    // Log authentication result
    if (authorized) {
      console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);
    } else {
      console.warn(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');

      // For diagnostics endpoints, we'll continue even if auth fails
      // This allows us to diagnose auth issues
      console.log(`[${requestId}] Continuing with diagnostics despite auth failure`);
    }
  } catch (authError) {
    console.error(`[${requestId}] Authentication error:`, authError);
    // Continue with diagnostics despite auth error
    console.log(`[${requestId}] Continuing with diagnostics despite auth error`);
  }

  // Extract user and role from authResult if available
  const user = authResult?.user || { email: 'diagnostics-user' };
  const role = authResult?.role || 'diagnostics';

  try {
    // Define API endpoints to check
    const endpoints = [
      { name: 'customers', url: '/api/admin/customers' },
      { name: 'bookings', url: '/api/admin/bookings' },
      { name: 'payments', url: '/api/admin/payments' },
      { name: 'inventory', url: '/api/admin/inventory' },
      { name: 'users', url: '/api/admin/users' }
    ];

    // Check each endpoint
    const results = await Promise.all(
      endpoints.map(async (endpoint) => {
        try {
          // Create a timeout for the request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          // Make the request
          const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || '';
          const response = await fetch(`${siteUrl}${endpoint.url}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${req.headers.authorization?.split(' ')[1] || ''}`
            },
            signal: controller.signal
          });

          // Clear the timeout
          clearTimeout(timeoutId);

          return {
            name: endpoint.name,
            url: endpoint.url,
            status: response.status,
            ok: response.ok,
            statusText: response.statusText
          };
        } catch (endpointError) {
          return {
            name: endpoint.name,
            url: endpoint.url,
            status: 0,
            ok: false,
            statusText: endpointError.message || 'Request failed'
          };
        }
      })
    );

    // Check if all endpoints are healthy
    const allHealthy = results.every(result => result.ok);

    // Return results
    return res.status(200).json({
      status: allHealthy ? 'healthy' : 'error',
      message: allHealthy ? 'All API endpoints are responding correctly' : 'Some API endpoints are not responding correctly',
      endpoints: results,
      requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`[${requestId}] API diagnostics error:`, error);
    return res.status(500).json({
      error: 'API diagnostics failed',
      message: error.message || 'An error occurred while checking API endpoints',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
