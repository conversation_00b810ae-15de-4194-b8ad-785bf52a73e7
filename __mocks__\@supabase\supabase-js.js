const mockSubscription = {
  unsubscribe: jest.fn()
};

const mockSupabaseClient = {
  auth: {
    onAuthStateChange: jest.fn().mockImplementation((callback) => {
      return { 
        data: { 
          subscription: mockSubscription 
        } 
      };
    }),
    getSession: jest.fn().mockImplementation(() => {
      return {
        data: {
          session: {
            user: { id: 'test-user-id' }
          }
        },
        error: null
      };
    }),
    signInWithPassword: jest.fn().mockImplementation(() => {
      return {
        data: {
          user: { id: 'test-user-id' },
          session: { access_token: 'test-token' }
        },
        error: null
      };
    }),
    signOut: jest.fn().mockImplementation(() => {
      return { error: null };
    }),
    refreshSession: jest.fn().mockImplementation(() => {
      return {
        data: {
          session: { access_token: 'refreshed-token' }
        },
        error: null
      };
    })
  },
  from: jest.fn().mockImplementation((table) => {
    return {
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockImplementation(() => {
        if (table === 'user_roles') {
          return {
            data: { role: 'admin' },
            error: null
          };
        }
        return {
          data: {},
          error: null
        };
      }),
      limit: jest.fn().mockImplementation(() => {
        return {
          data: [{ id: 'test-item-id' }],
          error: null
        };
      })
    };
  })
};

module.exports = {
  createClient: jest.fn().mockImplementation(() => mockSupabaseClient)
};
