-- Create or replace view for customer statistics
CREATE OR REPLACE VIEW customer_statistics AS
SELECT
  c.id,
  c.name,
  c.email,
  c.marketing_consent,
  COUNT(DISTINCT b.id) AS booking_count,
  COUNT(DISTINCT o.id) AS order_count,
  COALESCE(SUM(o.total), 0) AS total_spent,
  MIN(LEAST(b.created_at, o.created_at)) AS first_interaction,
  MAX(GREATEST(b.created_at, o.created_at)) AS last_interaction
FROM customers c
LEFT JOIN bookings b ON c.id = b.customer_id
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.id, c.name, c.email, c.marketing_consent;
