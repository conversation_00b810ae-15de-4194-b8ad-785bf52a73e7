import { useState } from 'react';
import styles from '@/styles/GiftCardShowcase.module.css';
import AnimatedSection from './AnimatedSection';
import { safeRender } from '@/lib/safe-render-utils';

/**
 * GiftCardShowcase component with interactive gift card customization
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {string} props.subtitle - Section subtitle
 * @param {Array} props.giftCardOptions - Array of gift card options (preset amounts)
 * @returns {JSX.Element}
 */
const GiftCardShowcase = ({
  title = 'Gift the Magic of Sparkles',
  subtitle = 'Perfect for any occasion, let them choose their own sparkle journey',
  giftCardOptions = [],
  ...props
}) => {
  const [amount, setAmount] = useState('50');
  const [customAmount, setCustomAmount] = useState('');
  const [recipient, setRecipient] = useState('');
  const [senderName, setSenderName] = useState('');
  const [message, setMessage] = useState('');
  const [selectedDesign, setSelectedDesign] = useState(0);
  const [isCustomAmount, setIsCustomAmount] = useState(false);

  // Default gift card options if none provided
  const defaultGiftCardOptions = [
    { value: '25', label: '$25' },
    { value: '50', label: '$50' },
    { value: '75', label: '$75' },
    { value: '100', label: '$100' }
  ];

  const displayOptions = giftCardOptions.length > 0 ? giftCardOptions : defaultGiftCardOptions;

  // Gift card design options
  const giftCardDesigns = [
    {
      name: 'Ocean Waves',
      image: '/images/gift-card-hero.jpg',
      color: '#4ECDC4'
    },
    {
      name: 'Sparkle Magic',
      image: '/images/services/glitter-tattoo.jpeg',
      color: '#FF6B6B'
    },
    {
      name: 'Mystic Dream',
      image: '/images/gallery/gallery-1.jpg',
      color: '#FFE66D'
    }
  ];

  // Handle custom amount toggle
  const handleAmountToggle = (isCustom) => {
    setIsCustomAmount(isCustom);
    if (!isCustom) {
      setAmount('50');
      setCustomAmount('');
    } else {
      setAmount('');
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    const giftCardData = {
      amount: isCustomAmount ? customAmount : amount,
      recipient,
      senderName,
      message,
      design: giftCardDesigns[selectedDesign]
    };

    // Here you would typically send this data to your backend
    console.log('Gift card purchase:', giftCardData);

    // Show success message or redirect
    alert('Gift card added to cart!');
  };

  return (
    <section className={styles.giftCardSection} {...props}>
      <div className={styles.giftCardContainer}>
        <AnimatedSection animation="fade-up">
          <h2 className={styles.sectionTitle}>{safeRender(title)}</h2>
          <p className={styles.sectionSubtitle}>{safeRender(subtitle)}</p>
        </AnimatedSection>

        <div className={styles.giftCardContent}>
          <div className={styles.giftCardPreview}>
            <AnimatedSection animation="fade-right">
              <div className={styles.giftCardImageContainer} style={{ borderColor: safeRender(giftCardDesigns[selectedDesign]?.color, '#4ECDC4') }}>
                <img
                  src={safeRender(giftCardDesigns[selectedDesign]?.image, '/images/gallery/gallery-1.jpg')}
                  alt={safeRender(giftCardDesigns[selectedDesign]?.name, 'Gift Card')}
                  className={styles.giftCardImage}
                />
                <div className={styles.giftCardOverlay}>
                  <div className={styles.giftCardInfo}>
                    <div className={styles.giftCardAmount}>
                      ${isCustomAmount ? customAmount || '0' : amount}
                    </div>
                    <div className={styles.giftCardName}>
                      Ocean Soul Sparkles<br />Gift Card
                    </div>
                  </div>
                </div>
              </div>

              <div className={styles.designSelector}>
                {giftCardDesigns.map((design, index) => (
                  <button
                    key={index}
                    className={`${styles.designOption} ${selectedDesign === index ? styles.activeDesign : ''}`}
                    onClick={() => setSelectedDesign(index)}
                    style={{
                      borderColor: safeRender(design.color, '#4ECDC4'),
                      backgroundColor: selectedDesign === index ? `${safeRender(design.color, '#4ECDC4')}30` : 'transparent'
                    }}
                  >
                    <img src={safeRender(design.image, '/images/gallery/gallery-1.jpg')} alt={safeRender(design.name, 'Design')} />
                    <span>{safeRender(design.name)}</span>
                  </button>
                ))}
              </div>
            </AnimatedSection>
          </div>

          <div className={styles.giftCardForm}>
            <AnimatedSection animation="fade-left">
              <form onSubmit={handleSubmit}>
                <div className={styles.formGroup}>
                  <label className={styles.formLabel}>Select Amount</label>
                  <div className={styles.amountSelector}>
                    <div className={styles.amountToggle}>
                      <button
                        type="button"
                        className={`${styles.amountToggleBtn} ${!isCustomAmount ? styles.activeToggle : ''}`}
                        onClick={() => handleAmountToggle(false)}
                      >
                        Preset Amount
                      </button>
                      <button
                        type="button"
                        className={`${styles.amountToggleBtn} ${isCustomAmount ? styles.activeToggle : ''}`}
                        onClick={() => handleAmountToggle(true)}
                      >
                        Custom Amount
                      </button>
                    </div>

                    {!isCustomAmount ? (
                      <div className={styles.presetAmounts}>
                        {displayOptions.map((option) => (
                          <button
                            key={safeRender(option.value, 'option')}
                            type="button"
                            className={`${styles.amountBtn} ${amount === option.value ? styles.activeAmount : ''}`}
                            onClick={() => setAmount(option.value)}
                          >
                            {safeRender(option.label)}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <div className={styles.customAmountInput}>
                        <span className={styles.currencySymbol}>$</span>
                        <input
                          type="number"
                          min="10"
                          max="500"
                          value={customAmount}
                          onChange={(e) => setCustomAmount(e.target.value)}
                          placeholder="Enter amount (10-500)"
                          className={styles.formInput}
                          required={isCustomAmount}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="recipient" className={styles.formLabel}>Recipient's Email</label>
                  <input
                    type="email"
                    id="recipient"
                    value={recipient}
                    onChange={(e) => setRecipient(e.target.value)}
                    placeholder="Where should we send this gift?"
                    className={styles.formInput}
                    required
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="senderName" className={styles.formLabel}>Your Name</label>
                  <input
                    type="text"
                    id="senderName"
                    value={senderName}
                    onChange={(e) => setSenderName(e.target.value)}
                    placeholder="Who is this gift from?"
                    className={styles.formInput}
                    required
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="message" className={styles.formLabel}>Personal Message</label>
                  <textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Add a personal message (optional)"
                    className={styles.formTextarea}
                    rows="3"
                  ></textarea>
                </div>

                <button type="submit" className={styles.submitButton}>
                  <span className={styles.btnText}>Add to Cart</span>
                  <span className={styles.btnIcon}>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 5V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </span>
                </button>
              </form>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GiftCardShowcase;