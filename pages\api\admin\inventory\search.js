import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Advanced Inventory Search API Endpoint
 * Provides comprehensive search and filtering capabilities for inventory management
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    const {
      search = '',
      category = 'all',
      stockLevel = 'all',
      priceRange = {},
      costRange = {},
      supplier = 'all',
      abcClassification = 'all',
      lastRestocked = '',
      location = 'all',
      sortBy = 'name',
      sortOrder = 'asc',
      limit = 50,
      offset = 0
    } = req.query;

    // Parse range filters
    const parsedPriceRange = typeof priceRange === 'string' ? JSON.parse(priceRange || '{}') : priceRange;
    const parsedCostRange = typeof costRange === 'string' ? JSON.parse(costRange || '{}') : costRange;

    // Start building the query using the inventory_summary view
    let query = supabaseAdmin
      .from('inventory_summary')
      .select('*', { count: 'exact' });

    // Apply search filter using full-text search
    if (search) {
      query = query.or(`name.ilike.%${search}%,sku.ilike.%${search}%,barcode.ilike.%${search}%`);
    }

    // Apply category filter
    if (category !== 'all') {
      query = query.eq('category', category);
    }

    // Apply stock level filter
    if (stockLevel !== 'all') {
      query = query.eq('stock_status', stockLevel);
    }

    // Apply price range filter
    if (parsedPriceRange.min) {
      query = query.gte('price', parseFloat(parsedPriceRange.min));
    }
    if (parsedPriceRange.max) {
      query = query.lte('price', parseFloat(parsedPriceRange.max));
    }

    // Apply cost range filter
    if (parsedCostRange.min) {
      query = query.gte('cost_price', parseFloat(parsedCostRange.min));
    }
    if (parsedCostRange.max) {
      query = query.lte('cost_price', parseFloat(parsedCostRange.max));
    }

    // Apply supplier filter
    if (supplier !== 'all') {
      query = query.eq('supplier_name', supplier);
    }

    // Apply ABC classification filter
    if (abcClassification !== 'all') {
      query = query.eq('abc_classification', abcClassification);
    }

    // Apply last restocked filter
    if (lastRestocked) {
      const days = parseInt(lastRestocked);
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      query = query.gte('last_restock_date', cutoffDate.toISOString());
    }

    // Apply sorting
    const validSortColumns = ['name', 'sku', 'price', 'cost_price', 'quantity', 'stock_status', 'inventory_value', 'last_restock_date'];
    if (validSortColumns.includes(sortBy)) {
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    const limitNum = Math.min(parseInt(limit) || 50, 100); // Max 100 items per request
    const offsetNum = parseInt(offset) || 0;
    query = query.range(offsetNum, offsetNum + limitNum - 1);

    // Execute query
    const { data, error: queryError, count } = await query;

    if (queryError) {
      throw queryError;
    }

    // Calculate additional metrics
    const totalValue = data?.reduce((sum, item) => sum + (item.inventory_value || 0), 0) || 0;
    const lowStockCount = data?.filter(item => item.stock_status === 'low_stock').length || 0;
    const outOfStockCount = data?.filter(item => item.stock_status === 'out_of_stock').length || 0;

    return res.status(200).json({
      products: data || [],
      pagination: {
        total: count || 0,
        offset: offsetNum,
        limit: limitNum,
        hasMore: (offsetNum + limitNum) < (count || 0)
      },
      summary: {
        totalProducts: data?.length || 0,
        totalValue: totalValue,
        lowStockCount: lowStockCount,
        outOfStockCount: outOfStockCount
      },
      filters: {
        search,
        category,
        stockLevel,
        priceRange: parsedPriceRange,
        costRange: parsedCostRange,
        supplier,
        abcClassification,
        lastRestocked,
        location
      }
    });

  } catch (error) {
    console.error('Inventory search error:', error);
    return res.status(500).json({
      error: 'Failed to search inventory',
      message: error.message
    });
  }
}
