#!/usr/bin/env node

/**
 * Test Supabase Email Capabilities
 * 
 * This script tests what email features are currently available in your Supabase project
 * and helps you understand what you need to configure.
 */

require('dotenv').config({ path: '.env.local' });

async function testSupabaseEmail() {
  console.log('🧪 Testing Supabase Email Configuration...\n');

  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase credentials in .env.local');
    return;
  }

  console.log('✅ Supabase credentials found');
  console.log(`📍 Project URL: ${supabaseUrl}`);

  // Test 1: Check if custom SMTP is configured
  console.log('\n1️⃣ Checking Supabase Email Configuration:');
  console.log('📧 To configure Supabase email:');
  console.log(`   👉 Visit: https://supabase.com/dashboard/project/${extractProjectId(supabaseUrl)}/settings/auth`);
  console.log('   📝 Scroll to "SMTP Settings" section');
  console.log('   ⚙️  Configure with your Gmail settings for professional emails');

  // Test 2: Check default email behavior
  console.log('\n2️⃣ Current Email Behavior:');
  console.log('🔹 Authentication emails: Using Supabase default (limited to team members)');
  console.log('🔹 Rate limit: 2 emails per hour (development only)');
  console.log('🔹 Recipients: Only authorized team members can receive emails');

  // Test 3: Recommended setup
  console.log('\n3️⃣ Recommended Configuration:');
  console.log('✨ Use your working Gmail SMTP settings in Supabase dashboard:');
  console.log('   📧 SMTP Host: smtp.gmail.com');
  console.log('   🔢 SMTP Port: 587'); 
  console.log('   👤 SMTP User: <EMAIL>');
  console.log('   🔐 SMTP Password: [Your app password from .env.local]');
  console.log('   🎨 From Name: Ocean Soul Sparkles');

  // Test 4: Benefits comparison
  console.log('\n4️⃣ Migration Benefits:');
  console.log('✅ Simpler configuration (dashboard vs code files)');
  console.log('✅ Integrated auth emails (automatic signup/reset emails)');
  console.log('✅ Professional email templates');
  console.log('✅ Better security and rate limiting');
  console.log('✅ Same Gmail account, better management');

  // Test 5: Action items
  console.log('\n5️⃣ Next Steps:');
  console.log('1. Configure Supabase SMTP with your Gmail settings');
  console.log('2. Test user registration to see automatic emails');
  console.log('3. Gradually migrate business emails to simpler approach');
  console.log('4. Remove complex Google Cloud email files when ready');

  console.log('\n🎯 Summary:');
  console.log('Your Gmail credentials work perfectly! Using them through Supabase');
  console.log('will give you better integration and simpler management.');
  console.log('\n📖 Read the migration guide: migrate-to-supabase-email.md');
}

function extractProjectId(url) {
  try {
    return url.split('://')[1].split('.')[0];
  } catch (e) {
    return 'your-project-id';
  }
}

// Run the test
testSupabaseEmail().catch(console.error);
