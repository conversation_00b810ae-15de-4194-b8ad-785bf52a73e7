-- Enhance customers table with additional fields
ALTER TABLE public.customers
ADD COLUMN IF NOT EXISTS birth_date DATE,
ADD COLUMN IF NOT EXISTS occupation TEXT,
ADD COLUMN IF NOT EXISTS referral_source TEXT,
ADD COLUMN IF NOT EXISTS customer_since DATE DEFAULT CURRENT_DATE,
ADD COLUMN IF NOT EXISTS lifetime_value DECIMAL(10, 2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_booking_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS booking_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS vip BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS profile_image_url TEXT;

-- Create customer_tags table
CREATE TABLE IF NOT EXISTS public.customer_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  color TEXT DEFAULT '#6a0dad',
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create customer_tag_assignments table
CREATE TABLE IF NOT EXISTS public.customer_tag_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES public.customer_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id, tag_id)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS customers_vip_idx ON public.customers(vip);
CREATE INDEX IF NOT EXISTS customers_last_booking_date_idx ON public.customers(last_booking_date);
CREATE INDEX IF NOT EXISTS customers_booking_count_idx ON public.customers(booking_count);
CREATE INDEX IF NOT EXISTS customers_lifetime_value_idx ON public.customers(lifetime_value);
CREATE INDEX IF NOT EXISTS customers_customer_since_idx ON public.customers(customer_since);
CREATE INDEX IF NOT EXISTS customer_tag_assignments_customer_id_idx ON public.customer_tag_assignments(customer_id);
CREATE INDEX IF NOT EXISTS customer_tag_assignments_tag_id_idx ON public.customer_tag_assignments(tag_id);

-- Set up RLS policies
ALTER TABLE public.customer_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_tag_assignments ENABLE ROW LEVEL SECURITY;

-- Create policies for customer_tags
CREATE POLICY "Staff can view all customer tags" ON public.customer_tags
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can insert customer tags" ON public.customer_tags
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can update customer tags" ON public.customer_tags
  FOR UPDATE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Create policies for customer_tag_assignments
CREATE POLICY "Staff can view all customer tag assignments" ON public.customer_tag_assignments
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can insert customer tag assignments" ON public.customer_tag_assignments
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can delete customer tag assignments" ON public.customer_tag_assignments
  FOR DELETE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Create function to update customer statistics
CREATE OR REPLACE FUNCTION update_customer_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update customer statistics when a booking is created, updated, or deleted
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    -- Update last_booking_date and booking_count
    UPDATE public.customers
    SET 
      last_booking_date = (
        SELECT MAX(start_time)
        FROM public.bookings
        WHERE customer_id = NEW.customer_id
          AND status != 'canceled'
      ),
      booking_count = (
        SELECT COUNT(*)
        FROM public.bookings
        WHERE customer_id = NEW.customer_id
          AND status != 'canceled'
      )
    WHERE id = NEW.customer_id;
    
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Update last_booking_date and booking_count
    UPDATE public.customers
    SET 
      last_booking_date = (
        SELECT MAX(start_time)
        FROM public.bookings
        WHERE customer_id = OLD.customer_id
          AND status != 'canceled'
      ),
      booking_count = (
        SELECT COUNT(*)
        FROM public.bookings
        WHERE customer_id = OLD.customer_id
          AND status != 'canceled'
      )
    WHERE id = OLD.customer_id;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for customer statistics
DROP TRIGGER IF EXISTS booking_customer_statistics_trigger ON public.bookings;
CREATE TRIGGER booking_customer_statistics_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION update_customer_statistics();

-- Insert default customer tags
INSERT INTO public.customer_tags (name, color, description)
VALUES 
  ('VIP', '#FFD700', 'Very important customers'),
  ('Regular', '#4CAF50', 'Regular customers'),
  ('New', '#2196F3', 'New customers'),
  ('Inactive', '#9E9E9E', 'Customers who haven''t booked in a while'),
  ('High Value', '#F44336', 'Customers with high lifetime value')
ON CONFLICT (name) DO NOTHING;
