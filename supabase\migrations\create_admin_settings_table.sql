-- Create admin_settings table for Ocean Soul Sparkles
-- This resolves the "relation public.admin_settings does not exist" error

-- Create the admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.admin_settings (
  setting_key TEXT PRIMARY KEY,
  setting_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.admin_settings ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access
DROP POLICY IF EXISTS "Admin users can manage admin settings" ON public.admin_settings;
CREATE POLICY "Admin users can manage admin settings" 
  ON public.admin_settings 
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON public.admin_settings(setting_key);

-- Insert default admin settings if they don't exist
INSERT INTO public.admin_settings (setting_key, setting_value) VALUES
  -- Email Configuration
  ('gmail_api_tokens', '{}'),
  ('gmail_smtp_user', ''),
  ('gmail_smtp_password', ''),
  ('gmail_from_email', ''),
  ('gmail_from_name', 'Ocean Soul Sparkles'),
  ('workspace_smtp_user', ''),
  ('workspace_smtp_password', ''),
  ('workspace_from_email', ''),
  ('workspace_from_name', 'Ocean Soul Sparkles'),
  ('onesignal_app_id', ''),
  ('onesignal_rest_api_key', ''),
  ('email_service_priority', 'gmail,workspace,onesignal'),
  
  -- Google Cloud Configuration
  ('google_client_id', ''),
  ('google_client_secret', ''),
  ('google_project_id', ''),
  
  -- System Configuration
  ('system_maintenance_mode', 'false'),
  ('system_debug_mode', 'false'),
  ('system_email_enabled', 'true'),
  ('system_notifications_enabled', 'true')
ON CONFLICT (setting_key) DO NOTHING;

-- Create a function to safely get admin settings
CREATE OR REPLACE FUNCTION public.get_admin_setting(setting_key_param text)
RETURNS text
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT setting_value 
  FROM public.admin_settings 
  WHERE setting_key = setting_key_param;
$$;

-- Create a function to safely set admin settings
CREATE OR REPLACE FUNCTION public.set_admin_setting(setting_key_param text, setting_value_param text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.admin_settings (setting_key, setting_value, updated_at)
  VALUES (setting_key_param, setting_value_param, NOW())
  ON CONFLICT (setting_key) 
  DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_at = EXCLUDED.updated_at;
END;
$$;

-- Grant necessary permissions
GRANT SELECT ON public.admin_settings TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_setting(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_admin_setting(text, text) TO authenticated;

-- Add comments
COMMENT ON TABLE public.admin_settings IS 'Administrative settings for Ocean Soul Sparkles system';
COMMENT ON FUNCTION public.get_admin_setting(text) IS 'Safely retrieve an admin setting value';
COMMENT ON FUNCTION public.set_admin_setting(text, text) IS 'Safely set an admin setting value';
