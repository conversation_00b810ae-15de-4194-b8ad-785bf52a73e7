import React from 'react';
import { adjustColor, getTextColor } from '@/lib/color-utils';
import { STATUS_COLORS } from '@/lib/booking-status';
import styles from '@/styles/admin/BookingEventWrapper.module.css';

/**
 * Custom event wrapper component for react-big-calendar
 * 
 * @param {Object} props - Component props from react-big-calendar
 * @returns {JSX.Element}
 */
export default function BookingEventWrapper({ event, children }) {
  // Get event data
  const { resource } = event;
  const { status, serviceColor } = resource;
  
  // Base color from service
  let backgroundColor = serviceColor || '#6a0dad';
  let borderColor = backgroundColor;
  
  // Modify based on status
  if (status && STATUS_COLORS[status]) {
    // For confirmed status, use the service color
    if (status === 'confirmed') {
      backgroundColor = adjustColor(backgroundColor, 10); // Slightly brighter
      borderColor = STATUS_COLORS[status]; // Green border
    } 
    // For pending status, use a muted version of the service color
    else if (status === 'pending') {
      backgroundColor = adjustColor(backgroundColor, -15); // Slightly darker
      borderColor = STATUS_COLORS[status]; // Orange border
    }
    // For canceled status, use gray
    else if (status === 'canceled') {
      backgroundColor = '#aaaaaa'; // Gray
      borderColor = STATUS_COLORS[status]; // Red border
    }
    // For no-show status, use dark gray
    else if (status === 'no_show') {
      backgroundColor = '#777777'; // Dark gray
      borderColor = STATUS_COLORS[status]; // Dark red border
    }
    // For completed status, use the service color with a green border
    else if (status === 'completed') {
      backgroundColor = adjustColor(backgroundColor, 5); // Slightly brighter
      borderColor = STATUS_COLORS[status]; // Dark green border
    }
    // For in-progress status, use a brighter version of the service color
    else if (status === 'in_progress') {
      backgroundColor = adjustColor(backgroundColor, 20); // Brighter
      borderColor = STATUS_COLORS[status]; // Blue border
    }
    // For rescheduled status, use a purple-tinted version of the service color
    else if (status === 'rescheduled') {
      backgroundColor = adjustColor(backgroundColor, -10); // Slightly darker
      borderColor = STATUS_COLORS[status]; // Purple border
    }
  }
  
  // Determine text color based on background color
  const textColor = getTextColor(backgroundColor);
  
  // Apply styles
  const style = {
    backgroundColor,
    borderLeft: `4px solid ${borderColor}`,
    color: textColor,
    opacity: status === 'canceled' ? 0.7 : 1,
  };
  
  return (
    <div className={styles.eventWrapper} style={style}>
      {children}
      {status && status !== 'confirmed' && (
        <div className={styles.statusIndicator} style={{ backgroundColor: borderColor }}>
          {status.charAt(0).toUpperCase()}
        </div>
      )}
    </div>
  );
}
