.testimonialSection {
  padding: 2rem 2rem;
  background-color: transparent;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.testimonialTitle {
  font-size: 2.5rem;
  color: var(--text-color);
  margin-bottom: 1rem;
  display: none; /* Hide the title since we're using the one from the parent section */
}

.divider {
  width: 70px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 0 auto 3rem;
  border-radius: 3px;
  display: none; /* Hide the divider since we're using the one from the parent section */
}

.testimonialSliderContainer {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
}

.testimonialSlider {
  position: relative;
  height: 200px;
  margin-bottom: 2rem;
}

.testimonialSlide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 1rem;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.testimonialSlide.active {
  opacity: 1;
  transform: translateY(0);
}

.testimonialQuote {
  font-size: 1.3rem;
  line-height: 1.6;
  color: var(--text-color);
  font-style: italic;
  margin: 0 0 1.5rem;
  position: relative;
}

.testimonialQuote::before,
.testimonialQuote::after {
  content: '"';
  color: var(--primary-color);
  font-size: 2.5rem;
  opacity: 0.5;
}

.testimonialAuthor {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1.1rem;
}

.testimonialControls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.testimonialNavButton {
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.testimonialNavButton:hover {
  background-color: var(--primary-color);
  color: white;
}

.testimonialDots {
  display: flex;
  gap: 0.5rem;
}

.testimonialDot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.testimonialDot:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.testimonialDot.activeDot {
  background-color: var(--primary-color);
  transform: scale(1.2);
}

@media (max-width: 768px) {
  .testimonialSection {
    padding: 3rem 1.5rem;
  }

  .testimonialTitle {
    font-size: 2rem;
  }

  .testimonialQuote {
    font-size: 1.1rem;
  }

  .testimonialSlider {
    height: 280px;
  }
}

@media (max-width: 480px) {
  .testimonialSection {
    padding: 2.5rem 1rem;
  }

  .testimonialTitle {
    font-size: 1.75rem;
  }

  .testimonialQuote {
    font-size: 1rem;
  }

  .testimonialSlider {
    height: 320px;
  }
}
