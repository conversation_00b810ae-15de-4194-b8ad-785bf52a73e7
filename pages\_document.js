import { Html, Head, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Extension Error Suppression - Load first to catch early errors */}
        <script src="/js/extension-error-suppression.js"></script>

        {/* Square Performance Monitor - Load early to catch performance issues */}
        <script src="/js/square-performance-monitor.js"></script>

        {/* Font preloading */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
        <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap" rel="stylesheet" />

        {/* OneSignal Web Push Notifications - Only loaded in production */}
        {process.env.NODE_ENV === 'production' && (
          <>
            {/* Store OneSignal configuration as global variables */}
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  // Store OneSignal configuration
                  window.__ONESIGNAL_APP_ID__ = "${process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID || '************************************'}";
                  window.__ONESIGNAL_SAFARI_WEB_ID__ = "${process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID || 'web.onesignal.auto.************************************'}";

                  // Prevent hydration errors by defining a mock OneSignal object
                  // This will be replaced by the real OneSignal when it loads
                  try {
                    // Only create mock if OneSignal doesn't exist yet
                    if (!window.OneSignal) {
                      window.OneSignal = {
                        _mock: true,
                        getNotificationPermission: function() { return Promise.resolve('default'); },
                        isPushNotificationsEnabled: function() { return Promise.resolve(false); },
                        showNativePrompt: function() { return Promise.resolve(); },
                        showHttpPrompt: function() { return Promise.resolve(); },
                        showCategorySlidedown: function() { return Promise.resolve(); },
                        getUserId: function() { return Promise.resolve('mock-user-id'); },
                        setExternalUserId: function() { return Promise.resolve(); },
                        removeExternalUserId: function() { return Promise.resolve(); },
                        setEmail: function() { return Promise.resolve(); },
                        sendTag: function() { return Promise.resolve(); },
                        getTags: function() { return Promise.resolve({}); },
                        on: function() {},
                        once: function() {},
                        off: function() {},
                        init: function() { return Promise.resolve(); }
                      };
                      console.log('Created OneSignal mock object');
                    }
                  } catch (e) {
                    console.error('Error creating OneSignal mock:', e);
                  }
                `
              }}
            />            {/* Load OneSignal SDK */}
            <script src="https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.page.js" defer></script>

            {/* Load our robust initialization script */}
            <script src="/js/onesignal-robust-init.js" defer></script>
          </>
        )}

        {/* Structured Data - Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "OceanSoulSparkles",
              "url": "https://www.oceansoulsparkles.com.au",
              "logo": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
              "sameAs": [
                "https://www.instagram.com/oceansoulsparkles",
                "https://www.facebook.com/OceanSoulSparkles/"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+61-XXX-XXX-XXX",
                "contactType": "customer service",
                "email": "<EMAIL>"
              }
            })
          }}
        />

        {/* Structured Data - LocalBusiness */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "EntertainmentBusiness",
              "name": "OceanSoulSparkles",
              "image": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
              "url": "https://www.oceansoulsparkles.com.au",
              "telephone": "+61-XXX-XXX-XXX",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "addressLocality": "Melbourne",
                "addressRegion": "Victoria",
                "addressCountry": "AU"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": "-37.8136",
                "longitude": "144.9631"
              },
              "priceRange": "$$"
            })
          }}
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}
