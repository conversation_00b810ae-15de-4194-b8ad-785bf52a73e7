import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import { authenticatedFetch } from '@/lib/auth-utils'
import LoadingButton from '@/components/admin/LoadingButton'
import styles from '@/styles/admin/CustomerForm.module.css'

export default function CustomerForm({ customer, onSave, onCancel }) {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'Australia',
    notes: '',
    marketing_consent: false,
    customer_status: 'active',
    customer_tier: 'bronze'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)
  const [preferences, setPreferences] = useState([])
  const [newPreference, setNewPreference] = useState({ key: '', value: '' })
  const [activeTab, setActiveTab] = useState('contact')
  const [formErrors, setFormErrors] = useState({})
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Initialize form with customer data if editing
  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name || '',
        email: customer.email || '',
        phone: customer.phone || '',
        address: customer.address || '',
        city: customer.city || '',
        state: customer.state || '',
        postal_code: customer.postal_code || '',
        country: customer.country || 'Australia',
        notes: customer.notes || '',
        marketing_consent: customer.marketing_consent || false,
        customer_status: customer.customer_status || 'active',
        customer_tier: customer.customer_tier || 'bronze'
      })
    }
  }, [customer])

  // Load customer preferences if editing
  useEffect(() => {
    if (customer?.id && isAuthenticated) {
      const fetchPreferences = async () => {
        try {
          // Use the authenticatedFetch utility
          const data = await authenticatedFetch(`/api/customers/${customer.id}`);

          if (data.preferences && Array.isArray(data.preferences)) {
            const formattedPreferences = data.preferences.map(pref => ({
              key: pref.preference_key,
              value: pref.preference_value
            }));
            setPreferences(formattedPreferences);
          }
        } catch (error) {
          console.error('Error fetching preferences:', error);
          setError('Failed to load customer preferences. Please try again.');
        }
      };

      fetchPreferences();
    }
  }, [customer, isAuthenticated])

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    const newValue = type === 'checkbox' ? checked : value

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }))

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }

    // Track unsaved changes
    setHasUnsavedChanges(true)
  }

  // Validate form fields
  const validateForm = () => {
    const errors = {}

    // Required fields
    if (!formData.name?.trim()) {
      errors.name = 'Name is required'
    }
    if (!formData.email?.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }

    // Optional field validation
    if (formData.phone && !/^[\d\s\-\+\(\)]+$/.test(formData.phone)) {
      errors.phone = 'Please enter a valid phone number'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab)
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validate form before submission
    if (!validateForm()) {
      setError('Please fix the errors below before saving.')
      return
    }

    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Prepare request data
      const requestData = {
        ...formData,
        preferences: preferences.map(pref => ({
          key: pref.key,
          value: pref.value
        }))
      }

      let savedCustomer;

      if (customer?.id) {
        // Update existing customer using authenticatedFetch
        savedCustomer = await authenticatedFetch(`/api/customers/${customer.id}`, {
          method: 'PUT',
          body: JSON.stringify(requestData)
        });
      } else {
        // Create new customer using authenticatedFetch
        savedCustomer = await authenticatedFetch('/api/customers', {
          method: 'POST',
          body: JSON.stringify(requestData)
        });
      }

      setSuccessMessage(customer?.id ? 'Customer updated successfully!' : 'Customer created successfully!')
      setHasUnsavedChanges(false)

      // Call onSave callback if provided
      if (onSave) {
        onSave(savedCustomer)
      } else {
        // Navigate to customer details page after a short delay
        setTimeout(() => {
          router.push(`/admin/customers/${savedCustomer.id}`)
        }, 1500)
      }
    } catch (error) {
      console.error('Error saving customer:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle adding a new preference
  const handleAddPreference = () => {
    if (newPreference.key && newPreference.value) {
      setPreferences([...preferences, { ...newPreference }])
      setNewPreference({ key: '', value: '' })
    }
  }

  // Handle removing a preference
  const handleRemovePreference = (index) => {
    setPreferences(preferences.filter((_, i) => i !== index))
  }

  // Handle preference input changes
  const handlePreferenceChange = (e) => {
    const { name, value } = e.target
    setNewPreference(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Render form field with error handling
  const renderField = (name, label, type = 'text', required = false, options = null, placeholder = '') => {
    const hasError = formErrors[name]
    const fieldId = `field-${name}`

    return (
      <div className={styles.formGroup}>
        <label htmlFor={fieldId} className={styles.fieldLabel}>
          {label}
          {required && <span className={styles.required}>*</span>}
        </label>

        {type === 'select' ? (
          <select
            id={fieldId}
            name={name}
            value={formData[name] || ''}
            onChange={handleChange}
            disabled={loading}
            className={`${styles.select} ${hasError ? styles.fieldError : ''}`}
          >
            {options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : type === 'textarea' ? (
          <textarea
            id={fieldId}
            name={name}
            value={formData[name] || ''}
            onChange={handleChange}
            disabled={loading}
            className={`${styles.textarea} ${hasError ? styles.fieldError : ''}`}
            placeholder={placeholder}
            rows={4}
          />
        ) : type === 'checkbox' ? (
          <div className={styles.checkboxWrapper}>
            <input
              type="checkbox"
              id={fieldId}
              name={name}
              checked={formData[name] || false}
              onChange={handleChange}
              disabled={loading}
              className={styles.checkbox}
            />
            <label htmlFor={fieldId} className={styles.checkboxLabel}>
              {placeholder}
            </label>
          </div>
        ) : (
          <input
            type={type}
            id={fieldId}
            name={name}
            value={formData[name] || ''}
            onChange={handleChange}
            disabled={loading}
            className={`${styles.input} ${hasError ? styles.fieldError : ''}`}
            placeholder={placeholder}
            required={required}
          />
        )}

        {hasError && <span className={styles.errorText}>{hasError}</span>}
      </div>
    )
  }

  return (
    <div className={styles.customerForm}>
      {/* Sticky Header */}
      <div className={styles.stickyHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerInfo}>
            <h1 className={styles.customerName}>
              {customer?.id ? `Edit: ${customer.name}` : 'New Customer'}
            </h1>
            {hasUnsavedChanges && (
              <span className={styles.unsavedIndicator}>• Unsaved changes</span>
            )}
          </div>

          <div className={styles.headerActions}>
            <button
              type="button"
              onClick={() => router.push(`/admin/customers/${customer?.id}`)}
              className={styles.viewProfileButton}
              disabled={!customer?.id}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              View Profile
            </button>

            <button
              type="button"
              onClick={() => router.push(`/admin/bookings/new?customer_id=${customer?.id}`)}
              className={styles.newBookingButton}
              disabled={!customer?.id}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              New Booking
            </button>

            <LoadingButton
              type="button"
              onClick={onCancel || (() => router.back())}
              loading={loading}
              variant="secondary"
              className={styles.cancelButton}
            >
              Cancel
            </LoadingButton>

            <LoadingButton
              type="submit"
              form="customer-form"
              loading={loading}
              loadingText="Saving..."
              variant="primary"
              className={styles.saveButton}
            >
              {customer?.id ? 'Update Customer' : 'Create Customer'}
            </LoadingButton>
          </div>
        </div>
      </div>

      {/* Breadcrumb Navigation */}
      <div className={styles.breadcrumb}>
        <span onClick={() => router.push('/admin')} className={styles.breadcrumbLink}>Admin</span>
        <span className={styles.breadcrumbSeparator}>›</span>
        <span onClick={() => router.push('/admin/customers')} className={styles.breadcrumbLink}>Customers</span>
        <span className={styles.breadcrumbSeparator}>›</span>
        {customer?.id && (
          <>
            <span onClick={() => router.push(`/admin/customers/${customer.id}`)} className={styles.breadcrumbLink}>
              {customer.name}
            </span>
            <span className={styles.breadcrumbSeparator}>›</span>
          </>
        )}
        <span className={styles.breadcrumbCurrent}>Edit</span>
      </div>

      {/* Error and Success Messages */}
      {error && <div className={styles.error}>{error}</div>}
      {successMessage && <div className={styles.success}>{successMessage}</div>}

      {/* Tabbed Interface */}
      <div className={styles.tabsContainer}>
        <div className={styles.tabs}>
          <button
            type="button"
            className={`${styles.tabButton} ${activeTab === 'contact' ? styles.activeTab : ''}`}
            onClick={() => handleTabChange('contact')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            Contact Info
          </button>

          <button
            type="button"
            className={`${styles.tabButton} ${activeTab === 'management' ? styles.activeTab : ''}`}
            onClick={() => handleTabChange('management')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            Management
          </button>

          <button
            type="button"
            className={`${styles.tabButton} ${activeTab === 'marketing' ? styles.activeTab : ''}`}
            onClick={() => handleTabChange('marketing')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            Marketing
          </button>

          <button
            type="button"
            className={`${styles.tabButton} ${activeTab === 'preferences' ? styles.activeTab : ''}`}
            onClick={() => handleTabChange('preferences')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
            </svg>
            Preferences
          </button>
        </div>
      </div>

      {/* Form Content */}
      <form id="customer-form" onSubmit={handleSubmit} className={styles.formContent}>
        <div className={styles.tabContent}>

          {/* Contact Information Tab */}
          {activeTab === 'contact' && (
            <div className={styles.tabPanel}>
              <div className={styles.sectionHeader}>
                <h2>Contact Information</h2>
                <p className={styles.sectionDescription}>
                  Basic customer contact details and address information.
                </p>
              </div>

              <div className={styles.formGrid}>
                {renderField('name', 'Full Name', 'text', true, null, 'Enter customer\'s full name')}
                {renderField('email', 'Email Address', 'email', true, null, 'Enter email address')}
                {renderField('phone', 'Phone Number', 'tel', false, null, 'Enter phone number')}
                {renderField('country', 'Country', 'text', false, null, 'Australia')}
              </div>

              <div className={styles.sectionDivider}>
                <h3>Address Information</h3>
                <p className={styles.sectionSubtext}>Optional address details for service delivery and billing.</p>
              </div>

              <div className={styles.formGrid}>
                {renderField('address', 'Street Address', 'text', false, null, 'Enter street address')}
                {renderField('city', 'City', 'text', false, null, 'Enter city')}
                {renderField('state', 'State', 'select', false, [
                  { value: '', label: 'Select State' },
                  { value: 'NSW', label: 'NSW' },
                  { value: 'VIC', label: 'VIC' },
                  { value: 'QLD', label: 'QLD' },
                  { value: 'WA', label: 'WA' },
                  { value: 'SA', label: 'SA' },
                  { value: 'TAS', label: 'TAS' },
                  { value: 'ACT', label: 'ACT' },
                  { value: 'NT', label: 'NT' }
                ])}
                {renderField('postal_code', 'Postal Code', 'text', false, null, 'Enter postal code')}
              </div>

              <div className={styles.fullWidth}>
                {renderField('notes', 'Customer Notes', 'textarea', false, null, 'Add any notes about this customer...')}
              </div>
            </div>
          )}

          {/* Management Settings Tab */}
          {activeTab === 'management' && (
            <div className={styles.tabPanel}>
              <div className={styles.sectionHeader}>
                <h2>Customer Management</h2>
                <p className={styles.sectionDescription}>
                  Manage customer status, tier level, and account settings.
                </p>
              </div>

              <div className={styles.formGrid}>
                <div className={styles.formGroup}>
                  <label className={styles.fieldLabel}>
                    Customer Status
                    <span className={styles.required}>*</span>
                  </label>
                  {renderField('customer_status', '', 'select', false, [
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' },
                    { value: 'suspended', label: 'Suspended' }
                  ])}
                  <small className={styles.fieldHelp}>
                    Active customers can book services. Inactive customers are hidden from booking. Suspended customers are blocked.
                  </small>
                </div>

                <div className={styles.formGroup}>
                  <label className={styles.fieldLabel}>Customer Tier</label>
                  {renderField('customer_tier', '', 'select', false, [
                    { value: 'bronze', label: 'Bronze' },
                    { value: 'silver', label: 'Silver' },
                    { value: 'gold', label: 'Gold' },
                    { value: 'platinum', label: 'Platinum' }
                  ])}
                  <small className={styles.fieldHelp}>
                    Customer tier affects pricing and service availability.
                  </small>
                </div>
              </div>
            </div>
          )}

          {/* Marketing Preferences Tab */}
          {activeTab === 'marketing' && (
            <div className={styles.tabPanel}>
              <div className={styles.sectionHeader}>
                <h2>Marketing Preferences</h2>
                <p className={styles.sectionDescription}>
                  Configure customer communication and marketing consent settings.
                </p>
              </div>

              <div className={styles.marketingOptions}>
                {renderField('marketing_consent', 'Marketing Communications', 'checkbox', false, null, 'Customer has consented to receive marketing communications')}

                <div className={styles.marketingInfo}>
                  <div className={styles.infoBox}>
                    <h4>What this includes:</h4>
                    <ul>
                      <li>Promotional emails about new services and offers</li>
                      <li>SMS notifications for special events and discounts</li>
                      <li>Newsletter updates about Ocean Soul Sparkles</li>
                      <li>Birthday and anniversary special offers</li>
                    </ul>
                  </div>

                  <div className={styles.complianceNote}>
                    <strong>Privacy Compliance:</strong> Customers can opt-out at any time.
                    All marketing communications include unsubscribe options as required by Australian Privacy Laws.
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Customer Preferences Tab */}
          {activeTab === 'preferences' && (
            <div className={styles.tabPanel}>
              <div className={styles.sectionHeader}>
                <h2>Customer Preferences</h2>
                <p className={styles.sectionDescription}>
                  Manage custom preferences and settings specific to this customer.
                </p>
              </div>

              <div className={styles.preferencesContainer}>
                {preferences.length > 0 && (
                  <div className={styles.preferencesList}>
                    <h3>Current Preferences</h3>
                    {preferences.map((pref, index) => (
                      <div key={index} className={styles.preferenceItem}>
                        <div className={styles.preferenceContent}>
                          <strong>{pref.key}:</strong> {pref.value}
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemovePreference(index)}
                          className={styles.removeButton}
                          disabled={loading}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                <div className={styles.addPreferenceSection}>
                  <h3>Add New Preference</h3>
                  <div className={styles.addPreference}>
                    <div className={styles.preferenceInputs}>
                      <input
                        type="text"
                        placeholder="Preference Key (e.g., 'Preferred Time')"
                        name="key"
                        value={newPreference.key}
                        onChange={handlePreferenceChange}
                        disabled={loading}
                        className={styles.input}
                      />
                      <input
                        type="text"
                        placeholder="Preference Value (e.g., 'Morning appointments')"
                        name="value"
                        value={newPreference.value}
                        onChange={handlePreferenceChange}
                        disabled={loading}
                        className={styles.input}
                      />
                    </div>
                    <button
                      type="button"
                      onClick={handleAddPreference}
                      disabled={!newPreference.key || !newPreference.value || loading}
                      className={styles.addButton}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                      Add Preference
                    </button>
                  </div>

                  <div className={styles.preferencesHelp}>
                    <h4>Common Preferences:</h4>
                    <ul>
                      <li><strong>Preferred Time:</strong> Morning, Afternoon, Evening</li>
                      <li><strong>Communication Method:</strong> Email, SMS, Phone</li>
                      <li><strong>Service Style:</strong> Bold, Natural, Colorful</li>
                      <li><strong>Appointment Reminders:</strong> 24 hours, 1 hour, None</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

        </div>
      </form>
    </div>
  )
}
