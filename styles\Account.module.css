.accountPage {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.title {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.subtitle {
  color: var(--secondary-color);
  font-size: 1.2rem;
}

.contentWrapper {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.sidebar {
  flex: 1;
  min-width: 250px;
}

.mainContent {
  flex: 3;
  min-width: 300px;
}

.navigationList {
  list-style: none;
  padding: 0;
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navigationItem {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.3s;
}

.navigationItem:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.navigationItem.active {
  background-color: var(--primary-color);
  color: white;
}

.navigationItem:last-child {
  border-bottom: none;
}

.sectionTitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.logoutButton {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
  margin-top: 1rem;
}

.logoutButton:hover {
  background-color: #f1f1f1;
  border-color: #ccc;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.loadingState {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.errorState {
  color: var(--error-color);
  background-color: #ffeaea;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .contentWrapper {
    flex-direction: column;
  }
  
  .sidebar, .mainContent {
    width: 100%;
  }
}
