import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState, useRef } from 'react'
import { toast } from 'react-toastify'
import { supabase } from '@/lib/supabase'
import authTokenManager from '@/lib/auth-token-manager'

export default function ProtectedRoute({
  children,
  adminOnly = false,
  devOnly = false,
  staffOnly = false,
  requiredRole = null
}) {
  const {
    user,
    role,
    loading: authLoading,
    hasAdminAccess,
    hasStaffAccess,
    hasFullAccess,
    isDev,
    isAdmin,
    isStaff
  } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(true)
  const [authTimeout, setAuthTimeout] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const timeoutRef = useRef(null)
  const retryTimeoutRef = useRef(null)

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  // Set up authentication timeout mechanism (disabled in development)
  useEffect(() => {
    // Skip timeout mechanisms in development mode or with auth bypass to prevent screen flashing
    if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS === 'true') {
      console.log('ProtectedRoute: Development mode or auth bypass - skipping timeout mechanisms')
      return
    }

    // Check if either authLoading or loading is true and timeout hasn't started
    if ((authLoading || loading) && !authTimeout) {
      // Set a timeout for authentication
      timeoutRef.current = setTimeout(() => {
        console.log('ProtectedRoute: Authentication timeout reached')
        setAuthTimeout(true)

        // Attempt automatic recovery with reduced attempts
        if (retryCount < 2) { // Reduced from 3 to 2
          console.log(`ProtectedRoute: Attempting recovery (attempt ${retryCount + 1}/2)`)
          setRetryCount(prev => prev + 1)

          // Try auth refresh instead of full page reload
          retryTimeoutRef.current = setTimeout(() => {
            console.log('ProtectedRoute: Attempting auth refresh')
            // Redirect to login instead of page reload
            router.push('/admin/login')
          }, 3000) // Increased delay
        } else {
          console.log('ProtectedRoute: Max retry attempts reached, redirecting to login')
          // After max retries, redirect to login
          router.push('/admin/login')
        }
      }, 20000) // Increased timeout to 20 seconds
    }

    // Clear timeout if both auth states complete
    if (!authLoading && !loading && timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
      setAuthTimeout(false)
    }
  }, [authLoading, loading, authTimeout, retryCount, router])

  // Store session token for API calls
  useEffect(() => {
    if (user && !authLoading) {
      const storeSessionToken = async () => {
        try {
          const { data: { session }, error } = await supabase.auth.getSession()

          if (session?.access_token && typeof window !== 'undefined') {
            // Store token using the auth token manager for consistency
            authTokenManager.storeTokenInMultipleLocations(session.access_token)
            console.log('ProtectedRoute: Auth token stored for API access')
          } else if (error) {
            console.warn('ProtectedRoute: Failed to get session for token storage:', error)
          }
        } catch (error) {
          console.warn('ProtectedRoute: Error storing session token:', error)
        }
      }

      storeSessionToken()
    }
  }, [user, authLoading])

  // Handle authentication and authorization with enhanced redirect loop prevention
  useEffect(() => {
    // In development mode with auth bypass, skip complex auth checks completely
    if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS === 'true') {
      console.log('ProtectedRoute: Development mode or auth bypass enabled - bypassing ALL auth checks and timeouts')
      setIsAuthorized(true)
      setError(null)
      setLoading(false)
      setAuthTimeout(false)
      setRetryCount(0)

      // Clear any existing timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
        retryTimeoutRef.current = null
      }

      return
    }

    // Always run checkAccess, but handle loading states appropriately
    console.log('ProtectedRoute: Running access check', { authLoading, user: !!user, role })

    const checkAccess = async () => {
      try {
        console.log('ProtectedRoute: Checking access for user:', user?.email, 'role:', role, 'authLoading:', authLoading)

        // If auth is still loading, don't make decisions yet unless we have cached user data
        if (authLoading && !user) {
          console.log('ProtectedRoute: Auth still loading and no user data, waiting...')
          // In development, don't set aggressive timeouts
          if (process.env.NODE_ENV !== 'development') {
            // Set a fallback timeout to prevent infinite loading (production only)
            if (!timeoutRef.current) {
              timeoutRef.current = setTimeout(() => {
                console.log('ProtectedRoute: Fallback timeout - forcing redirect to login')
                setLoading(false)
                router.replace('/admin/login')
              }, 10000) // 10 second fallback
            }
          }
          return
        }

        // Check if we're already on a login page to prevent redirect loops
        const isOnLoginPage = router.pathname === '/admin/login' ||
                             router.pathname === '/admin/reset-password' ||
                             router.pathname === '/admin/forgot-password'

        // If no user and not on login page, redirect to login
        if (!user && !isOnLoginPage) {
          console.log('ProtectedRoute: No user found, redirecting to login')

          // Set loading to false before redirecting
          setLoading(false)

          // Clear any existing timeouts to prevent conflicts
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
            timeoutRef.current = null
          }

          // Store redirect path and navigate to login
          sessionStorage.setItem('redirect_after_login', router.asPath)

          // Use replace to prevent back button issues
          router.replace('/admin/login').catch(error => {
            console.error('ProtectedRoute: Failed to redirect to login:', error)
            // Fallback: force navigation
            window.location.href = '/admin/login'
          })
          return
        }

        // If user exists but we're on login page, redirect to intended destination
        if (user && isOnLoginPage) {
          const redirectPath = sessionStorage.getItem('redirect_after_login') || '/admin'
          sessionStorage.removeItem('redirect_after_login')
          console.log('ProtectedRoute: User authenticated on login page, redirecting to:', redirectPath)

          // Set loading to false before redirecting
          setLoading(false)

          router.replace(redirectPath)
          return
        }

        // Enhanced authorization logic with new role system
        // Special case for known admin users (fallback)
        const knownAdmins = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        const isKnownAdmin = knownAdmins.includes(user.email);

        // DEV users have unrestricted access to everything
        if (isDev || isKnownAdmin) {
          console.log('ProtectedRoute: DEV user or known admin - granting unrestricted access')
          setIsAuthorized(true)
          setError(null)
          setLoading(false)
          return
        }

        // Check specific role requirements for non-DEV users
        let authorized = false
        let errorMessage = 'Access denied.'

        if (devOnly) {
          authorized = false // Only actual DEV users can access dev-only sections
          errorMessage = 'Access denied. Developer privileges required.'
        } else if (adminOnly) {
          authorized = hasAdminAccess
          errorMessage = 'Access denied. Admin privileges required.'
        } else if (staffOnly) {
          authorized = hasStaffAccess
          errorMessage = 'Access denied. Staff privileges required.'
        } else if (requiredRole) {
          authorized = role === requiredRole
          errorMessage = `Access denied. ${requiredRole} role required.`
        } else {
          // Default: require at least staff access for admin panel
          authorized = hasStaffAccess
          errorMessage = 'Access denied. Staff privileges required.'
        }

        // Redirect Artists and Braiders to their limited dashboard
        if (authorized && (role === 'artist' || role === 'braider')) {
          const currentPath = router.pathname
          const allowedPaths = [
            '/admin/artist-dashboard',
            '/admin/profile',
            '/admin/schedule',
            '/admin/payments'
          ]

          if (!allowedPaths.some(path => currentPath.startsWith(path))) {
            router.replace('/admin/artist-dashboard')
            return null
          }
        }

        if (!authorized) {
          console.log('ProtectedRoute: User lacks required privileges', {
            role,
            devOnly,
            adminOnly,
            staffOnly,
            requiredRole
          })
          setError(errorMessage)

          // Only show toast in development to avoid production UI issues
          if (process.env.NODE_ENV === 'development') {
            toast.error(errorMessage, {
              autoClose: 5000,
              position: 'top-center'
            })
          }

          setIsAuthorized(false)

          // In production, redirect to login after a brief delay
          if (process.env.NODE_ENV === 'production') {
            setTimeout(() => {
              router.replace('/admin/login')
            }, 2000)
          }
        } else {
          // User is authorized
          console.log('ProtectedRoute: User authorized', { role, authorized })
          setIsAuthorized(true)
          setError(null)
        }

        // Always set loading to false at the end
        setLoading(false)
      } catch (error) {
        console.error('Access verification failed:', error)
        const errorMsg = 'Authentication failed: ' + (error?.message || 'Unknown error')
        setError(errorMsg)
        setLoading(false)

        // In production, redirect to login on authentication errors
        if (process.env.NODE_ENV === 'production') {
          setTimeout(() => {
            router.replace('/admin/login')
          }, 3000)
        }
      }
    }

    checkAccess()
  }, [user, role, authLoading]) // Simplified dependencies to prevent unnecessary re-renders

  // In development mode or with auth bypass, completely bypass all authentication logic
  if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS === 'true') {
    console.log('ProtectedRoute: Development mode or auth bypass enabled - rendering children directly without auth checks')
    return children
  }

  // Show loading state, error, or children
  if (authLoading || loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen" data-testid="auth-loading">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600 auth-loading">
          {authTimeout ? 'Attempting recovery...' : 'Authenticating...'}
        </div>
        <div className="text-xs text-gray-400 mt-2">
          {authTimeout ? (
            retryCount < 3 ? (
              `Recovery attempt ${retryCount}/3 - Page will refresh automatically`
            ) : (
              'Redirecting to login...'
            )
          ) : (
            'If this takes too long, the page will automatically recover'
          )}
        </div>
        {authTimeout && retryCount > 0 && (
          <div className="text-xs text-orange-500 mt-1">
            Authentication is taking longer than expected. Automatic recovery in progress.
          </div>
        )}
      </div>
    )
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-center mt-4 px-4 max-w-md">
          <p className="font-semibold">Authorization Error:</p>
          <p>{error || 'Unauthorized access'}</p>
          <button
            onClick={() => router.push('/admin/login')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Return to Login
          </button>
        </div>
      </div>
    )
  }

  return children
}
