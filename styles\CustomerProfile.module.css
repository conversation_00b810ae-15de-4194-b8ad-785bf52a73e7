/* Customer Profile Styles */
.profileContainer {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: var(--font-primary);
}

.profileHeader, .editHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.profileHeader h1, .editHeader h1 {
  font-size: 1.8rem;
  margin: 0;
  color: var(--color-primary, #6a0dad);
}

.editButton {
  background-color: var(--color-primary, #6a0dad);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.editButton:hover {
  background-color: var(--color-primary-dark, #4e0980);
}

.cancelButton {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancelButton:hover {
  background-color: #eee;
}

.profileSection {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.profileSection h2 {
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--color-primary, #6a0dad);
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.infoItem {
  display: flex;
  flex-direction: column;
}

.infoLabel {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 4px;
}

.infoValue {
  font-size: 1.1rem;
  color: #333;
}

.noData {
  color: #999;
  font-style: italic;
}

.preferencesContainer {
  padding: 15px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.preferenceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.preferenceLabel {
  font-weight: 500;
}

.preferenceStatus {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.preferenceStatus.active {
  background-color: rgba(76, 175, 80, 0.1);
  color: #388e3c;
}

.preferenceStatus.inactive {
  background-color: rgba(158, 158, 158, 0.1);
  color: #616161;
}

.preferencesDescription {
  font-size: 0.9rem;
  color: #666;
  margin-top: 10px;
  line-height: 1.4;
}

.accountInfo {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  color: #666;
}

.accountInfo h3 {
  font-size: 1rem;
  margin-top: 0;
  margin-bottom: 5px;
  color: #333;
}

.accountInfo p {
  margin: 0;
  font-size: 0.9rem;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.error {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
  padding: 15px;
  border-radius: 6px;
  margin: 20px 0;
}

.notFound {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 600px) {
  .profileHeader, .editHeader {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
}
