import { useState } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * ArtistSelector component for choosing an artist for the selected service
 * 
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service with available artists
 * @param {Function} props.onArtistSelect - Callback when artist is selected
 * @param {Function} props.onBack - Callback to go back to service selection
 * @returns {JSX.Element}
 */
export default function ArtistSelector({ service, onArtistSelect, onBack }) {
  const [selectedArtistId, setSelectedArtistId] = useState(null)
  const [showAnyArtist, setShowAnyArtist] = useState(false)

  if (!service) {
    return (
      <div className={styles.artistSelector}>
        <div className={styles.errorMessage}>
          <h3>No Service Selected</h3>
          <p>Please select a service first.</p>
          <button className={styles.backButton} onClick={onBack}>
            ← Back to Services
          </button>
        </div>
      </div>
    )
  }

  // Filter artists available today
  const availableArtists = service.availableArtists?.filter(artist => artist.isAvailableToday) || []

  const handleArtistClick = (artist) => {
    setSelectedArtistId(artist.id)
    setShowAnyArtist(false)
    
    // Small delay for visual feedback
    setTimeout(() => {
      onArtistSelect({
        artist,
        isAnyArtist: false
      })
    }, 150)
  }

  const handleAnyArtistClick = () => {
    setSelectedArtistId('any')
    setShowAnyArtist(true)
    
    // Small delay for visual feedback
    setTimeout(() => {
      onArtistSelect({
        artist: null,
        isAnyArtist: true
      })
    }, 150)
  }

  const getSkillLevelColor = (skillLevel) => {
    const colors = {
      'beginner': '#4CAF50',
      'intermediate': '#FF9800', 
      'advanced': '#2196F3',
      'expert': '#9C27B0'
    }
    return colors[skillLevel] || '#757575'
  }

  const getSkillLevelIcon = (skillLevel) => {
    const icons = {
      'beginner': '🌱',
      'intermediate': '⭐',
      'advanced': '🏆',
      'expert': '👑'
    }
    return icons[skillLevel] || '⭐'
  }

  return (
    <div className={styles.artistSelector}>
      <div className={styles.selectorHeader}>
        <h2 className={styles.selectorTitle}>Choose Your Artist</h2>
        <p className={styles.selectorSubtitle}>
          For {safeRender(service.name, 'Selected Service')}
        </p>
      </div>

      <div className={styles.artistGrid}>
        {/* Any Available Artist Option */}
        <div
          className={`${styles.artistCard} ${styles.anyArtistCard} ${
            selectedArtistId === 'any' ? styles.selected : ''
          }`}
          onClick={handleAnyArtistClick}
        >
          <div className={styles.artistAvatar}>
            <span className={styles.anyArtistIcon}>🎯</span>
          </div>
          <div className={styles.artistInfo}>
            <h3 className={styles.artistName}>Any Available Artist</h3>
            <p className={styles.artistSpecialty}>
              System will assign the next available artist
            </p>
            <div className={styles.artistBadge}>
              <span className={styles.badgeText}>Auto-Assign</span>
            </div>
          </div>
          <div className={styles.artistRate}>
            <span className={styles.rateText}>Standard Rate</span>
            <span className={styles.rateAmount}>{service.priceRange}</span>
          </div>
        </div>

        {/* Individual Artist Cards */}
        {availableArtists.map((artist) => {
          const isSelected = selectedArtistId === artist.id
          const skillColor = getSkillLevelColor(artist.skillLevel)
          const skillIcon = getSkillLevelIcon(artist.skillLevel)
          
          return (
            <div
              key={artist.id}
              className={`${styles.artistCard} ${isSelected ? styles.selected : ''}`}
              onClick={() => handleArtistClick(artist)}
            >
              <div className={styles.artistAvatar}>
                <span className={styles.artistInitials}>
                  {(artist.displayName || artist.name || 'A').charAt(0).toUpperCase()}
                </span>
              </div>
              
              <div className={styles.artistInfo}>
                <h3 className={styles.artistName}>
                  {safeRender(artist.displayName || artist.name, 'Artist')}
                </h3>
                
                <div className={styles.artistSkillLevel}>
                  <span 
                    className={styles.skillBadge}
                    style={{ backgroundColor: skillColor }}
                  >
                    {skillIcon} {artist.skillLevel}
                  </span>
                </div>

                {artist.isPrimaryService && (
                  <div className={styles.primaryServiceBadge}>
                    <span className={styles.badgeText}>Specialist</span>
                  </div>
                )}
              </div>

              <div className={styles.artistRate}>
                <span className={styles.rateText}>Rate</span>
                <span className={styles.rateAmount}>
                  {artist.customRate ? `$${artist.customRate}` : service.priceRange}
                </span>
              </div>
            </div>
          )
        })}
      </div>

      {availableArtists.length === 0 && (
        <div className={styles.noArtistsMessage}>
          <h3>No Artists Available</h3>
          <p>Unfortunately, no artists are available for this service today.</p>
          <p>Please try selecting a different service or check back later.</p>
        </div>
      )}

      <div className={styles.navigationButtons}>
        <button 
          className={styles.backButton}
          onClick={onBack}
        >
          ← Back to Services
        </button>
        
        <div className={styles.selectionInfo}>
          {selectedArtistId ? (
            <p>
              {selectedArtistId === 'any' 
                ? 'System will assign the next available artist'
                : `Selected: ${availableArtists.find(a => a.id === selectedArtistId)?.displayName || 'Artist'}`
              }
            </p>
          ) : (
            <p>Select an artist above to continue</p>
          )}
        </div>
      </div>
    </div>
  )
}
