/**
 * Development Authentication Toggle Component
 * 
 * This component provides a toggle for enabling/disabling development authentication.
 * It should ONLY be rendered in development mode.
 */

import { useState, useEffect } from 'react';
import { testDevAuth } from '@/lib/dev-auth';
import styles from '@/styles/admin/DevTools.module.css';

export default function DevAuthToggle() {
  const [enabled, setEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  
  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Load the current setting from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const useDevAuth = localStorage.getItem('use_dev_auth');
        setEnabled(useDevAuth === 'true');
      } catch (error) {
        console.warn('Error loading dev auth setting:', error);
      }
    }
  }, []);
  
  // Toggle the development authentication
  const toggleDevAuth = () => {
    const newValue = !enabled;
    setEnabled(newValue);
    
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.setItem('use_dev_auth', newValue.toString());
        
        // Force reload to apply the change
        window.location.reload();
      } catch (error) {
        console.warn('Error saving dev auth setting:', error);
      }
    }
  };
  
  // Test the development authentication
  const runAuthTest = async () => {
    setLoading(true);
    setTestResult(null);
    
    try {
      const result = await testDevAuth();
      setTestResult({
        success: true,
        data: result
      });
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Don't render anything in production
  if (!isDevelopment) {
    return null;
  }
  
  return (
    <div className={styles.devToolsContainer}>
      <div className={styles.devToolsHeader}>
        <h3>Development Authentication</h3>
        <div className={styles.devToolsToggle}>
          <label className={styles.switch}>
            <input
              type="checkbox"
              checked={enabled}
              onChange={toggleDevAuth}
            />
            <span className={styles.slider}></span>
          </label>
          <span>{enabled ? 'Enabled' : 'Disabled'}</span>
        </div>
      </div>
      
      <div className={styles.devToolsContent}>
        <p>
          {enabled 
            ? 'Using development bypass token for authentication. API requests will bypass normal authentication.' 
            : 'Using normal authentication. API requests will require valid tokens.'}
        </p>
        
        <div className={styles.devToolsActions}>
          <button 
            onClick={runAuthTest}
            disabled={loading}
            className={styles.testButton}
          >
            {loading ? 'Testing...' : 'Test Authentication'}
          </button>
          
          {testResult && (
            <button
              onClick={() => setShowDetails(!showDetails)}
              className={styles.detailsButton}
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>
          )}
        </div>
        
        {testResult && (
          <div className={`${styles.testResult} ${testResult.success ? styles.success : styles.error}`}>
            <p>{testResult.success ? 'Authentication test passed!' : 'Authentication test failed!'}</p>
            
            {showDetails && (
              <pre className={styles.resultDetails}>
                {JSON.stringify(testResult.success ? testResult.data : testResult.error, null, 2)}
              </pre>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
