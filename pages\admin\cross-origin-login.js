import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import supabase from '@/lib/supabase';
import styles from '@/styles/admin/Login.module.css';

/**
 * Special login page for cross-origin access
 * This page is designed to be used when accessing the admin panel from a different origin
 */
export default function CrossOriginLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  // Check if we're in a cross-origin context
  const isCrossOrigin = process.env.NEXT_PUBLIC_ALLOW_CROSS_ORIGIN === 'true';
  const devUrl = process.env.NEXT_PUBLIC_DEV_URL || '';
  // Check if already logged in and handle redirect
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: sessionData } = await supabase.auth.getSession();
        if (sessionData?.session?.user) {
          // Already logged in, check for redirect
          let redirectUrl = '/admin';
          try {
            const storedRedirect = sessionStorage.getItem('redirect_after_login');
            if (storedRedirect) {
              console.log('Cross-origin login: Found redirect URL in sessionStorage:', storedRedirect);
              redirectUrl = storedRedirect;
              // Clear the redirect URL from sessionStorage
              sessionStorage.removeItem('redirect_after_login');
            }
          } catch (storageError) {
            console.warn('Error accessing sessionStorage:', storageError);
          }

          // Redirect to the appropriate page
          console.log('Cross-origin login: Already logged in, redirecting to:', redirectUrl);
          router.push(redirectUrl);
        }
      } catch (err) {
        console.error('Auth check error:', err);
        // Not logged in, stay on login page
      }
    };

    checkAuth();
  }, [router]);

  // Handle login form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {      console.log('Cross-origin login: Attempting login with email:', email);

      // Use Supabase to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (signInError) {
        throw new Error(signInError.message || 'Login failed');
      }

      console.log('Cross-origin login: Login successful');

      // Get the session token
      const { data: sessionData } = await supabase.auth.getSession();
      const token = sessionData?.session?.access_token;

      if (!token) {
        throw new Error('No authentication token available after login');
      }

      console.log('Cross-origin login: Token obtained');

      // Get the redirect URL from sessionStorage
      let redirectUrl = '/admin';
      try {
        const storedRedirect = sessionStorage.getItem('redirect_after_login');
        if (storedRedirect) {
          console.log('Cross-origin login: Found redirect URL in sessionStorage:', storedRedirect);
          redirectUrl = storedRedirect;
          // Clear the redirect URL from sessionStorage
          sessionStorage.removeItem('redirect_after_login');
        }
      } catch (storageError) {
        console.warn('Error accessing sessionStorage:', storageError);
      }

      // Store the token in sessionStorage for cross-origin access
      if (typeof window !== 'undefined') {
        try {
          // Store token in sessionStorage
          const tokenCache = JSON.stringify({
            token,
            expires: new Date().getTime() + 3600000, // 1 hour expiry
          });

          sessionStorage.setItem('oss_auth_token_cache', tokenCache);
          console.log('Cross-origin login: Token stored in sessionStorage');

          // Redirect to the appropriate page
          console.log('Cross-origin login: Redirecting to:', redirectUrl);
          router.push(redirectUrl);
        } catch (storageError) {
          console.error('Error storing token:', storageError);
          setError('Failed to store authentication token');
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message || 'Login failed. Please check your credentials and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Cross-Origin Admin Login | Ocean Soul Sparkles</title>
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.loginHeader}>
            <h1>Cross-Origin Admin Login</h1>
            <p>This special login page is for accessing the admin panel from a different origin.</p>
            {isCrossOrigin && devUrl && (
              <p className={styles.devInfo}>
                Development URL: <strong>{devUrl}</strong>
              </p>
            )}
          </div>

          {error && (
            <div className={styles.errorMessage}>
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className={styles.loginForm}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className={styles.input}
                placeholder="<EMAIL>"
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="password">Password</label>
              <div className={styles.passwordInput}>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className={styles.input}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  className={styles.passwordToggle}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? 'Hide' : 'Show'}
                </button>
              </div>
            </div>

            <button
              type="submit"
              className={styles.loginButton}
              disabled={loading}
            >
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>

          <div className={styles.loginFooter}>
            <p>
              <a href="/admin/login" className={styles.link}>
                Return to standard login
              </a>
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
