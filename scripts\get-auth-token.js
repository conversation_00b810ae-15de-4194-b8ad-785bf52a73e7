/**
 * Get Authentication Token Script
 *
 * This script authenticates with the Supabase API directly to get a valid token
 * for testing purposes.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or key not found in environment variables.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Admin credentials
const email = '<EMAIL>';
const password = 'WonderLand12345%$#@!'; // This is just for testing purposes

async function getAuthToken() {
  try {
    console.log(`Authenticating with Supabase as ${email}...`);

    // Sign in with email and password
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Authentication error:', error.message);
      process.exit(1);
    }

    if (!data || !data.session) {
      console.error('No session data returned from authentication');
      process.exit(1);
    }

    const token = data.session.access_token;
    console.log('Authentication successful!');
    console.log(`Token: ${token.substring(0, 10)}...${token.substring(token.length - 10)}`);

    // Save token to file for use in tests
    const tokenDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(tokenDir)) {
      fs.mkdirSync(tokenDir);
    }

    const tokenPath = path.join(tokenDir, 'auth-token.txt');
    fs.writeFileSync(tokenPath, token);
    console.log(`Token saved to ${tokenPath}`);

    return token;
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
getAuthToken().catch(error => {
  console.error('Error getting auth token:', error);
  process.exit(1);
});
