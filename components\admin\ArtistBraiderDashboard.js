import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css'

export default function ArtistBraiderDashboard() {
  const { user, role } = useAuth()
  const [dashboardData, setDashboardData] = useState({
    profile: null,
    upcomingBookings: [],
    recentPayments: [],
    stats: {
      totalBookings: 0,
      totalEarnings: 0,
      averageRating: 0,
      completedServices: 0
    }
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (user && (role === 'artist' || role === 'braider')) {
      fetchDashboardData()
    }
  }, [user, role])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch artist/braider profile and dashboard data
      const response = await fetch('/api/artist/dashboard', {
        headers: {
          'Authorization': `Bear<PERSON> ${user.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data')
      }

      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading your dashboard...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.error}>
        <h2>Unable to Load Dashboard</h2>
        <p>{error}</p>
        <button onClick={fetchDashboardData} className={styles.retryButton}>
          Try Again
        </button>
      </div>
    )
  }

  const { profile, upcomingBookings, recentPayments, stats } = dashboardData

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Welcome back, {profile?.artist_name || user?.email}!</h1>
        <p className={styles.subtitle}>
          {role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'} Dashboard
        </p>
      </div>

      {/* Stats Overview */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>📅</div>
          <div className={styles.statContent}>
            <h3>{stats.totalBookings}</h3>
            <p>Total Bookings</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>💰</div>
          <div className={styles.statContent}>
            <h3>${stats.totalEarnings?.toFixed(2) || '0.00'}</h3>
            <p>Total Earnings</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>⭐</div>
          <div className={styles.statContent}>
            <h3>{stats.averageRating?.toFixed(1) || 'N/A'}</h3>
            <p>Average Rating</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>✅</div>
          <div className={styles.statContent}>
            <h3>{stats.completedServices}</h3>
            <p>Completed Services</p>
          </div>
        </div>
      </div>

      <div className={styles.contentGrid}>
        {/* Upcoming Bookings */}
        <div className={styles.section}>
          <h2>Upcoming Bookings</h2>
          {upcomingBookings.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No upcoming bookings scheduled</p>
            </div>
          ) : (
            <div className={styles.bookingsList}>
              {upcomingBookings.slice(0, 5).map(booking => (
                <div key={booking.id} className={styles.bookingCard}>
                  <div className={styles.bookingDate}>
                    {new Date(booking.start_time).toLocaleDateString('en-AU', {
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                  <div className={styles.bookingDetails}>
                    <h4>{booking.service_name}</h4>
                    <p>{booking.customer_name}</p>
                    <span className={styles.bookingTime}>
                      {new Date(booking.start_time).toLocaleTimeString('en-AU', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  <div className={styles.bookingStatus}>
                    <span className={`${styles.statusBadge} ${styles[booking.status]}`}>
                      {booking.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Recent Payments */}
        <div className={styles.section}>
          <h2>Recent Payments</h2>
          {recentPayments.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No recent payments</p>
            </div>
          ) : (
            <div className={styles.paymentsList}>
              {recentPayments.slice(0, 5).map(payment => (
                <div key={payment.id} className={styles.paymentCard}>
                  <div className={styles.paymentDate}>
                    {new Date(payment.created_at).toLocaleDateString('en-AU')}
                  </div>
                  <div className={styles.paymentDetails}>
                    <h4>{payment.service_name}</h4>
                    <p>{payment.customer_name}</p>
                  </div>
                  <div className={styles.paymentAmount}>
                    <span className={styles.amount}>
                      ${payment.amount?.toFixed(2)}
                    </span>
                    <span className={styles.commission}>
                      (${payment.commission?.toFixed(2)} commission)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className={styles.quickActions}>
        <h2>Quick Actions</h2>
        <div className={styles.actionButtons}>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>👤</span>
            Update Profile
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>📅</span>
            View Schedule
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>💰</span>
            Payment History
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>⚙️</span>
            Availability Settings
          </button>
        </div>
      </div>
    </div>
  )
}
