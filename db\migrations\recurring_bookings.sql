-- Add recurring booking fields to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS recurring_pattern TEXT,
ADD COLUMN IF NOT EXISTS recurring_end_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS parent_booking_id UUID REFERENCES public.bookings(id),
ADD COLUMN IF NOT EXISTS booking_series_id UUID;

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS bookings_booking_series_id_idx ON public.bookings(booking_series_id);
CREATE INDEX IF NOT EXISTS bookings_parent_booking_id_idx ON public.bookings(parent_booking_id);
CREATE INDEX IF NOT EXISTS bookings_is_recurring_idx ON public.bookings(is_recurring);

-- Create booking_conflicts table
CREATE TABLE IF NOT EXISTS public.booking_conflicts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  conflicting_booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  conflict_type TEXT NOT NULL,
  resolved BOOLEAN DEFAULT FALSE,
  resolution_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS booking_conflicts_booking_id_idx ON public.booking_conflicts(booking_id);
CREATE INDEX IF NOT EXISTS booking_conflicts_conflicting_booking_id_idx ON public.booking_conflicts(conflicting_booking_id);
CREATE INDEX IF NOT EXISTS booking_conflicts_resolved_idx ON public.booking_conflicts(resolved);

-- Create locations table
CREATE TABLE IF NOT EXISTS public.locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Australia',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS locations_name_idx ON public.locations(name);
CREATE INDEX IF NOT EXISTS locations_is_active_idx ON public.locations(is_active);

-- Set up RLS policies
ALTER TABLE public.booking_conflicts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- Create policies for booking_conflicts
CREATE POLICY "Staff can view all booking conflicts" ON public.booking_conflicts
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can insert booking conflicts" ON public.booking_conflicts
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can update booking conflicts" ON public.booking_conflicts
  FOR UPDATE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Create policies for locations
CREATE POLICY "Anyone can view active locations" ON public.locations
  FOR SELECT USING (is_active = true);

CREATE POLICY "Staff can view all locations" ON public.locations
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can insert locations" ON public.locations
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can update locations" ON public.locations
  FOR UPDATE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Insert default locations
INSERT INTO public.locations (name, is_active)
VALUES 
  ('Studio', true),
  ('Client Location', true),
  ('Online', true),
  ('Outdoor', true)
ON CONFLICT (id) DO NOTHING;
