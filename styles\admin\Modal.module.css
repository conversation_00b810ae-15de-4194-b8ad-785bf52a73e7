.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.2s ease-out;
}

.modalContainer {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

.modalSmall {
  width: 90%;
  max-width: 500px;
}

.modalMedium {
  width: 90%;
  max-width: 800px;
}

.modalLarge {
  width: 90%;
  max-width: 1200px;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modalTitle {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.modalContent {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.closeButton,
.closeButtonCorner {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.closeButton:hover,
.closeButtonCorner:hover {
  color: #333;
}

.closeButtonCorner {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .modalContainer {
    width: 95%;
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 12px 15px;
  }
  
  .modalTitle {
    font-size: 1.2rem;
  }
  
  .modalContent {
    padding: 15px;
  }
}
