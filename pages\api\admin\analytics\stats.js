import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for admin analytics statistics
 * This endpoint uses service_role key to bypass RLS policies
 * Uses the simplified authentication approach
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request using our simplified auth module
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Only allow GET requests for analytics
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    const { timeframe = 'last30days' } = req.query;
    console.log(`Fetching analytics stats for timeframe: ${timeframe}`);

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;

    switch (timeframe) {
      case 'last7days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'last90days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 90);
        break;
      case 'last30days':
      default:
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 30);
        break;
    }

    const startDateIso = startDate.toISOString();
    console.log(`Date range: ${startDateIso} to ${now.toISOString()}`);

    // Get total bookings for the period
    const { count: totalBookings, error: bookingsError } = await adminClient
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDateIso);

    if (bookingsError) {
      console.error('Error fetching bookings:', bookingsError);
      throw bookingsError;
    }

    // Get total orders and revenue for the period (using correct column names)
    const { data: orders, error: ordersError } = await adminClient
      .from('orders')
      .select('id, total')
      .gte('order_date', startDateIso);

    if (ordersError) {
      console.error('Error fetching orders:', ordersError);
      throw ordersError;
    }

    const totalOrders = orders?.length || 0;
    const totalRevenue = orders?.reduce((sum, order) => sum + (parseFloat(order.total) || 0), 0) || 0;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Get pageviews and sessions for conversion rate calculation
    const { data: analytics, error: analyticsError } = await adminClient
      .from('analytics')
      .select('sessions, page_views')
      .gte('date', startDateIso)
      .single();

    if (analyticsError && analyticsError.code !== 'PGRST116') { // Ignore not found error
      console.error('Error fetching analytics:', analyticsError);
      throw analyticsError;
    }

    const sessions = analytics?.sessions || 100; // Default value if no analytics
    const conversionRate = totalOrders / sessions;

    // Get top products - we need to filter by order date, so we'll get order_items and filter manually
    const { data: topProducts, error: productsError } = await adminClient
      .from('order_items')
      .select(`
        product_id,
        products:product_id (name),
        quantity,
        price,
        orders:order_id (order_date)
      `);

    if (productsError) {
      console.error('Error fetching top products:', productsError);
      throw productsError;
    }

    // Process and group products data, filtering by date
    const productMap = {};
    topProducts?.forEach(item => {
      // Filter by order date
      const orderDate = new Date(item.orders?.order_date);
      if (orderDate >= startDate) {
        const productId = item.product_id;
        const productName = item.products?.name || 'Unknown Product';
        const quantity = item.quantity || 0;
        const revenue = (item.price || 0) * quantity;

        if (!productMap[productId]) {
          productMap[productId] = {
            id: productId,
            name: productName,
            unitsSold: 0,
            revenue: 0
          };
        }

        productMap[productId].unitsSold += quantity;
        productMap[productId].revenue += revenue;
      }
    });

    const processedTopProducts = Object.values(productMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Get top services (get price from services table via foreign key)
    const { data: topServices, error: servicesError } = await adminClient
      .from('bookings')
      .select(`
        service_id,
        services:service_id (name, price)
      `)
      .gte('created_at', startDateIso);

    if (servicesError) {
      console.error('Error fetching top services:', servicesError);
      throw servicesError;
    }

    // Process and group services data, filtering by date
    const serviceMap = {};
    topServices?.forEach(booking => {
      // Filter by booking date
      const bookingDate = new Date(booking.created_at);
      if (bookingDate >= startDate) {
        const serviceId = booking.service_id;
        const serviceName = booking.services?.name || 'Unknown Service';
        const revenue = parseFloat(booking.services?.price || 0);

        if (!serviceMap[serviceId]) {
          serviceMap[serviceId] = {
            id: serviceId,
            name: serviceName,
            bookings: 0,
            revenue: 0
          };
        }

        serviceMap[serviceId].bookings += 1;
        serviceMap[serviceId].revenue += revenue;
      }
    });

    const processedTopServices = Object.values(serviceMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    console.log('Analytics stats fetched successfully:', {
      totalBookings: totalBookings || 0,
      totalOrders,
      totalRevenue,
      averageOrderValue,
      conversionRate,
      topProductsCount: processedTopProducts.length,
      topServicesCount: processedTopServices.length
    });

    // Return all analytics data
    return res.status(200).json({
      totalBookings: totalBookings || 0,
      totalOrders,
      totalRevenue,
      averageOrderValue,
      conversionRate,
      topProducts: processedTopProducts,
      topServices: processedTopServices
    });
  } catch (error) {
    console.error('Error fetching analytics stats:', error);
    return res.status(500).json({
      error: 'Server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
