import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import styles from '@/styles/admin/Login.module.css'

export default function ActivateAccount() {
  const router = useRouter()
  const { token } = router.query
  
  const [step, setStep] = useState('validating') // validating, form, success, error
  const [tokenValidation, setTokenValidation] = useState({ valid: false, error: null })
  const [userInfo, setUserInfo] = useState(null)
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  })
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')

  // Validate token on component mount
  useEffect(() => {
    if (token) {
      validateActivationToken(token)
    }
  }, [token])

  const validateActivationToken = async (activationToken) => {
    try {
      console.log('Validating activation token:', activationToken.substring(0, 8) + '...')
      
      const response = await fetch('/api/auth/validate-activation-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: activationToken }),
      })

      const result = await response.json()

      if (response.ok && result.valid) {
        setTokenValidation({ valid: true, error: null })
        setUserInfo(result.user)
        setStep('form')
        console.log('✅ Activation token validated successfully')
      } else {
        setTokenValidation({ valid: false, error: result.error || 'Invalid activation token' })
        setStep('error')
        console.error('❌ Token validation failed:', result.error)
      }
    } catch (error) {
      console.error('❌ Error validating token:', error)
      setTokenValidation({ valid: false, error: 'Failed to validate activation token' })
      setStep('error')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validate passwords
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return
    }

    setSubmitting(true)

    try {
      const response = await fetch('/api/auth/activate-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: token,
          password: formData.password
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('✅ Account activated successfully')
        setStep('success')
      } else {
        setError(result.error || 'Failed to activate account')
        console.error('❌ Account activation failed:', result.error)
      }
    } catch (error) {
      console.error('❌ Error activating account:', error)
      setError('An error occurred while activating your account')
    } finally {
      setSubmitting(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Show loading state while validating token
  if (step === 'validating') {
    return (
      <>
        <Head>
          <title>Activating Account - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loginContainer}>
          <div className={styles.loginCard}>
            <div className={styles.logoContainer}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles Logo"
                className={styles.logo}
              />
            </div>
            <h1 className={styles.title}>Activating Your Account</h1>
            <div className={styles.loading}>
              <p>Validating your activation link...</p>
              <div className={styles.spinner}></div>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Show error state for invalid tokens
  if (step === 'error') {
    return (
      <>
        <Head>
          <title>Activation Error - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loginContainer}>
          <div className={styles.loginCard}>
            <div className={styles.logoContainer}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles Logo"
                className={styles.logo}
              />
            </div>
            <h1 className={styles.title}>Activation Error</h1>
            <div className={styles.error}>
              <p>{tokenValidation.error || 'Invalid or expired activation link'}</p>
              <p>Please contact your administrator for a new activation link.</p>
            </div>
            <button
              onClick={() => window.location.href = 'https://www.oceansoulsparkles.com.au'}
              className={styles.submitButton}
            >
              Return to Home
            </button>
          </div>
        </div>
      </>
    )
  }

  // Show success state after activation
  if (step === 'success') {
    return (
      <>
        <Head>
          <title>Account Activated - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loginContainer}>
          <div className={styles.loginCard}>
            <div className={styles.logoContainer}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles Logo"
                className={styles.logo}
              />
            </div>
            <h1 className={styles.title}>🎉 Account Activated!</h1>
            <div className={styles.success}>
              <p>Congratulations! Your Ocean Soul Sparkles account has been successfully activated.</p>
              <p>You can now log in to your staff portal with your email and the password you just created.</p>
            </div>
            <div className={styles.buttonGroup}>
              <button
                onClick={() => router.push('/staff-login')}
                className={styles.submitButton}
              >
                Go to Staff Login
              </button>
              <button
                onClick={() => window.location.href = 'https://www.oceansoulsparkles.com.au'}
                className={`${styles.submitButton} ${styles.secondaryButton}`}
              >
                Return to Home
              </button>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Show password creation form
  return (
    <>
      <Head>
        <title>Create Your Password - Ocean Soul Sparkles</title>
      </Head>
      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="/images/bannerlogo.PNG"
              alt="Ocean Soul Sparkles Logo"
              className={styles.logo}
            />
          </div>
          
          <h1 className={styles.title}>Welcome to Ocean Soul Sparkles!</h1>
          
          {userInfo && (
            <div className={styles.welcomeMessage}>
              <p>Hello <strong>{userInfo.name}</strong>!</p>
              <p>Your {userInfo.role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'} application has been approved.</p>
              <p>Please create a secure password to activate your account:</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.inputGroup}>
              <label htmlFor="password">Create Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                required
                minLength={8}
                placeholder="Enter a secure password (min 8 characters)"
                className={styles.input}
              />
            </div>

            <div className={styles.inputGroup}>
              <label htmlFor="confirmPassword">Confirm Password</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                placeholder="Confirm your password"
                className={styles.input}
              />
            </div>

            {error && (
              <div className={styles.error}>{error}</div>
            )}

            <button
              type="submit"
              disabled={submitting}
              className={styles.submitButton}
            >
              {submitting ? 'Activating Account...' : 'Activate My Account'}
            </button>
          </form>

          <div className={styles.helpText}>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </div>
    </>
  )
}
