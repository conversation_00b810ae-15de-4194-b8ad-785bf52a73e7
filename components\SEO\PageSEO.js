import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import CanonicalUrl from './CanonicalUrl';
import BreadcrumbSchema from './BreadcrumbSchema';

const PageSEO = ({ 
  title, 
  description, 
  ogImage = 'https://www.oceansoulsparkles.com.au/images/og-image.jpg',
  ogType = 'website'
}) => {
  const router = useRouter();
  const url = `https://www.oceansoulsparkles.com.au${router.asPath}`;
  
  return (
    <>
      <Head>
        {/* Basic Meta Tags */}
        <title>{title}</title>
        <meta name="description" content={description} />
        
        {/* Open Graph Tags */}
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:type" content={ogType} />
        <meta property="og:url" content={url} />
        <meta property="og:image" content={ogImage} />
        
        {/* Twitter Card Tags */}
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={ogImage} />
      </Head>
      
      <CanonicalUrl />
      <BreadcrumbSchema />
    </>
  );
};

export default PageSEO;
