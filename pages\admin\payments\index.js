import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PaymentList from '@/components/admin/PaymentList';
import styles from '@/styles/admin/PaymentsPage.module.css';

export default function PaymentsPage() {
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    pending: 0,
    failed: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch payment statistics
  useEffect(() => {
    const fetchPaymentStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/payments/stats');
        
        if (!response.ok) {
          throw new Error('Failed to fetch payment statistics');
        }
        
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error('Error fetching payment stats:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPaymentStats();
  }, [refreshKey]);

  // Function to refresh data
  const refreshData = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Payments">
        <div className={styles.paymentsPage}>
          <div className={styles.header}>
            <h1>Payment Management</h1>
            <button 
              className={styles.refreshButton}
              onClick={refreshData}
              disabled={loading}
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>

          {error && (
            <div className={styles.errorBox}>
              <p>Error: {error}</p>
              <button onClick={refreshData}>Try Again</button>
            </div>
          )}

          <div className={styles.statsContainer}>
            <div className={styles.statsCard}>
              <h3>Total Payments</h3>
              <p className={styles.statValue}>{loading ? '...' : stats.total}</p>
            </div>
            <div className={styles.statsCard}>
              <h3>Completed</h3>
              <p className={`${styles.statValue} ${styles.completed}`}>
                {loading ? '...' : stats.completed}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Pending</h3>
              <p className={`${styles.statValue} ${styles.pending}`}>
                {loading ? '...' : stats.pending}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Failed</h3>
              <p className={`${styles.statValue} ${styles.failed}`}>
                {loading ? '...' : stats.failed}
              </p>
            </div>
          </div>

          <PaymentList refreshData={refreshData} />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
