.inventoryAnalytics {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analyticsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.analyticsHeader h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.timeRangeSelector {
  display: flex;
  align-items: center;
}

.timeSelect {
  padding: 8px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.timeSelect:focus {
  outline: none;
  border-color: #0066cc;
}

.metricsNavigation {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  overflow-x: auto;
}

.metricTab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.metricTab:hover {
  background: #e9ecef;
  color: #333;
}

.metricTab.active {
  background: white;
  color: #0066cc;
  border-bottom-color: #0066cc;
}

.metricIcon {
  font-size: 16px;
}

.analyticsContent {
  padding: 20px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e1e5e9;
  border-top: 4px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #dc3545;
  text-align: center;
}

.retryButton {
  margin-top: 15px;
  padding: 10px 20px;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s;
}

.retryButton:hover {
  background: #0052a3;
}

/* Overview Analytics Styles */
.overviewAnalytics {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.kpiGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.kpiCard {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #0066cc;
  transition: transform 0.2s;
}

.kpiCard:hover {
  transform: translateY(-2px);
}

.kpiValue {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.kpiLabel {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 5px;
}

.kpiSubtext {
  font-size: 12px;
  color: #999;
}

.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.chartCard {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.chartCard h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* Alerts Analytics Styles */
.alertsAnalytics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.alertsHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.noAlerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #28a745;
}

.noAlertsIcon {
  font-size: 48px;
  margin-bottom: 15px;
}

.alertsList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.alertItem {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #dc3545;
  transition: transform 0.2s;
}

.alertItem:hover {
  transform: translateX(5px);
}

.alertIcon {
  font-size: 24px;
  flex-shrink: 0;
}

.alertContent {
  flex: 1;
}

.alertTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.alertMessage {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.alertMeta {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.alertSeverity {
  font-weight: 600;
}

.alertTime {
  color: #999;
}

.alertActions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.alertAction {
  padding: 6px 12px;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background 0.2s;
}

.alertAction:hover {
  background: #0052a3;
}

/* ABC Analysis Styles */
.abcAnalysis {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.abcHeader h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.abcHeader p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.abcGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.abcCard {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  transition: transform 0.2s;
}

.abcCard:hover {
  transform: translateY(-2px);
}

.abcIndicator {
  width: 12px;
  height: 60px;
  border-radius: 6px;
  flex-shrink: 0;
}

.abcContent {
  flex: 1;
}

.abcClass {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.abcCount {
  font-size: 20px;
  font-weight: 700;
  color: #0066cc;
  margin-bottom: 2px;
}

.abcPercentage {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.abcDescription {
  font-size: 12px;
  color: #999;
  line-height: 1.3;
}

/* Top Performers Styles */
.topPerformers {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.performersHeader h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.performersHeader p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.noData {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
  text-align: center;
}

.performersList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.performerItem {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  transition: all 0.2s;
}

.performerItem:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.performerRank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #0066cc;
  color: white;
  border-radius: 50%;
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
}

.performerInfo {
  flex: 1;
}

.performerName {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.performerSku {
  font-size: 12px;
  color: #666;
}

.performerMetrics {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 3px;
}

.performerValue {
  font-size: 16px;
  font-weight: 700;
  color: #28a745;
}

.performerQuantity {
  font-size: 12px;
  color: #666;
}

.performerStatus {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.performerStatus.in_stock {
  background: #d4edda;
  color: #155724;
}

.performerStatus.low_stock {
  background: #fff3cd;
  color: #856404;
}

.performerStatus.out_of_stock {
  background: #f8d7da;
  color: #721c24;
}

/* Needs Attention Styles */
.needsAttention {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.attentionHeader h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.attentionHeader p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.noIssues {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #28a745;
}

.noIssuesIcon {
  font-size: 48px;
  margin-bottom: 15px;
}

.attentionList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.attentionItem {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #fff3cd;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
  transition: transform 0.2s;
}

.attentionItem:hover {
  transform: translateX(5px);
}

.attentionInfo {
  flex: 1;
}

.attentionName {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.attentionSku {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
}

.attentionValue {
  font-size: 14px;
  color: #28a745;
  font-weight: 600;
}

.attentionMetrics {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  margin: 0 15px;
}

.attentionStock,
.attentionThreshold {
  font-size: 12px;
  color: #666;
}

.attentionStatus {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.attentionStatus.low_stock {
  background: #fff3cd;
  color: #856404;
}

.attentionStatus.out_of_stock {
  background: #f8d7da;
  color: #721c24;
}

.attentionActions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.attentionAction {
  padding: 6px 12px;
  background: #ffc107;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background 0.2s;
  white-space: nowrap;
}

.attentionAction:hover {
  background: #e0a800;
}

/* Stock Chart Styles */
.stockChart {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stockItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.stockIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.stockLabel {
  flex: 1;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.stockCount {
  font-size: 14px;
  font-weight: 700;
  color: #0066cc;
  min-width: 30px;
  text-align: right;
}

.stockPercentage {
  font-size: 12px;
  color: #666;
  min-width: 40px;
  text-align: right;
}

/* Insights List Styles */
.insightsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.noInsights {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  text-align: center;
}

.insightItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #0066cc;
}

.insightIcon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.insightContent {
  flex: 1;
}

.insightTitle {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.insightMessage {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
}

.insightAction {
  font-size: 11px;
  color: #0066cc;
  font-style: italic;
}

.insightPriority {
  font-size: 10px;
  font-weight: 700;
  flex-shrink: 0;
  margin-top: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analyticsHeader {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .metricsNavigation {
    flex-wrap: wrap;
  }

  .metricTab {
    padding: 12px 15px;
  }

  .kpiGrid {
    grid-template-columns: 1fr;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
  }

  .abcGrid {
    grid-template-columns: 1fr;
  }

  .performerItem,
  .attentionItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .performerMetrics,
  .attentionMetrics {
    align-items: flex-start;
  }

  .attentionActions {
    flex-direction: row;
    width: 100%;
  }
}
