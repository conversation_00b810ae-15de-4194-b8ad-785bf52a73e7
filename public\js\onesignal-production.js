/**
 * Production-specific OneSignal initialization
 *
 * This script initializes OneSignal in a way that's reliable in production environments,
 * with proper error handling, initialization checks, and event dispatching.
 */

(function() {
  // Wrap everything in try/catch to prevent breaking the page
  try {
    // Only run in production environment
    if (window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.includes('.local')) {
      console.log('[OneSignal] Skipping initialization in development environment');
      return;
    }

    // Set initialization flag
    window.__ONESIGNAL_INITIALIZING__ = true;

  // Function to safely initialize OneSignal
  function initializeOneSignal() {
    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeOneSignal);
        return;
      }

      // Check if OneSignal is available
      if (!window.OneSignal) {
        console.warn('[OneSignal] SDK not loaded yet, will retry');
        setTimeout(initializeOneSignal, 500);
        return;
      }

      // Initialize OneSignal
      window.OneSignal.init({
        appId: window.__ONESIGNAL_APP_ID__ || "************************************",
        safari_web_id: window.__ONESIGNAL_SAFARI_WEB_ID__ || "web.onesignal.auto.************************************",
        notifyButton: {
          enable: true,
          size: 'medium',
          theme: 'default',
          position: 'bottom-right',
          offset: {
            bottom: '20px',
            right: '20px',
          },
          showCredit: false,
          text: {
            'tip.state.unsubscribed': 'Subscribe to notifications',
            'tip.state.subscribed': 'You are subscribed to notifications',
            'tip.state.blocked': 'You have blocked notifications',
            'message.prenotify': 'Click to subscribe to notifications',
            'message.action.subscribed': 'Thanks for subscribing!',
            'message.action.resubscribed': 'You are subscribed to notifications',
            'message.action.unsubscribed': 'You will no longer receive notifications',
            'dialog.main.title': 'Manage Notifications',
            'dialog.main.button.subscribe': 'SUBSCRIBE',
            'dialog.main.button.unsubscribe': 'UNSUBSCRIBE',
            'dialog.blocked.title': 'Unblock Notifications',
            'dialog.blocked.message': 'Follow these instructions to allow notifications:',
          },
          colors: {
            'circle.background': '#6a0dad',
            'circle.foreground': 'white',
            'badge.background': '#6a0dad',
            'badge.foreground': 'white',
            'badge.bordercolor': 'white',
            'pulse.color': 'white',
            'dialog.button.background.hovering': '#6a0dad',
            'dialog.button.background.active': '#6a0dad',
            'dialog.button.background': '#6a0dad',
            'dialog.button.foreground': 'white',
          },
        },
        welcomeNotification: {
          title: 'OceanSoulSparkles',
          message: 'Thanks for subscribing to notifications!',
        },
        promptOptions: {
          slidedown: {
            prompts: [
              {
                type: "push",
                autoPrompt: false,
              }
            ]
          }
        }
      }).then(function() {
        // Mark as initialized
        window.__ONESIGNAL_INITIALIZED__ = true;
        window.__ONESIGNAL_INITIALIZING__ = false;

        // Dispatch initialization event
        const event = new CustomEvent('onesignal:initialized');
        document.dispatchEvent(event);

        console.log('[OneSignal] Successfully initialized');
      }).catch(function(error) {
        console.error('[OneSignal] Initialization error:', error);
        window.__ONESIGNAL_INITIALIZING__ = false;

        // Dispatch error event
        const event = new CustomEvent('onesignal:error', { detail: error });
        document.dispatchEvent(event);
      });
    } catch (error) {
      console.error('[OneSignal] Critical initialization error:', error);
      window.__ONESIGNAL_INITIALIZING__ = false;
    }
  }

  // Start initialization
  initializeOneSignal();

  } catch (error) {
    // Catch any errors to prevent breaking the page
    console.error('[OneSignal] Critical error in initialization script:', error);
  }
})();
