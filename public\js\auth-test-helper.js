/**
 * Authentication Test Helper
 * 
 * This script provides browser-based utilities for testing authentication flows
 * in the Ocean Soul Sparkles admin panel. It can be loaded in the browser console
 * to assist with manual testing.
 */

// Create namespace for auth test utilities
window.authTest = {};

/**
 * Extract the current authentication token from browser storage
 * @returns {string|null} - The authentication token or null if not found
 */
authTest.extractToken = function() {
  try {
    // Check for token in localStorage
    const token = localStorage.getItem('oss_auth_token');
    if (token) {
      try {
        const parsedToken = JSON.parse(token);
        return parsedToken.access_token || null;
      } catch (e) {
        return token;
      }
    }
    
    // Check for token in Supabase storage
    const supabaseToken = localStorage.getItem('sb-ndlgbcsbidyhxbpqzgqp-auth-token');
    if (supabaseToken) {
      try {
        const parsedToken = JSON.parse(supabaseToken);
        return parsedToken.access_token || null;
      } catch (e) {
        return supabaseToken;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting token:', error);
    return null;
  }
};

/**
 * Make an authenticated API request
 * @param {string} url - The API endpoint URL
 * @param {string} method - The HTTP method (GET, POST, etc.)
 * @param {Object} body - The request body (for POST, PUT, etc.)
 * @returns {Promise<Object>} - The API response
 */
authTest.makeRequest = async function(url, method = 'GET', body = null) {
  try {
    const token = authTest.extractToken();
    if (!token) {
      console.error('No authentication token found');
      return { error: 'No authentication token found' };
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    const options = {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined
    };

    const response = await fetch(url, options);
    const data = await response.json();

    return {
      status: response.status,
      data,
      headers: response.headers
    };
  } catch (error) {
    console.error('Error making request:', error);
    return { error: error.message };
  }
};

/**
 * Test the current authentication state
 * @returns {Promise<Object>} - Authentication test results
 */
authTest.checkAuth = async function() {
  try {
    const result = await authTest.makeRequest('/api/admin/diagnostics/auth-check');
    
    console.log('Authentication Check Result:');
    console.log(`Status: ${result.status}`);
    console.log('Data:', result.data);
    
    return result;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return { error: error.message };
  }
};

/**
 * Monitor token refresh events
 * @param {number} duration - Duration to monitor in milliseconds
 * @returns {Promise<void>}
 */
authTest.monitorTokenRefresh = async function(duration = 300000) {
  console.log(`Monitoring token refresh for ${duration / 1000} seconds...`);
  
  const originalFetch = window.fetch;
  let refreshCount = 0;
  
  // Override fetch to monitor token refresh requests
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('/auth/v1/token')) {
      refreshCount++;
      console.log(`Token refresh detected (${refreshCount}):`, new Date().toISOString());
    }
    return originalFetch.apply(this, args);
  };
  
  // Start monitoring
  const startTime = Date.now();
  const initialToken = authTest.extractToken();
  console.log('Initial token:', initialToken ? initialToken.substring(0, 10) + '...' : 'None');
  
  // Check token periodically
  const interval = setInterval(() => {
    const currentToken = authTest.extractToken();
    const elapsed = Math.floor((Date.now() - startTime) / 1000);
    
    console.log(`[${elapsed}s] Current token: ${currentToken ? currentToken.substring(0, 10) + '...' : 'None'}`);
    console.log(`[${elapsed}s] Token refreshes: ${refreshCount}`);
    
    if (currentToken !== initialToken) {
      console.log(`[${elapsed}s] Token has changed!`);
    }
    
    if (Date.now() - startTime >= duration) {
      clearInterval(interval);
      window.fetch = originalFetch;
      console.log('Token refresh monitoring completed');
    }
  }, 30000);
  
  // Return promise that resolves when monitoring is complete
  return new Promise(resolve => {
    setTimeout(() => {
      clearInterval(interval);
      window.fetch = originalFetch;
      console.log('Token refresh monitoring completed');
      resolve({
        refreshCount,
        duration: (Date.now() - startTime) / 1000,
        tokenChanged: authTest.extractToken() !== initialToken
      });
    }, duration);
  });
};

/**
 * Test session persistence by refreshing the page
 * @returns {Promise<void>}
 */
authTest.testSessionPersistence = async function() {
  try {
    // Check current authentication
    const beforeRefresh = await authTest.checkAuth();
    console.log('Authentication before refresh:', beforeRefresh);
    
    // Store test data in sessionStorage
    sessionStorage.setItem('authTestData', JSON.stringify({
      timestamp: Date.now(),
      authStatus: beforeRefresh.status === 200 ? 'authenticated' : 'unauthenticated',
      path: window.location.pathname
    }));
    
    // Refresh the page
    console.log('Refreshing page in 3 seconds...');
    setTimeout(() => {
      window.location.reload();
    }, 3000);
  } catch (error) {
    console.error('Error testing session persistence:', error);
  }
};

/**
 * Check session persistence after page refresh
 * @returns {Object|null} - Test results or null if no test was in progress
 */
authTest.checkSessionPersistence = function() {
  try {
    // Check if test data exists
    const testDataStr = sessionStorage.getItem('authTestData');
    if (!testDataStr) {
      return null;
    }
    
    // Parse test data
    const testData = JSON.parse(testDataStr);
    const elapsed = (Date.now() - testData.timestamp) / 1000;
    
    console.log('Session persistence test results:');
    console.log(`Time elapsed: ${elapsed.toFixed(2)} seconds`);
    console.log(`Previous auth status: ${testData.authStatus}`);
    console.log(`Previous path: ${testData.path}`);
    console.log(`Current path: ${window.location.pathname}`);
    
    // Clean up test data
    sessionStorage.removeItem('authTestData');
    
    // Check current authentication
    authTest.checkAuth().then(afterRefresh => {
      console.log('Authentication after refresh:', afterRefresh);
      
      const success = 
        (testData.authStatus === 'authenticated' && afterRefresh.status === 200) ||
        (testData.authStatus === 'unauthenticated' && afterRefresh.status !== 200);
      
      console.log(`Test result: ${success ? 'PASS' : 'FAIL'}`);
      
      return {
        success,
        beforeRefresh: testData.authStatus,
        afterRefresh: afterRefresh.status === 200 ? 'authenticated' : 'unauthenticated',
        elapsed
      };
    });
  } catch (error) {
    console.error('Error checking session persistence:', error);
    return { error: error.message };
  }
};

/**
 * Run all browser-based authentication tests
 * @returns {Promise<Object>} - Test results
 */
authTest.runAllTests = async function() {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };
  
  // Test 1: Authentication Check
  try {
    console.log('\nRunning Authentication Check...');
    const authCheck = await authTest.checkAuth();
    
    results.tests.authCheck = {
      name: 'Authentication Check',
      status: authCheck.status === 200 ? 'Pass' : 'Fail',
      details: authCheck
    };
  } catch (error) {
    results.tests.authCheck = {
      name: 'Authentication Check',
      status: 'Error',
      details: { error: error.message }
    };
  }
  
  // Test 2: Token Extraction
  try {
    console.log('\nRunning Token Extraction Test...');
    const token = authTest.extractToken();
    
    results.tests.tokenExtraction = {
      name: 'Token Extraction',
      status: token ? 'Pass' : 'Fail',
      details: { tokenFound: !!token }
    };
    
    console.log(`Token found: ${!!token}`);
  } catch (error) {
    results.tests.tokenExtraction = {
      name: 'Token Extraction',
      status: 'Error',
      details: { error: error.message }
    };
  }
  
  // Generate summary
  let summary = '\nTest Summary:\n';
  let passCount = 0;
  let failCount = 0;
  let errorCount = 0;
  
  for (const [testId, test] of Object.entries(results.tests)) {
    summary += `${testId}: ${test.status}\n`;
    if (test.status === 'Pass') passCount++;
    else if (test.status === 'Fail') failCount++;
    else errorCount++;
  }
  
  summary += `\nTotal: ${Object.keys(results.tests).length} tests\n`;
  summary += `Passed: ${passCount} tests\n`;
  summary += `Failed: ${failCount} tests\n`;
  summary += `Errors: ${errorCount} tests\n`;
  
  console.log(summary);
  
  return results;
};

// Check for session persistence test on page load
document.addEventListener('DOMContentLoaded', function() {
  const testResults = authTest.checkSessionPersistence();
  if (testResults) {
    console.log('Session persistence test detected and completed');
  }
});

// Log initialization message
console.log('Auth Test Helper loaded. Use authTest.runAllTests() to run all tests.');
