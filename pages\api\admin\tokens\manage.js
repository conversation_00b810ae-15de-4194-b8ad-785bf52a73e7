import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for managing application tokens
 * GET /api/admin/tokens/manage?userId=xxx - Get tokens for a user
 * POST /api/admin/tokens/manage - Create new token
 * PATCH /api/admin/tokens/manage - Update token status
 * DELETE /api/admin/tokens/manage - Invalidate token
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Token management API called: ${req.method}`)

  try {
    // Verify admin authentication
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' })
    }

    const { user: adminUser, role: adminRole } = authResult

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ error: 'Admin privileges required' })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    switch (req.method) {
      case 'GET':
        return await handleGetTokens(req, res, adminClient, requestId)
      case 'POST':
        return await handleCreateToken(req, res, adminClient, adminUser, requestId)
      case 'PATCH':
        return await handleUpdateToken(req, res, adminClient, adminUser, requestId)
      case 'DELETE':
        return await handleInvalidateToken(req, res, adminClient, adminUser, requestId)
      default:
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in token management:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

// Get tokens for a user
async function handleGetTokens(req, res, adminClient, requestId) {
  const { userId } = req.query

  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' })
  }

  console.log(`[${requestId}] Fetching tokens for user: ${userId}`)

  // Get user tokens with application details
  const { data: tokens, error: tokensError } = await adminClient
    .from('application_tokens')
    .select(`
      *,
      artist_braider_applications (
        application_type,
        status
      ),
      created_by_user:user_profiles!created_by (
        name
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })

  if (tokensError) {
    console.error(`[${requestId}] Error fetching tokens:`, tokensError)
    return res.status(500).json({ error: 'Failed to fetch tokens' })
  }

  // Format tokens with status information
  const formattedTokens = tokens.map(token => ({
    id: token.id,
    token: token.token.substring(0, 8) + '...', // Only show first 8 characters for security
    tokenType: token.token_type,
    status: getTokenStatus(token),
    createdAt: token.created_at,
    expiresAt: token.expires_at,
    usedAt: token.used_at,
    isUsed: token.is_used,
    applicationType: token.artist_braider_applications?.application_type,
    applicationStatus: token.artist_braider_applications?.status,
    createdBy: token.created_by_user?.name || 'System',
    ipAddress: token.ip_address,
    userAgent: token.user_agent
  }))

  return res.status(200).json({
    success: true,
    tokens: formattedTokens
  })
}

// Create new token
async function handleCreateToken(req, res, adminClient, adminUser, requestId) {
  const { userId, applicationType = 'application_access' } = req.body

  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' })
  }

  console.log(`[${requestId}] Creating new token for user: ${userId}`)

  // Get application for this user
  const { data: application, error: appError } = await adminClient
    .from('artist_braider_applications')
    .select('*')
    .eq('user_id', userId)
    .single()

  if (appError) {
    console.error(`[${requestId}] Error fetching application:`, appError)
    return res.status(404).json({ error: 'Application not found' })
  }

  // Generate new token
  const { data: tokenData, error: tokenError } = await adminClient
    .rpc('generate_application_token')

  if (tokenError || !tokenData) {
    console.error(`[${requestId}] Error generating token:`, tokenError)
    return res.status(500).json({ error: 'Failed to generate token' })
  }

  // Set expiration (7 days from now)
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + 7)

  // Prepare token data
  const tokenInsertData = {
    user_id: userId,
    application_id: application.id,
    token: tokenData,
    token_type: applicationType,
    expires_at: expiresAt.toISOString()
  }

  // Only set created_by if the admin user exists in auth.users table
  // Skip for development admin to avoid foreign key constraint violation
  if (adminUser.id !== '00000000-0000-4000-8000-000000000001') {
    tokenInsertData.created_by = adminUser.id
  } else {
    console.log(`[${requestId}] Skipping created_by field for development admin to avoid foreign key constraint`)
  }

  // Store token
  const { data: newToken, error: insertError } = await adminClient
    .from('application_tokens')
    .insert([tokenInsertData])
    .select()
    .single()

  if (insertError) {
    console.error(`[${requestId}] Error storing token:`, insertError)
    return res.status(500).json({ error: 'Failed to store token' })
  }

  return res.status(201).json({
    success: true,
    message: 'Token created successfully',
    token: {
      id: newToken.id,
      token: newToken.token.substring(0, 8) + '...',
      fullToken: newToken.token, // Include full token in response for immediate use
      expiresAt: newToken.expires_at,
      status: 'active'
    }
  })
}

// Update token status
async function handleUpdateToken(req, res, adminClient, adminUser, requestId) {
  const { tokenId, action } = req.body

  if (!tokenId || !action) {
    return res.status(400).json({ error: 'Token ID and action are required' })
  }

  console.log(`[${requestId}] Updating token ${tokenId} with action: ${action}`)

  let updateData = { updated_at: new Date().toISOString() }

  switch (action) {
    case 'invalidate':
      updateData.is_used = true
      updateData.used_at = new Date().toISOString()
      break
    case 'extend':
      // Extend expiration by 7 days
      const newExpiration = new Date()
      newExpiration.setDate(newExpiration.getDate() + 7)
      updateData.expires_at = newExpiration.toISOString()
      break
    default:
      return res.status(400).json({ error: 'Invalid action' })
  }

  const { error: updateError } = await adminClient
    .from('application_tokens')
    .update(updateData)
    .eq('id', tokenId)

  if (updateError) {
    console.error(`[${requestId}] Error updating token:`, updateError)
    return res.status(500).json({ error: 'Failed to update token' })
  }

  return res.status(200).json({
    success: true,
    message: `Token ${action}d successfully`
  })
}

// Invalidate token
async function handleInvalidateToken(req, res, adminClient, adminUser, requestId) {
  const { tokenId } = req.body

  if (!tokenId) {
    return res.status(400).json({ error: 'Token ID is required' })
  }

  console.log(`[${requestId}] Invalidating token: ${tokenId}`)

  const { error: invalidateError } = await adminClient
    .from('application_tokens')
    .update({
      is_used: true,
      used_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', tokenId)

  if (invalidateError) {
    console.error(`[${requestId}] Error invalidating token:`, invalidateError)
    return res.status(500).json({ error: 'Failed to invalidate token' })
  }

  return res.status(200).json({
    success: true,
    message: 'Token invalidated successfully'
  })
}

// Helper function to determine token status
function getTokenStatus(token) {
  if (token.is_used) {
    return 'used'
  }
  
  const now = new Date()
  const expiresAt = new Date(token.expires_at)
  
  if (expiresAt < now) {
    return 'expired'
  }
  
  return 'active'
}
