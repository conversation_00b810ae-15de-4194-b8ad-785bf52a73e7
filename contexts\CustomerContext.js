// Customer context for managing customer state across the application
import { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

const CustomerContext = createContext();

export function CustomerProvider({ children }) {
  const { user, isAuthenticated } = useAuth();
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
    // Load customer data if authenticated or from local storage for guest customers
  // Optimized to reduce API calls and prevent unnecessary re-renders
  useEffect(() => {
    // Track loading state to prevent duplicate API calls
    let isMounted = true;
    let loadStarted = false;

    async function loadCustomerData() {
      if (loadStarted || !isMounted) return;
      loadStarted = true;

      setLoading(true);
      try {
        // First, try to load from session storage to avoid API call
        const sessionCustomer = sessionStorage.getItem('customerData');

        // Check if authenticated user with customer ID
        const hasAuthCustomerId = isAuthenticated &&
                                user &&
                                user.user_metadata &&
                                user.user_metadata.customer_id;

        // If authenticated with ID, verify data freshness or fetch new data
        if (hasAuthCustomerId) {
          const customerId = user.user_metadata.customer_id;

          // If we already have session data for this customer, use it first
          if (sessionCustomer) {
            const parsedCustomer = JSON.parse(sessionCustomer);
            // Check if session customer matches authenticated customer ID
            if (parsedCustomer && parsedCustomer.id === customerId) {
              setCustomer(parsedCustomer);
              setLoading(false);

              // Quietly refresh in the background if data is older than 5 minutes
              const lastUpdated = parsedCustomer.lastFetched || 0;
              const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

              if (lastUpdated < fiveMinutesAgo) {
                // Fetch fresh data without setting loading state
                try {
                  const response = await fetch(`/api/public/customers?id=${customerId}`);
                  if (response.ok && isMounted) {
                    const data = await response.json();
                    if (data.customer) {
                      // Add timestamp for future freshness checks
                      data.customer.lastFetched = Date.now();
                      // Update session storage
                      sessionStorage.setItem('customerData', JSON.stringify(data.customer));
                      // Only update state if data actually changed
                      setCustomer(prev => {
                        if (JSON.stringify(prev) === JSON.stringify(data.customer)) return prev;
                        return data.customer;
                      });
                    }
                  }
                } catch (backgroundErr) {
                  console.debug('Background customer refresh failed:', backgroundErr);
                }
              }

              return; // Exit early with cached data
            }
          }

          // No valid cached data, fetch from API
          const response = await fetch(`/api/public/customers?id=${customerId}`);
          if (response.ok && isMounted) {
            const data = await response.json();
            if (data.customer) {
              // Add timestamp for future freshness checks
              data.customer.lastFetched = Date.now();
              setCustomer(data.customer);
              // Save in session storage as well
              sessionStorage.setItem('customerData', JSON.stringify(data.customer));
              return;
            }
          }
        }
        // If not authenticated or couldn't load from API, use session storage
        else if (sessionCustomer && isMounted) {
          setCustomer(JSON.parse(sessionCustomer));
        }
      } catch (err) {
        console.error('Error loading customer data:', err);
        if (isMounted) setError(err);
      } finally {
        if (isMounted) setLoading(false);
      }
    }

    loadCustomerData();

    // Cleanup function to prevent updates after unmount
    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, user]);

  // Save guest customer data
  const saveGuestCustomer = (customerData) => {
    setCustomer(customerData);
    sessionStorage.setItem('customerData', JSON.stringify(customerData));
  };

  // Update customer data
  const updateCustomer = async (updateData) => {
    if (!customer?.id) {
      setError(new Error('No customer ID available for update'));
      return { data: null, error: new Error('No customer ID available for update') };
    }

    try {
      const response = await fetch('/api/public/customers', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: customer.id,
          ...updateData
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update customer');
      }

      // Update local state
      setCustomer(data.customer);
      // Update session storage
      sessionStorage.setItem('customerData', JSON.stringify(data.customer));

      return { data: data.customer, error: null };
    } catch (err) {
      setError(err);
      return { data: null, error: err };
    }
  };

  // Clear customer data
  const clearCustomer = () => {
    setCustomer(null);
    sessionStorage.removeItem('customerData');
  };

  // Check if a user is logged in as a guest
  const isGuest = () => {
    return !!customer && !isAuthenticated;
  };

  const hasMarketingConsent = () => {
    return customer?.marketing_consent === true;
  };

  const value = {
    customer,
    loading,
    error,
    saveGuestCustomer,
    updateCustomer,
    clearCustomer,
    isGuest,
    hasMarketingConsent
  };

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
}

export function useCustomer() {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomer must be used within a CustomerProvider');
  }
  return context;
}

export default CustomerContext;
