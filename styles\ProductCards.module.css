.productsSection {
  padding: 6rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #333;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: #1A73E8;
  border-radius: 2px;
}

.sectionSubtitle {
  font-size: 1.2rem;
  text-align: center;
  max-width: 800px;
  margin: 2rem auto 4rem;
  color: #666;
  line-height: 1.6;
}

.productHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  gap: 2rem;
}

.categoryNav {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.categoryButton {
  background: none;
  border: none;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

.categoryButton:hover {
  color: #1A73E8;
}

.activeCategory {
  color: #1A73E8;
  font-weight: 600;
}

.activeIndicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 3px;
  width: 30px;
  background-color: #1A73E8;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.cartContainer {
  position: relative;
}

.cartButton {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  position: relative;
  color: #333;
  transition: all 0.3s ease;
}

.cartButton:hover {
  color: #1A73E8;
  transform: scale(1.1);
}

.cartCount {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #FF6B6B;
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cartDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  min-width: 320px;
  z-index: 100;
  padding: 1.5rem;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cartDropdown h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  color: #333;
  position: relative;
}

.cartDropdown h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #1A73E8;
  border-radius: 2px;
}

.emptyCart {
  text-align: center;
  color: #888;
  padding: 2rem 0;
}

.cartItems {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.cartItem {
  display: flex;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.cartItem:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.cartItemImage {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1rem;
}

.cartItemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cartItemDetails {
  flex: 1;
}

.cartItemDetails h4 {
  font-size: 0.95rem;
  margin: 0 0 0.5rem;
  color: #333;
}

.cartItemPrice {
  display: flex;
  justify-content: space-between;
  color: #555;
  font-size: 0.9rem;
}

.cartTotal {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.checkoutButton {
  width: 100%;
  background-color: #1A73E8;
  color: white;
  border: none;
  padding: 0.8rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkoutButton:hover {
  background-color: #1557b0;
}

.disabledButton {
  background-color: #ccc !important;
  cursor: not-allowed;
}

.disabledButton:hover {
  background-color: #ccc !important;
  transform: none;
}

.policyLinks {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.85rem;
}

.policyLinks a {
  color: #1A73E8;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.policyLinks a:hover {
  color: #1557b0;
}

.policyLinks a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #1557b0;
  transition: width 0.3s ease;
}

.policyLinks a:hover::after {
  width: 100%;
}

.policyCheckbox {
  margin-bottom: 1.5rem;
}

.policyCheckbox label {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #555;
  cursor: pointer;
}

.policyCheckbox input {
  margin-top: 0.2rem;
}

.policyCheckbox span {
  line-height: 1.4;
}

.productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-top: 2rem;
}

.productCardWrapper {
  transition: all 0.3s ease;
}

.productCard {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.productCardWrapper:hover .productCard {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-10px);
}

.productImageContainer {
  height: 250px;
  position: relative;
  overflow: hidden;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.productCardWrapper:hover .productImage {
  transform: scale(1.08);
}

.productBadge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: #FF6B6B;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

.adminEditButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.adminEditButton:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.productOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.productCardWrapper:hover .productOverlay {
  opacity: 1;
}

.quickViewButton {
  background-color: white;
  color: #333;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateY(20px);
  opacity: 0;
}

.productCardWrapper:hover .quickViewButton {
  transform: translateY(0);
  opacity: 1;
  transition-delay: 0.1s;
}

.quickViewButton:hover {
  background-color: #f0f0f0;
}

.productContent {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.productName {
  font-size: 1.2rem;
  margin: 0 0 1rem;
  color: #333;
}

.productDescription {
  color: #666;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  line-height: 1.6;
  flex-grow: 1;
}

.productFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.productPrice {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1A73E8;
}

.addToCartButton {
  background-color: #1A73E8;
  color: white;
  border: none;
  padding: 0.7rem 1.2rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.addToCartButton:hover {
  background-color: #1557b0;
}

.buttonIcon {
  display: inline-flex;
}

/* Flying product animation */
.flyingProduct {
  position: fixed;
  width: 20px;
  height: 20px;
  background-color: #1A73E8;
  border-radius: 50%;
  pointer-events: none;
  z-index: 1000;
  animation: flyToCart 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

@keyframes flyToCart {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.1) translate(800px, -500px);
    opacity: 0;
  }
}

/* Responsive styles */
@media (max-width: 1200px) {
  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .sectionTitle {
    font-size: 2.5rem;
  }
}

@media (max-width: 992px) {
  .productHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .categoryNav {
    width: 100%;
    justify-content: center;
  }

  .cartContainer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
  }

  .productImageContainer {
    height: 220px;
  }
}

@media (max-width: 768px) {
  .productsSection {
    padding: 4rem 1.5rem;
  }

  .sectionTitle {
    font-size: 2.2rem;
  }

  .sectionSubtitle {
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }

  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.5rem;
  }

  .productName {
    font-size: 1.1rem;
  }

  .productDescription {
    font-size: 0.9rem;
  }

  .productPrice {
    font-size: 1.2rem;
  }

  .addToCartButton {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  .cartDropdown {
    min-width: 280px;
    right: -70px;
  }

  .cartDropdown::before {
    right: 85px;
  }
}

@media (max-width: 576px) {
  .productsGrid {
    grid-template-columns: 1fr;
  }

  .productImageContainer {
    height: 250px;
  }

  .categoryButton {
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
  }
}