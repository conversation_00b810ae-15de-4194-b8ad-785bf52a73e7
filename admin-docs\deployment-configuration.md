# Deployment Configuration

This document explains the deployment configuration for the Ocean Soul Sparkles website, including how to use the Vercel deployment webhook.

## Vercel Deployment Webhook

A Vercel deployment webhook has been configured to trigger deployments directly from external systems:

```
https://api.vercel.com/v1/integrations/deploy/prj_jxutxkTDXotGVtNxjeyCgc6V8ZKs/NdQQuIPOwE
```

This webhook is stored in `config/deployment.js` and can be used to trigger deployments programmatically.

### How Deployment Webhooks Work

Vercel deployment webhooks allow you to trigger deployments without needing to push code to your repository. This is useful for:

1. Triggering deployments from CI/CD pipelines
2. Automating deployments based on external events
3. Deploying from content management systems
4. Manually triggering deployments without pushing code

When the webhook URL is called with a POST request, Vercel will pull the latest code from your repository and start a new deployment.

### Configuration Files

The deployment configuration is stored in the following files:

1. `config/deployment.js` - Contains the webhook URLs and environment configurations
2. `scripts/trigger-deployment.js` - A script to trigger deployments manually
3. `.github/workflows/deploy-to-vercel.yml` - A GitHub Actions workflow to trigger deployments from CI/CD

### Using the Deployment Webhook

#### Manual Deployment

To trigger a deployment manually, run:

```bash
npm run deploy
```

Or directly:

```bash
node scripts/trigger-deployment.js
```

You can specify the environment to deploy to:

```bash
node scripts/trigger-deployment.js PRODUCTION
```

#### Automated Deployment with GitHub Actions

The GitHub Actions workflow in `.github/workflows/deploy-to-vercel.yml` will automatically trigger a deployment when code is pushed to the main branch.

You can also manually trigger a deployment from the GitHub Actions tab by selecting the "Deploy to Vercel" workflow and clicking "Run workflow".

#### Calling the Webhook Directly

You can call the webhook directly using curl or any HTTP client:

```bash
curl -X POST "https://api.vercel.com/v1/integrations/deploy/prj_jxutxkTDXotGVtNxjeyCgc6V8ZKs/NdQQuIPOwE"
```

## Security Considerations

The deployment webhook URL is a sensitive piece of information that should be treated like an API key. It allows anyone who has it to trigger deployments to your Vercel project.

### Best Practices

1. **Do not share the webhook URL publicly** - It should only be shared with trusted team members
2. **Store the webhook URL securely** - In this project, it's stored in `config/deployment.js`
3. **Consider using environment variables** - For even more security, you can move the webhook URL to an environment variable
4. **Rotate the webhook URL periodically** - You can generate a new webhook URL in the Vercel dashboard

### Rotating the Webhook URL

If you need to rotate the webhook URL:

1. Go to your Vercel project settings
2. Navigate to the "Git" section
3. Under "Deploy Hooks", create a new hook
4. Update the URL in `config/deployment.js`

## Troubleshooting

If you encounter issues with the deployment webhook:

1. **Check the webhook URL** - Make sure it's correct and hasn't been rotated
2. **Check your Vercel project settings** - Make sure the webhook is still active
3. **Check the Vercel deployment logs** - Look for any errors in the deployment process
4. **Try triggering a deployment manually** - Use the Vercel dashboard to trigger a deployment

## References

- [Vercel Deploy Hooks Documentation](https://vercel.com/docs/git/deploy-hooks)
- [Vercel API Documentation](https://vercel.com/docs/rest-api)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
