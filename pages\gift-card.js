import Head from 'next/head'
import Link from 'next/link'
import styles from '@/styles/GiftCard.module.css'
import Layout from '@/components/Layout'
import GiftCardShowcase from '@/components/GiftCardShowcase'
import AnimatedSection from '@/components/AnimatedSection'

export default function GiftCard() {
  // Gift card options for the showcase component
  const giftCardOptions = [
    { value: '50', label: '$50' },
    { value: '100', label: '$100' },
    { value: '200', label: '$200' },
    { value: '300', label: '$300' }
  ];

  return (
    <Layout>
      <Head>
        <title>Gift Cards | OceanSoulSparkles</title>
        <meta name="description" content="Give the gift of creativity with OceanSoulSparkles gift cards. Perfect for birthdays, holidays, or any special occasion." />
      </Head>

      <main className={styles.main}>
        <section className={styles.heroSection}>
          <div className={styles.heroOverlay}></div>
          <div className={styles.heroContent}>
            <AnimatedSection animation="fade-up">
              <h1 className={styles.heroTitle}>Gift Cards</h1>
              <p className={styles.heroSubtitle}>
                Give the gift of creativity and sparkle
              </p>
              <div className={styles.heroActions}>
                <a href="#customize" className={styles.heroCta}>Customize Your Gift Card</a>
                <a href="#how-it-works" className={styles.heroSecondary}>How It Works</a>
              </div>
            </AnimatedSection>
          </div>
          <div className={styles.scrollIndicator}>
            <div className={styles.mouseIcon}></div>
            <span>Scroll to explore</span>
          </div>
        </section>

        <section className={styles.perfectGiftSection}>
          <div className={styles.shapeDividerTop}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" className={styles.shapeFill}></path>
            </svg>
          </div>

          <div className={styles.sectionContainer}>
            <AnimatedSection animation="fade-right">
              <div className={styles.perfectGiftContent}>
                <h2 className={styles.sectionTitle}>The Perfect Gift</h2>
                <p className={styles.sectionText}>
                  Looking for a unique and memorable gift? OceanSoulSparkles gift cards are the perfect way to share
                  the joy of creativity with your loved ones. Our gift cards can be used for any of our services,
                  including face painting, airbrush body art, and braiding.
                </p>
                <p className={styles.sectionText}>
                  Whether it's for a birthday, holiday, or just because, a gift card allows the recipient to choose
                  the experience that's perfect for them. It's an ideal present for children and adults alike who
                  appreciate artistic expression and want to add some sparkle to their next event.
                </p>
                <div className={styles.valueProposition}>
                  <div className={styles.valueItem}>
                    <div className={styles.valueIcon}>🎨</div>
                    <h3>Creativity</h3>
                    <p>Support their artistic expression</p>
                  </div>
                  <div className={styles.valueItem}>
                    <div className={styles.valueIcon}>🎁</div>
                    <h3>Choice</h3>
                    <p>Let them decide what they want</p>
                  </div>
                  <div className={styles.valueItem}>
                    <div className={styles.valueIcon}>✨</div>
                    <h3>Magic</h3>
                    <p>Give the gift of sparkle & joy</p>
                  </div>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection animation="fade-left">
              <div className={styles.giftCardOptions}>
                <h3>Gift Card Options</h3>
                <div className={styles.optionsGrid}>
                  <div className={styles.optionCard}>
                    <div className={styles.optionCardInner}>
                      <h4>$50 Gift Card</h4>
                      <p>Perfect for individual services</p>
                      <span className={styles.price}>$50</span>
                    </div>
                  </div>
                  <div className={styles.optionCard}>
                    <div className={styles.optionCardInner}>
                      <h4>$100 Gift Card</h4>
                      <p>Great for multiple services or small groups</p>
                      <span className={styles.price}>$100</span>
                    </div>
                  </div>
                  <div className={styles.optionCard}>
                    <div className={styles.optionCardInner}>
                      <h4>$200 Gift Card</h4>
                      <p>Ideal for parties and group events</p>
                      <span className={styles.price}>$200</span>
                    </div>
                  </div>
                  <div className={styles.optionCard}>
                    <div className={styles.optionCardInner}>
                      <h4>Custom Amount</h4>
                      <p>Choose your own gift card value</p>
                      <span className={styles.price}>You decide</span>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
          
          <div className={styles.shapeDividerBottom}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" className={styles.shapeFill}></path>
            </svg>
          </div>
        </section>

        {/* Interactive Gift Card Customization */}
        <section id="customize" className={styles.customizeSection}>
          <GiftCardShowcase 
            title="Customize Your Gift Card"
            subtitle="Select a design, choose an amount, and add a personal message"
            giftCardOptions={giftCardOptions}
          />
        </section>

        <section id="how-it-works" className={styles.howItWorksSection}>
          <div className={styles.sectionContainer}>
            <AnimatedSection animation="fade-up">
              <h2 className={styles.sectionTitle}>How It Works</h2>
            </AnimatedSection>
            
            <div className={styles.stepsContainer}>
              <AnimatedSection animation="fade-up" delay={100}>
                <div className={styles.stepCard}>
                  <div className={styles.stepNumber}>1</div>
                  <div className={styles.stepContent}>
                    <h3>Choose Your Gift Card</h3>
                    <p>
                      Select from our standard gift card amounts or create a custom value to suit your budget.
                    </p>
                  </div>
                </div>
              </AnimatedSection>
              
              <AnimatedSection animation="fade-up" delay={200}>
                <div className={styles.stepCard}>
                  <div className={styles.stepNumber}>2</div>
                  <div className={styles.stepContent}>
                    <h3>Personalize Your Message</h3>
                    <p>
                      Add a personal message to make your gift extra special.
                    </p>
                  </div>
                </div>
              </AnimatedSection>
              
              <AnimatedSection animation="fade-up" delay={300}>
                <div className={styles.stepCard}>
                  <div className={styles.stepNumber}>3</div>
                  <div className={styles.stepContent}>
                    <h3>Delivery Options</h3>
                    <p>
                      Choose between a digital gift card sent via email or a physical card mailed to the recipient.
                    </p>
                  </div>
                </div>
              </AnimatedSection>
              
              <AnimatedSection animation="fade-up" delay={400}>
                <div className={styles.stepCard}>
                  <div className={styles.stepNumber}>4</div>
                  <div className={styles.stepContent}>
                    <h3>Redeem & Enjoy</h3>
                    <p>
                      The recipient can easily redeem their gift card when booking any of our services.
                    </p>
                  </div>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </section>

        <section className={styles.purchaseSection}>
          <div className={styles.sectionContainer}>
            <AnimatedSection animation="fade-up">
              <h2 className={styles.sectionTitle}>Purchase a Gift Card</h2>
              
              <div className={styles.purchaseFormContainer}>
                <form className={styles.purchaseForm}>
                  <div className={styles.formGrid}>
                    <div className={styles.formGroup}>
                      <label htmlFor="giftAmount">Gift Card Amount <span className={styles.required}>*</span></label>
                      <select id="giftAmount" name="giftAmount" className={styles.formSelect} required>
                        <option value="">Select an amount</option>
                        <option value="50">$50</option>
                        <option value="100">$100</option>
                        <option value="200">$200</option>
                        <option value="custom">Custom Amount</option>
                      </select>
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="customAmount">Custom Amount</label>
                      <div className={styles.inputWithIcon}>
                        <span className={styles.currencySymbol}>$</span>
                        <input type="number" id="customAmount" name="customAmount" className={styles.formInput} placeholder="Enter amount" min="20" />
                      </div>
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="recipientName">Recipient's Name <span className={styles.required}>*</span></label>
                      <input type="text" id="recipientName" name="recipientName" className={styles.formInput} required />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="deliveryMethod">Delivery Method <span className={styles.required}>*</span></label>
                      <select id="deliveryMethod" name="deliveryMethod" className={styles.formSelect} required>
                        <option value="">Select delivery method</option>
                        <option value="email">Email (Digital Gift Card)</option>
                        <option value="mail">Mail (Physical Gift Card)</option>
                      </select>
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="recipientEmail">Recipient's Email</label>
                      <input type="email" id="recipientEmail" name="recipientEmail" className={styles.formInput} placeholder="For digital delivery" />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="senderName">Your Name <span className={styles.required}>*</span></label>
                      <input type="text" id="senderName" name="senderName" className={styles.formInput} required />
                    </div>

                    <div className={styles.formGroup}>
                      <label htmlFor="senderEmail">Your Email <span className={styles.required}>*</span></label>
                      <input type="email" id="senderEmail" name="senderEmail" className={styles.formInput} required />
                    </div>
                    
                    <div className={styles.formGroup}>
                      <label htmlFor="deliveryDate">Delivery Date</label>
                      <input type="date" id="deliveryDate" name="deliveryDate" className={styles.formInput} />
                    </div>
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="recipientAddress">Recipient's Address</label>
                    <textarea id="recipientAddress" name="recipientAddress" className={styles.formTextarea} placeholder="For physical delivery"></textarea>
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="personalMessage">Personal Message</label>
                    <textarea id="personalMessage" name="personalMessage" className={styles.formTextarea} placeholder="Add a personal message to the recipient"></textarea>
                  </div>

                  <div className={styles.formActions}>
                    <button type="submit" className={styles.submitButton}>
                      <span className={styles.btnIcon}>🎁</span>
                      <span>Purchase Gift Card</span>
                    </button>
                  </div>
                </form>
              </div>
            </AnimatedSection>
          </div>
        </section>

        <section className={styles.faqSection}>
          <div className={styles.sectionContainer}>
            <AnimatedSection animation="fade-up">
              <h2 className={styles.sectionTitle}>Frequently Asked Questions</h2>
            </AnimatedSection>
            
            <div className={styles.faqGrid}>
              <AnimatedSection animation="fade-up" delay={100}>
                <div className={styles.faqItem}>
                  <h3>How long are gift cards valid?</h3>
                  <p>
                    Our gift cards are valid for 12 months from the date of purchase, giving the recipient plenty of time to use their gift.
                  </p>
                </div>
              </AnimatedSection>
              
              <AnimatedSection animation="fade-up" delay={200}>
                <div className={styles.faqItem}>
                  <h3>Can gift cards be used for any service?</h3>
                  <p>
                    Yes! Gift cards can be applied to any of our services including face painting, airbrush body art, and braiding.
                  </p>
                </div>
              </AnimatedSection>
              
              <AnimatedSection animation="fade-up" delay={300}>
                <div className={styles.faqItem}>
                  <h3>What if the service costs more than the gift card value?</h3>
                  <p>
                    If the chosen service costs more than the gift card value, the recipient can simply pay the difference.
                  </p>
                </div>
              </AnimatedSection>
              
              <AnimatedSection animation="fade-up" delay={400}>
                <div className={styles.faqItem}>
                  <h3>Can I get a refund on a gift card?</h3>
                  <p>
                    Gift cards are non-refundable but can be transferred to another person if needed.
                  </p>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </section>
        
        <section className={styles.ctaSection}>
          <div className={styles.ctaOverlay}></div>
          <div className={styles.ctaContent}>
            <AnimatedSection animation="fade-up">
              <h2>Ready to Spread Some Magic?</h2>
              <p>Give the gift of creativity, joy, and sparkle to someone special today</p>
              <a href="#customize" className={styles.ctaButton}>Create Your Gift Card</a>
            </AnimatedSection>
          </div>
        </section>
      </main>
    </Layout>
  )
}
