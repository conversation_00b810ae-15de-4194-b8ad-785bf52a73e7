import { useState } from 'react'
import userManagement from '@/lib/user-management'
import styles from '@/styles/admin/users/UserForm.module.css'

export default function UserForm({ onCancel, onSuccess, setError }) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user',
    name: '',
    phone: '',
    notes: '',
    sendWelcomeEmail: true
  })
  const [loading, setLoading] = useState(false)
  const [formErrors, setFormErrors] = useState({})
  const [showPassword, setShowPassword] = useState(false)

  // Role options with descriptions
  const roleOptions = [
    { value: 'user', label: 'User', description: 'Basic user access' },
    { value: 'braider', label: 'Braider', description: 'Hair braiding specialist' },
    { value: 'artist', label: 'Artist', description: 'Beauty and styling artist' },
    { value: 'admin', label: 'Admin', description: 'Full administrative access' },
    { value: 'dev', label: 'Developer', description: 'System development access' }
  ]

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    const newValue = type === 'checkbox' ? checked : value
    setFormData(prev => ({ ...prev, [name]: newValue }))

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Validate form
  const validateForm = () => {
    const errors = {}

    // Email validation
    if (!formData.email) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid'
    }

    // Password validation (only required for non-artist/braider roles)
    const requiresPassword = !['artist', 'braider'].includes(formData.role)
    if (requiresPassword) {
      if (!formData.password) {
        errors.password = 'Password is required'
      } else if (formData.password.length < 8) {
        errors.password = 'Password must be at least 8 characters'
      }

      // Confirm password validation
      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
      }
    }

    // Name validation (now required)
    if (!formData.name || formData.name.trim().length === 0) {
      errors.name = 'Name is required'
    } else if (formData.name.length > 100) {
      errors.name = 'Name is too long (max 100 characters)'
    }

    // Phone validation (optional but if provided, should be valid)
    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/\s/g, ''))) {
      errors.phone = 'Please enter a valid phone number'
    }

    // Role validation
    if (!formData.role) {
      errors.role = 'Role is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()

    // Validate form
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      console.log('UserForm: Creating new user with email:', formData.email)

      // Use the user management API client
      await userManagement.createUser({
        email: formData.email,
        password: formData.password,
        role: formData.role,
        name: formData.name,
        phone: formData.phone || undefined,
        notes: formData.notes || undefined,
        sendWelcomeEmail: formData.sendWelcomeEmail
      })

      // Call success callback
      onSuccess()
    } catch (err) {
      console.error('Error creating user:', err)
      setError(err.message || 'Failed to create user')
    } finally {
      setLoading(false)
    }
  }

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev)
  }

  return (
    <div className={styles.userFormContainer}>
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="email">Email Address *</label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`${styles.input} ${formErrors.email ? styles.inputError : ''}`}
            placeholder="<EMAIL>"
            required
          />
          {formErrors.email && <div className={styles.errorMessage}>{formErrors.email}</div>}
        </div>

        {/* Password fields - only show for roles that require passwords */}
        {!['artist', 'braider'].includes(formData.role) && (
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="password">Password *</label>
              <div className={styles.passwordInputContainer}>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`${styles.input} ${formErrors.password ? styles.inputError : ''}`}
                  placeholder="Minimum 8 characters"
                  required
                />
                <button
                  type="button"
                  className={styles.passwordToggle}
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                      <line x1="1" y1="1" x2="23" y2="23"></line>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                  )}
                </button>
              </div>
              {formErrors.password && <div className={styles.errorMessage}>{formErrors.password}</div>}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="confirmPassword">Confirm Password *</label>
              <input
                type={showPassword ? "text" : "password"}
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`${styles.input} ${formErrors.confirmPassword ? styles.inputError : ''}`}
                placeholder="Confirm password"
                required
              />
              {formErrors.confirmPassword && <div className={styles.errorMessage}>{formErrors.confirmPassword}</div>}
            </div>
          </div>
        )}

        {/* Info message for Artist/Braider roles */}
        {['artist', 'braider'].includes(formData.role) && (
          <div className={styles.infoMessage}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="16" x2="12" y2="12"/>
              <line x1="12" y1="8" x2="12.01" y2="8"/>
            </svg>
            <span>
              No password required. {formData.role === 'artist' ? 'Artists' : 'Braiders'} will receive a secure application link via email to complete their onboarding.
            </span>
          </div>
        )}

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="name">Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`${styles.input} ${formErrors.name ? styles.inputError : ''}`}
              placeholder="User's full name"
              required
            />
            {formErrors.name && <div className={styles.errorMessage}>{formErrors.name}</div>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="phone">Phone Number</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}
              placeholder="+61 4XX XXX XXX"
            />
            {formErrors.phone && <div className={styles.errorMessage}>{formErrors.phone}</div>}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="role">Role *</label>
          <select
            id="role"
            name="role"
            value={formData.role}
            onChange={handleChange}
            className={styles.select}
            required
          >
            {roleOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <div className={styles.helpText}>
            {roleOptions.find(opt => opt.value === formData.role)?.description}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            className={styles.textarea}
            placeholder="Additional notes about this user..."
            rows="3"
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              name="sendWelcomeEmail"
              checked={formData.sendWelcomeEmail}
              onChange={handleChange}
            />
            Send welcome email to new user
          </label>
          <div className={styles.helpText}>
            For Artist/Braider roles, this will include onboarding instructions
          </div>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            className={styles.cancelButton}
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Creating User...' : 'Create User'}
          </button>
        </div>
      </form>
    </div>
  )
}
