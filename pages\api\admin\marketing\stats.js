import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for admin marketing dashboard statistics
 * This endpoint uses service_role key to bypass RLS policies
 * Uses the simplified authentication approach
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Only allow GET requests for stats
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check if development auth bypass is enabled
  const devBypass = process.env.NEXT_PUBLIC_DEV_AUTH_BYPASS === 'true';

  if (!devBypass) {
    // Authenticate request using our simplified auth module
    const { authorized, error, user, role } = await authenticateAdminRequest(req);
    if (!authorized) {
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      });
    }
  } else {
    console.log('Development auth bypass enabled - skipping authentication for marketing stats');
  }
  try {
    // Generate a unique request ID for tracking
    const requestId = Math.random().toString(36).substring(2, 8);
    console.log(`[${requestId}] Fetching marketing stats`);

    // Get admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error(`[${requestId}] Admin client not available.`);
      return res.status(500).json({ error: 'Database connection failed' });
    }

    console.log(`[${requestId}] Admin client obtained successfully`);

    // Initialize stats with default values
    let totalSegments = 0;
    let totalCampaigns = 0;
    let activeCampaigns = 0;
    let customersWithConsent = 0;
    let totalCustomers = 0;

    // Get segments count - use customer_segments table
    try {
      const { count: segmentsCount, error: segmentsError } = await adminClient
        .from('customer_segments')
        .select('*', { count: 'exact', head: true });

      if (segmentsError) {
        console.warn(`[${requestId}] Error fetching segments count:`, segmentsError);
        // Continue with default value
      } else {
        totalSegments = segmentsCount || 0;
      }
    } catch (segmentsError) {
      console.warn(`[${requestId}] Exception fetching segments:`, segmentsError);
    }

    // Get campaigns count - check if campaigns table exists, otherwise use mock data
    try {
      // First check if campaigns table exists
      const { data: tableCheck, error: tableError } = await adminClient
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'campaigns')
        .single();

      if (tableError || !tableCheck) {
        console.warn(`[${requestId}] campaigns table does not exist, using default value`);
        totalCampaigns = 0;
        activeCampaigns = 0;
      } else {
        // Table exists, try to get counts
        const { count: campaignsCount, error: campaignsError } = await adminClient
          .from('campaigns')
          .select('*', { count: 'exact', head: true });

        if (campaignsError) {
          console.warn(`[${requestId}] Error fetching campaigns count:`, campaignsError);
          totalCampaigns = 0;
        } else {
          totalCampaigns = campaignsCount || 0;
        }

        // Get active campaigns count
        const { count: activeCampaignsCount, error: activeCampaignsError } = await adminClient
          .from('campaigns')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'active');

        if (activeCampaignsError) {
          console.warn(`[${requestId}] Error fetching active campaigns count:`, activeCampaignsError);
          activeCampaigns = 0;
        } else {
          activeCampaigns = activeCampaignsCount || 0;
        }
      }
    } catch (campaignsError) {
      console.warn(`[${requestId}] Exception fetching campaigns:`, campaignsError);
      totalCampaigns = 0;
      activeCampaigns = 0;
    }

    // Get customers with marketing consent - try different approaches
    try {
      // First try customer_preferences table
      const { count: consentCount, error: consentError } = await adminClient
        .from('customer_preferences')
        .select('*', { count: 'exact', head: true })
        .eq('marketing_consent', true);

      if (consentError) {
        console.warn(`[${requestId}] customer_preferences table error, trying customers table:`, consentError);

        // Try customers table with marketing_consent column
        const { count: customersConsentCount, error: customersConsentError } = await adminClient
          .from('customers')
          .select('*', { count: 'exact', head: true })
          .eq('marketing_consent', true);

        if (customersConsentError) {
          console.warn(`[${requestId}] customers.marketing_consent column error:`, customersConsentError);
          customersWithConsent = 0;
        } else {
          customersWithConsent = customersConsentCount || 0;
        }
      } else {
        customersWithConsent = consentCount || 0;
      }
    } catch (consentError) {
      console.warn(`[${requestId}] Exception fetching consent:`, consentError);
      customersWithConsent = 0;
    }

    // Get total customers
    try {
      const { count: customersCount, error: customersError } = await adminClient
        .from('customers')
        .select('*', { count: 'exact', head: true });

      if (customersError) {
        console.warn(`[${requestId}] Error fetching customers count:`, customersError);
        // Continue with default value
      } else {
        totalCustomers = customersCount || 0;
      }
    } catch (customersError) {
      console.warn(`[${requestId}] Exception fetching customers:`, customersError);
    }

    console.log(`[${requestId}] Marketing stats fetched successfully`);

    // Return all stats
    return res.status(200).json({
      totalSegments,
      totalCampaigns,
      activeCampaigns,
      customersWithConsent,
      totalCustomers,
      _meta: {
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching marketing stats:', error);
    return res.status(500).json({
      error: 'Server error',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
