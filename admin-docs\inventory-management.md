# Inventory Management System

This document provides detailed implementation instructions for the OceanSoulSparkles admin panel inventory management system.

## Overview

The inventory management system allows administrators to track product stock levels, manage product information, receive alerts for low inventory, and handle purchase orders. It integrates with the online store and provides real-time inventory updates.

## Features

- Product stock tracking
- Low inventory alerts
- Purchase order management
- Product information management
- Stock adjustment history
- Inventory reports
- Integration with online store

## Database Schema

Create the following tables in Supabase:

```sql
-- Products table
CREATE TABLE public.products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  sku TEXT UNIQUE,
  price DECIMAL(10, 2) NOT NULL,
  sale_price DECIMAL(10, 2),
  cost_price DECIMAL(10, 2),
  category_id UUID REFERENCES public.product_categories(id),
  image_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Product categories table
CREATE TABLE public.product_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES public.product_categories(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inventory table
CREATE TABLE public.inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 0,
  low_stock_threshold INTEGER DEFAULT 5,
  last_restock_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inventory transactions table
CREATE TABLE public.inventory_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL,
  transaction_type TEXT NOT NULL,
  reference_id UUID,
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Purchase orders table
CREATE TABLE public.purchase_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  supplier_id UUID REFERENCES public.suppliers(id),
  order_date TIMESTAMPTZ NOT NULL,
  expected_delivery_date TIMESTAMPTZ,
  status TEXT NOT NULL,
  total_amount DECIMAL(10, 2),
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Purchase order items table
CREATE TABLE public.purchase_order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  purchase_order_id UUID REFERENCES public.purchase_orders(id) NOT NULL,
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  received_quantity INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Suppliers table
CREATE TABLE public.suppliers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  contact_name TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchase_order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all products" ON public.products
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert products" ON public.products
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update products" ON public.products
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

-- Similar policies for other tables
```

## Implementation Steps

### 1. Create Product Management API Endpoints

Create the following API endpoints:

```javascript
// pages/api/products/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getProducts(req, res)
    case 'POST':
      return createProduct(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get products with optional filters
async function getProducts(req, res) {
  const { 
    search, 
    category_id, 
    is_active,
    sort_by,
    sort_order,
    limit,
    offset
  } = req.query

  let query = supabase
    .from('products')
    .select(`
      *,
      categories:category_id (name),
      inventory:inventory (quantity, low_stock_threshold)
    `)

  // Apply filters
  if (search) {
    query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,sku.ilike.%${search}%`)
  }
  if (category_id) {
    query = query.eq('category_id', category_id)
  }
  if (is_active !== undefined) {
    query = query.eq('is_active', is_active === 'true')
  }

  // Apply sorting
  if (sort_by) {
    const order = sort_order === 'desc' ? false : true
    query = query.order(sort_by, { ascending: order })
  } else {
    query = query.order('name', { ascending: true })
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit)
  }
  if (offset) {
    query = query.offset(offset)
  }

  const { data, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    products: data,
    total: count
  })
}

// Create a new product
async function createProduct(req, res) {
  const { 
    name, 
    description, 
    sku, 
    price, 
    sale_price, 
    cost_price, 
    category_id, 
    image_url, 
    is_active,
    initial_stock,
    low_stock_threshold
  } = req.body

  // Validate required fields
  if (!name || !price) {
    return res.status(400).json({ error: 'Name and price are required' })
  }

  // Start a transaction
  const { data, error } = await supabase.rpc('create_product_with_inventory', {
    p_name: name,
    p_description: description,
    p_sku: sku,
    p_price: price,
    p_sale_price: sale_price,
    p_cost_price: cost_price,
    p_category_id: category_id,
    p_image_url: image_url,
    p_is_active: is_active !== undefined ? is_active : true,
    p_initial_stock: initial_stock || 0,
    p_low_stock_threshold: low_stock_threshold || 5
  })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(201).json(data)
}
```

Create additional API endpoints for individual product operations:

```javascript
// pages/api/products/[id].js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin, isAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getProduct(id, res)
    case 'PUT':
      return updateProduct(id, req, res)
    case 'DELETE':
      // Only admins can delete products
      const isAdminUser = await isAdmin()
      if (!isAdminUser) {
        return res.status(403).json({ error: 'Forbidden' })
      }
      return deleteProduct(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single product with inventory information
async function getProduct(id, res) {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      categories:category_id (id, name),
      inventory:inventory (id, quantity, low_stock_threshold, last_restock_date)
    `)
    .eq('id', id)
    .single()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data) {
    return res.status(404).json({ error: 'Product not found' })
  }

  return res.status(200).json(data)
}

// Update a product
async function updateProduct(id, req, res) {
  const { 
    name, 
    description, 
    sku, 
    price, 
    sale_price, 
    cost_price, 
    category_id, 
    image_url, 
    is_active 
  } = req.body

  // Validate required fields
  if (!name || !price) {
    return res.status(400).json({ error: 'Name and price are required' })
  }

  // Update product
  const { data, error } = await supabase
    .from('products')
    .update({
      name,
      description,
      sku,
      price,
      sale_price,
      cost_price,
      category_id,
      image_url,
      is_active,
      updated_at: new Date()
    })
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Product not found' })
  }

  return res.status(200).json(data[0])
}

// Delete a product
async function deleteProduct(id, res) {
  // First check if product has any associated orders
  const { data: orderItems, error: orderCheckError } = await supabase
    .from('order_items')
    .select('id')
    .eq('product_id', id)
    .limit(1)

  if (orderCheckError) {
    return res.status(500).json({ error: orderCheckError.message })
  }

  if (orderItems && orderItems.length > 0) {
    return res.status(409).json({ error: 'Cannot delete product with associated orders' })
  }

  // Delete product (will cascade to inventory)
  const { data, error } = await supabase
    .from('products')
    .delete()
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Product not found' })
  }

  return res.status(200).json({ message: 'Product deleted successfully' })
}
```

### 2. Create Inventory Management API Endpoints

Create API endpoints for inventory management:

```javascript
// pages/api/inventory/adjust.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'
import { v4 as uuidv4 } from 'uuid'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { product_id, quantity, transaction_type, notes } = req.body
  const { user } = await isStaffOrAdmin()

  // Validate required fields
  if (!product_id || quantity === undefined || !transaction_type) {
    return res.status(400).json({ error: 'Missing required fields' })
  }

  // Validate transaction type
  const validTypes = ['restock', 'adjustment', 'sale', 'return', 'damaged', 'lost']
  if (!validTypes.includes(transaction_type)) {
    return res.status(400).json({ error: 'Invalid transaction type' })
  }

  try {
    // Start a transaction
    const { data, error } = await supabase.rpc('adjust_inventory', {
      p_product_id: product_id,
      p_quantity: quantity,
      p_transaction_type: transaction_type,
      p_notes: notes,
      p_created_by: user.id
    })

    if (error) {
      return res.status(500).json({ error: error.message })
    }

    return res.status(200).json(data)
  } catch (error) {
    console.error('Error adjusting inventory:', error)
    return res.status(500).json({ error: 'Failed to adjust inventory' })
  }
}
```

### 3. Create Purchase Order API Endpoints

Create API endpoints for purchase order management:

```javascript
// pages/api/purchase-orders/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getPurchaseOrders(req, res)
    case 'POST':
      return createPurchaseOrder(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get purchase orders with optional filters
async function getPurchaseOrders(req, res) {
  const { 
    supplier_id, 
    status, 
    start_date, 
    end_date,
    limit,
    offset
  } = req.query

  let query = supabase
    .from('purchase_orders')
    .select(`
      *,
      suppliers:supplier_id (name)
    `)

  // Apply filters
  if (supplier_id) {
    query = query.eq('supplier_id', supplier_id)
  }
  if (status) {
    query = query.eq('status', status)
  }
  if (start_date) {
    query = query.gte('order_date', start_date)
  }
  if (end_date) {
    query = query.lte('order_date', end_date)
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit)
  }
  if (offset) {
    query = query.offset(offset)
  }

  // Order by order date
  query = query.order('order_date', { ascending: false })

  const { data, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    purchase_orders: data,
    total: count
  })
}

// Create a new purchase order
async function createPurchaseOrder(req, res) {
  const { 
    supplier_id, 
    order_date, 
    expected_delivery_date, 
    status, 
    notes, 
    items 
  } = req.body
  const { user } = await isStaffOrAdmin()

  // Validate required fields
  if (!supplier_id || !order_date || !status || !items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({ error: 'Missing required fields' })
  }

  // Calculate total amount
  const total_amount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)

  try {
    // Start a transaction
    const { data, error } = await supabase.rpc('create_purchase_order', {
      p_supplier_id: supplier_id,
      p_order_date: order_date,
      p_expected_delivery_date: expected_delivery_date,
      p_status: status,
      p_total_amount: total_amount,
      p_notes: notes,
      p_created_by: user.id,
      p_items: items
    })

    if (error) {
      return res.status(500).json({ error: error.message })
    }

    return res.status(201).json(data)
  } catch (error) {
    console.error('Error creating purchase order:', error)
    return res.status(500).json({ error: 'Failed to create purchase order' })
  }
}
```

### 4. Create Low Stock Alert System

Create a system to alert administrators about low stock levels:

```javascript
// lib/inventory.js
import { supabase } from './supabase'
import { sendEmail } from './email'

// Check for low stock items
export async function checkLowStockItems() {
  const { data, error } = await supabase
    .from('inventory')
    .select(`
      *,
      products:product_id (name, sku)
    `)
    .lte('quantity', 'low_stock_threshold')

  if (error) {
    console.error('Error checking low stock items:', error)
    return { success: false, error }
  }

  return { success: true, lowStockItems: data }
}

// Send low stock alerts
export async function sendLowStockAlerts(adminEmails) {
  const { success, lowStockItems, error } = await checkLowStockItems()

  if (!success) {
    return { success: false, error }
  }

  if (lowStockItems.length === 0) {
    return { success: true, message: 'No low stock items found' }
  }

  // Format email content
  const itemsList = lowStockItems.map(item => `
    <tr>
      <td>${item.products.name}</td>
      <td>${item.products.sku || 'N/A'}</td>
      <td>${item.quantity}</td>
      <td>${item.low_stock_threshold}</td>
    </tr>
  `).join('')

  const emailContent = `
    <h2>Low Stock Alert</h2>
    <p>The following items are running low on stock:</p>
    <table border="1" cellpadding="5" cellspacing="0">
      <tr>
        <th>Product</th>
        <th>SKU</th>
        <th>Current Quantity</th>
        <th>Threshold</th>
      </tr>
      ${itemsList}
    </table>
    <p>Please restock these items soon.</p>
  `

  // Send email to all admin emails
  try {
    for (const email of adminEmails) {
      await sendEmail({
        to: email,
        subject: 'OceanSoulSparkles - Low Stock Alert',
        html: emailContent
      })
    }

    return { success: true, message: 'Low stock alerts sent successfully' }
  } catch (error) {
    console.error('Error sending low stock alerts:', error)
    return { success: false, error }
  }
}
```

### 5. Create Inventory Dashboard Component

Create a component for the inventory dashboard:

```javascript
// components/admin/InventoryDashboard.js
import { useState, useEffect } from 'react'
import Link from 'next/link'
import styles from '@/styles/admin/InventoryDashboard.module.css'

export default function InventoryDashboard() {
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [categories, setCategories] = useState([])
  const [stockFilter, setStockFilter] = useState('all') // 'all', 'low', 'out'

  // Fetch products and categories
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Fetch categories
        const categoriesResponse = await fetch('/api/product-categories')
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories')
        }
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData)

        // Fetch products
        let url = '/api/products?limit=100'
        if (search) {
          url += `&search=${encodeURIComponent(search)}`
        }
        if (categoryFilter) {
          url += `&category_id=${categoryFilter}`
        }

        const productsResponse = await fetch(url)
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products')
        }
        const productsData = await productsResponse.json()
        
        // Filter by stock status if needed
        let filteredProducts = productsData.products
        if (stockFilter === 'low') {
          filteredProducts = filteredProducts.filter(product => 
            product.inventory && 
            product.inventory.quantity <= product.inventory.low_stock_threshold &&
            product.inventory.quantity > 0
          )
        } else if (stockFilter === 'out') {
          filteredProducts = filteredProducts.filter(product => 
            product.inventory && product.inventory.quantity === 0
          )
        }
        
        setProducts(filteredProducts)
      } catch (error) {
        console.error('Error fetching data:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [search, categoryFilter, stockFilter])

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
  }

  // Handle category filter change
  const handleCategoryChange = (e) => {
    setCategoryFilter(e.target.value)
  }

  // Handle stock filter change
  const handleStockFilterChange = (filter) => {
    setStockFilter(filter)
  }

  if (error) {
    return <div className={styles.error}>{error}</div>
  }

  return (
    <div className={styles.inventoryDashboard}>
      <div className={styles.filters}>
        <div className={styles.searchBar}>
          <input
            type="text"
            placeholder="Search products..."
            value={search}
            onChange={handleSearchChange}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.categoryFilter}>
          <select
            value={categoryFilter}
            onChange={handleCategoryChange}
            className={styles.categorySelect}
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.stockFilter}>
          <button
            className={`${styles.filterButton} ${stockFilter === 'all' ? styles.active : ''}`}
            onClick={() => handleStockFilterChange('all')}
          >
            All Stock
          </button>
          <button
            className={`${styles.filterButton} ${stockFilter === 'low' ? styles.active : ''}`}
            onClick={() => handleStockFilterChange('low')}
          >
            Low Stock
          </button>
          <button
            className={`${styles.filterButton} ${stockFilter === 'out' ? styles.active : ''}`}
            onClick={() => handleStockFilterChange('out')}
          >
            Out of Stock
          </button>
        </div>
      </div>

      {loading ? (
        <div className={styles.loading}>Loading inventory data...</div>
      ) : (
        <div className={styles.productGrid}>
          {products.length === 0 ? (
            <div className={styles.noProducts}>No products found</div>
          ) : (
            products.map(product => (
              <div key={product.id} className={styles.productCard}>
                <div className={styles.productImage}>
                  {product.image_url ? (
                    <img src={product.image_url} alt={product.name} />
                  ) : (
                    <div className={styles.noImage}>No Image</div>
                  )}
                </div>
                <div className={styles.productInfo}>
                  <h3>{product.name}</h3>
                  <p className={styles.sku}>SKU: {product.sku || 'N/A'}</p>
                  <p className={styles.price}>${product.price.toFixed(2)}</p>
                  <div className={styles.stockInfo}>
                    <span className={styles.stockLabel}>Stock:</span>
                    <span className={getStockLevelClass(product)}>
                      {product.inventory ? product.inventory.quantity : 0}
                    </span>
                  </div>
                </div>
                <div className={styles.productActions}>
                  <Link href={`/admin/products/${product.id}`}>
                    <a className={styles.viewButton}>View</a>
                  </Link>
                  <button
                    className={styles.adjustButton}
                    onClick={() => handleAdjustStock(product)}
                  >
                    Adjust Stock
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  )
}

// Helper function to get stock level class
function getStockLevelClass(product) {
  if (!product.inventory) return styles.outOfStock
  
  const { quantity, low_stock_threshold } = product.inventory
  
  if (quantity === 0) return styles.outOfStock
  if (quantity <= low_stock_threshold) return styles.lowStock
  return styles.inStock
}
```

## Testing

1. Test product creation, editing, and deletion
2. Test inventory adjustments
3. Test purchase order creation and management
4. Test low stock alerts
5. Test inventory dashboard
6. Test integration with online store

## Security Considerations

- Implement proper authentication and authorization
- Validate all input data
- Protect against SQL injection
- Implement rate limiting for API endpoints
- Log all inventory changes for audit purposes
