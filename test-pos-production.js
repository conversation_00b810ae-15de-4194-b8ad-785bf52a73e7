/**
 * Test script to verify POS Square payment integration in production
 * This script can be run in the browser console to test the payment form
 */

console.log('🧪 Testing POS Square Payment Integration in Production...')

// Test 1: Check if the page loads without errors
console.log('✅ Test 1: Page loaded successfully')

// Test 2: Check if Square SDK loads
const checkSquareSDK = () => {
  if (window.Square) {
    console.log('✅ Test 2: Square SDK loaded successfully')
    return true
  } else {
    console.log('❌ Test 2: Square SDK not loaded')
    return false
  }
}

// Test 3: Check if payment form container exists
const checkContainer = () => {
  const container = document.querySelector('[data-testid="square-card-container"]') || 
                   document.getElementById('pos-square-card-container') ||
                   document.querySelector('.cardForm')
  
  if (container) {
    console.log('✅ Test 3: Payment form container found:', container)
    
    // Check for enhanced container management attributes
    const dataSquareContainer = container.getAttribute('data-square-container')
    const dataContainerId = container.getAttribute('data-container-id')
    
    if (dataSquareContainer && dataContainerId) {
      console.log('✅ Test 3a: Enhanced container tracking enabled:', {
        squareContainer: dataSquareContainer,
        containerId: dataContainerId
      })
    } else {
      console.log('⚠️ Test 3a: Enhanced container tracking missing - old version detected')
    }
    
    return container
  } else {
    console.log('❌ Test 3: Payment form container not found')
    return null
  }
}

// Test 4: Check for DOM manipulation errors and Square container issues
const checkForDOMErrors = () => {
  const originalError = console.error
  const originalWarn = console.warn
  let domErrors = []
  let squareWarnings = []
  
  console.error = (...args) => {
    const errorMsg = args.join(' ')
    if (errorMsg.includes('removeChild') || 
        errorMsg.includes('Node') || 
        errorMsg.includes('DOM') ||
        errorMsg.includes('Square container')) {
      domErrors.push(errorMsg)
    }
    originalError.apply(console, args)
  }

  console.warn = (...args) => {
    const warnMsg = args.join(' ')
    if (warnMsg.includes('Square container was removed') ||
        warnMsg.includes('Multiple GoTrueClient instances') ||
        warnMsg.includes('Square') ||
        warnMsg.includes('third-party cookies')) {
      squareWarnings.push(warnMsg)
    }
    originalWarn.apply(console, args)
  }
  
  // Restore original console methods after 8 seconds
  setTimeout(() => {
    console.error = originalError
    console.warn = originalWarn
    
    if (domErrors.length === 0 && squareWarnings.length === 0) {
      console.log('✅ Test 4: No DOM or Square container errors detected')
    } else {
      if (domErrors.length > 0) {
        console.log('❌ Test 4a: DOM manipulation errors detected:', domErrors)
      }
      if (squareWarnings.length > 0) {
        console.log('⚠️ Test 4b: Square/Auth warnings detected:', squareWarnings)
        
        // Specific checks for known issues
        const containerRemovedWarnings = squareWarnings.filter(w => w.includes('Square container was removed'))
        const multipleClientWarnings = squareWarnings.filter(w => w.includes('Multiple GoTrueClient instances'))
        
        if (containerRemovedWarnings.length > 0) {
          console.log('🔧 Fix needed: Square container DOM manipulation timing issue')
        }
        if (multipleClientWarnings.length > 0) {
          console.log('🔧 Fix needed: Multiple Supabase client instances detected')
        }
      }
    }
  }, 8000)
}

// Test 5: Check for multiple client instances and enhanced Supabase singleton
const checkClientInstances = () => {
  console.log('🔄 Test 5: Checking for multiple client instances...')
  
  // Check for multiple Supabase clients with enhanced singleton detection
  const supabaseClientCount = window.__SUPABASE_CLIENT_COUNT || 0
  const supabaseClientExists = !!window.__SUPABASE_CLIENT_INSTANCE
  const isCreatingInstance = !!window.__SUPABASE_CREATING_INSTANCE
  
  if (supabaseClientCount > 1) {
    console.log(`⚠️ Test 5a: Multiple Supabase clients detected: ${supabaseClientCount}`)
  } else {
    console.log(`✅ Test 5a: Supabase client instances: ${supabaseClientCount || (supabaseClientExists ? 1 : 0)}`)
  }
  
  if (isCreatingInstance) {
    console.log('🔄 Test 5a-extra: Supabase instance creation in progress')
  }
  
  // Check for proper singleton implementation
  if (window.supabase && typeof window.supabase.from === 'function') {
    console.log('✅ Test 5a-singleton: Supabase singleton appears functional')
  } else {
    console.log('⚠️ Test 5a-singleton: Supabase singleton not available or non-functional')
  }
  
  // Check for multiple Square SDK instances
  const squareScripts = document.querySelectorAll('script[src*="square.js"]')
  if (squareScripts.length > 1) {
    console.log(`⚠️ Test 5b: Multiple Square SDK scripts detected: ${squareScripts.length}`)
  } else {
    console.log(`✅ Test 5b: Square SDK scripts: ${squareScripts.length}`)
  }
  
  // Check global Square instances
  if (window.Square) {
    console.log('✅ Test 5c: Single Square SDK instance available')
  } else {
    console.log('❌ Test 5c: No Square SDK instance found')
  }
}

// Test 6: Try to trigger Square form initialization with enhanced container monitoring
const testSquareFormInit = () => {
  if (window.Square && checkContainer()) {
    console.log('🔄 Test 6: Attempting Square form initialization...')
    
    // Monitor for container removal during initialization
    const container = checkContainer()
    if (container) {
      const containerId = container.getAttribute('data-container-id')
      console.log(`🔍 Test 6-setup: Monitoring container with ID: ${containerId}`)
      
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.removedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE && 
                  (node.id && node.id.includes('square-card') || 
                   node.className && node.className.includes('square') ||
                   node.getAttribute && node.getAttribute('data-square-container'))) {
                console.log('⚠️ Square container element removed during initialization:', {
                  tag: node.tagName,
                  id: node.id,
                  className: node.className,
                  containerId: node.getAttribute('data-container-id'),
                  squareContainer: node.getAttribute('data-square-container')
                })
              }
            })
            
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE && 
                  (node.id && node.id.includes('square') || 
                   node.className && (node.className.includes('square') || node.className.includes('card')))) {
                console.log('✅ Square-related element added:', {
                  tag: node.tagName,
                  id: node.id,
                  className: node.className
                })
              }
            })
          }
        })
      })
      
      observer.observe(document.body, { 
        childList: true, 
        subtree: true,
        attributes: false 
      })
      
      // Stop observing after 5 seconds
      setTimeout(() => observer.disconnect(), 5000)
    }
    
    // Trigger force initialization event
    const event = new CustomEvent('forceSquareInit', {
      detail: { source: 'production-test' }
    })
    window.dispatchEvent(event)
    
    setTimeout(() => {
      const container = checkContainer()
      if (container && container.children.length > 0) {
        console.log('✅ Test 6: Square form appears to have initialized')
        
        // Check if enhanced container attributes are preserved
        const dataSquareContainer = container.getAttribute('data-square-container')
        const dataContainerId = container.getAttribute('data-container-id')
        
        if (dataSquareContainer && dataContainerId) {
          console.log('✅ Test 6-persistence: Container tracking attributes preserved after initialization')
        } else {
          console.log('⚠️ Test 6-persistence: Container tracking attributes lost during initialization')
        }
      } else {
        console.log('⚠️ Test 6: Square form initialization unclear')
      }
    }, 3000)
  } else {
    console.log('❌ Test 6: Cannot test Square form - SDK or container missing')
  }
}

// Run all tests
setTimeout(() => {
  console.log('🚀 Starting POS production tests...')
  
  checkForDOMErrors()
  
  setTimeout(() => {
    checkSquareSDK()
    checkContainer()
    checkClientInstances()
    testSquareFormInit()
  }, 1000)
}, 1000)

console.log('📋 Test script loaded. Tests will run automatically.')
console.log('💡 You can also run individual tests manually:')
console.log('   - checkSquareSDK()')
console.log('   - checkContainer()')
console.log('   - checkClientInstances()')
console.log('   - testSquareFormInit()')
console.log('   - diagnoseSquareIssues()')
console.log('   - testAllEnhancements()')

// Comprehensive test for all enhancements
window.testAllEnhancements = () => {
  console.log('🚀 Testing All POSSquarePayment Enhancements...')
  
  // Test 1: SSR Fix
  console.log('🔍 Test E1: SSR window.Square access fix')
  if (typeof window !== 'undefined' && window.Square) {
    console.log('✅ E1: SSR-safe window.Square access working')
  } else {
    console.log('⚠️ E1: window.Square not available (may be expected in SSR)')
  }
  
  // Test 2: Enhanced Supabase Singleton
  console.log('🔍 Test E2: Enhanced Supabase singleton implementation')
  const hasProperSingleton = window.supabase && 
                             typeof window.supabase.from === 'function' &&
                             (window.__SUPABASE_CLIENT_COUNT || 0) <= 1
  
  if (hasProperSingleton) {
    console.log('✅ E2: Enhanced Supabase singleton working correctly')
  } else {
    console.log('❌ E2: Supabase singleton issues detected')
    diagnoseSupabaseIssues()
  }
  
  // Test 3: Enhanced Container Management
  console.log('🔍 Test E3: Enhanced Square container management')
  const containers = document.querySelectorAll('[data-square-container]')
  const hasEnhancedContainers = containers.length > 0
  
  if (hasEnhancedContainers) {
    console.log('✅ E3: Enhanced container management detected')
    containers.forEach((container, i) => {
      const containerId = container.getAttribute('data-container-id')
      const isValidId = containerId && containerId.includes('square-card-') && containerId.includes('-')
      console.log(`  Container ${i + 1}: ${isValidId ? '✅' : '⚠️'} ID format: ${containerId}`)
    })
  } else {
    console.log('⚠️ E3: No enhanced containers found - component may not be mounted')
  }
  
  // Test 4: Console Memory Management
  console.log('🔍 Test E4: Console memory management')
  if (window.getConsoleErrors) {
    const data = window.getConsoleErrors()
    const hasMemoryManagement = data.errors.length <= 100 && data.warnings.length <= 100
    
    if (hasMemoryManagement) {
      console.log('✅ E4: Console memory management working (max entries respected)')
    } else {
      console.log('⚠️ E4: Console arrays may be growing too large', {
        errors: data.errors.length,
        warnings: data.warnings.length
      })
    }
  } else {
    console.log('⚠️ E4: Console monitoring not available')
  }
  
  // Test 5: Ref-based Payment Form Management
  console.log('🔍 Test E5: Payment form ref-based management')
  // This is harder to test from outside, but we can check for absence of useEffect loops
  setTimeout(() => {
    if (window.getConsoleErrors) {
      const errors = window.getConsoleErrors()
      const hasUseEffectLoops = errors.errors.some(e => 
        e.includes('Maximum update depth exceeded') || 
        e.includes('useEffect') ||
        e.includes('infinite')
      )
      
      if (!hasUseEffectLoops) {
        console.log('✅ E5: No useEffect infinite loops detected')
      } else {
        console.log('❌ E5: Potential useEffect loops detected')
      }
    }
  }, 2000)
  
  console.log('📊 Enhancement test summary will be available in 3 seconds...')
  setTimeout(() => {
    console.log('📊 ENHANCEMENT TEST SUMMARY:')
    console.log('   E1: SSR Fix - Check logs above')
    console.log('   E2: Supabase Singleton - Check logs above') 
    console.log('   E3: Container Management - Check logs above')
    console.log('   E4: Console Memory - Check logs above')
    console.log('   E5: Ref Management - Check logs above')
  }, 3000)
}

const diagnoseSupabaseIssues = () => {
  console.log('🔧 Diagnosing Supabase singleton issues:')
  console.log('  - Client count:', window.__SUPABASE_CLIENT_COUNT)
  console.log('  - Instance flag:', window.__SUPABASE_CLIENT_INSTANCE)
  console.log('  - Creating flag:', window.__SUPABASE_CREATING_INSTANCE)
  console.log('  - Global supabase:', !!window.supabase)
  console.log('  - Supabase functional:', !!(window.supabase && window.supabase.from))
}

// Additional diagnostic functions
window.diagnoseSquareIssues = () => {
  console.log('🔍 Diagnosing Square integration issues...')
  
  // Check console errors from monitoring
  if (window.getConsoleErrors) {
    const errors = window.getConsoleErrors()
    console.log('Recent errors:', errors.errors.slice(-5))
    console.log('Recent warnings:', errors.warnings.slice(-5))
  }
  
  // Check DOM state with enhanced container detection
  const containers = document.querySelectorAll('[class*="square"], [id*="square"], [class*="card"], [class*="payment"], [data-square-container], [data-container-id]')
  console.log('Payment-related DOM elements found:', containers.length)
  containers.forEach((el, i) => {
    console.log(`Element ${i + 1}:`, {
      tag: el.tagName,
      id: el.id,
      className: el.className,
      children: el.children.length,
      hasSquareContent: el.innerHTML.includes('square') || el.innerHTML.includes('card'),
      dataSquareContainer: el.getAttribute('data-square-container'),
      dataContainerId: el.getAttribute('data-container-id')
    })
  })
  
  // Check client instances
  checkClientInstances()
  
  // Enhanced Supabase singleton diagnostics
  console.log('🔍 Supabase singleton diagnostics:')
  console.log('  Global supabase instance:', !!window.supabase)
  console.log('  Instance count tracking:', window.__SUPABASE_CLIENT_COUNT)
  console.log('  Instance exists flag:', window.__SUPABASE_CLIENT_INSTANCE)
  console.log('  Creating instance flag:', window.__SUPABASE_CREATING_INSTANCE)
  
  // Check for container stability
  const squareContainers = document.querySelectorAll('[data-square-container]')
  console.log(`🔍 Enhanced Square containers found: ${squareContainers.length}`)
  squareContainers.forEach((container, i) => {
    console.log(`  Container ${i + 1}:`, {
      id: container.id,
      containerId: container.getAttribute('data-container-id'),
      isAttached: document.body.contains(container),
      hasSquareContent: container.children.length > 0
    })
  })
}
