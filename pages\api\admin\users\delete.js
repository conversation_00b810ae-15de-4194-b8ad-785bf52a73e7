import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * Determine if a user is "fully active" and should not be deleted
 * A user is considered fully active if they have:
 * - Logged in at least once, OR
 * - Have an approved application (for artist/braider), OR
 * - Have an active profile with successful subscription status
 */
function determineIfUserIsFullyActive(authUser, userRole, userProfile, applicationStatus) {
  // If user has logged in, they are considered active
  if (userProfile?.login_count > 0) {
    return true
  }

  // If user has an approved application, they are considered active
  if (applicationStatus === 'approved') {
    return true
  }

  // If user has successful subscription status, they are considered active
  if (userProfile?.subscription_status === 'successful') {
    return true
  }

  // If user profile is explicitly marked as active and they have a role beyond 'user'
  if (userProfile?.is_active && userRole?.role && userRole.role !== 'user') {
    return true
  }

  // Check if user was created more than 7 days ago and has a profile
  // This indicates they've completed the signup process
  if (userProfile && authUser?.created_at) {
    const createdDate = new Date(authUser.created_at)
    const daysSinceCreation = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceCreation > 7) {
      return true
    }
  }

  // Otherwise, user is not considered fully active
  return false
}

export default async function handler(req, res) {
  // Only allow DELETE requests
  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request using our robust auth module
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      console.error('Delete user API: Authentication failed:', error?.message || 'Unknown error')
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    console.log('Delete user API: Authentication successful. User:', user?.email, 'Role:', role)
    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ error: 'Unauthorized. Only administrators and developers can delete users.' })
    }

    // Get admin client to bypass RLS policies
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get user ID from request body
    const { userId } = req.body

    // Validate required fields
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    // Prevent self-deletion
    if (userId === user.id) {
      return res.status(400).json({ error: 'Cannot delete your own account' })
    }

    // Check if user exists in auth.users (the authoritative source)
    let userToDelete = null
    try {
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers()
      if (authError) {
        console.error('Error fetching auth users:', authError)
        return res.status(500).json({ error: 'Failed to fetch user data' })
      }

      userToDelete = authData.users.find(u => u.id === userId)
      if (!userToDelete) {
        console.error('User not found in auth.users:', userId)
        return res.status(404).json({ error: 'User not found' })
      }
    } catch (error) {
      console.error('Error checking user existence:', error)
      return res.status(500).json({ error: 'Failed to verify user existence' })
    }

    // Get comprehensive user data to determine if deletion is allowed
    const { data: userRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', userId)
      .single()

    // Note: roleError is acceptable here as user might not have a role record
    if (roleError && roleError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching user role:', roleError)
      return res.status(500).json({ error: 'Failed to fetch user role' })
    }

    // Get user profile to check activity status
    const { data: userProfile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('is_active, subscription_status, login_count, last_login_at')
      .eq('id', userId)
      .single()

    // Note: profileError is acceptable here as user might not have a profile record
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching user profile:', profileError)
      return res.status(500).json({ error: 'Failed to fetch user profile' })
    }

    // Get application status for artist/braider users
    let applicationStatus = null
    if (userRole?.role === 'artist' || userRole?.role === 'braider') {
      const { data: application, error: appError } = await adminClient
        .from('artist_braider_applications')
        .select('status, reviewed_at')
        .eq('user_id', userId)
        .eq('application_type', userRole.role)
        .single()

      if (appError && appError.code !== 'PGRST116') {
        console.error('Error fetching application status:', appError)
        return res.status(500).json({ error: 'Failed to fetch application status' })
      }

      applicationStatus = application?.status || null
    }

    // Determine if user is "fully active"
    const isFullyActive = determineIfUserIsFullyActive(userToDelete, userRole, userProfile, applicationStatus)

    // Apply deletion restrictions based on user role and status
    if (isFullyActive && role !== 'dev') {
      return res.status(403).json({
        error: 'Cannot delete active user',
        message: 'This user is fully active and cannot be deleted. Use the deactivate function instead. Only developers can delete active users (not recommended).',
        userStatus: {
          role: userRole?.role || 'unknown',
          isActive: userProfile?.is_active || false,
          hasLoggedIn: (userProfile?.login_count || 0) > 0,
          applicationStatus: applicationStatus,
          lastLogin: userProfile?.last_login_at
        },
        recommendation: 'Use /api/admin/users/deactivate to deactivate this account instead'
      })
    }

    // Prevent deletion of dev users unless current user is also dev
    if (userRole?.role === 'dev' && role !== 'dev') {
      return res.status(403).json({ error: 'Only developers can delete other developer accounts' })
    }

    // Additional protection for admin users
    if (userRole?.role === 'admin' && role !== 'dev') {
      return res.status(403).json({ error: 'Only developers can delete admin accounts' })
    }

    // Log the deletion activity before deleting
    try {
      await adminClient
        .from('user_activity_log')
        .insert([
          {
            user_id: userId,
            activity_type: 'user_deleted',
            activity_description: `User account deleted by ${user.email}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          }
        ])
    } catch (activityError) {
      console.error('Error logging user deletion activity:', activityError)
      // Continue anyway - activity logging is not critical
    }

    // Delete user from Supabase Auth (this will cascade delete related records due to foreign key constraints)
    const { error: deleteError } = await adminClient.auth.admin.deleteUser(userId)

    if (deleteError) {
      console.error('Error deleting user from auth:', deleteError)
      return res.status(500).json({ error: 'Failed to delete user account' })
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: `User ${userToDelete.email || 'Unknown'} deleted successfully`
    })

  } catch (error) {
    console.error('Unexpected error deleting user:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
