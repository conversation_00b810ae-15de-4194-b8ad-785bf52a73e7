#!/usr/bin/env node

/**
 * Test script to verify the inventory dashboard foreign key fix
 */

require('dotenv').config({ path: '.env.local' });

async function testInventoryDashboard() {
  console.log('🧪 Testing Inventory Dashboard Foreign Key Fix...\n');

  try {
    // Test the API endpoint directly
    const response = await fetch('http://localhost:3000/api/admin/inventory/dashboard', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token', // This will be bypassed in dev mode
        'Content-Type': 'application/json',
      },
    });

    console.log(`📡 API Response Status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API Response received successfully!');
    console.log('\n📊 Dashboard Data Summary:');
    console.log(`   📦 Total Products: ${data.totalProducts || 0}`);
    console.log(`   💰 Total Value: $${(data.totalValue || 0).toFixed(2)}`);
    console.log(`   ⚠️  Low Stock Count: ${data.lowStockCount || 0}`);
    console.log(`   🚫 Out of Stock Count: ${data.outOfStockCount || 0}`);
    console.log(`   📈 Recent Movements: ${data.recentMovements?.length || 0} items`);

    // Check if recent movements have user names (this was the failing part)
    if (data.recentMovements && data.recentMovements.length > 0) {
      console.log('\n👥 Recent Movements User Data:');
      data.recentMovements.slice(0, 3).forEach((movement, index) => {
        console.log(`   ${index + 1}. Product: ${movement.product_name || 'Unknown'}`);
        console.log(`      User: ${movement.user_name || 'Unknown User'}`);
        console.log(`      Type: ${movement.type || 'Unknown'}`);
        console.log(`      Quantity: ${movement.quantity || 0}`);
        console.log('');
      });
    }

    console.log('🎉 Foreign key relationship fix appears to be working!');
    console.log('✅ The inventory dashboard can now load user profile data successfully.');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testInventoryDashboard().catch(console.error);
