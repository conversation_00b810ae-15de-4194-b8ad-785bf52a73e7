import { authenticateAdminRequest } from '@/lib/admin-auth';
import fs from 'fs';
import path from 'path';

/**
 * API endpoint for managing Google Search Console verification files
 *
 * GET: List existing Google verification files
 * DELETE: Remove a specific Google verification file
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Google verification files API called: ${req.method} ${req.url}`);

  // Authenticate admin request
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  // Only allow admin users
  if (role !== 'admin') {
    console.error(`[${requestId}] User ${user?.email} with role ${role} attempted to access Google verification files`);
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Only admin users can manage Google verification files',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);

  const publicDir = path.join(process.cwd(), 'public');
  const googleVerificationPattern = /^google[a-f0-9]+\.html$/i;

  try {
    if (req.method === 'GET') {
      console.log(`[${requestId}] Listing Google verification files`);

      // In development mode with auth bypass, return mock data
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Development mode: returning mock verification files`);
        return res.status(200).json({
          success: true,
          files: [],
          count: 0,
          requestId,
          timestamp: new Date().toISOString(),
          _note: 'Development mode - no verification files configured'
        });
      }

      try {
        // List existing Google verification files
        const files = fs.readdirSync(publicDir)
          .filter(filename => googleVerificationPattern.test(filename))
          .map(filename => {
            const filePath = path.join(publicDir, filename);
            const stats = fs.statSync(filePath);

            return {
              filename,
              url: `/${filename}`,
              size: stats.size,
              created: stats.birthtime,
              modified: stats.mtime
            };
          });

        console.log(`[${requestId}] Found ${files.length} Google verification files`);
        return res.status(200).json({
          success: true,
          files,
          count: files.length,
          requestId,
          timestamp: new Date().toISOString()
        });
      } catch (fsError) {
        console.error(`[${requestId}] File system error:`, fsError.message);
        return res.status(200).json({
          success: true,
          files: [],
          count: 0,
          requestId,
          timestamp: new Date().toISOString(),
          _note: 'Could not access public directory - no files listed'
        });
      }

    } else if (req.method === 'DELETE') {
      // Delete a specific Google verification file
      const { filename } = req.query;

      if (!filename) {
        return res.status(400).json({ error: 'Filename is required' });
      }

      if (!googleVerificationPattern.test(filename)) {
        return res.status(400).json({ error: 'Invalid filename format' });
      }

      const filePath = path.join(publicDir, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({ error: 'File not found' });
      }

      try {
        fs.unlinkSync(filePath);
        console.log(`Deleted Google verification file: ${filename}`);

        return res.status(200).json({
          success: true,
          message: `File ${filename} deleted successfully`
        });
      } catch (error) {
        console.error(`Error deleting file ${filename}:`, error);
        return res.status(500).json({ error: 'Failed to delete file' });
      }

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error('Error managing Google verification files:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to manage verification files'
    });
  }
}
