# Developer Guidelines for Authentication

This document provides guidelines for developers working with the Ocean Soul Sparkles authentication system.

## General Principles

1. **Use the unified Supabase client**
   - Always use the Supabase client from `lib/supabase.js`
   - Never create multiple instances of the Supabase client
   - Do not use deprecated authentication methods

2. **Follow the authentication flow**
   - Use the documented authentication flow
   - Do not create custom authentication flows
   - Do not bypass authentication checks

3. **Handle errors gracefully**
   - Provide user-friendly error messages
   - Log authentication errors for debugging
   - Do not expose sensitive information in error messages

4. **Respect user roles**
   - Check user roles before allowing access to protected resources
   - Do not hardcode role checks in multiple places
   - Use the role-based access control system

## Client-Side Authentication

### Using the AuthProvider

The `AuthProvider` component manages authentication state in the React application. Always use it to access authentication information:

```jsx
// pages/admin/dashboard.js
import { useAuth } from '@/components/admin/AuthProvider'

export default function Dashboard() {
  const { user, role, loading } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (!user) {
    return <div>Not authenticated</div>
  }

  return (
    <div>
      <h1>Dashboard</h1>
      <p>Welcome, {user.email}</p>
      <p>Role: {role}</p>
    </div>
  )
}
```

### Protecting Routes

Use the `ProtectedRoute` component to protect routes that require authentication:

```jsx
// pages/admin/settings.js
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import AdminLayout from '@/components/admin/AdminLayout'

export default function SettingsPage() {
  return (
    <ProtectedRoute adminOnly>
      <AdminLayout>
        <h1>Settings</h1>
        {/* Settings content */}
      </AdminLayout>
    </ProtectedRoute>
  )
}
```

### Making Authenticated API Requests

Use the `authenticatedFetch` function to make authenticated API requests:

```javascript
// components/admin/CustomerList.js
import { useState, useEffect } from 'react'
import { authenticatedFetch } from '@/lib/auth'

export default function CustomerList() {
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true)
        const response = await authenticatedFetch('/api/admin/customers')
        
        if (!response.ok) {
          throw new Error(`Failed to fetch customers: ${response.status}`)
        }
        
        const data = await response.json()
        setCustomers(data)
      } catch (error) {
        console.error('Error fetching customers:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchCustomers()
  }, [])

  // Render customers
}
```

## Server-Side Authentication

### Creating Protected API Routes

Use the `withAdminAuth` higher-order function to protect API routes:

```javascript
// pages/api/admin/settings/index.js
import { withAdminAuth } from '@/lib/admin-auth'

const handler = async (req, res) => {
  const { method } = req

  switch (method) {
    case 'GET':
      // Handle GET request
      return res.status(200).json({ /* settings data */ })
    case 'PUT':
      // Handle PUT request
      return res.status(200).json({ success: true })
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Wrap the handler with authentication middleware
export default withAdminAuth(handler)
```

### Accessing User Information in API Routes

The authentication middleware adds user and role information to the request object:

```javascript
// pages/api/admin/dashboard/stats.js
import { withAdminAuth } from '@/lib/admin-auth'

const handler = async (req, res) => {
  // Access user and role information
  const { user, role } = req

  console.log(`Request from user: ${user.email}, role: ${role}`)

  // Process the request
  return res.status(200).json({ /* stats data */ })
}

export default withAdminAuth(handler)
```

### Using the Admin Client

For server-side operations that require admin privileges, use the admin client:

```javascript
// pages/api/admin/users/create.js
import { getAdminClient } from '@/lib/supabase'
import { withAdminAuth } from '@/lib/admin-auth'

const handler = async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { email, password, role } = req.body

    // Validate input
    if (!email || !password || !role) {
      return res.status(400).json({ error: 'Missing required fields' })
    }

    // Get admin client
    const adminClient = getAdminClient()

    // Create user
    const { data: userData, error: userError } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    })

    if (userError) {
      throw userError
    }

    // Set user role
    const { error: roleError } = await adminClient
      .from('user_roles')
      .insert([{ id: userData.user.id, role }])

    if (roleError) {
      throw roleError
    }

    return res.status(201).json({ success: true, user: userData.user })
  } catch (error) {
    console.error('Error creating user:', error)
    return res.status(500).json({ error: error.message })
  }
}

export default withAdminAuth(handler)
```

## Best Practices

### 1. Token Handling

- Never store tokens in local variables or expose them in client-side code
- Let the Supabase client handle token storage and refresh
- Do not manually manipulate tokens

### 2. Error Handling

- Always handle authentication errors gracefully
- Provide clear error messages to users
- Log authentication errors for debugging
- Include error codes for easier troubleshooting

```javascript
// Example of good error handling
try {
  const { data, error } = await signIn(email, password)
  
  if (error) {
    if (error.message.includes('Invalid login credentials')) {
      setError('Invalid email or password')
    } else if (error.message.includes('rate limit')) {
      setError('Too many login attempts. Please try again later.')
    } else {
      setError('An error occurred during login. Please try again.')
      console.error('Login error:', error)
    }
    return
  }
  
  // Success handling
} catch (error) {
  setError('An unexpected error occurred. Please try again.')
  console.error('Unexpected login error:', error)
}
```

### 3. Role-Based Access Control

- Always check user roles before allowing access to protected resources
- Use the role-based access control system consistently
- Do not hardcode role checks in multiple places

```javascript
// Example of good role-based access control
import { useAuth } from '@/components/admin/AuthProvider'

export default function AdminFeature() {
  const { role } = useAuth()
  
  if (role !== 'admin') {
    return <div>You do not have permission to access this feature.</div>
  }
  
  return (
    <div>
      {/* Admin-only feature */}
    </div>
  )
}
```

### 4. Testing

- Write tests for authentication flows
- Test both successful and failed authentication scenarios
- Test role-based access control
- Test token refresh and session persistence

```javascript
// Example of authentication testing
describe('Authentication', () => {
  test('should login successfully with valid credentials', async () => {
    // Test implementation
  })
  
  test('should return error with invalid credentials', async () => {
    // Test implementation
  })
  
  test('should maintain session after page refresh', async () => {
    // Test implementation
  })
  
  test('should refresh token before expiration', async () => {
    // Test implementation
  })
})
```

## Common Pitfalls to Avoid

1. **Creating multiple Supabase clients**
   - This can lead to inconsistent authentication state
   - Always use the unified client from `lib/supabase.js`

2. **Manually handling tokens**
   - Let the Supabase client handle token storage and refresh
   - Do not try to implement custom token handling

3. **Inconsistent authentication checks**
   - Use the provided authentication middleware consistently
   - Do not create custom authentication checks

4. **Hardcoding role checks**
   - Use the role-based access control system
   - Do not hardcode role checks in multiple places

5. **Ignoring error handling**
   - Always handle authentication errors gracefully
   - Provide clear error messages to users
