import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for managing individual events
 * Handles GET, PUT, and DELETE operations for specific events
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  const { eventId } = req.query;
  
  console.log(`[${requestId}] Event API called: ${req.method} for event ${eventId}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetEvent(req, res, eventId, requestId);
    } else if (req.method === 'PUT') {
      return await handleUpdateEvent(req, res, eventId, user, requestId);
    } else if (req.method === 'DELETE') {
      return await handleDeleteEvent(req, res, eventId, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request - fetch single event
 */
async function handleGetEvent(req, res, eventId, requestId) {
  try {
    console.log(`[${requestId}] Fetching event:`, eventId);

    // Fetch the event
    const { data: event, error } = await supabase
      .from('events')
      .select(`
        id,
        name,
        location,
        description,
        start_date,
        end_date,
        status,
        max_capacity,
        current_bookings,
        created_by,
        created_at,
        updated_at,
        total_expenses,
        total_revenue,
        net_profit,
        artist_ticket_cost,
        artists_pay_tickets,
        expense_budget,
        revenue_target
      `)
      .eq('id', eventId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.log(`[${requestId}] Event not found:`, eventId);
        return res.status(404).json({ error: 'Event not found' });
      }
      console.error(`[${requestId}] Error fetching event:`, error);
      throw error;
    }

    // Get QR code count for this event
    const { data: qrCodes, error: qrError } = await supabase
      .from('event_qr_codes')
      .select('id')
      .eq('event_id', eventId);

    if (qrError) {
      console.warn(`[${requestId}] Error fetching QR code count:`, qrError);
    }

    const eventWithQRCount = {
      ...event,
      qr_code_count: qrCodes ? qrCodes.length : 0
    };

    console.log(`[${requestId}] Event fetched successfully`);
    return res.status(200).json({ event: eventWithQRCount });
  } catch (error) {
    console.error(`[${requestId}] Error in handleGetEvent:`, error);
    throw error;
  }
}

/**
 * Handle PUT request - update event
 */
async function handleUpdateEvent(req, res, eventId, user, requestId) {
  try {
    const {
      name,
      location,
      description,
      start_date,
      end_date,
      max_capacity,
      status,
      // New financial tracking fields
      artist_ticket_cost,
      artists_pay_tickets,
      expense_budget,
      revenue_target
    } = req.body;

    console.log(`[${requestId}] Updating event:`, {
      eventId,
      name,
      location,
      start_date,
      end_date,
      artist_ticket_cost,
      artists_pay_tickets,
      expense_budget,
      revenue_target
    });

    // Validate required fields
    if (!name || !location || !start_date || !end_date) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['name', 'location', 'start_date', 'end_date']
      });
    }

    // Validate dates
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);

    if (startDate >= endDate) {
      return res.status(400).json({
        error: 'Event start date must be before end date'
      });
    }

    // Validate financial fields
    if (artist_ticket_cost && (isNaN(artist_ticket_cost) || parseFloat(artist_ticket_cost) < 0)) {
      return res.status(400).json({
        error: 'Artist ticket cost must be a valid positive number'
      });
    }

    if (expense_budget && (isNaN(expense_budget) || parseFloat(expense_budget) < 0)) {
      return res.status(400).json({
        error: 'Expense budget must be a valid positive number'
      });
    }

    if (revenue_target && (isNaN(revenue_target) || parseFloat(revenue_target) < 0)) {
      return res.status(400).json({
        error: 'Revenue target must be a valid positive number'
      });
    }

    // Check if event exists and user has permission to update it
    const { data: existingEvent, error: fetchError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Event not found' });
      }
      throw fetchError;
    }

    // Prepare update data
    const updateData = {
      name,
      location,
      description: description || '',
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      status: status || 'active',
      max_capacity: max_capacity ? parseInt(max_capacity) : null,
      updated_at: new Date().toISOString()
    };

    // Add financial fields if provided
    if (artist_ticket_cost !== undefined) {
      updateData.artist_ticket_cost = artist_ticket_cost ? parseFloat(artist_ticket_cost) : 0.00;
    }
    if (artists_pay_tickets !== undefined) {
      updateData.artists_pay_tickets = Boolean(artists_pay_tickets);
    }
    if (expense_budget !== undefined) {
      updateData.expense_budget = expense_budget ? parseFloat(expense_budget) : null;
    }
    if (revenue_target !== undefined) {
      updateData.revenue_target = revenue_target ? parseFloat(revenue_target) : null;
    }

    // Update event
    const { data: event, error } = await supabase
      .from('events')
      .update(updateData)
      .eq('id', eventId)
      .select()
      .single();

    if (error) {
      console.error(`[${requestId}] Error updating event:`, error);
      throw error;
    }

    console.log(`[${requestId}] Event updated successfully:`, event.id);
    return res.status(200).json({ 
      event: {
        ...event,
        qr_code_count: 0 // Will be updated by frontend if needed
      }
    });
  } catch (error) {
    console.error(`[${requestId}] Error in handleUpdateEvent:`, error);
    throw error;
  }
}

/**
 * Handle DELETE request - delete event
 */
async function handleDeleteEvent(req, res, eventId, user, requestId) {
  try {
    console.log(`[${requestId}] Deleting event:`, eventId);

    // Check if event exists and user has permission to delete it
    const { data: existingEvent, error: fetchError } = await supabase
      .from('events')
      .select('id, created_by, name')
      .eq('id', eventId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Event not found' });
      }
      throw fetchError;
    }

    // Check if event has associated QR codes or bookings
    const { data: qrCodes, error: qrError } = await supabase
      .from('event_qr_codes')
      .select('id')
      .eq('event_id', eventId);

    if (qrError) {
      console.warn(`[${requestId}] Error checking QR codes:`, qrError);
    }

    const { data: eventBookings, error: bookingError } = await supabase
      .from('event_bookings')
      .select('id')
      .eq('event_id', eventId);

    if (bookingError) {
      console.warn(`[${requestId}] Error checking event bookings:`, bookingError);
    }

    // Warn if event has associated data
    if (qrCodes && qrCodes.length > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete event with associated QR codes. Please delete QR codes first.' 
      });
    }

    if (eventBookings && eventBookings.length > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete event with associated bookings.' 
      });
    }

    // Delete the event
    const { error: deleteError } = await supabase
      .from('events')
      .delete()
      .eq('id', eventId);

    if (deleteError) {
      console.error(`[${requestId}] Error deleting event:`, deleteError);
      throw deleteError;
    }

    console.log(`[${requestId}] Event deleted successfully:`, eventId);
    return res.status(200).json({ 
      message: 'Event deleted successfully',
      eventId: eventId
    });
  } catch (error) {
    console.error(`[${requestId}] Error in handleDeleteEvent:`, error);
    throw error;
  }
}
