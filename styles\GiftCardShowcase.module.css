.giftCardSection {
  padding: 6rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.giftCardContainer {
  width: 100%;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #333;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: #1A73E8;
  border-radius: 2px;
}

.sectionSubtitle {
  font-size: 1.2rem;
  text-align: center;
  max-width: 800px;
  margin: 2rem auto 4rem;
  color: #666;
  line-height: 1.6;
}

.giftCardContent {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  align-items: center;
  justify-content: center;
}

.giftCardPreview {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.giftCardImageContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  border: 4px solid;
  transition: all 0.3s ease;
}

.giftCardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.giftCardPreview:hover .giftCardImage {
  transform: scale(1.05);
}

.giftCardOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  color: white;
}

.giftCardInfo {
  text-align: right;
  margin-top: auto;
}

.giftCardAmount {
  font-size: 3rem;
  font-weight: 800;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  margin-bottom: 0.5rem;
}

.giftCardName {
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.4;
}

.designSelector {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: center;
}

.designOption {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid transparent;
  padding: 0;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.designOption img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.designOption span {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0.3rem 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.7rem;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.designOption:hover span {
  transform: translateY(0);
}

.activeDesign {
  transform: translateY(-5px);
}

.giftCardForm {
  flex: 1;
  min-width: 300px;
  max-width: 550px;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formLabel {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.formInput, .formTextarea {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.formInput:focus, .formTextarea:focus {
  border-color: #1A73E8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
  outline: none;
}

.formTextarea {
  resize: vertical;
  min-height: 100px;
}

.amountSelector {
  margin-top: 1rem;
}

.amountToggle {
  display: flex;
  margin-bottom: 1rem;
  background-color: #f5f5f5;
  border-radius: 30px;
  padding: 0.3rem;
}

.amountToggleBtn {
  flex: 1;
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 30px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.activeToggle {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  color: #1A73E8;
}

.presetAmounts {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.amountBtn {
  flex: 1;
  min-width: 80px;
  background-color: white;
  border: 1px solid #ddd;
  padding: 0.7rem 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.amountBtn:hover {
  border-color: #1A73E8;
  transform: translateY(-2px);
}

.activeAmount {
  background-color: #1A73E8;
  color: white;
  border-color: #1A73E8;
}

.customAmountInput {
  position: relative;
}

.currencySymbol {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-weight: 600;
}

.customAmountInput .formInput {
  padding-left: 2rem;
}

.submitButton {
  width: 100%;
  background-color: #1A73E8;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.submitButton:hover {
  background-color: #1557b0;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btnIcon {
  display: inline-flex;
  transition: transform 0.3s ease;
}

.submitButton:hover .btnIcon {
  transform: rotate(90deg);
}

/* Payment options styles */
.paymentOptions {
  padding: 1rem 0;
}

.paymentTitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
  font-weight: 600;
}

.paymentSummary {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.summaryItem:last-child {
  margin-bottom: 0;
}

.backButton {
  width: 100%;
  padding: 0.75rem;
  background-color: transparent;
  color: #1A73E8;
  border: 1px solid #1A73E8;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.backButton:hover {
  background-color: rgba(26, 115, 232, 0.1);
}

.securePaymentNote {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #4CAF50;
  margin-top: 1.5rem;
  justify-content: center;
}

.securePaymentNote svg {
  margin-right: 0.5rem;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .sectionTitle {
    font-size: 2.5rem;
  }

  .giftCardAmount {
    font-size: 2.5rem;
  }
}

@media (max-width: 992px) {
  .giftCardContent {
    flex-direction: column;
  }

  .giftCardPreview, .giftCardForm {
    max-width: 600px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .giftCardSection {
    padding: 4rem 1.5rem;
  }

  .sectionTitle {
    font-size: 2.2rem;
  }

  .sectionSubtitle {
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }

  .giftCardAmount {
    font-size: 2.2rem;
  }

  .giftCardName {
    font-size: 1.1rem;
  }

  .designOption {
    width: 70px;
    height: 50px;
  }
}

@media (max-width: 576px) {
  .giftCardSection {
    padding: 3rem 1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .formLabel {
    font-size: 0.9rem;
  }

  .designSelector {
    gap: 0.5rem;
  }

  .designOption {
    width: 60px;
    height: 45px;
  }

  .amountBtn {
    min-width: 70px;
    padding: 0.6rem 0;
    font-size: 0.9rem;
  }

  .amountToggleBtn {
    font-size: 0.8rem;
  }
}