/**
 * API endpoint for checking token validity
 * This endpoint helps diagnose authentication issues by providing detailed information
 * about the authentication token.
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';

export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);

  try {
    console.log(`[${requestId}] Token check request received`);

    // Extract token from Authorization header
    let token = null;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log(`[${requestId}] Token found in Authorization header`);
    } else {
      console.log(`[${requestId}] No token found in Authorization header`);
      return res.status(401).json({
        success: false,
        message: 'No authentication token provided in Authorization header',
        requestId
      });
    }

    // Check token format
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      console.log(`[${requestId}] Invalid token format`);
      return res.status(401).json({
        success: false,
        message: 'Invalid token format',
        requestId
      });
    }

    // Check authentication
    const authResult = await authenticateAdminRequest(req);

    if (!authResult.authorized) {
      console.warn(`[${requestId}] Authentication failed:`, authResult.error?.message || 'Unknown error');

      // Return detailed error information for diagnostics
      return res.status(401).json({
        success: false,
        endpoint: {
          status: 'error',
          message: 'Authentication failed',
          error: authResult.error?.message || 'Unknown error',
          authorized: false,
          tokenFound: true
        },
        status: 401,
        requestId
      });
    }

    // Authentication successful
    console.log(`[${requestId}] Authentication successful for user: ${authResult.user.email}`);

    // Parse token payload
    let payload = null;
    try {
      const base64Url = tokenParts[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        Buffer.from(base64, 'base64').toString().split('').map(c => {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join('')
      );
      payload = JSON.parse(jsonPayload);
    } catch (parseError) {
      console.error(`[${requestId}] Error parsing token payload:`, parseError);
    }

    return res.status(200).json({
      success: true,
      endpoint: {
        status: 'success',
        message: 'Token is valid',
        user: {
          id: authResult.user.id,
          email: authResult.user.email,
          role: authResult.role
        },
        authorized: true,
        tokenInfo: {
          format: 'valid',
          payload: payload ? {
            sub: payload.sub,
            exp: payload.exp,
            iat: payload.iat,
            expiresAt: new Date(payload.exp * 1000).toISOString(),
            issuedAt: new Date(payload.iat * 1000).toISOString()
          } : null
        }
      },
      status: 200,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Token check error:`, error);

    return res.status(500).json({
      success: false,
      message: 'An error occurred while checking token',
      error: error.message,
      requestId
    });
  }
}
