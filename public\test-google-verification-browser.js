/**
 * Browser-based test for Google Search Console verification
 * 
 * Instructions:
 * 1. Open browser console on any page
 * 2. Copy and paste this entire script
 * 3. Run: testGoogleVerification()
 */

window.testGoogleVerification = async function() {
  console.log('🚀 Testing Google Search Console Verification Implementation...\n');
  
  const TEST_CODE = 'HtjqFmAXzFBvlS4lE';
  
  // Test 1: Check if we're on a public page
  const isAdminPage = window.location.pathname.startsWith('/admin');
  console.log(`📍 Current page: ${window.location.pathname}`);
  console.log(`🔒 Is admin page: ${isAdminPage}`);
  
  // Test 2: Check if verification meta tag exists
  const verificationMeta = document.querySelector('meta[name="google-site-verification"]');
  
  if (verificationMeta) {
    const content = verificationMeta.getAttribute('content');
    console.log(`✅ Google verification meta tag found: ${content}`);
    
    if (isAdminPage) {
      console.log('❌ SECURITY ISSUE: Verification tag found on admin page!');
    } else {
      console.log('✅ Verification tag correctly present on public page');
    }
  } else {
    console.log('❌ Google verification meta tag NOT found');
    
    if (isAdminPage) {
      console.log('✅ SECURITY OK: No verification tag on admin page (as expected)');
    } else {
      console.log('⚠️  No verification tag on public page - may need to set in admin settings');
    }
  }
  
  // Test 3: Check if GoogleVerification component is loaded
  console.log('\n🔍 Checking component integration...');
  
  // Look for any React components in the page
  const reactRoot = document.querySelector('#__next');
  if (reactRoot) {
    console.log('✅ React app detected');
  }
  
  // Test 4: Manual verification instructions
  console.log('\n📋 Manual verification steps:');
  console.log('1. Go to /admin/settings');
  console.log('2. Find "Google Search Console Verification" field');
  console.log(`3. Enter test code: ${TEST_CODE}`);
  console.log('4. Save settings');
  console.log('5. Navigate to a public page (/, /about, /services)');
  console.log('6. Check page source for: <meta name="google-site-verification" content="HtjqFmAXzFBvlS4lE">');
  console.log('7. Navigate to an admin page (/admin, /admin/settings)');
  console.log('8. Verify the meta tag is NOT present in admin page source');
  
  return {
    isAdminPage,
    hasVerificationTag: !!verificationMeta,
    verificationContent: verificationMeta?.getAttribute('content'),
    securityOK: isAdminPage ? !verificationMeta : true
  };
};

// Auto-run test when script loads
console.log('🔧 Google verification test loaded. Run: testGoogleVerification()');

// Also provide a quick check function
window.quickVerificationCheck = function() {
  const meta = document.querySelector('meta[name="google-site-verification"]');
  const isAdmin = window.location.pathname.startsWith('/admin');
  
  console.log(`Page: ${window.location.pathname}`);
  console.log(`Admin page: ${isAdmin}`);
  console.log(`Has verification tag: ${!!meta}`);
  if (meta) console.log(`Verification content: ${meta.getAttribute('content')}`);
  
  return { isAdmin, hasTag: !!meta, content: meta?.getAttribute('content') };
};
