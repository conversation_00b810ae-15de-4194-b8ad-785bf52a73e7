import { useState } from 'react';
import styles from '@/styles/admin/RecurringBookingForm.module.css';

/**
 * Component for creating recurring bookings
 * 
 * @param {Object} props - Component props
 * @param {Object} props.initialBooking - Initial booking data
 * @param {Function} props.onSave - Function to call when form is saved
 * @param {Function} props.onCancel - Function to call when form is canceled
 * @returns {JSX.Element}
 */
export default function RecurringBookingForm({ initialBooking, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    pattern: 'weekly',
    interval: 1,
    endType: 'count',
    count: 4,
    endDate: '',
    daysOfWeek: {
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false
    }
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Set initial day of week based on the initial booking
  useState(() => {
    if (initialBooking && initialBooking.start_time) {
      const startDate = new Date(initialBooking.start_time);
      const dayOfWeek = startDate.getDay();
      const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      
      setFormData(prev => ({
        ...prev,
        daysOfWeek: {
          ...prev.daysOfWeek,
          [days[dayOfWeek]]: true
        },
        endDate: new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from start
      }));
    }
  }, [initialBooking]);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      if (name.startsWith('daysOfWeek.')) {
        const day = name.split('.')[1];
        setFormData(prev => ({
          ...prev,
          daysOfWeek: {
            ...prev.daysOfWeek,
            [day]: checked
          }
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          [name]: checked
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Validate form
      if (formData.pattern === 'weekly' && !Object.values(formData.daysOfWeek).some(v => v)) {
        throw new Error('Please select at least one day of the week');
      }
      
      if (formData.endType === 'date' && !formData.endDate) {
        throw new Error('Please select an end date');
      }
      
      if (formData.endType === 'count' && (!formData.count || formData.count < 1)) {
        throw new Error('Please enter a valid number of occurrences');
      }
      
      // Call the onSave callback with the form data
      onSave({
        ...initialBooking,
        recurring: {
          pattern: formData.pattern,
          interval: parseInt(formData.interval, 10),
          endType: formData.endType,
          count: formData.endType === 'count' ? parseInt(formData.count, 10) : null,
          endDate: formData.endType === 'date' ? formData.endDate : null,
          daysOfWeek: formData.pattern === 'weekly' ? formData.daysOfWeek : null
        }
      });
    } catch (error) {
      console.error('Error in recurring booking form:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className={styles.recurringBookingForm}>
      <h2 className={styles.title}>Create Recurring Booking</h2>
      
      {error && (
        <div className={styles.error}>{error}</div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className={styles.formGroup}>
          <label htmlFor="pattern">Repeat Pattern</label>
          <select
            id="pattern"
            name="pattern"
            value={formData.pattern}
            onChange={handleInputChange}
            className={styles.select}
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="interval">Repeat Every</label>
          <div className={styles.intervalGroup}>
            <input
              type="number"
              id="interval"
              name="interval"
              min="1"
              max="12"
              value={formData.interval}
              onChange={handleInputChange}
              className={styles.numberInput}
            />
            <span>{formData.pattern === 'daily' ? 'days' : formData.pattern === 'weekly' ? 'weeks' : 'months'}</span>
          </div>
        </div>
        
        {formData.pattern === 'weekly' && (
          <div className={styles.formGroup}>
            <label>Repeat On</label>
            <div className={styles.daysOfWeek}>
              {Object.keys(formData.daysOfWeek).map((day) => (
                <label key={day} className={styles.dayCheckbox}>
                  <input
                    type="checkbox"
                    name={`daysOfWeek.${day}`}
                    checked={formData.daysOfWeek[day]}
                    onChange={handleInputChange}
                  />
                  <span>{day.charAt(0).toUpperCase() + day.slice(1, 3)}</span>
                </label>
              ))}
            </div>
          </div>
        )}
        
        <div className={styles.formGroup}>
          <label>End</label>
          <div className={styles.endOptions}>
            <label className={styles.radioOption}>
              <input
                type="radio"
                name="endType"
                value="count"
                checked={formData.endType === 'count'}
                onChange={handleInputChange}
              />
              <span>After</span>
              <input
                type="number"
                name="count"
                min="1"
                max="52"
                value={formData.count}
                onChange={handleInputChange}
                disabled={formData.endType !== 'count'}
                className={styles.countInput}
              />
              <span>occurrences</span>
            </label>
            
            <label className={styles.radioOption}>
              <input
                type="radio"
                name="endType"
                value="date"
                checked={formData.endType === 'date'}
                onChange={handleInputChange}
              />
              <span>On date</span>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleInputChange}
                disabled={formData.endType !== 'date'}
                className={styles.dateInput}
              />
            </label>
          </div>
        </div>
        
        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            className={styles.cancelButton}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Creating...' : 'Create Recurring Bookings'}
          </button>
        </div>
      </form>
    </div>
  );
}
