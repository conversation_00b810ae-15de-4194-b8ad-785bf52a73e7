import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import BookingCalendar from '@/components/admin/BookingCalendar';
import CustomerSearch from '@/components/admin/CustomerSearch';
import DataExport from '@/components/admin/DataExport';
import { isAuthenticated } from '@/lib/unified-auth-fetch';
import styles from '@/styles/admin/Dashboard.module.css';

/**
 * Admin Dashboard Page
 *
 * This page demonstrates the integration of the enhanced booking and customer management components.
 */
export default function Dashboard() {
  const [authenticated, setAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('calendar');
  const [refreshKey, setRefreshKey] = useState(0);
  const [selectedBooking, setSelectedBooking] = useState(null);

  // Check authentication status using unified auth system (no listeners to prevent flashing)
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('[Dashboard] Checking authentication status...');
        const authStatus = await isAuthenticated();
        console.log('[Dashboard] Authentication status:', authStatus);
        setAuthenticated(authStatus);
      } catch (error) {
        console.error('[Dashboard] Error checking authentication:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []); // Only run once on mount to prevent flashing

  // Handle booking selection
  const handleSelectBooking = (booking) => {
    setSelectedBooking(booking);
  };

  // Handle slot selection
  const handleSelectSlot = ({ start, end }) => {
    // Navigate to booking creation page with pre-filled date/time
    const startTime = encodeURIComponent(start.toISOString());
    const endTime = encodeURIComponent(end.toISOString());
    window.location.href = `/admin/bookings/new?start_time=${startTime}&end_time=${endTime}`;
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Render loading state
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  // Render login prompt if not authenticated
  if (!authenticated) {
    return (
      <div className={styles.authContainer}>
        <h1>Admin Dashboard</h1>
        <p>Please log in to access the admin dashboard.</p>
        <Link href="/admin/login" className={styles.loginButton}>
          Log In
        </Link>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Admin Dashboard | Ocean Soul Sparkles</title>
        <meta name="description" content="Admin dashboard for Ocean Soul Sparkles" />
      </Head>

      <div className={styles.dashboard}>
        <header className={styles.header}>
          <h1 className={styles.title}>Admin Dashboard</h1>
          <div className={styles.userInfo}>
            <span className={styles.userEmail}>Admin User</span>
            <button
              className={styles.logoutButton}
              onClick={() => window.location.href = '/admin/login'}
            >
              Log Out
            </button>
          </div>
        </header>

        <div className={styles.tabs}>
          <button
            className={`${styles.tabButton} ${activeTab === 'calendar' ? styles.active : ''}`}
            onClick={() => setActiveTab('calendar')}
          >
            Booking Calendar
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'customers' ? styles.active : ''}`}
            onClick={() => setActiveTab('customers')}
          >
            Customer Search
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'export' ? styles.active : ''}`}
            onClick={() => setActiveTab('export')}
          >
            Data Export
          </button>
        </div>

        <div className={styles.tabContent}>
          {activeTab === 'calendar' && (
            <div className={styles.calendarTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Booking Calendar</h2>
                <div className={styles.tabActions}>
                  <Link
                    href="/admin/bookings/new"
                    className={styles.newButton}
                  >
                    New Booking
                  </Link>
                  <button
                    className={styles.refreshButton}
                    onClick={handleRefresh}
                  >
                    Refresh
                  </button>
                </div>
              </div>

              <div className={styles.calendarContainer}>
                <BookingCalendar
                  onSelectBooking={handleSelectBooking}
                  onSelectSlot={handleSelectSlot}
                  refreshKey={refreshKey}
                />
              </div>

              {selectedBooking && (
                <div className={styles.selectedBookingInfo}>
                  <h3>Selected Booking</h3>
                  <p>
                    <strong>Customer:</strong> {selectedBooking.customerName}
                  </p>
                  <p>
                    <strong>Service:</strong> {selectedBooking.serviceName}
                  </p>
                  <p>
                    <strong>Date:</strong> {new Date(selectedBooking.start_time).toLocaleDateString()}
                  </p>
                  <p>
                    <strong>Time:</strong> {new Date(selectedBooking.start_time).toLocaleTimeString()} - {new Date(selectedBooking.end_time).toLocaleTimeString()}
                  </p>
                  <div className={styles.bookingActions}>
                    <Link
                      href={`/admin/bookings/${selectedBooking.id}`}
                      className={styles.viewButton}
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'customers' && (
            <div className={styles.customersTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Customer Search</h2>
                <div className={styles.tabActions}>
                  <Link
                    href="/admin/customers/new"
                    className={styles.newButton}
                  >
                    New Customer
                  </Link>
                </div>
              </div>

              <CustomerSearch />
            </div>
          )}

          {activeTab === 'export' && (
            <div className={styles.exportTab}>
              <div className={styles.tabHeader}>
                <h2 className={styles.tabTitle}>Data Export</h2>
              </div>

              <DataExport />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
