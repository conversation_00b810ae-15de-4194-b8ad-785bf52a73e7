/**
 * Enhanced fetch utility for making authenticated API requests
 * Automatically adds authorization headers and handles common error cases
 */
import { supabase } from './supabase';
import { getApiUrl, createAuthFetchOptions, refreshAuthToken } from './cross-origin';
import { redirectToAppropriateLogin } from './auth-redirect';

/**
 * Make an authenticated API request with proper error handling
 *
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - The response data
 * @throws {Error} - If the request fails
 */
export async function apiFetch(url, options = {}) {
  // Handle cross-origin requests using the helper
  const requestUrl = typeof url === 'string' && url.startsWith('/') ? getApiUrl(url) : url;

  if (requestUrl !== url) {
    console.log(`API Fetch: Using cross-origin URL: ${url} -> ${requestUrl}`);
  }
  try {
    console.log(`API Fetch: Requesting ${url}`);

    // Get fetch options with authentication
    const fetchOptions = await createAuthFetchOptions(options);

    // Check if we have an Authorization header
    if (!fetchOptions.headers.Authorization) {
      console.warn('API Fetch: No Authorization header available');

      // If this is an admin API request, redirect to the appropriate login page
      if (typeof window !== 'undefined' && typeof requestUrl === 'string' && requestUrl.includes('/api/admin/')) {
        console.log('API Fetch: Admin API request without authentication, redirecting to login');

        // Use the auth-redirect helper to redirect to the appropriate login page
        redirectToAppropriateLogin(window.location.pathname);
        throw new Error('Redirecting to login');
      }
    }

    // Make the request with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    // Add the abort signal to the fetch options
    fetchOptions.signal = controller.signal;

    // Make the request
    const response = await fetch(requestUrl, fetchOptions);

    clearTimeout(timeoutId);

    // Parse the response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // Handle error responses
    if (!response.ok) {
      const errorMessage = data.error || data.message || 'Unknown error';
      console.error(`API Fetch Error (${response.status}):`, errorMessage);

      // Handle 401 Unauthorized errors
      if (response.status === 401) {
        console.log('API Fetch: 401 Unauthorized, attempting to refresh token');

        // Try to refresh the token
        const newToken = await refreshAuthToken();

        if (newToken) {
          console.log('API Fetch: Token refreshed, retrying request');

          // Update the Authorization header with the new token
          fetchOptions.headers.Authorization = `Bearer ${newToken}`;

          // Retry the request
          try {
            const retryResponse = await fetch(requestUrl, fetchOptions);

            // Parse the retry response
            let retryData;
            const retryContentType = retryResponse.headers.get('content-type');
            if (retryContentType && retryContentType.includes('application/json')) {
              retryData = await retryResponse.json();
            } else {
              retryData = await retryResponse.text();
            }

            if (!retryResponse.ok) {
              const retryErrorMessage = retryData.error || retryData.message || 'Unknown error';
              console.error(`API Fetch Retry Error (${retryResponse.status}):`, retryErrorMessage);
              throw new Error(retryErrorMessage);
            }

            return retryData;
          } catch (retryError) {
            console.error('API Fetch: Retry failed:', retryError);

            // If this is an admin API request and retry failed, redirect to the appropriate login page
            if (typeof window !== 'undefined' && typeof requestUrl === 'string' && requestUrl.includes('/api/admin/')) {
              console.log('API Fetch: Admin API retry failed, redirecting to login');

              // Use the auth-redirect helper to redirect to the appropriate login page
              redirectToAppropriateLogin(window.location.pathname);
              throw new Error('Redirecting to login');
            }

            throw retryError;
          }
        } else {
          console.error('API Fetch: Token refresh failed');

          // If this is an admin API request and token refresh failed, redirect to the appropriate login page
          if (typeof window !== 'undefined' && typeof requestUrl === 'string' && requestUrl.includes('/api/admin/')) {
            console.log('API Fetch: Admin API token refresh failed, redirecting to login');

            // Use the auth-redirect helper to redirect to the appropriate login page
            redirectToAppropriateLogin(window.location.pathname);
            throw new Error('Redirecting to login');
          }
        }
      }

      // Throw the error with the error message
      throw new Error(errorMessage);
    }

    return data;
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('API Fetch: Request timed out');
      throw new Error('Request timed out');
    }

    // If the error is about redirecting to login, don't log it as an error
    if (error.message === 'Redirecting to login') {
      throw error;
    }

    console.error('API Fetch Error:', error);
    throw error;
  }
}

export default apiFetch;
