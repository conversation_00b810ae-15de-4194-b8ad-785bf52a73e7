import { useState } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import AutomationForm from '@/components/admin/marketing/AutomationForm'
import styles from '@/styles/admin/marketing/AutomationCreate.module.css'

export default function NewAutomation() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Handle form submission
  const handleSubmit = async (automationData) => {
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Create automation
      const response = await fetch('/api/marketing/automations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(automationData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create automation')
      }

      const data = await response.json()
      setSuccessMessage('Automation created successfully')

      // Redirect to automation detail page after a short delay
      setTimeout(() => {
        router.push(`/admin/marketing/automations/${data.id}`)
      }, 1500)
    } catch (error) {
      console.error('Error creating automation:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <AdminLayout>
      <div className={styles.automationCreate}>
        <div className={styles.header}>
          <h2>Create Automated Message</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push('/admin/marketing/automations')}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <AutomationForm
          onSubmit={handleSubmit}
          onCancel={() => router.push('/admin/marketing/automations')}
        />
      </div>
    </AdminLayout>
  )
}
