.applicationsContainer {
  width: 100%;
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Filters Section */
.filtersSection {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filterGroup label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
  min-width: 120px;
}

.searchInput {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  min-width: 200px;
}

.searchInput:focus,
.filterSelect:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.emptyState p {
  margin: 0;
  font-size: 0.9rem;
}

/* Table Styles */
.tableContainer {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #dee2e6;
}

.applicationsTable {
  width: 100%;
  border-collapse: collapse;
}

.applicationsTable th {
  background-color: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.applicationsTable th:hover {
  background-color: #e9ecef;
}

.applicationsTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.applicationRow:hover {
  background-color: #f8f9fa;
}

/* Cell Styles */
.applicantCell {
  min-width: 180px;
}

.applicantInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.applicantName {
  font-weight: 500;
  color: #333;
}

.applicantPhone {
  font-size: 0.8rem;
  color: #666;
}

.typeCell {
  min-width: 100px;
}

.typeBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.typeBadge.artist {
  background-color: #e3f2fd;
  color: #1976d2;
}

.typeBadge.braider {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.experienceCell {
  min-width: 120px;
  color: #666;
  font-size: 0.9rem;
}

.statusCell {
  min-width: 140px;
}

.dateCell {
  min-width: 160px;
  color: #666;
  font-size: 0.9rem;
}

.actionsCell {
  min-width: 200px;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.reviewButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.reviewButton:hover {
  background-color: #0056b3;
}

.reviewButton svg {
  width: 16px;
  height: 16px;
}

.deleteButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.deleteButton:hover {
  background-color: #c82333;
}

.deleteButton svg {
  width: 16px;
  height: 16px;
}

.cancelButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #ffc107;
  color: #212529;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.cancelButton:hover {
  background-color: #e0a800;
}

.cancelButton svg {
  width: 16px;
  height: 16px;
}

/* Results Summary */
.resultsSummary {
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  text-align: center;
}

.resultsSummary p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filtersSection {
    flex-direction: column;
    gap: 12px;
  }

  .filterGroup {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .filterGroup label {
    min-width: 60px;
  }

  .searchInput {
    min-width: auto;
    flex: 1;
  }

  .tableContainer {
    overflow-x: auto;
  }

  .applicationsTable {
    min-width: 600px;
  }

  .applicationsTable th,
  .applicationsTable td {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .filtersSection {
    padding: 12px;
  }

  .applicationsTable {
    min-width: 500px;
  }

  .applicationsTable th,
  .applicationsTable td {
    padding: 6px 8px;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .reviewButton,
  .deleteButton,
  .cancelButton {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}
