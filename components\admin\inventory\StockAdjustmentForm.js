import { useState } from 'react';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/inventory/StockAdjustmentForm.module.css';

/**
 * StockAdjustmentForm component for adjusting product inventory levels
 * 
 * @param {Object} props - Component props
 * @param {Object} props.product - Product data
 * @param {Function} props.onSave - Function to call when adjustment is saved
 * @param {Function} props.onCancel - Function to call when form is cancelled
 * @returns {JSX.Element}
 */
export default function StockAdjustmentForm({ product, onSave, onCancel }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    quantity: '',
    transaction_type: 'restock',
    notes: ''
  });

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle numeric input changes
  const handleNumericChange = (e) => {
    const { name, value } = e.target;
    // Allow empty string or valid integer
    if (value === '' || /^-?\d+$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.quantity) {
      setError('Quantity is required');
      return;
    }
    
    // Convert quantity to number
    const quantity = parseInt(formData.quantity, 10);
    
    // Validate quantity based on transaction type
    if (['sale', 'damaged', 'lost'].includes(formData.transaction_type) && quantity > 0) {
      setError(`${formData.transaction_type} transactions should have a negative quantity`);
      return;
    }
    
    if (['restock', 'return'].includes(formData.transaction_type) && quantity < 0) {
      setError(`${formData.transaction_type} transactions should have a positive quantity`);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Prepare data for API
      const adjustmentData = {
        product_id: product.id,
        quantity,
        transaction_type: formData.transaction_type,
        notes: formData.notes
      };
      
      // Create stock movement
      const response = await fetch('/api/admin/inventory/movements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(adjustmentData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to adjust stock');
      }
      
      const { new_stock } = await response.json();
      
      toast.success(`Stock ${formData.transaction_type} successful. New stock: ${new_stock}`);
      
      // Call onSave callback
      if (onSave) {
        onSave(new_stock);
      }
    } catch (err) {
      console.error('Error adjusting stock:', err);
      setError(err.message || 'Failed to adjust stock');
      toast.error(err.message || 'Failed to adjust stock');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className={styles.adjustmentForm} onSubmit={handleSubmit}>
      <h2>Adjust Stock: {product.name}</h2>
      
      {error && <div className={styles.errorMessage}>{error}</div>}
      
      <div className={styles.formGroup}>
        <label htmlFor="transaction_type">Transaction Type</label>
        <select
          id="transaction_type"
          name="transaction_type"
          value={formData.transaction_type}
          onChange={handleChange}
          className={styles.formControl}
          required
        >
          <option value="restock">Restock (Add Inventory)</option>
          <option value="sale">Sale (Remove Inventory)</option>
          <option value="adjustment">Adjustment</option>
          <option value="return">Return (Add Inventory)</option>
          <option value="damaged">Damaged (Remove Inventory)</option>
          <option value="lost">Lost (Remove Inventory)</option>
        </select>
      </div>
      
      <div className={styles.formGroup}>
        <label htmlFor="quantity">Quantity</label>
        <input
          type="text"
          id="quantity"
          name="quantity"
          value={formData.quantity}
          onChange={handleNumericChange}
          className={styles.formControl}
          required
        />
        <small>
          {['sale', 'damaged', 'lost'].includes(formData.transaction_type)
            ? 'Use negative numbers for removing inventory'
            : formData.transaction_type === 'adjustment'
            ? 'Use positive numbers to add, negative to remove'
            : 'Use positive numbers for adding inventory'}
        </small>
      </div>
      
      <div className={styles.formGroup}>
        <label htmlFor="notes">Notes</label>
        <textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          rows={3}
          className={styles.formControl}
        />
      </div>
      
      <div className={styles.currentStock}>
        <span className={styles.stockLabel}>Current Stock:</span>
        <span className={styles.stockValue}>{product.stock || 0}</span>
      </div>
      
      <div className={styles.formActions}>
        <button 
          type="button" 
          className={styles.cancelButton}
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </button>
        <button 
          type="submit" 
          className={styles.saveButton}
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Adjust Stock'}
        </button>
      </div>
    </form>
  );
}
