/**
 * Admin Health Check API
 * 
 * Simple endpoint to check if admin authentication is working
 * Used by the auto-recovery system to detect authentication issues
 */

import { withAdminAuth } from '@/lib/admin-auth';

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // If we get here, authentication is working (handled by withAdminAuth)
    return res.status(200).json({ 
      health: 'ok',
      user: req.user?.id || 'unknown',
      role: req.role || 'unknown',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return res.status(500).json({ 
      error: 'Health check failed',
      health: 'error',
      message: error.message
    });
  }
}

export default withAdminAuth(handler);
