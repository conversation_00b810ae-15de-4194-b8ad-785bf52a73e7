import { useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from '@/styles/AboutHeroShowcase.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * AboutHeroShowcase component with visual effects and animated content
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Hero section title
 * @param {string} props.subtitle - Hero section subtitle
 * @param {string} props.backgroundImage - URL of the background image
 * @param {Array} props.teamMembers - Array of team member objects with name, role, and image
 * @param {string} props.ctaText - Call to action button text
 * @param {string} props.ctaLink - Call to action button link
 * @returns {JSX.Element}
 */
const AboutHeroShowcase = ({
  title = 'About OceanSoulSparkles',
  subtitle = 'The creative minds behind the magic',
  backgroundImage = '/background.png',
  teamMembers = [],
  ctaText = 'Book With Us',
  ctaLink = '/book-online',
  ...props
}) => {
  const showcaseRef = useRef(null);
  const sparkleContainerRef = useRef(null); // Ref for the new sparkle container

  useEffect(() => {
    const sparkleContainer = sparkleContainerRef.current;
    if (!sparkleContainer) return;

    // Keep track of all timeouts to clear them on unmount
    const timeouts = [];

    const createSparkle = (x, y) => {
      const sparkle = document.createElement('span');
      sparkle.innerHTML = '✨'; // Sparkle emoji or character
      sparkle.className = styles.sparkle;
      sparkle.style.left = `${x}px`;
      sparkle.style.top = `${y}px`;
      sparkle.style.transform = `translate(-50%, -50%) scale(${Math.random() * 0.5 + 0.5})`; // Random initial scale
      sparkle.style.opacity = '1';

      sparkleContainer.appendChild(sparkle);

      // Animate and remove sparkle
      const animationTimeout = setTimeout(() => {
        sparkle.style.transform = `translate(-50%, -50%) scale(0) rotate(${Math.random() * 360}deg)`; // Shrink and rotate
        sparkle.style.opacity = '0';

        const removalTimeout = setTimeout(() => {
          // Check if the sparkle is still a child of the container before removing
          if (sparkle.parentNode === sparkleContainer) {
            sparkleContainer.removeChild(sparkle);
          }
        }, 500); // Remove after fade out

        timeouts.push(removalTimeout);
      }, Math.random() * 1000 + 500); // Lifespan of sparkle

      timeouts.push(animationTimeout);
    };

    let lastSparkleTime = 0;
    const SPARKLE_INTERVAL = 50; // Milliseconds between sparkles

    const handleMouseMove = (e) => {
      if (!showcaseRef.current) return;
      const now = Date.now();
      if (now - lastSparkleTime < SPARKLE_INTERVAL) {
        return;
      }
      lastSparkleTime = now;

      const rect = showcaseRef.current.getBoundingClientRect();
      // Create sparkle at mouse position relative to the showcase container
      createSparkle(e.clientX - rect.left, e.clientY - rect.top);
    };

    // Add parallax effect on scroll (existing code, can be kept or modified)
    const handleScroll = () => {
      if (!showcaseRef.current) return;

      const scrollPosition = window.scrollY;
      const showcaseOffset = showcaseRef.current.offsetTop;
      const scrollRelative = scrollPosition - showcaseOffset;

      if (scrollRelative > -500 && scrollRelative < 500) {
        showcaseRef.current.style.backgroundPositionY = `${50 + (scrollRelative * 0.05)}%`;
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);

      // Clear all timeouts to prevent attempts to modify DOM after unmount
      timeouts.forEach(timeout => clearTimeout(timeout));

      if (sparkleContainer) {
        sparkleContainer.innerHTML = ''; // Clear any remaining sparkles on unmount
      }
    };
  }, []);

  // Default team members if none provided
  const defaultTeamMembers = [
    {
      name: 'Jess',
      role: 'Founder & Creative Director',
      image: '/images/services/face-paint.jpg',
      description: 'Visionary artist with a passion for creating magical experiences'
    },
    {
      name: 'Sarah',
      role: 'Face Painting Specialist',
      image: '/images/about-hero.jpg',
      description: 'Expert in creating stunning character designs for all ages'
    },
    {
      name: 'Michael',
      role: 'Airbrush Artist',
      image: '/images/services/face-paint.jpg',
      description: 'Brings precision and detail to every airbrush creation'
    }
  ];

  const showcaseTeamMembers = teamMembers.length > 0 ? teamMembers : defaultTeamMembers;

  return (
    <section
      ref={showcaseRef}
      className={styles.aboutShowcase}
      style={{ backgroundImage: `url(${backgroundImage})` }}
      {...props}
    >
      <div className={styles.overlayGradient}></div>
      <div ref={sparkleContainerRef} className={styles.sparkleContainer}></div> {/* Add new sparkle container */}

      <div className={styles.showcaseContent}>
        <AnimatedSection animation="fade-in" delay={100}>
          <h1 className={styles.showcaseTitle}>{title}</h1>
          <div className={styles.titleUnderline}></div>
          <p className={styles.showcaseSubtitle}>{subtitle}</p>
        </AnimatedSection>

        <div className={styles.teamShowcase}>
          {showcaseTeamMembers.map((member, index) => (
            <AnimatedSection
              key={index}
              animation="scale-in"
              delay={200 + (index * 150)}
              className={styles.teamMemberCard}
            >
              <div className={styles.memberImageContainer}>
                <div className={styles.memberImageBorder}>
                  <img src={member.image} alt={member.name} className={styles.memberImage} />
                </div>
              </div>
              <div className={styles.memberInfo}>
                <h3 className={styles.memberName}>{member.name}</h3>
                <p className={styles.memberRole}>{member.role}</p>
                <p className={styles.memberDescription}>{member.description}</p>
              </div>
            </AnimatedSection>
          ))}
        </div>

        <AnimatedSection animation="fade-in" delay={500} className={styles.teamNote}>
          <p>
            For larger events, Jess works with a select team of talented artists who share her commitment to
            quality, creativity, and customer service. Each team member is professionally trained and brings their
            own unique style and expertise to OceanSoulSparkles.
          </p>
        </AnimatedSection>

        <AnimatedSection animation="fade-in" delay={700} className={styles.aboutCta}>
          <p className={styles.ctaText}>Ready to experience the magic firsthand?</p>
          <Link href={ctaLink} className="button button--glow">
            {ctaText}
          </Link>
        </AnimatedSection>
      </div>

      <div className={styles.scrollIndicator}>
        <div className={styles.scrollCircle}></div>
        <span>Scroll to learn more</span>
      </div>
    </section>
  );
};

export default AboutHeroShowcase;