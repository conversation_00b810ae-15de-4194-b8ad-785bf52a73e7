#!/usr/bin/env node

/**
 * Test script for the inventory API endpoints
 * This script tests the fix for the 404 error in product updates
 */

// Simple test using curl commands instead of fetch
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:3001/api';
const AUTH_TOKEN = 'dev-bypass-token';

// Test data
const testProduct = {
  name: 'Test Product for API Fix',
  description: 'A test product to verify the API fix for 404 errors',
  price: 29.99,
  sku: 'TEST-API-001',
  stock: 15,
  category: 'test',
  status: 'active',
  featured: false
};

async function makeRequest(url, method = 'GET', data = null) {
  let curlCommand = `curl -s -w "\\nHTTP_STATUS:%{http_code}" -X ${method} "${url}" -H "Content-Type: application/json" -H "Authorization: Bearer ${AUTH_TOKEN}"`;

  if (data && (method === 'POST' || method === 'PUT')) {
    curlCommand += ` -d '${JSON.stringify(data)}'`;
  }

  console.log(`Making ${method} request to: ${url}`);

  try {
    const { stdout, stderr } = await execAsync(curlCommand);

    if (stderr) {
      console.error('Curl stderr:', stderr);
    }

    // Split response and status
    const parts = stdout.split('\nHTTP_STATUS:');
    const responseBody = parts[0];
    const statusCode = parseInt(parts[1]) || 0;

    console.log(`Response status: ${statusCode}`);

    let jsonData = null;
    try {
      jsonData = JSON.parse(responseBody);
      console.log(`Response body (JSON):`, JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log(`Response body (text): ${responseBody}`);
    }

    return { status: statusCode, json: jsonData, text: responseBody };
  } catch (error) {
    console.error(`Request failed:`, error.message);
    return { error: error.message };
  }
}

async function testInventoryAPI() {
  console.log('🧪 Testing Inventory API Endpoints\n');

  let createdProductId = null;

  try {
    // Test 1: Create a product (POST /api/admin/inventory/products)
    console.log('📝 Test 1: Creating a product...');
    const createResponse = await makeRequest(`${API_BASE}/admin/inventory/products`, 'POST', testProduct);

    if (createResponse.status === 201 && createResponse.json?.product?.id) {
      createdProductId = createResponse.json.product.id;
      console.log(`✅ Product created successfully with ID: ${createdProductId}\n`);
    } else {
      console.log(`❌ Failed to create product. Status: ${createResponse.status}\n`);
      return;
    }

    // Test 2: Get the product (GET /api/admin/inventory/products/[id])
    console.log('📖 Test 2: Getting the product by ID...');
    const getResponse = await makeRequest(`${API_BASE}/admin/inventory/products/${createdProductId}`);

    if (getResponse.status === 200 && getResponse.json?.product) {
      console.log(`✅ Product retrieved successfully\n`);
    } else {
      console.log(`❌ Failed to get product. Status: ${getResponse.status}\n`);
    }

    // Test 3: Update the product (PUT /api/admin/inventory/products/[id]) - This is the main test
    console.log('✏️ Test 3: Updating the product (testing the 404 fix)...');
    const updateData = {
      name: 'Updated Test Product',
      description: 'Updated description to test the API fix',
      price: 35.99,
      stock: 20
    };

    const updateResponse = await makeRequest(`${API_BASE}/admin/inventory/products/${createdProductId}`, 'PUT', updateData);

    if (updateResponse.status === 200 && updateResponse.json?.product) {
      console.log(`✅ Product updated successfully! The 404 fix is working.\n`);
    } else {
      console.log(`❌ Failed to update product. Status: ${updateResponse.status}`);
      console.log(`This indicates the 404 issue still exists.\n`);
    }

    // Test 4: Delete the product (DELETE /api/admin/inventory/products/[id])
    console.log('🗑️ Test 4: Deleting the test product...');
    const deleteResponse = await makeRequest(`${API_BASE}/admin/inventory/products/${createdProductId}`, 'DELETE');

    if (deleteResponse.status === 200) {
      console.log(`✅ Product deleted successfully\n`);
    } else {
      console.log(`❌ Failed to delete product. Status: ${deleteResponse.status}\n`);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }

  console.log('🏁 Test completed!');
}

// Run the test
testInventoryAPI().catch(console.error);
