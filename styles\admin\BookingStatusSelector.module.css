.statusSelector {
  margin-bottom: 20px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
}

.closeButton {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 1.2rem;
  line-height: 1;
  cursor: pointer;
  color: #721c24;
}

.statusButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.statusButton {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  background-color: transparent;
  border: 2px solid;
  transition: all 0.2s ease;
}

.statusButton:hover:not(:disabled) {
  filter: brightness(0.9);
}

.statusButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.statusButton.active {
  cursor: default;
}

.confirmationDialog {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  border: 1px solid #dee2e6;
}

.confirmationTitle {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.1rem;
  color: #333;
}

.notesField {
  margin-bottom: 16px;
}

.notesField label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #495057;
}

.notesInput {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  resize: vertical;
}

.confirmationActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancelButton,
.confirmButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
}

.cancelButton:hover {
  background-color: #e9ecef;
}

.confirmButton {
  background-color: #6a0dad;
  color: white;
}

.confirmButton:hover {
  filter: brightness(0.9);
}
