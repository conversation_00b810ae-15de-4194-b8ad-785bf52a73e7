/**
 * Booking status constants and utilities
 */

// Booking status constants
export const BOOKING_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELED: 'canceled',
  NO_SHOW: 'no_show',
  RESCHEDULED: 'rescheduled'
};

// Status display names for UI
export const STATUS_DISPLAY_NAMES = {
  [BOOKING_STATUSES.PENDING]: 'Pending',
  [BOOKING_STATUSES.CONFIRMED]: 'Confirmed',
  [BOOKING_STATUSES.IN_PROGRESS]: 'In Progress',
  [BOOKING_STATUSES.COMPLETED]: 'Completed',
  [BOOKING_STATUSES.CANCELED]: 'Canceled',
  [BOOKING_STATUSES.NO_SHOW]: 'No Show',
  [BOOKING_STATUSES.RESCHEDULED]: 'Rescheduled'
};

// Status colors for UI
export const STATUS_COLORS = {
  [BOOKING_STATUSES.PENDING]: '#f39c12', // Orange
  [BOOKING_STATUSES.CONFIRMED]: '#2ecc71', // Green
  [BOOKING_STATUSES.IN_PROGRESS]: '#3498db', // Blue
  [BOOKING_STATUSES.COMPLETED]: '#27ae60', // Dark Green
  [BOOKING_STATUSES.CANCELED]: '#e74c3c', // Red
  [BOOKING_STATUSES.NO_SHOW]: '#c0392b', // Dark Red
  [BOOKING_STATUSES.RESCHEDULED]: '#9b59b6' // Purple
};

// Valid status transitions
export const STATUS_TRANSITIONS = {
  [BOOKING_STATUSES.PENDING]: [
    BOOKING_STATUSES.CONFIRMED, 
    BOOKING_STATUSES.CANCELED
  ],
  [BOOKING_STATUSES.CONFIRMED]: [
    BOOKING_STATUSES.IN_PROGRESS, 
    BOOKING_STATUSES.CANCELED, 
    BOOKING_STATUSES.NO_SHOW, 
    BOOKING_STATUSES.RESCHEDULED
  ],
  [BOOKING_STATUSES.IN_PROGRESS]: [
    BOOKING_STATUSES.COMPLETED, 
    BOOKING_STATUSES.CANCELED
  ],
  [BOOKING_STATUSES.COMPLETED]: [],
  [BOOKING_STATUSES.CANCELED]: [
    BOOKING_STATUSES.PENDING, 
    BOOKING_STATUSES.CONFIRMED
  ],
  [BOOKING_STATUSES.NO_SHOW]: [
    BOOKING_STATUSES.RESCHEDULED
  ],
  [BOOKING_STATUSES.RESCHEDULED]: [
    BOOKING_STATUSES.CONFIRMED, 
    BOOKING_STATUSES.CANCELED
  ]
};

/**
 * Check if a status transition is valid
 * @param {string} currentStatus - Current booking status
 * @param {string} newStatus - New booking status
 * @returns {boolean} - Whether the transition is valid
 */
export const isValidStatusTransition = (currentStatus, newStatus) => {
  try {
    // Validate input parameters
    if (!currentStatus || !newStatus) {
      console.error('Invalid status values:', { currentStatus, newStatus });
      return false;
    }

    // If current status doesn't exist in our definitions, log and allow transition
    if (!STATUS_TRANSITIONS[currentStatus]) {
      console.warn(`No transition rules defined for status: ${currentStatus}`);
      return true;
    }

    // Check if new status is in the allowed transitions
    const isValid = STATUS_TRANSITIONS[currentStatus].includes(newStatus);
    
    if (!isValid) {
      console.warn(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
    
    return isValid;
  } catch (error) {
    console.error('Status validation error:', error);
    return false;
  }
};

/**
 * Get available status options based on current status
 * @param {string} currentStatus - Current booking status
 * @returns {Array} - Array of available status options
 */
export const getAvailableStatusOptions = (currentStatus) => {
  // If current status doesn't exist in our definitions, return all statuses
  if (!STATUS_TRANSITIONS[currentStatus]) {
    return Object.values(BOOKING_STATUSES);
  }
  
  // Return current status plus allowed transitions
  return [currentStatus, ...STATUS_TRANSITIONS[currentStatus]];
};
