.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: scaleIn 0.3s ease;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.closeButton:hover {
  background-color: #f0f0f0;
  transform: rotate(90deg);
}

.modalContent {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.productImage {
  flex: 1;
  position: relative;
  background-color: #f9f9f9;
}

.productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.productDetails {
  flex: 1;
  padding: 3rem 2rem;
  overflow-y: auto;
  max-height: 90vh;
}

.productName {
  font-size: 1.8rem;
  margin: 0 0 1rem;
  color: #333;
}

.productPrice {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1A73E8;
  margin-bottom: 1.5rem;
}

.productDescription {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1rem;
  line-height: 1.6;
}

.productFeatures, .productDetails {
  margin-bottom: 2rem;
}

.productFeatures h3, .productDetails h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
}

.productFeatures ul, .productDetails ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.productFeatures li, .productDetails li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #666;
}

.productFeatures li:before, .productDetails li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1A73E8;
}

.addToCartButton {
  background-color: #1A73E8;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
}

.addToCartButton:hover {
  background-color: #1557b0;
}

.productBadge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: #FF6B6B;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .modalContent {
    flex-direction: column;
  }
  
  .productImage {
    height: 300px;
  }
  
  .productDetails {
    padding: 2rem 1.5rem;
  }
  
  .productName {
    font-size: 1.5rem;
  }
  
  .productPrice {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .modal {
    width: 95%;
  }
  
  .productImage {
    height: 250px;
  }
  
  .productDetails {
    padding: 1.5rem 1rem;
  }
  
  .productName {
    font-size: 1.3rem;
  }
  
  .closeButton {
    top: 0.5rem;
    right: 0.5rem;
    width: 35px;
    height: 35px;
  }
}
