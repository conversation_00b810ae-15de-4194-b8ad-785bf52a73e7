import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import styles from '@/styles/admin/BookingFilters.module.css';

export default function BookingFilters({ 
  onFiltersChange, 
  customers = [], 
  services = [],
  initialFilters = {} 
}) {
  const router = useRouter();
  
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    service: 'all',
    customer: 'all',
    dateRange: 'this_week',
    customStartDate: '',
    customEndDate: '',
    location: '',
    ...initialFilters
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const dateRangePresets = [
    { value: 'today', label: 'Today' },
    { value: 'tomorrow', label: 'Tomorrow' },
    { value: 'this_week', label: 'This Week' },
    { value: 'next_week', label: 'Next Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'next_month', label: 'Next Month' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'canceled', label: 'Canceled' },
    { value: 'no_show', label: 'No Show' }
  ];

  // Load filters from URL on component mount
  useEffect(() => {
    const urlFilters = {};
    Object.keys(filters).forEach(key => {
      if (router.query[key]) {
        urlFilters[key] = router.query[key];
      }
    });
    
    if (Object.keys(urlFilters).length > 0) {
      setFilters(prev => ({ ...prev, ...urlFilters }));
    }
  }, [router.query]);

  // Update URL when filters change
  useEffect(() => {
    const query = { ...router.query };
    
    // Add non-default filters to URL
    Object.keys(filters).forEach(key => {
      if (filters[key] && filters[key] !== '' && filters[key] !== 'all' && filters[key] !== 'this_week') {
        query[key] = filters[key];
      } else {
        delete query[key];
      }
    });

    router.push({
      pathname: router.pathname,
      query
    }, undefined, { shallow: true });
  }, [filters]);

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Calculate date range for presets
    const processedFilters = processDateRange(newFilters);
    onFiltersChange(processedFilters);
  };

  const processDateRange = (filterData) => {
    const processed = { ...filterData };
    const now = new Date();
    
    switch (filterData.dateRange) {
      case 'today':
        processed.startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
        processed.endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString();
        break;
      case 'tomorrow':
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        processed.startDate = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate()).toISOString();
        processed.endDate = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate() + 1).toISOString();
        break;
      case 'this_week':
        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
        const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 6));
        processed.startDate = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate()).toISOString();
        processed.endDate = new Date(endOfWeek.getFullYear(), endOfWeek.getMonth(), endOfWeek.getDate() + 1).toISOString();
        break;
      case 'next_week':
        const nextWeekStart = new Date(now.setDate(now.getDate() - now.getDay() + 7));
        const nextWeekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 13));
        processed.startDate = new Date(nextWeekStart.getFullYear(), nextWeekStart.getMonth(), nextWeekStart.getDate()).toISOString();
        processed.endDate = new Date(nextWeekEnd.getFullYear(), nextWeekEnd.getMonth(), nextWeekEnd.getDate() + 1).toISOString();
        break;
      case 'this_month':
        processed.startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
        processed.endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString();
        break;
      case 'next_month':
        processed.startDate = new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString();
        processed.endDate = new Date(now.getFullYear(), now.getMonth() + 2, 1).toISOString();
        break;
      case 'custom':
        if (filterData.customStartDate) {
          processed.startDate = new Date(filterData.customStartDate).toISOString();
        }
        if (filterData.customEndDate) {
          processed.endDate = new Date(filterData.customEndDate + 'T23:59:59').toISOString();
        }
        break;
    }
    
    return processed;
  };

  const clearFilters = () => {
    const defaultFilters = {
      search: '',
      status: 'all',
      service: 'all',
      customer: 'all',
      dateRange: 'this_week',
      customStartDate: '',
      customEndDate: '',
      location: ''
    };
    setFilters(defaultFilters);
    onFiltersChange(processDateRange(defaultFilters));
  };

  const hasActiveFilters = () => {
    return filters.search !== '' || 
           filters.status !== 'all' || 
           filters.service !== 'all' || 
           filters.customer !== 'all' || 
           filters.location !== '' ||
           filters.dateRange !== 'this_week';
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search !== '') count++;
    if (filters.status !== 'all') count++;
    if (filters.service !== 'all') count++;
    if (filters.customer !== 'all') count++;
    if (filters.location !== '') count++;
    if (filters.dateRange !== 'this_week') count++;
    return count;
  };

  const renderActiveFilters = () => {
    const activeFilters = [];
    
    if (filters.search) {
      activeFilters.push(
        <span key="search" className={styles.activeFilter}>
          Search: "{filters.search}"
          <button onClick={() => handleFilterChange('search', '')}>×</button>
        </span>
      );
    }
    
    if (filters.status !== 'all') {
      const statusLabel = statusOptions.find(s => s.value === filters.status)?.label;
      activeFilters.push(
        <span key="status" className={styles.activeFilter}>
          Status: {statusLabel}
          <button onClick={() => handleFilterChange('status', 'all')}>×</button>
        </span>
      );
    }
    
    if (filters.service !== 'all') {
      const serviceLabel = services.find(s => s.id === filters.service)?.name;
      activeFilters.push(
        <span key="service" className={styles.activeFilter}>
          Service: {serviceLabel}
          <button onClick={() => handleFilterChange('service', 'all')}>×</button>
        </span>
      );
    }
    
    if (filters.dateRange !== 'this_week') {
      const dateLabel = dateRangePresets.find(d => d.value === filters.dateRange)?.label;
      activeFilters.push(
        <span key="dateRange" className={styles.activeFilter}>
          Date: {dateLabel}
          <button onClick={() => handleFilterChange('dateRange', 'this_week')}>×</button>
        </span>
      );
    }
    
    return activeFilters;
  };

  return (
    <div className={styles.filtersContainer}>
      <div className={styles.filtersHeader}>
        <div className={styles.filtersTitle}>
          <h3>Booking Filters</h3>
          {hasActiveFilters() && (
            <span className={styles.activeCount}>
              {getActiveFilterCount()} active
            </span>
          )}
        </div>
        <div className={styles.filtersActions}>
          <button
            className={styles.toggleButton}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Hide Filters' : 'Show Filters'}
          </button>
          {hasActiveFilters() && (
            <button
              className={styles.clearButton}
              onClick={clearFilters}
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className={styles.filtersContent}>
          <div className={styles.filtersRow}>
            {/* Search Input */}
            <div className={styles.filterGroup}>
              <label>Search</label>
              <input
                type="text"
                placeholder="Customer name, email, or booking ID..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className={styles.searchInput}
              />
            </div>

            {/* Status Filter */}
            <div className={styles.filterGroup}>
              <label>Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className={styles.select}
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Service Filter */}
            <div className={styles.filterGroup}>
              <label>Service</label>
              <select
                value={filters.service}
                onChange={(e) => handleFilterChange('service', e.target.value)}
                className={styles.select}
              >
                <option value="all">All Services</option>
                {services.map(service => (
                  <option key={service.id} value={service.id}>
                    {service.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range Filter */}
            <div className={styles.filterGroup}>
              <label>Date Range</label>
              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className={styles.select}
              >
                {dateRangePresets.map(preset => (
                  <option key={preset.value} value={preset.value}>
                    {preset.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Custom Date Range (shown when 'custom' is selected) */}
          {filters.dateRange === 'custom' && (
            <div className={styles.customDateRow}>
              <div className={styles.filterGroup}>
                <label>Start Date</label>
                <input
                  type="date"
                  value={filters.customStartDate}
                  onChange={(e) => handleFilterChange('customStartDate', e.target.value)}
                  className={styles.dateInput}
                />
              </div>
              <div className={styles.filterGroup}>
                <label>End Date</label>
                <input
                  type="date"
                  value={filters.customEndDate}
                  onChange={(e) => handleFilterChange('customEndDate', e.target.value)}
                  className={styles.dateInput}
                />
              </div>
            </div>
          )}

          {/* Location Filter */}
          <div className={styles.filtersRow}>
            <div className={styles.filterGroup}>
              <label>Location</label>
              <input
                type="text"
                placeholder="Filter by location..."
                value={filters.location}
                onChange={(e) => handleFilterChange('location', e.target.value)}
                className={styles.textInput}
              />
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters() && (
            <div className={styles.activeFilters}>
              <span className={styles.activeFiltersLabel}>Active Filters:</span>
              <div className={styles.activeFiltersList}>
                {renderActiveFilters()}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
