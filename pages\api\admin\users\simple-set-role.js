import { getAdminClient, getCurrentUserFromRequest } from '@/lib/supabase'

export default async function handler(req, res) {
  // Set proper content type
  res.setHeader('Content-Type', 'application/json');

  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Simple set-role endpoint called: ${req.method}`);

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Log request details
    console.log(`[${requestId}] Request body:`, JSON.stringify(req.body, null, 2));

    // Extract data from request body
    const { userId, role: newRole } = req.body || {};
    
    console.log(`[${requestId}] Extracted - userId: "${userId}", role: "${newRole}"`);

    // Validate required fields
    if (!userId || !newRole) {
      console.error(`[${requestId}] Missing required fields - userId: ${!!userId}, role: ${!!newRole}`);
      return res.status(400).json({ 
        error: 'User ID and role are required',
        provided: { userId: !!userId, role: !!newRole },
        requestId 
      })
    }    // Validate role
    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user']
    console.log(`[${requestId}] Validating role "${newRole}" against:`, validRoles);
    
    if (!validRoles.includes(newRole)) {
      console.error(`[${requestId}] Invalid role: "${newRole}" (type: ${typeof newRole})`);
      return res.status(400).json({ 
        error: 'Invalid role. Must be one of: dev, admin, artist, braider, user',
        provided: newRole,
        providedType: typeof newRole,
        validRoles,
        requestId 
      })
    }

    console.log(`[${requestId}] Role validation passed`);

    // Try authentication using the fixed getCurrentUserFromRequest
    let authUser, authRole;
    try {
      const authResult = await getCurrentUserFromRequest(req);
      authUser = authResult.user;
      authRole = authResult.role;
      console.log(`[${requestId}] Authentication successful - User: ${authUser?.email}, Role: ${authRole}`);
    } catch (authError) {
      console.error(`[${requestId}] Authentication failed:`, authError.message);
      return res.status(401).json({
        error: 'Authentication failed',
        message: authError.message,
        requestId
      });
    }

    // Check if user has admin permissions
    if (!authUser || authRole !== 'admin') {
      console.error(`[${requestId}] Insufficient permissions - Role: ${authRole}`);
      return res.status(403).json({ 
        error: 'Unauthorized. Only administrators can update user roles.',
        userRole: authRole,
        requestId 
      })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      console.error(`[${requestId}] Failed to get admin client`);
      return res.status(500).json({ error: 'Failed to initialize admin client', requestId })
    }

    console.log(`[${requestId}] Admin client initialized successfully`);

    // Check if target user exists
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)
    if (userError || !userData || !userData.user) {
      console.error(`[${requestId}] Target user not found:`, userError?.message);
      return res.status(404).json({ 
        error: 'User not found',
        userId,
        requestId 
      })
    }

    console.log(`[${requestId}] Target user found: ${userData.user.email}`);

    // Check if user already has a role
    const { data: existingRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('*')
      .eq('id', userId)
      .single()

    console.log(`[${requestId}] Existing role check - Found: ${!!existingRole}, Error: ${roleError?.message}`);

    let result
    if (existingRole) {
      // Update existing role
      console.log(`[${requestId}] Updating existing role from "${existingRole.role}" to "${newRole}"`);
      result = await adminClient
        .from('user_roles')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('id', userId)
    } else {
      // Insert new role
      console.log(`[${requestId}] Inserting new role "${newRole}" for user`);
      result = await adminClient
        .from('user_roles')
        .insert([{ id: userId, role: newRole }])
    }

    if (result.error) {
      console.error(`[${requestId}] Database operation failed:`, result.error);
      return res.status(500).json({
        error: 'Failed to set user role',
        message: result.error.message,
        requestId
      })
    }

    console.log(`[${requestId}] Role update successful`);

    // Return success response
    return res.status(200).json({
      success: true,
      message: `Role set to ${newRole} for user ${userId}`,
      user: {
        id: userId,
        email: userData.user.email,
        role: newRole
      },
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({
      error: 'An unexpected error occurred',
      message: error.message,
      requestId
    })
  }
}
