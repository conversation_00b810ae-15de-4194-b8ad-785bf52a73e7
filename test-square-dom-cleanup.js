#!/usr/bin/env node

/**
 * Square Payment DOM Cleanup Test Script
 * Tests the React DOM manipulation fixes for Square payment component
 */

import { launch } from 'puppeteer';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 30000;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logResult(test, status, details = '') {
  const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
  const color = status === 'pass' ? 'green' : status === 'fail' ? 'red' : 'yellow';
  log(`${icon} ${test}: ${details}`, color);
}

// Test Square component mounting and unmounting
async function testSquareComponentLifecycle() {
  log('\n🧪 Testing Square Component Lifecycle...', 'blue');
  
  let browser;
  try {
    browser = await launch({ 
      headless: false, // Set to true for CI/automated testing
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    const consoleMessages = [];
    const errorMessages = [];
    
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        timestamp: Date.now()
      });
    });
    
    page.on('pageerror', error => {
      errorMessages.push({
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
    });
    
    // Navigate to POS page
    log('Navigating to POS page...', 'blue');
    await page.goto(`${BASE_URL}/admin/pos`, { waitUntil: 'networkidle0' });
    
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    // Check for initial page load errors
    const initialErrors = errorMessages.filter(err => 
      err.message.includes('NotFoundError') || 
      err.message.includes('removeChild')
    );
    
    if (initialErrors.length === 0) {
      logResult('Initial Page Load', 'pass', 'No DOM manipulation errors on load');
    } else {
      logResult('Initial Page Load', 'fail', `${initialErrors.length} DOM errors found`);
      initialErrors.forEach(err => console.log(`  Error: ${err.message}`));
    }
    
    // Test component mounting by selecting a service
    log('Testing component mounting...', 'blue');
    
    // Wait for services to load and select one
    await page.waitForSelector('[data-testid="service-card"], .service-card, .pos-service-item', { timeout: 10000 });
    const serviceCards = await page.$$('[data-testid="service-card"], .service-card, .pos-service-item');
    
    if (serviceCards.length > 0) {
      await serviceCards[0].click();
      logResult('Service Selection', 'pass', 'Service selected successfully');
      
      // Wait for tier selection
      await page.waitForTimeout(1000);
      
      // Select a tier if available
      const tierButtons = await page.$$('[data-testid="tier-button"], .tier-button, .pos-tier-item');
      if (tierButtons.length > 0) {
        await tierButtons[0].click();
        logResult('Tier Selection', 'pass', 'Tier selected successfully');
        
        // Wait for customer form
        await page.waitForTimeout(1000);
        
        // Skip customer info or fill minimal info
        const skipButton = await page.$('[data-testid="skip-customer"], .skip-button');
        const nextButton = await page.$('[data-testid="next-button"], .next-button, button[type="submit"]');
        
        if (skipButton) {
          await skipButton.click();
        } else if (nextButton) {
          await nextButton.click();
        }
        
        logResult('Customer Step', 'pass', 'Customer step completed');
        
        // Wait for payment method selection
        await page.waitForTimeout(1000);
        
        // Select card payment
        const cardPaymentButton = await page.$('[data-testid="card-payment"], .card-payment-button, button:contains("Card")');
        if (cardPaymentButton) {
          await cardPaymentButton.click();
          logResult('Payment Method Selection', 'pass', 'Card payment selected');
          
          // Wait for Square component to mount
          await page.waitForTimeout(3000);
          
          // Check for Square container
          const squareContainer = await page.$('[data-square-container="true"], #square-card-container, [id*="square-card"]');
          if (squareContainer) {
            logResult('Square Component Mount', 'pass', 'Square payment form mounted');
            
            // Test component unmounting by going back
            log('Testing component unmounting...', 'blue');
            
            const backButton = await page.$('[data-testid="back-button"], .back-button, button:contains("Back")');
            if (backButton) {
              await backButton.click();
              
              // Wait for unmounting
              await page.waitForTimeout(2000);
              
              // Check for DOM errors during unmounting
              const unmountErrors = errorMessages.filter(err => 
                err.timestamp > Date.now() - 5000 && (
                  err.message.includes('NotFoundError') || 
                  err.message.includes('removeChild') ||
                  err.message.includes('Node')
                )
              );
              
              if (unmountErrors.length === 0) {
                logResult('Component Unmount', 'pass', 'No DOM errors during unmount');
              } else {
                logResult('Component Unmount', 'fail', `${unmountErrors.length} DOM errors during unmount`);
                unmountErrors.forEach(err => console.log(`  Error: ${err.message}`));
              }
              
              // Test remounting
              log('Testing component remounting...', 'blue');
              
              // Go through the flow again
              await cardPaymentButton.click();
              await page.waitForTimeout(3000);
              
              const remountedContainer = await page.$('[data-square-container="true"], #square-card-container, [id*="square-card"]');
              if (remountedContainer) {
                logResult('Component Remount', 'pass', 'Square component remounted successfully');
              } else {
                logResult('Component Remount', 'fail', 'Square component failed to remount');
              }
              
            } else {
              logResult('Back Button', 'warn', 'Back button not found, cannot test unmounting');
            }
          } else {
            logResult('Square Component Mount', 'fail', 'Square payment form not found');
          }
        } else {
          logResult('Payment Method Selection', 'fail', 'Card payment button not found');
        }
      } else {
        logResult('Tier Selection', 'warn', 'No tiers available');
      }
    } else {
      logResult('Service Selection', 'fail', 'No services found');
    }
    
    // Final error check
    const finalErrors = errorMessages.filter(err => 
      err.message.includes('NotFoundError') || 
      err.message.includes('removeChild')
    );
    
    log('\n📊 Test Summary', 'bold');
    log('================', 'blue');
    
    if (finalErrors.length === 0) {
      log('🎉 All DOM manipulation tests passed! No React conflicts detected.', 'green');
    } else {
      log(`⚠️ ${finalErrors.length} DOM manipulation errors detected:`, 'yellow');
      finalErrors.forEach(err => log(`  - ${err.message}`, 'red'));
    }
    
    // Log relevant console messages
    const squareMessages = consoleMessages.filter(msg => 
      msg.text.includes('Square') || 
      msg.text.includes('Cleanup') ||
      msg.text.includes('DOM')
    );
    
    if (squareMessages.length > 0) {
      log('\n📝 Relevant Console Messages:', 'blue');
      squareMessages.slice(-10).forEach(msg => {
        const color = msg.type === 'error' ? 'red' : msg.type === 'warning' ? 'yellow' : 'reset';
        log(`  [${msg.type}] ${msg.text}`, color);
      });
    }
    
  } catch (error) {
    logResult('Test Execution', 'fail', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Main test runner
async function runDOMTests() {
  log('🧪 Square Payment DOM Cleanup Test Suite', 'bold');
  log('==========================================', 'blue');
  
  // Check if development server is running
  try {
    const response = await fetch(BASE_URL);
    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }
    logResult('Development Server', 'pass', 'Server is running');
  } catch (error) {
    logResult('Development Server', 'fail', 'Server not accessible. Please run: npm run dev');
    return;
  }
  
  await testSquareComponentLifecycle();
  
  log('\n💡 Next Steps:', 'blue');
  log('1. If tests pass, the DOM cleanup fix is working correctly');
  log('2. If tests fail, check browser console for specific error details');
  log('3. Monitor the component in development for any remaining issues');
}

// Run the tests
runDOMTests().catch(console.error);
