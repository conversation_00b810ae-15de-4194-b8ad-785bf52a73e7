// Test script for the users list API endpoint
const fetch = require('node-fetch');

async function testUsersAPI() {
  console.log('Testing /api/admin/users/list endpoint...');
  
  try {
    const response = await fetch('http://localhost:3000/api/admin/users/list', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // In development mode, we can test without authentication first
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const data = await response.text();
    console.log('Response body:', data);
    
    // Try to parse as <PERSON><PERSON><PERSON>
    try {
      const jsonData = JSON.parse(data);
      console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));
    } catch (parseError) {
      console.log('Response is not valid JSON');
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testUsersAPI();
