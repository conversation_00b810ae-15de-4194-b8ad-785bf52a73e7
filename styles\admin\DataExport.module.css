.dataExport {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 30px;
}

.title {
  font-size: 1.25rem;
  color: #333;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.exportForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.formGroup {
  margin-bottom: 16px;
}

.label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 0.95rem;
}

.select,
.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
  color: #495057;
  background-color: #fff;
  transition: border-color 0.2s;
}

.select:focus,
.input:focus {
  outline: none;
  border-color: #6a0dad;
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
}

.dateRangeInputs {
  grid-column: span 2;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.formActions {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.exportButton {
  padding: 10px 20px;
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.exportButton:hover {
  background-color: #5a0b9d;
}

.exportButton:disabled {
  background-color: #9c6fb6;
  cursor: not-allowed;
}

.securityNote {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  padding: 16px;
  border-radius: 4px;
  margin-top: 24px;
}

.securityTitle {
  font-size: 1rem;
  color: #856404;
  margin-top: 0;
  margin-bottom: 8px;
}

.securityText {
  font-size: 0.9rem;
  color: #856404;
  margin: 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .exportForm {
    grid-template-columns: 1fr;
  }
  
  .dateRangeInputs {
    grid-column: span 1;
    grid-template-columns: 1fr;
  }
  
  .formActions {
    grid-column: span 1;
  }
}
