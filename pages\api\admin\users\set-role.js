import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

export default async function handler(req, res) {
  // Set proper content type to avoid 406 errors
  res.setHeader('Content-Type', 'application/json');

  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] User set-role API endpoint called: ${req.method}`);

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Log request headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));

  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }

  if (req.headers['x-auth-token']) {
    console.log(`[${requestId}] x-auth-token header present`);
  }

  try {
    // Log the raw request body for debugging
    console.log(`[${requestId}] Raw request body:`, JSON.stringify(req.body, null, 2));
    console.log(`[${requestId}] Request body type:`, typeof req.body);

    // Get userId and role from request body FIRST to debug
    const { userId, role: newRole } = req.body || {};

    console.log(`[${requestId}] Extracted values - userId: "${userId}", newRole: "${newRole}"`);
    console.log(`[${requestId}] userId type: ${typeof userId}, newRole type: ${typeof newRole}`);

    // Validate required fields with detailed logging
    if (!userId) {
      console.error(`[${requestId}] Missing userId in request body`);
      return res.status(400).json({ error: 'User ID is required', requestId })
    }

    if (!newRole) {
      console.error(`[${requestId}] Missing role in request body`);
      return res.status(400).json({ error: 'Role is required', requestId })
    }    // Validate role with detailed logging
    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user']
    console.log(`[${requestId}] Validating role "${newRole}" against valid roles:`, validRoles);
    console.log(`[${requestId}] Role validation check: validRoles.includes("${newRole}") = ${validRoles.includes(newRole)}`);

    if (!validRoles.includes(newRole)) {
      console.error(`[${requestId}] Invalid role provided: "${newRole}" (type: ${typeof newRole})`);
      console.error(`[${requestId}] Valid roles are:`, validRoles);
      return res.status(400).json({
        error: 'Invalid role. Must be one of: dev, admin, artist, braider, user',
        provided: newRole,
        providedType: typeof newRole,
        validRoles: validRoles,
        requestId
      })
    }

    console.log(`[${requestId}] Role validation passed for: "${newRole}"`);

    // Authenticate request using our robust auth module
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error')
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed',
        requestId
      })
    }

    console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`)
    if (!user || role !== 'admin') {
      return res.status(403).json({ error: 'Unauthorized. Only administrators can update user roles.' })
    }

    // Get admin client to bypass RLS policies
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Check if user exists
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)
    if (userError || !userData || !userData.user) {
      return res.status(404).json({ error: 'User not found' })
    }

    // Check if user already has a role
    const { data: existingRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('*')
      .eq('id', userId)
      .single()

    let result

    if (existingRole) {
      // Update existing role
      result = await adminClient
        .from('user_roles')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('id', userId)
    } else {
      // Insert new role
      result = await adminClient
        .from('user_roles')
        .insert([{ id: userId, role: newRole }])
    }

    if (result.error) {
      console.error(`[${requestId}] Error setting user role:`, result.error)
      return res.status(500).json({
        error: 'Failed to set user role',
        message: result.error.message,
        requestId
      })
    }

    console.log(`[${requestId}] Successfully set role to ${newRole} for user ${userId}`)

    // Return success response
    return res.status(200).json({
      success: true,
      message: `Role set to ${newRole} for user ${userId}`,
      user: {
        id: userId,
        email: userData.user.email,
        role: newRole
      },
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Unexpected error setting user role:`, error)
    return res.status(500).json({
      error: 'An unexpected error occurred',
      message: error.message,
      requestId
    })
  }
}
