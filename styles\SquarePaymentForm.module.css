.squarePaymentContainer {
  width: 100%;
  margin-bottom: 1.5rem;
}

.paymentForm {
  width: 100%;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.cardContainer {
  height: 100px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 1rem;
  background-color: #fff;
}

.payButton {
  width: 100%;
  background-color: #006AFF;
  color: white;
  border: none;
  padding: 0.8rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.payButton:hover {
  background-color: #0055CC;
}

.payButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 106, 255, 0.2);
  border-radius: 50%;
  border-top-color: #006AFF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.securePaymentNote {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #4CAF50;
  margin-top: 1rem;
}

.securePaymentNote svg {
  margin-right: 0.5rem;
}
