/**
 * Authentication Test Utilities
 *
 * This file contains utility functions for testing authentication in the Ocean Soul Sparkles admin panel.
 * It provides methods for making authenticated API requests, testing token validation, and simulating
 * various authentication scenarios.
 */

// Import required dependencies
import fetch from 'node-fetch';

/**
 * Make an authenticated API request
 * @param {string} url - The API endpoint URL
 * @param {string} token - The authentication token
 * @param {string} method - The HTTP method (GET, POST, etc.)
 * @param {Object} body - The request body (for POST, PUT, etc.)
 * @returns {Promise<Object>} - The API response
 */
export const makeAuthenticatedRequest = async (url, token, method = 'GET', body = null) => {
  try {
    // Check if this is a mock token (for testing without a real API)
    if (token && token.includes('mock-signature-for-testing-purposes-only')) {
      console.log(`MOCK REQUEST: ${method} ${url}`);

      // Simulate different responses based on the URL and token
      if (url.includes('/api/admin/diagnostics/auth-check')) {
        return mockAuthCheckResponse(token);
      } else if (url.includes('/api/admin/customers')) {
        return mockCustomersResponse(token);
      } else if (url.includes('/api/admin/settings')) {
        return mockSettingsResponse(token);
      } else {
        return mockGenericResponse(url, token);
      }
    }

    // Real API request
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    const options = {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined
    };

    const response = await fetch(url, options);
    const data = await response.json();

    return {
      status: response.status,
      data,
      headers: response.headers
    };
  } catch (error) {
    console.error('Error making authenticated request:', error);
    return {
      status: 500,
      data: { error: error.message },
      headers: {}
    };
  }
};

/**
 * Mock response for auth check endpoint
 * @param {string} token - The authentication token
 * @returns {Object} - Mocked response
 */
const mockAuthCheckResponse = (token) => {
  if (!token) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Missing or invalid Authorization header' },
      headers: {}
    };
  }

  if (token.includes('expired')) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Token has expired' },
      headers: {}
    };
  }

  return {
    status: 200,
    data: {
      user: {
        id: 'mock-user-id',
        email: '<EMAIL>',
        created_at: new Date().toISOString()
      },
      role: 'admin',
      authorized: true
    },
    headers: {}
  };
};

/**
 * Mock response for customers endpoint
 * @param {string} token - The authentication token
 * @returns {Object} - Mocked response
 */
const mockCustomersResponse = (token) => {
  if (!token) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Missing or invalid Authorization header' },
      headers: {}
    };
  }

  if (token.includes('expired')) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Token has expired' },
      headers: {}
    };
  }

  return {
    status: 200,
    data: [
      {
        id: 'mock-customer-1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '0412345678',
        created_at: new Date().toISOString()
      },
      {
        id: 'mock-customer-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '0487654321',
        created_at: new Date().toISOString()
      }
    ],
    headers: {}
  };
};

/**
 * Mock response for settings endpoint
 * @param {string} token - The authentication token
 * @returns {Object} - Mocked response
 */
const mockSettingsResponse = (token) => {
  if (!token) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Missing or invalid Authorization header' },
      headers: {}
    };
  }

  if (token.includes('expired')) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Token has expired' },
      headers: {}
    };
  }

  return {
    status: 200,
    data: {
      site_name: 'Ocean Soul Sparkles',
      contact_email: '<EMAIL>',
      booking_enabled: true,
      maintenance_mode: false,
      updated_at: new Date().toISOString()
    },
    headers: {}
  };
};

/**
 * Mock response for generic endpoints
 * @param {string} url - The API endpoint URL
 * @param {string} token - The authentication token
 * @returns {Object} - Mocked response
 */
const mockGenericResponse = (url, token) => {
  if (!token) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Missing or invalid Authorization header' },
      headers: {}
    };
  }

  if (token.includes('expired')) {
    return {
      status: 401,
      data: { error: 'Unauthorized', message: 'Token has expired' },
      headers: {}
    };
  }

  return {
    status: 200,
    data: {
      message: `Mock response for ${url}`,
      timestamp: new Date().toISOString()
    },
    headers: {}
  };
};

/**
 * Make an API request with x-auth-token header
 * @param {string} url - The API endpoint URL
 * @param {string} token - The authentication token
 * @param {string} method - The HTTP method (GET, POST, etc.)
 * @param {Object} body - The request body (for POST, PUT, etc.)
 * @returns {Promise<Object>} - The API response
 */
export const makeXAuthTokenRequest = async (url, token, method = 'GET', body = null) => {
  try {
    const headers = {
      'Content-Type': 'application/json',
      'x-auth-token': token
    };

    const options = {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined
    };

    const response = await fetch(url, options);
    const data = await response.json();

    return {
      status: response.status,
      data,
      headers: response.headers
    };
  } catch (error) {
    console.error('Error making x-auth-token request:', error);
    return {
      status: 500,
      data: { error: error.message },
      headers: {}
    };
  }
};

/**
 * Create an expired JWT token for testing
 * @param {string} validToken - A valid JWT token to modify
 * @returns {string} - An expired JWT token
 */
export const createExpiredToken = (validToken) => {
  try {
    // Split the token into its parts
    const [header, payload, signature] = validToken.split('.');

    // Decode the payload
    const decodedPayload = JSON.parse(Buffer.from(payload, 'base64').toString());

    // Set expiration to a past time
    decodedPayload.exp = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago

    // Encode the modified payload
    const modifiedPayload = Buffer.from(JSON.stringify(decodedPayload)).toString('base64')
      .replace(/=/g, '')
      .replace(/\+/g, '-')
      .replace(/\//g, '_');

    // Reassemble the token (note: this will not pass signature validation)
    return `${header}.${modifiedPayload}.${signature}`;
  } catch (error) {
    console.error('Error creating expired token:', error);
    return validToken;
  }
};

/**
 * Extract token from browser localStorage
 * This function should be run in the browser console
 * @returns {string|null} - The authentication token or null if not found
 */
export const extractTokenFromBrowser = () => {
  try {
    // Check for token in localStorage
    const token = localStorage.getItem('oss_auth_token');
    if (token) {
      try {
        const parsedToken = JSON.parse(token);
        return parsedToken.access_token || null;
      } catch (e) {
        return token;
      }
    }

    // Check for token in Supabase storage
    const supabaseToken = localStorage.getItem('sb-ndlgbcsbidyhxbpqzgqp-auth-token');
    if (supabaseToken) {
      try {
        const parsedToken = JSON.parse(supabaseToken);
        return parsedToken.access_token || null;
      } catch (e) {
        return supabaseToken;
      }
    }

    return null;
  } catch (error) {
    console.error('Error extracting token from browser:', error);
    return null;
  }
};

/**
 * Test all authentication endpoints
 * @param {string} baseUrl - The base URL for API endpoints
 * @param {string} token - The authentication token
 * @returns {Promise<Object>} - Test results for all endpoints
 */
export const testAllAuthEndpoints = async (baseUrl, token) => {
  const endpoints = [
    '/api/admin/diagnostics/auth-check',
    '/api/auth/test',
    '/api/admin/customers',
    '/api/admin/bookings',
    '/api/admin/settings'
  ];

  const results = {};

  for (const endpoint of endpoints) {
    try {
      const result = await makeAuthenticatedRequest(`${baseUrl}${endpoint}`, token);
      results[endpoint] = {
        status: result.status,
        success: result.status >= 200 && result.status < 300,
        data: result.data
      };
    } catch (error) {
      results[endpoint] = {
        status: 500,
        success: false,
        error: error.message
      };
    }
  }

  return results;
};

/**
 * Generate a test report for authentication tests
 * @param {Object} results - Test results
 * @returns {string} - Formatted test report
 */
export const generateTestReport = (results) => {
  let report = '# Authentication Test Report\n\n';
  report += `Date: ${new Date().toISOString()}\n\n`;
  report += '## Endpoint Test Results\n\n';

  for (const [endpoint, result] of Object.entries(results)) {
    report += `### ${endpoint}\n\n`;
    report += `Status: ${result.status}\n`;
    report += `Success: ${result.success ? 'Yes' : 'No'}\n`;

    if (result.error) {
      report += `Error: ${result.error}\n`;
    } else {
      report += `Data: ${JSON.stringify(result.data, null, 2)}\n`;
    }

    report += '\n';
  }

  return report;
};

// Export a browser-friendly version of the utilities
if (typeof window !== 'undefined') {
  window.authTestUtils = {
    extractTokenFromBrowser,
    createExpiredToken
  };
}
