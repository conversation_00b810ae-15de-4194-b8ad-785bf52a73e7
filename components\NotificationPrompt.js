import { useState, useEffect } from 'react';
import styles from '@/styles/NotificationPrompt.module.css';
import { useOneSignalContext } from './OneSignalProvider';
import ClientOnly from './ClientOnly';

// Client-side only component
function NotificationPromptContent() {
  const [isVisible, setIsVisible] = useState(false);
  const { isInitialized, isAvailable, permissionStatus } = useOneSignalContext();

  useEffect(() => {
    // Skip if OneSignal is not initialized
    if (!isInitialized || !isAvailable) return;

    // Only check status if OneSignal is initialized
    checkSubscriptionStatus();
  }, [isInitialized, isAvailable, permissionStatus]);

  const checkSubscriptionStatus = async () => {
    try {
      // Only show prompt if not already subscribed or blocked
      if (permissionStatus !== 'granted' && permissionStatus !== 'denied') {
        // Check if we've shown the prompt before
        const hasPromptedBefore = localStorage.getItem('onesignal-prompt-shown');

        if (!hasPromptedBefore) {
          // Wait a bit before showing the prompt
          setTimeout(() => {
            setIsVisible(true);
          }, 5000); // Show after 5 seconds
        }
      }
    } catch (error) {
      console.error('Error checking subscription status:', error);
    }
  };

  const handleSubscribe = async () => {
    try {
      if (typeof window !== 'undefined' && window.OneSignal) {
        // Request notification permission
        await window.OneSignal.showNativePrompt();

        // Hide our custom prompt
        setIsVisible(false);

        // Remember that we've shown the prompt
        localStorage.setItem('onesignal-prompt-shown', 'true');
      }
    } catch (error) {
      console.error('Error subscribing to notifications:', error);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    // Remember that we've shown the prompt
    localStorage.setItem('onesignal-prompt-shown', 'true');
  };

  // Don't render anything if the prompt shouldn't be visible
  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.promptContainer}>
      <div className={styles.prompt}>
        <button className={styles.closeButton} onClick={handleClose}>×</button>
        <div className={styles.promptIcon}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
          </svg>
        </div>
        <div className={styles.promptContent}>
          <h3>Stay Updated!</h3>
          <p>Get notified about booking confirmations, special offers, and more.</p>
        </div>
        <div className={styles.promptActions}>
          <button className={styles.laterButton} onClick={handleClose}>
            Maybe Later
          </button>
          <button className={styles.subscribeButton} onClick={handleSubscribe}>
            Subscribe
          </button>
        </div>
      </div>
    </div>
  );
}

// Export a client-side only version of the component
export default function NotificationPrompt() {
  // In development, don't render anything to avoid issues
  if (process.env.NODE_ENV === 'development') {
    return null;
  }

  // In production, use the client-only wrapper
  return (
    <ClientOnly>
      <NotificationPromptContent />
    </ClientOnly>
  );
}
