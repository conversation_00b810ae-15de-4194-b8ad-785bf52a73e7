.sustainabilityShowcase {
  position: relative;
  padding: 8rem 2rem;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.8) 0%, rgba(162, 222, 208, 0.8) 100%);
  overflow: hidden;
  color: white;
}

.waveTop, .waveBottom {
  position: absolute;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  z-index: 2;
}

.waveTop {
  top: 0;
}

.waveBottom {
  bottom: 0;
  transform: rotate(180deg);
}

.waveTop svg, .waveBottom svg {
  display: block;
  width: calc(100% + 1.3px);
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

.showcaseContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  gap: 3rem;
  position: relative;
  z-index: 3;
}

.textContent {
  flex: 1;
  min-width: 300px;
  max-width: 700px;
}

.showcaseTitle {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.showcaseTitle::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 0;
  width: 80px;
  height: 4px;
  background-color: white;
  border-radius: 2px;
}

.showcaseDescription {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.9);
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.benefitCard {
  background-color: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 16px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.benefitCard:hover {
  transform: translateY(-10px);
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.benefitIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: inline-block;
}

.benefitTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.benefitDescription {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.imageContent {
  flex: 1;
  min-width: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.imageContainer {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.floatingElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.floatingLeaf {
  position: absolute;
  font-size: 2.5rem;
  animation: floatLeaf 5s ease-in-out infinite;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
}

@keyframes floatLeaf {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(10deg);
  }
}

.imageBorder {
  position: relative;
  border-radius: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1;
}

.imageBorder::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0) 30%,
    rgba(255, 255, 255, 0) 70%,
    rgba(255, 255, 255, 0.8) 100%
  );
  animation: rotateBorder 8s linear infinite;
}

@keyframes rotateBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.imageWrapper {
  position: relative;
  z-index: 2;
  overflow: hidden;
  border-radius: 10px;
  will-change: transform;
}

.showcaseImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 10px;
  transition: transform 0.5s ease;
}

.imageWrapper:hover .showcaseImage {
  transform: scale(1.05);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .showcaseTitle {
    font-size: 2.5rem;
  }
  
  .showcaseDescription {
    font-size: 1.1rem;
  }
  
  .benefitsGrid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 992px) {
  .sustainabilityShowcase {
    padding: 6rem 2rem;
  }
  
  .showcaseContent {
    flex-direction: column;
  }
  
  .textContent {
    order: 2;
    max-width: 100%;
  }
  
  .imageContent {
    order: 1;
    margin-bottom: 3rem;
  }
  
  .showcaseTitle {
    text-align: center;
    display: block;
  }
  
  .showcaseTitle::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .showcaseDescription {
    text-align: center;
  }
  
  .benefitsGrid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .sustainabilityShowcase {
    padding: 4rem 1.5rem;
  }
  
  .showcaseTitle {
    font-size: 2.2rem;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
  
  .benefitIcon {
    font-size: 2rem;
  }
  
  .benefitTitle {
    font-size: 1.2rem;
  }
  
  .floatingLeaf {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .sustainabilityShowcase {
    padding: 3rem 1rem 4rem;
  }
  
  .showcaseTitle {
    font-size: 2rem;
  }
  
  .showcaseDescription {
    font-size: 1rem;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr;
  }
  
  .benefitCard {
    padding: 1.5rem;
  }
  
  .imageBorder {
    padding: 15px;
  }
} 