/**
 * API Health Check Endpoint
 * 
 * This endpoint provides basic system health information, including
 * environment details and auth configuration status.
 * 
 * Unlike other API endpoints, this one does not require authentication.
 */

export default async function handler(req, res) {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    const hasSupabaseUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL;
    const hasSupabaseKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const hasSiteUrl = !!process.env.NEXT_PUBLIC_SITE_URL;
    
    // Basic connectivity check - we made it here so the API is responding
    res.status(200).json({
      status: 'online',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      auth: {
        // Don't reveal actual credentials, just whether they're configured
        supabaseConfigured: hasSupabaseUrl && hasSupabaseKey,
        siteUrlConfigured: hasSiteUrl,
        // These are particularly important in production
        productionReady: !isProduction || (hasSupabaseUrl && hasSupabaseKey && hasSiteUrl)
      }
    });
  } catch (err) {
    console.error('Health check error:', err);
    res.status(500).json({
      status: 'error',
      message: err.message
    });
  }
}
