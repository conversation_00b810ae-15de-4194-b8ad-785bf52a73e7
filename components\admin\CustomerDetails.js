import { useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import CustomerBooking<PERSON><PERSON><PERSON> from './CustomerBookingHistory'
import Modal from './Modal'
import LoadingButton from './LoadingButton'
import styles from '@/styles/admin/CustomerDetails.module.css'
import { useAuth } from '@/contexts/AuthContext'
import { authenticatedFetch } from '@/lib/auth-utils'

export default function CustomerDetails({ customer, bookings, preferences }) {
  const router = useRouter()
  const { isAdmin } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showGdprModal, setShowGdprModal] = useState(false)
  const [gdprReason, setGdprReason] = useState('')
  const [showNotificationModal, setShowNotificationModal] = useState(false)
  const [notificationData, setNotificationData] = useState({
    title: '',
    message: '',
    type: 'email'
  })

  if (!customer) {
    return <div className={styles.loading}>Loading customer details...</div>
  }

  const handleDelete = async () => {
    setLoading(true)
    setError(null)

    try {
      // Use authenticatedFetch for the DELETE request
      await authenticatedFetch(`/api/customers/${customer.id}`, {
        method: 'DELETE'
      });

      setSuccessMessage('Customer deleted successfully');

      // Navigate back to customer list after a short delay
      setTimeout(() => {
        router.push('/admin/customers');
      }, 1500);
    } catch (error) {
      console.error('Error deleting customer:', error);
      setError(error.message);
    } finally {
      setLoading(false);
      setShowDeleteModal(false);
    }
  }

  const handleGdprDelete = async () => {
    setLoading(true)
    setError(null)

    try {
      // Use authenticatedFetch for the GDPR delete request
      await authenticatedFetch(`/api/customers/${customer.id}/gdpr-delete`, {
        method: 'POST',
        body: JSON.stringify({
          reason: gdprReason || 'Customer request'
        })
      });

      setSuccessMessage('Customer data anonymized successfully');

      // Navigate back to customer list after a short delay
      setTimeout(() => {
        router.push('/admin/customers');
      }, 1500);
    } catch (error) {
      console.error('Error processing GDPR deletion:', error);
      setError(error.message);
    } finally {
      setLoading(false);
      setShowGdprModal(false);
    }
  }

  const handleNotificationChange = (e) => {
    const { name, value } = e.target
    setNotificationData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const sendNotification = async () => {
    if (!notificationData.title || !notificationData.message) {
      setError('Notification title and message are required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Use authenticatedFetch for sending notifications
      await authenticatedFetch('/api/notifications/send', {
        method: 'POST',
        body: JSON.stringify({
          customer_id: customer.id,
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type
        })
      });

      setSuccessMessage('Notification sent successfully');
      setShowNotificationModal(false);
      setNotificationData({
        title: '',
        message: '',
        type: 'email'
      });
    } catch (error) {
      console.error('Error sending notification:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }

  // Render customer status badge
  const renderStatusBadge = (status) => {
    const statusConfig = {
      active: { color: '#4caf50', label: 'Active' },
      inactive: { color: '#ff9800', label: 'Inactive' },
      suspended: { color: '#f44336', label: 'Suspended' },
      churned: { color: '#9e9e9e', label: 'Churned' }
    }

    const config = statusConfig[status] || { color: '#4caf50', label: 'Active' }

    return (
      <span
        className={styles.statusBadge}
        style={{ backgroundColor: config.color }}
      >
        {config.label}
      </span>
    )
  }

  // Render customer tier badge
  const renderTierBadge = (tier) => {
    if (!tier || tier === 'bronze') return null

    const tierColors = {
      silver: '#C0C0C0',
      gold: '#FFD700',
      platinum: '#E5E4E2'
    }

    return (
      <span
        className={styles.tierBadge}
        style={{ backgroundColor: tierColors[tier] }}
      >
        {tier.toUpperCase()}
      </span>
    )
  }

  return (
    <div className={styles.customerDetails}>
      {error && <div className={styles.error}>{error}</div>}
      {successMessage && <div className={styles.success}>{successMessage}</div>}

      {/* Enhanced Customer Profile Header */}
      <div className={styles.profileHeader}>
        <div className={styles.customerInfo}>
          <div className={styles.customerName}>
            <h1>{customer.name}</h1>
            <div className={styles.customerBadges}>
              {renderStatusBadge(customer.customer_status)}
              {renderTierBadge(customer.customer_tier)}
              {customer.marketing_consent && (
                <span className={styles.marketingBadge}>📧 Marketing Opt-in</span>
              )}
            </div>
          </div>
          <div className={styles.customerMeta}>
            <span className={styles.customerEmail}>{customer.email}</span>
            {customer.phone && (
              <span className={styles.customerPhone}>{customer.phone}</span>
            )}
            <span className={styles.customerSince}>
              Customer since {customer.created_at ? new Date(customer.created_at).toLocaleDateString() : 'N/A'}
            </span>
          </div>
        </div>

        <div className={styles.quickActions}>
          <button
            className={styles.primaryButton}
            onClick={() => router.push(`/admin/customers/${customer.id}/edit`)}
            disabled={loading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            Edit Profile
          </button>
          <button
            className={styles.secondaryButton}
            onClick={() => router.push(`/admin/bookings/new?customer_id=${customer.id}`)}
            disabled={loading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            New Booking
          </button>
          <button
            className={styles.secondaryButton}
            onClick={() => setShowNotificationModal(true)}
            disabled={loading}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            Send Email
          </button>
          {isAdmin && (
            <button
              className={styles.dangerButton}
              onClick={() => setShowDeleteModal(true)}
              disabled={loading}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
              </svg>
              Deactivate
            </button>
          )}
        </div>
      </div>

      {/* Customer Statistics */}
      <div className={styles.statsContainer}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>${customer.lifetime_value || '0'}</div>
            <div className={styles.statLabel}>Lifetime Value</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>{customer.total_bookings || bookings?.length || '0'}</div>
            <div className={styles.statLabel}>Total Bookings</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>
              {customer.last_booking_date ? new Date(customer.last_booking_date).toLocaleDateString() : 'Never'}
            </div>
            <div className={styles.statLabel}>Last Activity</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>{customer.customer_health_score || 'N/A'}</div>
            <div className={styles.statLabel}>Health Score</div>
          </div>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.leftColumn}>
          {/* Customer Notes Section */}
          <div className={styles.infoSection}>
            <h3>Customer Notes</h3>
            <div className={styles.notesContainer}>
              <div className={styles.existingNotes}>
                {customer.notes ? (
                  <div className={styles.noteItem}>
                    <div className={styles.noteContent}>{customer.notes}</div>
                    <div className={styles.noteDate}>
                      Added: {customer.updated_at ? new Date(customer.updated_at).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                ) : (
                  <div className={styles.noNotes}>No notes available</div>
                )}
              </div>
              <button
                className={styles.addNoteButton}
                onClick={() => router.push(`/admin/customers/${customer.id}/edit`)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Add Note
              </button>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Contact Information</h3>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Email:</span>
                <span className={styles.infoValue}>
                  <a href={`mailto:${customer.email}`}>{customer.email}</a>
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Phone:</span>
                <span className={styles.infoValue}>
                  {customer.phone ? (
                    <a href={`tel:${customer.phone}`}>{customer.phone}</a>
                  ) : (
                    'Not provided'
                  )}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Address:</span>
                <span className={styles.infoValue}>
                  {customer.address ? (
                    <>
                      {customer.address}<br />
                      {customer.city && `${customer.city}, `}
                      {customer.state && `${customer.state} `}
                      {customer.postal_code && customer.postal_code}
                      {customer.country && <><br />{customer.country}</>}
                    </>
                  ) : (
                    'Not provided'
                  )}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Created:</span>
                <span className={styles.infoValue}>
                  {new Date(customer.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {preferences && preferences.length > 0 && (
            <div className={styles.infoSection}>
              <h3>Customer Preferences</h3>
              <div className={styles.preferencesList}>
                {preferences.map((pref, index) => (
                  <div key={index} className={styles.preferenceItem}>
                    <span className={styles.preferenceKey}>{pref.key}:</span>
                    <span className={styles.preferenceValue}>{pref.value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {customer.notes && (
            <div className={styles.infoSection}>
              <h3>Notes</h3>
              <div className={styles.notes}>
                {customer.notes}
              </div>
            </div>
          )}

          <div className={styles.infoSection}>
            <h3>Marketing</h3>
            <div className={styles.marketingConsent}>
              <span className={styles.consentStatus}>
                {customer.marketing_consent ? (
                  <span className={styles.consentGranted}>Opted In</span>
                ) : (
                  <span className={styles.consentDenied}>Opted Out</span>
                )}
              </span>
              <p className={styles.consentDescription}>
                {customer.marketing_consent
                  ? 'Customer has consented to receive marketing communications.'
                  : 'Customer has not consented to receive marketing communications.'}
              </p>
              <button
                className={styles.notificationButton}
                onClick={() => setShowNotificationModal(true)}
                disabled={loading || !customer.email}
              >
                Send Notification
              </button>
            </div>
          </div>

          {isAdmin && (
            <div className={styles.infoSection}>
              <h3>GDPR Compliance</h3>
              <button
                className={styles.gdprButton}
                onClick={() => setShowGdprModal(true)}
                disabled={loading}
              >
                Anonymize Customer Data
              </button>
              <p className={styles.gdprDescription}>
                This will anonymize all personal data while preserving booking history.
                This action cannot be undone.
              </p>
            </div>
          )}
        </div>

        <div className={styles.rightColumn}>
          {/* Activity Timeline */}
          <div className={styles.infoSection}>
            <h3>Recent Activity</h3>
            <div className={styles.activityTimeline}>
              {bookings && bookings.length > 0 ? (
                bookings.slice(0, 5).map((booking, index) => (
                  <div key={booking.id || index} className={styles.timelineItem}>
                    <div className={styles.timelineIcon}>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                      </svg>
                    </div>
                    <div className={styles.timelineContent}>
                      <div className={styles.timelineTitle}>
                        Booking {booking.status || 'Scheduled'}
                      </div>
                      <div className={styles.timelineDescription}>
                        {booking.service_name || 'Service booking'}
                        {booking.booking_date && ` on ${new Date(booking.booking_date).toLocaleDateString()}`}
                      </div>
                      <div className={styles.timelineDate}>
                        {booking.created_at ? new Date(booking.created_at).toLocaleDateString() : 'N/A'}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className={styles.noActivity}>No recent activity</div>
              )}

              {/* Customer Registration */}
              <div className={styles.timelineItem}>
                <div className={styles.timelineIcon}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <div className={styles.timelineContent}>
                  <div className={styles.timelineTitle}>Customer Registered</div>
                  <div className={styles.timelineDescription}>
                    Joined Ocean Soul Sparkles
                  </div>
                  <div className={styles.timelineDate}>
                    {customer.created_at ? new Date(customer.created_at).toLocaleDateString() : 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className={styles.bookingHistory}>
            <CustomerBookingHistory bookings={bookings} />
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <Modal onClose={() => setShowDeleteModal(false)}>
          <div className={styles.modalContent}>
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete this customer? This action cannot be undone.</p>
            <div className={styles.modalActions}>
              <LoadingButton
                type="button"
                variant="secondary"
                className={styles.cancelButton}
                onClick={() => setShowDeleteModal(false)}
                loading={loading}
              >
                Cancel
              </LoadingButton>
              <LoadingButton
                type="button"
                variant="danger"
                className={styles.deleteButton}
                onClick={handleDelete}
                loading={loading}
                loadingText="Deleting..."
              >
                Delete Customer
              </LoadingButton>
            </div>
          </div>
        </Modal>
      )}

      {/* GDPR Deletion Modal */}
      {showGdprModal && (
        <Modal onClose={() => setShowGdprModal(false)}>
          <div className={styles.modalContent}>
            <h3>GDPR Data Anonymization</h3>
            <p>
              This will anonymize all personal data for this customer while preserving booking history.
              This action is designed for GDPR compliance and cannot be undone.
            </p>
            <div className={styles.formGroup}>
              <label htmlFor="gdprReason">Reason for Anonymization:</label>
              <input
                type="text"
                id="gdprReason"
                value={gdprReason}
                onChange={(e) => setGdprReason(e.target.value)}
                placeholder="e.g., Customer request, Data retention policy"
                className={styles.input}
              />
            </div>
            <div className={styles.modalActions}>
              <LoadingButton
                type="button"
                variant="secondary"
                className={styles.cancelButton}
                onClick={() => setShowGdprModal(false)}
                loading={loading}
              >
                Cancel
              </LoadingButton>
              <LoadingButton
                type="button"
                variant="primary"
                className={styles.gdprButton}
                onClick={handleGdprDelete}
                loading={loading}
                loadingText="Processing..."
              >
                Anonymize Data
              </LoadingButton>
            </div>
          </div>
        </Modal>
      )}

      {/* Notification Modal */}
      {showNotificationModal && (
        <Modal onClose={() => setShowNotificationModal(false)}>
          <div className={styles.modalContent}>
            <h3>Send Notification to Customer</h3>
            <p>
              Send a notification to {customer.name} via email or in-app notification.
            </p>
            <div className={styles.formGroup}>
              <label htmlFor="type">Notification Type:</label>
              <select
                id="type"
                name="type"
                value={notificationData.type}
                onChange={handleNotificationChange}
                className={styles.select}
                disabled={loading}
              >
                <option value="email">Email</option>
                <option value="sms">SMS</option>
                <option value="push">Push Notification</option>
              </select>
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="title">Title:</label>
              <input
                type="text"
                id="title"
                name="title"
                value={notificationData.title}
                onChange={handleNotificationChange}
                placeholder="Notification title"
                className={styles.input}
                disabled={loading}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="message">Message:</label>
              <textarea
                id="message"
                name="message"
                value={notificationData.message}
                onChange={handleNotificationChange}
                placeholder="Enter your message here..."
                className={styles.textarea}
                rows={4}
                disabled={loading}
              />
            </div>
            <div className={styles.modalActions}>
              <LoadingButton
                type="button"
                variant="secondary"
                className={styles.cancelButton}
                onClick={() => setShowNotificationModal(false)}
                loading={loading}
              >
                Cancel
              </LoadingButton>
              <LoadingButton
                type="button"
                variant="success"
                className={styles.sendButton}
                onClick={sendNotification}
                loading={loading}
                loadingText="Sending..."
                disabled={!notificationData.title || !notificationData.message}
              >
                Send Notification
              </LoadingButton>
            </div>
          </div>
        </Modal>
      )}
    </div>
  )
}
