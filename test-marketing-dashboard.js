/**
 * Test script to verify marketing dashboard functionality
 * This script tests that the authentication loop has been resolved
 * and the marketing APIs are working correctly.
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testMarketingAPIs() {
  console.log('🧪 Testing Marketing Dashboard APIs...\n');

  const tests = [
    {
      name: 'Marketing Stats API',
      url: `${BASE_URL}/api/admin/marketing/stats`,
      expectedStatus: 200
    },
    {
      name: 'Marketing Segments API',
      url: `${BASE_URL}/api/admin/marketing/segments`,
      expectedStatus: 200
    },
    {
      name: 'Marketing Campaigns API',
      url: `${BASE_URL}/api/admin/marketing/campaigns`,
      expectedStatus: 200
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`Testing ${test.name}...`);
      const response = await fetch(test.url);
      
      if (response.status === test.expectedStatus) {
        console.log(`✅ ${test.name}: PASSED (${response.status})`);
        passedTests++;
        
        // Try to parse response data
        try {
          const data = await response.json();
          console.log(`   Response data keys: ${Object.keys(data).join(', ')}`);
        } catch (e) {
          console.log(`   Response received but couldn't parse JSON`);
        }
      } else {
        console.log(`❌ ${test.name}: FAILED (Expected ${test.expectedStatus}, got ${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
    }
    console.log('');
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All marketing APIs are working correctly!');
    console.log('✅ Authentication loop has been resolved!');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above.');
  }
}

async function testPageAccess() {
  console.log('\n🌐 Testing Page Access...\n');

  const pages = [
    {
      name: 'Admin Login Page',
      url: `${BASE_URL}/admin/login`
    },
    {
      name: 'Admin Dashboard',
      url: `${BASE_URL}/admin`
    },
    {
      name: 'Marketing Dashboard',
      url: `${BASE_URL}/admin/marketing`
    }
  ];

  for (const page of pages) {
    try {
      console.log(`Testing ${page.name}...`);
      const response = await fetch(page.url);
      
      if (response.status === 200) {
        console.log(`✅ ${page.name}: ACCESSIBLE (${response.status})`);
      } else {
        console.log(`❌ ${page.name}: FAILED (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${page.name}: ERROR - ${error.message}`);
    }
  }
}

async function runTests() {
  console.log('🚀 Starting Ocean Soul Sparkles Marketing Dashboard Tests\n');
  console.log('=' .repeat(60));
  
  await testPageAccess();
  console.log('\n' + '=' .repeat(60));
  await testMarketingAPIs();
  
  console.log('\n' + '=' .repeat(60));
  console.log('✨ Test completed!');
}

// Run the tests
runTests().catch(console.error);
