#!/usr/bin/env node

/**
 * Production Onboarding Testing Script
 * Tests all components of the Artist/Braider onboarding system
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function testProductionOnboarding() {
  console.log('🧪 Testing Production Artist/Braider Onboarding System\n')

  let allPassed = true
  const results = []

  // Test 1: Database Schema
  console.log('1. Testing database schema...')
  try {
    // Test admin_settings table
    const { data: adminSettings, error: adminError } = await supabase
      .from('admin_settings')
      .select('setting_key')
      .limit(1)

    if (adminError && adminError.code !== 'PGRST116') {
      throw new Error(`admin_settings table error: ${adminError.message}`)
    }

    // Test application tables
    const { data: applications, error: appError } = await supabase
      .from('artist_braider_applications')
      .select('id')
      .limit(1)

    if (appError && appError.code !== 'PGRST116') {
      throw new Error(`artist_braider_applications table error: ${appError.message}`)
    }

    const { data: tokens, error: tokenError } = await supabase
      .from('application_tokens')
      .select('id')
      .limit(1)

    if (tokenError && tokenError.code !== 'PGRST116') {
      throw new Error(`application_tokens table error: ${tokenError.message}`)
    }

    console.log('✅ Database schema test passed')
    results.push({ test: 'database_schema', passed: true })
  } catch (error) {
    console.error('❌ Database schema test failed:', error.message)
    allPassed = false
    results.push({ test: 'database_schema', passed: false, error: error.message })
  }

  // Test 2: Token Generation
  console.log('\n2. Testing token generation...')
  try {
    const { data: generatedToken, error: generateError } = await supabase
      .rpc('generate_application_token')

    if (generateError) {
      throw new Error(`Token generation failed: ${generateError.message}`)
    }

    if (!generatedToken || typeof generatedToken !== 'string' || generatedToken.length < 10) {
      throw new Error('Invalid token generated')
    }

    console.log(`✅ Token generation test passed: ${generatedToken.substring(0, 8)}...`)
    results.push({ test: 'token_generation', passed: true, token: generatedToken.substring(0, 8) + '...' })
  } catch (error) {
    console.error('❌ Token generation test failed:', error.message)
    allPassed = false
    results.push({ test: 'token_generation', passed: false, error: error.message })
  }

  // Test 3: Token Validation
  console.log('\n3. Testing token validation...')
  try {
    // Test with invalid token
    const { data: invalidResult, error: invalidError } = await supabase
      .rpc('validate_application_token', { token_value: 'invalid-test-token' })

    if (invalidError) {
      throw new Error(`Token validation function error: ${invalidError.message}`)
    }

    if (!invalidResult || invalidResult.length === 0) {
      throw new Error('Token validation function returned no results')
    }

    const validation = invalidResult[0]
    if (validation.is_valid !== false) {
      throw new Error('Invalid token was incorrectly validated as valid')
    }

    console.log('✅ Token validation test passed')
    results.push({ test: 'token_validation', passed: true })
  } catch (error) {
    console.error('❌ Token validation test failed:', error.message)
    allPassed = false
    results.push({ test: 'token_validation', passed: false, error: error.message })
  }

  // Test 4: Email Configuration
  console.log('\n4. Testing email configuration...')
  try {
    const { getEmailConfig } = await import('../lib/email-service-manager.js')
    const emailConfig = await getEmailConfig()
    
    const hasGmail = !!(emailConfig.gmail.user && emailConfig.gmail.password)
    const hasWorkspace = !!(emailConfig.workspace.user && emailConfig.workspace.password)
    const hasOneSignal = !!(emailConfig.onesignal.appId && emailConfig.onesignal.restApiKey)
    
    console.log(`   Gmail SMTP: ${hasGmail ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   Workspace SMTP: ${hasWorkspace ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   OneSignal: ${hasOneSignal ? '✅ Configured' : '❌ Not configured'}`)
    
    if (hasGmail || hasWorkspace || hasOneSignal) {
      console.log('✅ Email configuration test passed')
      results.push({ test: 'email_configuration', passed: true, services: { gmail: hasGmail, workspace: hasWorkspace, onesignal: hasOneSignal } })
    } else {
      throw new Error('No email services are configured')
    }
  } catch (error) {
    console.error('❌ Email configuration test failed:', error.message)
    allPassed = false
    results.push({ test: 'email_configuration', passed: false, error: error.message })
  }

  // Test 5: API Endpoints
  console.log('\n5. Testing API endpoints...')
  try {
    // Test token validation API endpoint
    const tokenResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'}/api/applications/validate-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: 'test-invalid-token',
        role: 'artist'
      })
    })

    if (!tokenResponse.ok && tokenResponse.status !== 400) {
      throw new Error(`Token validation API returned unexpected status: ${tokenResponse.status}`)
    }

    const tokenResult = await tokenResponse.json()
    if (tokenResult.valid !== false) {
      throw new Error('Token validation API should return valid: false for invalid token')
    }

    console.log('✅ API endpoints test passed')
    results.push({ test: 'api_endpoints', passed: true })
  } catch (error) {
    console.error('❌ API endpoints test failed:', error.message)
    allPassed = false
    results.push({ test: 'api_endpoints', passed: false, error: error.message })
  }

  // Test 6: Email Template Generation
  console.log('\n6. Testing email template generation...')
  try {
    const { generateWelcomeEmail } = await import('../lib/email-templates.js')
    
    const testUser = {
      name: 'Test Artist',
      email: '<EMAIL>',
      role: 'artist',
      applicationToken: 'test-token-123456789'
    }

    const emailTemplate = generateWelcomeEmail(testUser)

    if (!emailTemplate.subject || !emailTemplate.htmlBody) {
      throw new Error('Email template generation failed - missing subject or body')
    }

    const expectedLink = `https://www.oceansoulsparkles.com.au/apply/artist?token=${testUser.applicationToken}`
    if (!emailTemplate.htmlBody.includes(expectedLink)) {
      throw new Error('Email template missing correct application link')
    }

    console.log('✅ Email template generation test passed')
    results.push({ test: 'email_template_generation', passed: true })
  } catch (error) {
    console.error('❌ Email template generation test failed:', error.message)
    allPassed = false
    results.push({ test: 'email_template_generation', passed: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Test Summary:')
  const passedCount = results.filter(r => r.passed).length
  const totalCount = results.length
  
  console.log(`   Passed tests: ${passedCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.test}${error}`)
  })

  console.log(`\n   Overall status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

  if (!allPassed) {
    console.log('\n🔧 Next Steps:')
    console.log('   1. Run the CRITICAL_PRODUCTION_FIX.sql script in Supabase SQL Editor')
    console.log('   2. Configure email services (Gmail SMTP, Workspace SMTP, or OneSignal)')
    console.log('   3. Verify environment variables are set correctly')
    console.log('   4. Test the complete onboarding flow manually')
  } else {
    console.log('\n🎉 All tests passed!')
    console.log('   The Artist/Braider onboarding system is ready for production use.')
    console.log('   You can now safely test the complete onboarding workflow.')
  }

  return allPassed
}

// Run the test
testProductionOnboarding()
  .then(success => {
    if (success) {
      console.log('\n✅ Production onboarding tests completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Production onboarding tests completed with failures!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Test script error:', error)
    process.exit(1)
  })
