/**
 * Unified Authentication Fetch Utility
 *
 * This module provides a single, consistent way to make authenticated API requests
 * that ensures proper token transmission to the server for all admin endpoints.
 *
 * Fixes the authentication issues by:
 * 1. Ensuring tokens are properly retrieved from storage
 * 2. Adding tokens to both Authorization header and cookies
 * 3. Providing fallback mechanisms for token refresh
 * 4. Maintaining compatibility with existing code
 */

import { getAuthToken, setCookieToken, storeTokenInMultipleLocations } from './auth-token-manager';
import { addCacheHeaders } from './cache-control-utils';

/**
 * Enhanced fetch function with automatic authentication and cache headers
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
export async function authenticatedFetch(url, options = {}) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Authenticated fetch to: ${url}`);

  try {
    // Get the current authentication token
    let token = await getAuthToken();

    if (!token) {
      console.warn(`[${requestId}] No authentication token available for request to ${url}`);

      // For admin API requests, this is a critical error in production
      if (url.includes('/api/admin/')) {
        // In production, redirect to login if no token is available
        if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
          console.log(`[${requestId}] Production mode: redirecting to admin login`);
          window.location.href = '/admin/login?redirect=' + encodeURIComponent(window.location.pathname);
          throw new Error('Redirecting to login');
        }
        throw new Error('Authentication required for admin API access');
      }
    }

    // Ensure token is stored in cookies for server-side access
    if (token) {
      setCookieToken(token);
    }

    // Prepare headers with authentication
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authentication headers if we have a token
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      headers['X-Auth-Token'] = token; // Fallback header for compatibility
    }

    // Determine if this is an admin request for cache strategy
    const isAdmin = url.includes('/api/admin/');
    const method = options.method || 'GET';

    // Extract endpoint and query params for cache strategy
    const urlParts = url.split('?');
    const endpoint = urlParts[0];
    const queryString = urlParts[1] || '';
    const queryParams = {};

    if (queryString) {
      const searchParams = new URLSearchParams(queryString);
      for (const [key, value] of searchParams.entries()) {
        queryParams[key] = value;
      }
    }

    // Add appropriate cache headers
    const enhancedOptions = addCacheHeaders(
      {
        ...options,
        headers,
        credentials: 'include' // Always include cookies
      },
      endpoint,
      method,
      isAdmin,
      queryParams
    );

    console.log(`[${requestId}] Making request with auth headers:`, {
      hasAuth: !!token,
      hasAuthHeader: !!headers['Authorization'],
      hasXAuthToken: !!headers['X-Auth-Token'],
      cacheControl: enhancedOptions.headers['Cache-Control']
    });

    // Make the request
    const response = await fetch(url, enhancedOptions);

    // Handle authentication errors
    if (response.status === 401 || response.status === 403) {
      console.warn(`[${requestId}] Authentication failed (${response.status}) for ${url}`);

      // In production, don't try to refresh tokens - redirect to login immediately
      if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
        console.log(`[${requestId}] Production mode: redirecting to admin login immediately`);
        window.location.href = '/admin/login?redirect=' + encodeURIComponent(window.location.pathname);
        return response; // Return the original response
      }

      // In development, try to refresh the token and retry once
      try {
        console.log(`[${requestId}] Development mode: attempting token refresh...`);
        const { refreshAuthToken } = await import('./auth-token-manager');
        const newToken = await refreshAuthToken();

        if (newToken) {
          console.log(`[${requestId}] Token refresh successful, retrying request`);

          // Store the new token
          storeTokenInMultipleLocations(newToken);

          // Update headers with new token
          headers['Authorization'] = `Bearer ${newToken}`;
          headers['X-Auth-Token'] = newToken;

          // Retry the request with new token
          const retryOptions = {
            ...enhancedOptions,
            headers
          };

          const retryResponse = await fetch(url, retryOptions);
          console.log(`[${requestId}] Retry response status: ${retryResponse.status}`);
          return retryResponse;
        }
      } catch (refreshError) {
        console.error(`[${requestId}] Token refresh failed:`, refreshError);
      }

      // If we're in the browser and this is an admin request, redirect to login
      if (typeof window !== 'undefined' && url.includes('/api/admin/')) {
        console.log(`[${requestId}] Redirecting to admin login due to authentication failure`);
        setTimeout(() => {
          window.location.href = '/admin/login?redirect=' + encodeURIComponent(window.location.pathname);
        }, 100);
      }
    }

    return response;
  } catch (error) {
    console.error(`[${requestId}] Authenticated fetch error:`, error);
    throw error;
  }
}

/**
 * Convenience function for GET requests
 * @param {string} url - The URL to fetch
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} The fetch response
 */
export async function authenticatedGet(url, options = {}) {
  return authenticatedFetch(url, { ...options, method: 'GET' });
}

/**
 * Convenience function for POST requests
 * @param {string} url - The URL to fetch
 * @param {Object} data - The data to send
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} The fetch response
 */
export async function authenticatedPost(url, data, options = {}) {
  return authenticatedFetch(url, {
    ...options,
    method: 'POST',
    body: JSON.stringify(data)
  });
}

/**
 * Convenience function for PUT requests
 * @param {string} url - The URL to fetch
 * @param {Object} data - The data to send
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} The fetch response
 */
export async function authenticatedPut(url, data, options = {}) {
  return authenticatedFetch(url, {
    ...options,
    method: 'PUT',
    body: JSON.stringify(data)
  });
}

/**
 * Convenience function for DELETE requests
 * @param {string} url - The URL to fetch
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Response>} The fetch response
 */
export async function authenticatedDelete(url, options = {}) {
  return authenticatedFetch(url, { ...options, method: 'DELETE' });
}

/**
 * Initialize authentication for the current session
 * This should be called when the app starts to ensure tokens are properly set up
 * Only initializes if we're on an admin route to avoid unnecessary authentication on public pages
 */
export async function initializeAuthentication() {
  try {
    // Only initialize authentication if we're on an admin route
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      const isAdminRoute = currentPath.startsWith('/admin');

      if (!isAdminRoute) {
        console.log('[UnifiedAuth] Skipping authentication initialization for public route:', currentPath);
        return false;
      }
    }

    console.log('[UnifiedAuth] Initializing authentication for admin route...');

    // Get the current token and ensure it's stored in all locations
    const token = await getAuthToken();

    if (token) {
      console.log('[UnifiedAuth] Token found, storing in all locations');
      storeTokenInMultipleLocations(token);
    } else {
      console.log('[UnifiedAuth] No token found during initialization');
    }

    return !!token;
  } catch (error) {
    console.error('[UnifiedAuth] Error during authentication initialization:', error);
    return false;
  }
}

/**
 * Check if the current user is authenticated
 * Only performs authentication checks for admin routes to avoid unnecessary calls on public pages
 * @returns {Promise<boolean>} True if authenticated, false otherwise
 */
export async function isAuthenticated() {
  try {
    // Only check authentication if we're on an admin route
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      const isAdminRoute = currentPath.startsWith('/admin');

      if (!isAdminRoute) {
        console.log('[UnifiedAuth] Skipping authentication check for public route:', currentPath);
        return false; // Public routes don't need authentication
      }
    }

    const token = await getAuthToken();

    if (!token) {
      console.log('[UnifiedAuth] No authentication token found for admin route');
      return false;
    }

    // In production, also verify the token is valid by making a test request
    if (process.env.NODE_ENV === 'production') {
      try {
        const testResponse = await fetch('/api/admin/auth-check', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-Auth-Token': token,
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        const isValid = testResponse.ok;
        console.log('[UnifiedAuth] Production token validation:', isValid ? 'valid' : 'invalid');
        return isValid;
      } catch (testError) {
        console.warn('[UnifiedAuth] Token validation test failed:', testError);
        return false;
      }
    }

    // In development, just check if token exists
    return true;
  } catch (error) {
    console.error('[UnifiedAuth] Error checking authentication status:', error);
    return false;
  }
}

export default {
  authenticatedFetch,
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  authenticatedDelete,
  initializeAuthentication,
  isAuthenticated
};
