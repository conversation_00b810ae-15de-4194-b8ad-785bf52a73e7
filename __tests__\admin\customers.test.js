import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/router'
import CustomerList from '@/components/admin/CustomerList'
import CustomerForm from '@/components/admin/CustomerForm'
import CustomerDetails from '@/components/admin/CustomerDetails'
import CustomerBookingHistory from '@/components/admin/CustomerBookingHistory'
import { useAuth } from '@/contexts/AuthContext'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}))

// Mock AuthContext
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn()
}))

// Mock fetch
global.fetch = jest.fn()

describe('Customer Management Components', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    
    // Mock router
    useRouter.mockReturnValue({
      push: jest.fn(),
      back: jest.fn(),
      query: { id: '123' }
    })
    
    // Mock auth context
    useAuth.mockReturnValue({
      isAdmin: true
    })
    
    // Mock fetch
    global.fetch.mockResolvedValue({
      ok: true,
      json: async () => ({})
    })
  })
  
  describe('CustomerList', () => {
    beforeEach(() => {
      // Mock fetch for customer list
      global.fetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          customers: [
            {
              id: '1',
              name: 'John Doe',
              email: '<EMAIL>',
              phone: '0412345678',
              city: 'Sydney',
              state: 'NSW',
              created_at: '2023-01-01T00:00:00Z'
            },
            {
              id: '2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              phone: '0487654321',
              city: 'Melbourne',
              state: 'VIC',
              created_at: '2023-01-02T00:00:00Z'
            }
          ],
          total: 2
        })
      })
    })
    
    it('renders customer list with data', async () => {
      render(<CustomerList />)
      
      // Check loading state
      expect(screen.getByText('Loading customers...')).toBeInTheDocument()
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })
      
      // Check filter controls
      expect(screen.getByText('All Cities')).toBeInTheDocument()
      expect(screen.getByText('All States')).toBeInTheDocument()
    })
    
    it('handles search input', async () => {
      render(<CustomerList />)
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
      
      // Type in search box
      const searchInput = screen.getByPlaceholderText('Search by name, email, or phone...')
      fireEvent.change(searchInput, { target: { value: 'john' } })
      
      // Check that fetch was called with search parameter
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('search=john'))
      })
    })
  })
  
  describe('CustomerForm', () => {
    it('renders empty form for new customer', () => {
      render(<CustomerForm />)
      
      // Check form fields
      expect(screen.getByLabelText('Name *')).toBeInTheDocument()
      expect(screen.getByLabelText('Email *')).toBeInTheDocument()
      expect(screen.getByLabelText('Phone')).toBeInTheDocument()
      
      // Check buttons
      expect(screen.getByText('Cancel')).toBeInTheDocument()
      expect(screen.getByText('Create Customer')).toBeInTheDocument()
    })
    
    it('renders form with customer data for editing', () => {
      const customer = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '0412345678',
        address: '123 Main St',
        city: 'Sydney',
        state: 'NSW',
        postal_code: '2000',
        country: 'Australia',
        notes: 'Test notes',
        marketing_consent: true
      }
      
      render(<CustomerForm customer={customer} />)
      
      // Check form fields have customer data
      expect(screen.getByLabelText('Name *')).toHaveValue('John Doe')
      expect(screen.getByLabelText('Email *')).toHaveValue('<EMAIL>')
      expect(screen.getByLabelText('Phone')).toHaveValue('0412345678')
      
      // Check marketing consent checkbox
      expect(screen.getByLabelText(/Customer has consented/)).toBeChecked()
      
      // Check buttons
      expect(screen.getByText('Cancel')).toBeInTheDocument()
      expect(screen.getByText('Update Customer')).toBeInTheDocument()
    })
  })
  
  describe('CustomerDetails', () => {
    const customer = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '0412345678',
      address: '123 Main St',
      city: 'Sydney',
      state: 'NSW',
      postal_code: '2000',
      country: 'Australia',
      notes: 'Test notes',
      marketing_consent: true,
      created_at: '2023-01-01T00:00:00Z'
    }
    
    const bookings = [
      {
        id: '1',
        start_time: '2023-02-01T10:00:00Z',
        end_time: '2023-02-01T11:00:00Z',
        status: 'confirmed',
        services: {
          name: 'Face Painting',
          price: '120.00',
          color: '#6e8efb'
        }
      }
    ]
    
    const preferences = [
      {
        preference_key: 'Favorite Color',
        preference_value: 'Blue'
      }
    ]
    
    it('renders customer details with data', () => {
      render(
        <CustomerDetails 
          customer={customer} 
          bookings={bookings} 
          preferences={preferences} 
        />
      )
      
      // Check customer info
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('0412345678')).toBeInTheDocument()
      
      // Check preferences
      expect(screen.getByText('Favorite Color:')).toBeInTheDocument()
      expect(screen.getByText('Blue')).toBeInTheDocument()
      
      // Check notes
      expect(screen.getByText('Test notes')).toBeInTheDocument()
      
      // Check marketing consent
      expect(screen.getByText('Opted In')).toBeInTheDocument()
      
      // Check buttons
      expect(screen.getByText('Edit')).toBeInTheDocument()
      expect(screen.getByText('Delete')).toBeInTheDocument()
    })
  })
  
  describe('CustomerBookingHistory', () => {
    const bookings = [
      {
        id: '1',
        start_time: '2023-02-01T10:00:00Z',
        end_time: '2023-02-01T11:00:00Z',
        status: 'confirmed',
        services: {
          name: 'Face Painting',
          price: '120.00',
          color: '#6e8efb'
        }
      },
      {
        id: '2',
        start_time: '2023-01-15T14:00:00Z',
        end_time: '2023-01-15T15:00:00Z',
        status: 'canceled',
        services: {
          name: 'Airbrush Tattoo',
          price: '80.00',
          color: '#a777e3'
        }
      }
    ]
    
    it('renders booking history with data', () => {
      render(<CustomerBookingHistory bookings={bookings} />)
      
      // Check summary
      expect(screen.getByText('Total Bookings:')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      
      // Check filter buttons
      expect(screen.getByText('All')).toBeInTheDocument()
      expect(screen.getByText('Upcoming')).toBeInTheDocument()
      expect(screen.getByText('Past')).toBeInTheDocument()
      
      // Check booking items
      expect(screen.getByText('Face Painting')).toBeInTheDocument()
      expect(screen.getByText('Airbrush Tattoo')).toBeInTheDocument()
      
      // Check status badges
      expect(screen.getByText('confirmed')).toBeInTheDocument()
      expect(screen.getByText('canceled')).toBeInTheDocument()
    })
    
    it('filters bookings correctly', () => {
      render(<CustomerBookingHistory bookings={bookings} />)
      
      // Click on confirmed filter
      fireEvent.click(screen.getByText('Confirmed'))
      
      // Check that only confirmed booking is shown
      expect(screen.getByText('Face Painting')).toBeInTheDocument()
      expect(screen.queryByText('Airbrush Tattoo')).not.toBeInTheDocument()
      
      // Click on canceled filter
      fireEvent.click(screen.getByText('Canceled'))
      
      // Check that only canceled booking is shown
      expect(screen.queryByText('Face Painting')).not.toBeInTheDocument()
      expect(screen.getByText('Airbrush Tattoo')).toBeInTheDocument()
    })
  })
})
