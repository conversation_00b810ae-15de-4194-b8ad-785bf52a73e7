import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Inventory Analytics API Endpoint
 * Provides comprehensive inventory analytics and insights
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    const { range = '90d' } = req.query;
    const days = parseInt(range.replace('d', ''));
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Get inventory summary data
    const { data: inventorySummary, error: summaryError } = await supabaseAdmin
      .from('inventory_summary')
      .select('*');

    if (summaryError) {
      throw summaryError;
    }

    // Calculate key metrics
    const totalProducts = inventorySummary?.length || 0;
    const totalInventoryValue = inventorySummary?.reduce((sum, item) => sum + (item.inventory_value || 0), 0) || 0;
    const lowStockCount = inventorySummary?.filter(item => item.stock_status === 'low_stock').length || 0;
    const outOfStockCount = inventorySummary?.filter(item => item.stock_status === 'out_of_stock').length || 0;
    const inStockCount = inventorySummary?.filter(item => item.stock_status === 'in_stock').length || 0;
    const overstockedCount = inventorySummary?.filter(item => item.stock_status === 'overstocked').length || 0;

    // Get inventory alerts
    const { data: alerts, error: alertsError } = await supabaseAdmin
      .from('inventory_alerts')
      .select('*')
      .eq('is_resolved', false)
      .order('severity', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(20);

    if (alertsError) {
      console.warn('Error fetching alerts:', alertsError);
    }

    // Calculate ABC distribution
    const abcDistribution = {
      A: inventorySummary?.filter(item => item.abc_classification === 'A').length || 0,
      B: inventorySummary?.filter(item => item.abc_classification === 'B').length || 0,
      C: inventorySummary?.filter(item => item.abc_classification === 'C').length || 0,
      Unclassified: inventorySummary?.filter(item => !item.abc_classification).length || 0
    };

    // Get top performers by inventory value
    const topPerformers = inventorySummary
      ?.filter(item => item.inventory_value > 0)
      .sort((a, b) => (b.inventory_value || 0) - (a.inventory_value || 0))
      .slice(0, 10) || [];

    // Get products needing attention (low stock + high value)
    const needsAttention = inventorySummary
      ?.filter(item =>
        (item.stock_status === 'low_stock' || item.stock_status === 'out_of_stock') &&
        (item.inventory_value || 0) > 100
      )
      .sort((a, b) => (b.inventory_value || 0) - (a.inventory_value || 0))
      .slice(0, 10) || [];

    // Calculate inventory turnover (simplified - using available data)
    let averageTurnoverRatio = 0;
    try {
      // Try to get sales data for turnover calculation
      const { data: salesData } = await supabaseAdmin
        .from('order_items')
        .select(`
          product_id,
          quantity,
          orders!inner(created_at, status)
        `)
        .gte('orders.created_at', startDate.toISOString())
        .in('orders.status', ['completed', 'delivered']);

      if (salesData?.length) {
        const productSales = {};
        salesData.forEach(item => {
          productSales[item.product_id] = (productSales[item.product_id] || 0) + item.quantity;
        });

        const turnoverRatios = inventorySummary
          ?.map(product => {
            const sales = productSales[product.id] || 0;
            const avgInventory = product.quantity || 1;
            return sales / avgInventory;
          })
          .filter(ratio => ratio > 0) || [];

        averageTurnoverRatio = turnoverRatios.length > 0
          ? turnoverRatios.reduce((sum, ratio) => sum + ratio, 0) / turnoverRatios.length
          : 0;
      }
    } catch (turnoverError) {
      console.warn('Error calculating turnover:', turnoverError);
    }

    // Calculate stock level trends (simplified)
    const stockDistribution = {
      in_stock: inStockCount,
      low_stock: lowStockCount,
      out_of_stock: outOfStockCount,
      overstocked: overstockedCount
    };

    // Generate insights
    const insights = generateInventoryInsights({
      totalProducts,
      totalInventoryValue,
      lowStockCount,
      outOfStockCount,
      averageTurnoverRatio,
      abcDistribution
    });

    const analytics = {
      summary: {
        totalProducts,
        totalInventoryValue: Math.round(totalInventoryValue * 100) / 100,
        lowStockCount,
        outOfStockCount,
        inStockCount,
        overstockedCount,
        inventoryTurnoverRatio: Math.round(averageTurnoverRatio * 100) / 100,
        stockoutRate: totalProducts > 0 ? Math.round((outOfStockCount / totalProducts) * 100 * 10) / 10 : 0,
        lowStockRate: totalProducts > 0 ? Math.round((lowStockCount / totalProducts) * 100 * 10) / 10 : 0
      },
      alerts: (alerts || []).map(alert => ({
        ...alert,
        severity: alert.severity,
        type: alert.alert_type
      })),
      abcDistribution,
      stockDistribution,
      topPerformers: topPerformers.map(product => ({
        id: product.id,
        name: product.name,
        sku: product.sku,
        inventory_value: Math.round((product.inventory_value || 0) * 100) / 100,
        quantity: product.quantity,
        stock_status: product.stock_status
      })),
      needsAttention: needsAttention.map(product => ({
        id: product.id,
        name: product.name,
        sku: product.sku,
        inventory_value: Math.round((product.inventory_value || 0) * 100) / 100,
        quantity: product.quantity,
        stock_status: product.stock_status,
        low_stock_threshold: product.low_stock_threshold
      })),
      insights,
      period: {
        days,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      }
    };

    return res.status(200).json(analytics);

  } catch (error) {
    console.error('Inventory analytics error:', error);
    return res.status(500).json({
      error: 'Failed to fetch inventory analytics',
      message: error.message
    });
  }
}

/**
 * Generate actionable insights based on inventory data
 */
function generateInventoryInsights(data) {
  const insights = [];

  // Stock level insights
  if (data.outOfStockCount > 0) {
    insights.push({
      type: 'warning',
      title: 'Out of Stock Items',
      message: `${data.outOfStockCount} products are currently out of stock`,
      action: 'Review and reorder immediately',
      priority: 'high'
    });
  }

  if (data.lowStockCount > data.totalProducts * 0.2) {
    insights.push({
      type: 'warning',
      title: 'High Low Stock Rate',
      message: `${Math.round((data.lowStockCount / data.totalProducts) * 100)}% of products are low on stock`,
      action: 'Review reorder points and supplier lead times',
      priority: 'medium'
    });
  }

  // Turnover insights
  if (data.averageTurnoverRatio < 2) {
    insights.push({
      type: 'info',
      title: 'Low Inventory Turnover',
      message: `Average turnover ratio is ${data.averageTurnoverRatio.toFixed(1)}x annually`,
      action: 'Consider reducing stock levels or improving sales',
      priority: 'medium'
    });
  }

  // ABC classification insights
  if (data.abcDistribution.Unclassified > data.totalProducts * 0.5) {
    insights.push({
      type: 'info',
      title: 'ABC Classification Needed',
      message: `${data.abcDistribution.Unclassified} products need ABC classification`,
      action: 'Run ABC analysis to optimize inventory management',
      priority: 'low'
    });
  }

  // Value insights
  if (data.totalInventoryValue > 50000) {
    insights.push({
      type: 'info',
      title: 'High Inventory Value',
      message: `Total inventory value is $${data.totalInventoryValue.toLocaleString()}`,
      action: 'Monitor for optimization opportunities',
      priority: 'low'
    });
  }

  return insights;
}
