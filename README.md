## Ocean Soul Sparkles Website

This is a Next.js project for the Ocean Soul Sparkles website, designed for deployment on Vercel.

## Getting Started

First, install the dependencies:

```bash
npm install
# or
yarn install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Recent Updates

### Supabase Integration Architecture (June 2025)

Fully integrated direct Supabase client architecture for authentication and database operations. Key features include:

- Simplified architecture with direct Supabase client usage
- Improved authentication reliability and performance
- Enhanced error handling and logging
- Standardized authentication flow across the application
- Reduced complexity and improved maintainability

For detailed documentation, see `docs/authentication/README.md`

### Admin Page White Screen Issue (May 18, 2025)

Fixed an issue where the admin panel would display a white screen in development mode due to authentication problems. Key improvements include:

- Enhanced Supabase authentication management
- Fixed race conditions in authentication state handling
- Improved error handling in authentication workflows
- Optimized session management to reduce API calls
- Added development mode logging for easier troubleshooting

## Project Structure

- `pages/`: Contains all the pages of the website
- `components/`: Contains reusable components
- `styles/`: Contains global styles and CSS modules
- `public/`: Contains static assets like images and the favicon

## Images

The website requires images from the Ocean Soul Sparkles website. We've provided several ways to download and use these images:

1. Use the `image_downloader.html` file to download images directly from your browser
2. Run the `download_images.js` or `download_key_images.js` script to download images automatically
3. Create the necessary directories using `create_image_dirs.bat` (Windows) or `create_image_dirs.sh` (Mac/Linux)

For detailed instructions, see the `IMAGE_INSTRUCTIONS.md` file.

## Deployment on Vercel

The easiest way to deploy this Next.js app is to use the [Vercel Platform](https://vercel.com/new).

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket)
2. Import the project into Vercel
3. Vercel will detect that you're using Next.js and set up the build configuration for you
4. Your site will be deployed automatically

### Deployment Webhook

A Vercel deployment webhook has been configured to trigger deployments directly from external systems:

```
https://api.vercel.com/v1/integrations/deploy/prj_jxutxkTDXotGVtNxjeyCgc6V8ZKs/NdQQuIPOwE
```

This webhook is stored in `config/deployment.js` and can be used to trigger deployments programmatically.

To trigger a deployment manually:

```bash
node scripts/trigger-deployment.js
```

## Custom Domain

To use your custom domain (oceansoulsparkles.com.au):

1. Go to your Vercel project settings
2. Navigate to the "Domains" section
3. Add your domain and follow the instructions to configure DNS settings

## Environment Variables

The application requires the following environment variables:

### Supabase Configuration (Required)

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### OneSignal Configuration (Optional)

```
NEXT_PUBLIC_ONESIGNAL_APP_ID=your-onesignal-app-id
ONESIGNAL_API_KEY=your-onesignal-api-key
ONESIGNAL_REST_API_KEY=your-onesignal-rest-api-key
```

### Site Configuration

```
NEXT_PUBLIC_SITE_URL=https://oceansoulsparkles.com.au
NEXT_PUBLIC_ADMIN_URL=https://admin.oceansoulsparkles.com.au
```

To add these environment variables:

1. Create a `.env.local` file in the project root for local development
2. For production, add them in your Vercel project settings:
   - Go to your Vercel project settings
   - Navigate to the "Environment Variables" section
   - Add your variables there

## Content Management

For content management, you can:

1. Edit the code directly for static content
2. Integrate with a headless CMS like Contentful, Sanity, or Strapi for dynamic content

## Support

For any questions or issues, please contact the developer.
