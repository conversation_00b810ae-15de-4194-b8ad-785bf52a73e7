/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['www.oceansoulsparkles.com.au', 'static.wixstatic.com'],
  },
  // Configure security headers and CORS for API routes
  async headers() {
    const isProduction = process.env.NODE_ENV === 'production';

    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          // CORS Headers
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: isProduction ? 'https://www.oceansoulsparkles.com.au' : '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, X-Client-Info, X-Auth-Token, X-OneSignal-Client',
          },
          // Security Headers
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          // HSTS Header (only in production)
          ...(isProduction ? [{
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload',
          }] : []),
          // Content Security Policy
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://connect.squareup.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://cdn.onesignal.com https://onesignal.com",
              "script-src-elem 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://connect.squareup.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://cdn.onesignal.com https://onesignal.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://onesignal.com https://sandbox.web.squarecdn.com https://web.squarecdn.com",
              "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://onesignal.com https://sandbox.web.squarecdn.com https://web.squarecdn.com",
              "img-src 'self' data: https: blob:",
              "font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com https://square-fonts-production-f.squarecdn.com https://d1g145x70srn7h.cloudfront.net",
              "connect-src 'self' https://ndlgbcsbidyhxbpqzgqp.supabase.co https://connect.squareup.com https://pci-connect.squareupsandbox.com https://pci-connect.squareup.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://onesignal.com https://api.onesignal.com wss://realtime.supabase.co",
              "frame-src 'self' https://js.squareup.com https://sandbox.web.squarecdn.com https://web.squarecdn.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; '),
          },
        ],
      },
    ];
  },
  // Force HTTPS redirects in production
  async redirects() {
    const isProduction = process.env.NODE_ENV === 'production';

    if (!isProduction) {
      return [];
    }

    return [
      // Redirect HTTP to HTTPS
      {
        source: '/(.*)',
        has: [
          {
            type: 'header',
            key: 'x-forwarded-proto',
            value: 'http',
          },
        ],
        destination: 'https://www.oceansoulsparkles.com.au/$1',
        permanent: true,
      },
      // Redirect non-www to www
      {
        source: '/(.*)',
        has: [
          {
            type: 'host',
            value: 'oceansoulsparkles.com.au',
          },
        ],
        destination: 'https://www.oceansoulsparkles.com.au/$1',
        permanent: true,
      },
    ];
  },
  compiler: {
    // Enable all experimental features
    styledComponents: true, // Enable CSS-in-JS
    emotion: true,
  },
  webpack: (config, { isServer }) => {
    // Add support for importing SVGs as React components
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    if (!isServer) {
      // Don't attempt to import Node.js built-in modules on the client side
      config.resolve.fallback = {
        fs: false,
        path: false,
        util: false,
        crypto: false,
      };
    }

    return config;
  },
}

export default nextConfig;
