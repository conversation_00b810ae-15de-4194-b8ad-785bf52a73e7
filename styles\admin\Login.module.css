.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  background-image: url('/background.png');
  background-size: cover;
  background-position: center;
  padding: 20px;
}

.loginCard {
  width: 100%;
  max-width: 400px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.logoContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.logo {
  max-width: 150px;
  height: auto;
}

.title {
  text-align: center;
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
}

.error {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  text-align: center;
  color: #6a0dad;
}

.spinner {
  width: 40px;
  height: 40px;
  margin: 15px auto;
  border: 4px solid rgba(106, 13, 173, 0.2);
  border-top-color: #6a0dad;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.formGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.input:focus {
  border-color: #6a0dad;
  outline: none;
}

.forgotPassword {
  text-align: right;
  font-size: 14px;
}

.forgotPassword a, .forgotPassword .link {
  color: #6a0dad;
  text-decoration: none;
}

.forgotPassword a:hover, .forgotPassword .link:hover {
  text-decoration: underline;
}

.loginButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.loginButton:hover {
  background-color: #5a0b8d;
}

.loginButton:disabled {
  background-color: #9c7fb3;
  cursor: not-allowed;
}

.backToSite {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
}

.backToSite a, .backToSite .link {
  color: #6a0dad;
  text-decoration: none;
}

.backToSite a:hover, .backToSite .link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .loginCard {
    padding: 20px;
  }

  .title {
    font-size: 20px;
  }

  .input {
    font-size: 14px;
  }

  .loginButton {
    font-size: 14px;
  }
}
