import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import BookingForm from '@/components/admin/BookingForm';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/BookingDetailPage.module.css';

export default function NewBookingPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [initialSlot, setInitialSlot] = useState(null);

  // Extract query parameters for pre-filling the form
  useEffect(() => {
    const { customer_id, start_time, end_time } = router.query;
    
    if (start_time || end_time || customer_id) {
      const slot = {};
      
      if (start_time) {
        try {
          slot.start = new Date(decodeURIComponent(start_time));
        } catch (error) {
          console.error('Error parsing start_time:', error);
        }
      }
      
      if (end_time) {
        try {
          slot.end = new Date(decodeURIComponent(end_time));
        } catch (error) {
          console.error('Error parsing end_time:', error);
        }
      }
      
      if (customer_id) {
        slot.customerId = customer_id;
      }
      
      setInitialSlot(slot);
    }
  }, [router.query]);

  // Handle successful booking creation
  const handleBookingSaved = (booking) => {
    toast.success('Booking created successfully!');
    // Navigate to the booking details page
    router.push(`/admin/bookings/${booking.id}`);
  };

  // Handle cancellation
  const handleCancel = () => {
    // Navigate back to bookings list
    router.push('/admin/bookings');
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Create New Booking">
        <div className={styles.bookingDetailPage}>
          <div className={styles.header}>
            <h2>Create New Booking</h2>
            <button 
              onClick={handleCancel}
              className={styles.backButton}
            >
              ← Back to Bookings
            </button>
          </div>

          <div className={styles.content}>
            <BookingForm
              booking={null}
              initialSlot={initialSlot}
              onSave={handleBookingSaved}
              onCancel={handleCancel}
            />
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
