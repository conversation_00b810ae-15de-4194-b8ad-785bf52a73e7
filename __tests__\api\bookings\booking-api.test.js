/**
 * Booking API Endpoints Integration Tests
 * 
 * Tests all booking-related API endpoints for:
 * - Public booking creation
 * - Admin booking management
 * - Error handling and validation
 * - Response formats and status codes
 */

import { createMocks } from 'node-mocks-http';
import bookingsHandler from '../../../pages/api/bookings/index';
import publicBookingsHandler from '../../../pages/api/public/bookings';
import adminBookingsHandler from '../../../pages/api/admin/bookings/index';

// Mock Supabase
jest.mock('../../../lib/supabase', () => ({
  __esModule: true,
  default: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn()
        })),
        limit: jest.fn()
      })),
      insert: jest.fn(() => ({
        select: jest.fn()
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn()
        }))
      })),
      delete: jest.fn(() => ({
        eq: jest.fn()
      }))
    }))
  },
  getAdminClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn()
        })),
        limit: jest.fn()
      })),
      insert: jest.fn(() => ({
        select: jest.fn()
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn()
        }))
      })),
      delete: jest.fn(() => ({
        eq: jest.fn()
      }))
    }))
  }))
}));

// Test data
const validBookingData = {
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+61400000000',
  date: '2024-12-25',
  time: '10:00',
  location: 'Test Location',
  message: 'Test booking message',
  service: {
    id: 'test-service-id',
    name: 'Face Painting',
    bookingType: 'Instant Book'
  },
  option: {
    hours: 2,
    price: 100
  },
  marketingConsent: true
};

const validAdminBookingData = {
  customer_id: 'test-customer-id',
  service_id: 'test-service-id',
  start_time: '2024-12-25T10:00:00Z',
  end_time: '2024-12-25T12:00:00Z',
  status: 'confirmed',
  location: 'Test Location',
  notes: 'Test booking'
};

describe('Booking API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Public Booking API (/api/bookings)', () => {
    test('should create booking with valid data', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validBookingData
      });

      // Mock successful database operations
      const mockSupabase = require('../../../lib/supabase').default;
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' } // Customer not found
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [{ id: 'new-customer-id', ...validBookingData }],
            error: null
          })
        })
      });

      await bookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(201);
      const responseData = JSON.parse(res._getData());
      expect(responseData.success).toBe(true);
    });

    test('should reject booking with missing required fields', async () => {
      const invalidData = { ...validBookingData };
      delete invalidData.name;
      delete invalidData.email;

      const { req, res } = createMocks({
        method: 'POST',
        body: invalidData
      });

      await bookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(400);
      const responseData = JSON.parse(res._getData());
      expect(responseData.error).toBe('Missing required fields');
    });

    test('should handle database errors gracefully', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validBookingData
      });

      // Mock database error
      const mockSupabase = require('../../../lib/supabase').default;
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockRejectedValue(new Error('Database connection failed'))
          })
        })
      });

      await bookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(500);
      const responseData = JSON.parse(res._getData());
      expect(responseData.error).toBeDefined();
    });

    test('should only accept POST requests', async () => {
      const { req, res } = createMocks({
        method: 'GET'
      });

      await bookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(405);
    });
  });

  describe('Public Booking API Alternative (/api/public/bookings)', () => {
    test('should create booking with customer ID', async () => {
      const dataWithCustomerId = {
        ...validBookingData,
        customerId: 'existing-customer-id'
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: dataWithCustomerId
      });

      // Mock successful operations
      const { getAdminClient } = require('../../../lib/supabase');
      const mockAdminClient = getAdminClient();
      mockAdminClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'test-service-id', name: 'Test Service' },
              error: null
            })
          })
        }),
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [{ id: 'new-booking-id', ...validAdminBookingData }],
            error: null
          })
        })
      });

      await publicBookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(201);
    });

    test('should validate service exists', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validBookingData
      });

      // Mock service not found
      const { getAdminClient } = require('../../../lib/supabase');
      const mockAdminClient = getAdminClient();
      mockAdminClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' }
            })
          })
        })
      });

      await publicBookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(400);
      const responseData = JSON.parse(res._getData());
      expect(responseData.error).toContain('Service not found');
    });
  });

  describe('Admin Booking API (/api/admin/bookings)', () => {
    test('should get all bookings', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        headers: {
          authorization: 'Bearer valid-admin-token'
        }
      });

      // Mock admin client
      const { getAdminClient } = require('../../../lib/supabase');
      const mockAdminClient = getAdminClient();
      mockAdminClient.from.mockReturnValue({
        select: jest.fn().mockResolvedValue({
          data: [
            { id: 'booking-1', status: 'confirmed' },
            { id: 'booking-2', status: 'pending' }
          ],
          error: null
        })
      });

      await adminBookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const responseData = JSON.parse(res._getData());
      expect(responseData.bookings).toHaveLength(2);
    });

    test('should create booking via admin API', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          authorization: 'Bearer valid-admin-token'
        },
        body: validAdminBookingData
      });

      // Mock successful creation
      const { getAdminClient } = require('../../../lib/supabase');
      const mockAdminClient = getAdminClient();
      mockAdminClient.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [{ id: 'new-booking-id', ...validAdminBookingData }],
            error: null
          })
        })
      });

      await adminBookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(201);
      const responseData = JSON.parse(res._getData());
      expect(responseData.booking.id).toBe('new-booking-id');
    });

    test('should validate required fields for admin booking creation', async () => {
      const invalidData = { ...validAdminBookingData };
      delete invalidData.customer_id;
      delete invalidData.service_id;

      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          authorization: 'Bearer valid-admin-token'
        },
        body: invalidData
      });

      await adminBookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(400);
      const responseData = JSON.parse(res._getData());
      expect(responseData.error).toBe('Missing required fields');
    });

    test('should require authentication for admin endpoints', async () => {
      const { req, res } = createMocks({
        method: 'GET'
        // No authorization header
      });

      await adminBookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(401);
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed JSON', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: 'invalid-json'
      });

      await bookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(400);
    });

    test('should handle database timeout', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: validBookingData
      });

      // Mock timeout error
      const mockSupabase = require('../../../lib/supabase').default;
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockRejectedValue(new Error('Request timeout'))
          })
        })
      });

      await bookingsHandler(req, res);

      expect(res._getStatusCode()).toBe(500);
    });
  });
});
