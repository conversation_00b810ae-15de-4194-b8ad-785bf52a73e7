-- Create a view to expose user profile information
-- This view makes it easier to join with auth.users data

-- First, check if the view already exists and drop it if it does
DROP VIEW IF EXISTS public.user_profiles;

-- Create the view
CREATE VIEW public.user_profiles AS
SELECT 
  au.id,
  au.email,
  au.raw_user_meta_data->>'name' as display_name,
  au.created_at,
  au.last_sign_in_at,
  ur.role
FROM 
  auth.users au
LEFT JOIN 
  public.user_roles ur ON au.id = ur.id;

-- Enable RLS on the view
ALTER VIEW public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for the view
CREATE POLICY "Users can view their own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Ad<PERSON> can view all profiles" ON public.user_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Grant permissions
GRANT SELECT ON public.user_profiles TO authenticated;
GRANT SELECT ON public.user_profiles TO service_role;
