.bookingHistory {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.bookingActions {
  display: flex;
  gap: 8px;
}

.newBookingButton {
  display: inline-block;
  padding: 6px 12px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.newBookingButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.statCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: #6e8efb;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
}

.additionalStats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  padding: 16px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 200px;
}

.statItemLabel {
  font-size: 0.9rem;
  color: #666;
}

.statItemValue {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.controlsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sortControl {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sortLabel {
  font-size: 0.9rem;
  color: #555;
}

.sortSelect {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sortSelect:hover {
  background-color: #e5e5e5;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.loading {
  text-align: center;
  padding: 32px;
  color: #666;
  font-size: 1rem;
}

.filterButton {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterButton:hover {
  background-color: #e5e5e5;
}

.filterButton.active {
  background-color: #6e8efb;
  color: white;
  border-color: #6e8efb;
}

.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bookingItem {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.serviceInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.serviceColor {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #6e8efb;
}

.serviceName {
  font-weight: 500;
  color: #333;
}

.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.confirmed {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.canceled {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
}

.inProgress {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.completed {
  background-color: rgba(0, 150, 136, 0.1);
  color: #009688;
}

.noShow {
  background-color: rgba(96, 125, 139, 0.1);
  color: #607d8b;
}

.rescheduled {
  background-color: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}

.bookingDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.bookingTime, .bookingLocation, .bookingPrice {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.9rem;
}

.bookingNotes {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #555;
}

.bookingActions {
  display: flex;
  justify-content: flex-end;
}

.viewButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: #6e8efb;
  color: white;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.noBookings {
  text-align: center;
  padding: 32px;
  color: #666;
  font-size: 1rem;
  background-color: #f9f9f9;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .controlsRow {
    flex-direction: column;
    align-items: flex-start;
  }

  .filters {
    justify-content: flex-start;
    margin-bottom: 12px;
  }

  .sortControl {
    width: 100%;
  }

  .sortSelect {
    flex-grow: 1;
  }

  .bookingHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .status {
    align-self: flex-start;
  }

  .additionalStats {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }
}
