import Head from 'next/head'
import { useState, useEffect, useCallback, useMemo } from 'react'
import Link from 'next/link'
import styles from '@/styles/Gallery.module.css'
import Layout from '@/components/Layout'
import AnimatedSection from '@/components/AnimatedSection'
import { progressiveImageLoad, getLoadingStrategy } from '@/lib/image-optimization'
import { usePerformanceMonitor } from '@/lib/performance-monitor'

export default function Gallery() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [selectedService, setSelectedService] = useState(null);
  const [imageLoadStates, setImageLoadStates] = useState({});
  const [imageErrors, setImageErrors] = useState(new Set());
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  // API data state
  const [apiGalleryData, setApiGalleryData] = useState([]);
  const [apiCategories, setApiCategories] = useState([]);
  const [apiLoading, setApiLoading] = useState(true);
  const [apiError, setApiError] = useState(null);

  // Performance monitoring
  const { trackImageLoad, imageLoaded, imageError: trackImageError, getStats } = usePerformanceMonitor('Gallery');

  // Gallery categories (memoized to prevent re-renders)
  const categories = useMemo(() => [
    { id: 'all', name: 'All' },
    { id: 'face', name: 'Face Art' },
    { id: 'body', name: 'Body Art' },
    { id: 'glitter', name: 'Glitter & Sparkles' },
    { id: 'uv', name: 'UV Art' },
    { id: 'kids', name: 'Kids Designs' },
    { id: 'events', name: 'Events' }
  ], []);

  // Fetch gallery data from API
  const fetchGalleryData = useCallback(async () => {
    try {
      setApiLoading(true);
      setApiError(null);

      const response = await fetch('/api/public/gallery?include_categories=true', {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch gallery data: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setApiGalleryData(data.data);
        if (data.categories) {
          setApiCategories([
            { id: 'all', name: 'All' },
            ...data.categories.map(cat => ({ id: cat.slug, name: cat.name }))
          ]);
        } else {
          // Use default categories if none provided
          setApiCategories(categories);
        }
        console.log('✅ Using database gallery data:', data.data.length, 'items');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching gallery data:', error);
      setApiError(error.message);
      setApiGalleryData([]);
      setApiCategories(categories);
    } finally {
      setApiLoading(false);
    }
  }, [categories]);

  // Fetch gallery data on component mount
  useEffect(() => {
    fetchGalleryData();
  }, [fetchGalleryData]);

  // Get current gallery data (only from API)
  const currentGalleryData = useMemo(() => {
    return apiGalleryData;
  }, [apiGalleryData]);

  // Get current categories (API or default)
  const currentCategories = useMemo(() => {
    return apiCategories.length > 0 ? apiCategories : categories;
  }, [apiCategories, categories]);

  // Optimized image preloading with performance monitoring
  const preloadImages = useCallback(async () => {
    if (imagesPreloaded || currentGalleryData.length === 0) return;

    try {
      const loadingStrategy = getLoadingStrategy();
      const imageUrls = currentGalleryData.map(item => item.mainImage);

      console.log('🖼️ Starting progressive image loading with strategy:', loadingStrategy);
      console.log('📊 Loading', imageUrls.length, 'images from database');

      // Track all images for performance monitoring
      currentGalleryData.forEach(item => {
        trackImageLoad(item.id, item.mainImage);
      });

      const results = await progressiveImageLoad(imageUrls, {
        batchSize: loadingStrategy.batchSize,
        batchDelay: 50,
        onProgress: (loaded, total) => {
          console.log(`📊 Image loading progress: ${loaded}/${total}`);
        },
        onBatchComplete: (batchIndex, totalBatches, batchResults) => {
          // Update states for completed batch
          batchResults.forEach((result, index) => {
            const globalIndex = (batchIndex - 1) * loadingStrategy.batchSize + index;
            const item = currentGalleryData[globalIndex];

            if (item) {
              if (result.status === 'loaded') {
                setImageLoadStates(prev => ({
                  ...prev,
                  [item.id]: 'loaded'
                }));
                imageLoaded(item.id, result.dimensions);
              } else {
                setImageErrors(prev => new Set([...prev, item.id]));
                setImageLoadStates(prev => ({
                  ...prev,
                  [item.id]: 'error'
                }));
                trackImageError(item.id, result.status);
              }
            }
          });
        }
      });

      setImagesPreloaded(true);
      console.log('✅ Image preloading completed:', getStats());
    } catch (error) {
      console.warn('Error preloading images:', error);
      setImagesPreloaded(true);
    }
  }, [imagesPreloaded, currentGalleryData, trackImageLoad, imageLoaded, trackImageError, getStats]);

  // Handle image loading success
  const handleImageLoad = useCallback((imageId) => {
    setImageLoadStates(prev => ({
      ...prev,
      [imageId]: 'loaded'
    }));
  }, []);

  // Handle image loading error
  const handleImageError = useCallback((imageId, imageSrc) => {
    console.warn(`Failed to load image: ${imageSrc}`);
    setImageErrors(prev => new Set([...prev, imageId]));
    setImageLoadStates(prev => ({
      ...prev,
      [imageId]: 'error'
    }));
  }, []);

  // Filter gallery items based on active category
  const filteredGallery = useMemo(() => {
    return activeCategory === 'all'
      ? currentGalleryData
      : currentGalleryData.filter(item => item.category === activeCategory);
  }, [activeCategory, currentGalleryData]);

  // Handle clicking on a gallery item to open lightbox
  const handleGalleryItemClick = (item) => {
    setSelectedService(item);
  };

  // Handle closing the lightbox
  const closeLightbox = () => {
    setSelectedService(null);
  };

  // Preload images when data is available
  useEffect(() => {
    if (!apiLoading && currentGalleryData.length > 0) {
      const timer = setTimeout(() => {
        preloadImages();
      }, 100); // Small delay to allow component to mount

      return () => clearTimeout(timer);
    }
  }, [apiLoading, currentGalleryData.length, preloadImages]);

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedService) return;

      if (e.key === 'Escape') {
        closeLightbox();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedService]);

  return (
    <Layout>
      <Head>
        <title>Gallery | OceanSoulSparkles</title>
        <meta name="description" content="Explore our magical gallery of face painting, festival makeup, airbrush art, braiding, and glitter designs. Get inspired for your next event with OceanSoulSparkles." />
        <meta name="google-site-verification" content="HtjqFmAXzFBvlS4IEWJe8iyN_UK4dROGIIt_oY-i1Ag" />
      </Head>

      <main className={styles.main}>
        {/* Gallery Hero Section */}
        <section className={styles.galleryHero}>
          <AnimatedSection animation="fade-in">
            <h1 className={styles.galleryTitle}>Our Magical Gallery</h1>
            <p className={styles.gallerySubtitle}>
              Browse through our collection of creative designs and get inspired for your next sparkly adventure
            </p>
          </AnimatedSection>

          {/* Category Filters */}
          <div className={styles.categoryFilters}>
            {currentCategories.map((category) => (
              <button
                key={category.id}
                className={`${styles.filterButton} ${activeCategory === category.id ? styles.activeFilter : ''}`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </section>

        {/* Loading State */}
        {apiLoading && (
          <section className={styles.loadingSection}>
            <div className={styles.loadingSpinner}>
              <div className={styles.spinner}></div>
            </div>
            <p>Loading gallery...</p>
          </section>
        )}

        {/* Error State */}
        {apiError && !apiLoading && (
          <section className={styles.errorSection}>
            <h2>Unable to load gallery</h2>
            <p>We're having trouble loading the gallery. Please try refreshing the page.</p>
            <button onClick={fetchGalleryData} className={styles.retryButton}>
              Try Again
            </button>
          </section>
        )}

        {/* Gallery Grid */}
        {!apiLoading && !apiError && (
          <section className={styles.galleryGrid}>
            {filteredGallery.length === 0 ? (
              <div className={styles.emptyState}>
                <h3>No images found</h3>
                <p>There are no images in this category yet.</p>
              </div>
            ) : (
              filteredGallery.map((item, index) => {
            const imageState = imageLoadStates[item.id] || 'loading';
            const hasError = imageErrors.has(item.id);
            const isPreloaded = index < 6; // First 6 images are preloaded

            return (
              <AnimatedSection
                key={item.id}
                animation="fade-in"
                className={styles.galleryItem}
                onClick={() => handleGalleryItemClick(item)}
              >
                <div className={styles.galleryImageContainer}>
                  {!hasError ? (
                    <>
                      <img
                        src={item.mainImage}
                        alt={item.title}
                        className={`${styles.galleryImage} ${imageState === 'loaded' ? styles.loaded : styles.loading}`}
                        onLoad={() => handleImageLoad(item.id)}
                        onError={() => handleImageError(item.id, item.mainImage)}
                        loading={isPreloaded ? "eager" : "lazy"}
                        decoding="async"
                        style={{
                          opacity: imageState === 'loaded' ? 1 : 0,
                          transition: 'opacity 0.3s ease'
                        }}
                      />
                      {imageState === 'loading' && (
                        <div className={styles.imageLoader}>
                          <div className={styles.spinner}></div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className={styles.imagePlaceholder}>
                      <span>Image unavailable</span>
                    </div>
                  )}

                  {imageState === 'loaded' && !hasError && (
                    <div className={styles.galleryOverlay}>
                      <span className={styles.viewMore}>View Larger</span>
                    </div>
                  )}
                </div>
                <h3 className={styles.galleryItemTitle}>{item.title}</h3>
              </AnimatedSection>
              );
              })
            )}
          </section>
        )}

        {/* Lightbox Only - No Related Images Section */}
        {selectedService && (
          <div className={styles.lightbox} onClick={closeLightbox}>
            <div className={styles.lightboxContent} onClick={(e) => e.stopPropagation()}>
              {(() => {
                const lightboxImageId = `lightbox-${selectedService.id}`;
                const lightboxImageState = imageLoadStates[lightboxImageId] || 'loading';
                const lightboxHasError = imageErrors.has(lightboxImageId);

                return (
                  <>
                    {!lightboxHasError ? (
                      <>
                        <img
                          src={selectedService.mainImage}
                          alt={selectedService.title}
                          className={styles.lightboxImage}
                          onLoad={() => handleImageLoad(lightboxImageId)}
                          onError={() => handleImageError(lightboxImageId, selectedService.mainImage)}
                          style={{ opacity: lightboxImageState === 'loaded' ? 1 : 0 }}
                        />
                        {lightboxImageState === 'loading' && (
                          <div className={styles.lightboxLoader}>
                            <div className={styles.spinner}></div>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className={styles.lightboxError}>
                        <span>Image could not be loaded</span>
                      </div>
                    )}
                  </>
                );
              })()}

              <p className={styles.lightboxCaption}>{selectedService.title}</p>

              <button className={styles.closeButton} onClick={closeLightbox}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <section className={styles.galleryCta}>
          <AnimatedSection animation="fade-up">
            <h2>Ready to bring these magical designs to your event?</h2>
            <p>From face painting to glitter art, we can create the perfect look for your special occasion.</p>
            <div className={styles.ctaButtons}>
              <Link href="/book-online" className={styles.primaryButton}>
                Book Online
              </Link>
              <Link href="/services" className={styles.secondaryButton}>
                View Services
              </Link>
              <Link href="/contact" className={styles.secondaryButton}>
                Contact Us
              </Link>
            </div>
          </AnimatedSection>
        </section>


      </main>
    </Layout>
  )
}
