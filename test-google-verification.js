/**
 * Test script for Google Search Console verification implementation
 *
 * This script tests:
 * 1. Setting a Google verification code in admin settings
 * 2. Verifying the meta tag appears on public pages
 * 3. Verifying the meta tag does NOT appear on admin pages (security isolation)
 */

const BASE_URL = 'http://localhost:3002';

// Test verification code (example from Google Search Console)
const TEST_VERIFICATION_CODE = 'HtjqFmAXzFBvlS4lE';

/**
 * Test setting Google verification code via admin API
 */
async function testSetVerificationCode() {
  console.log('\n🔧 Testing: Setting Google verification code...\n');

  try {
    // First, get current settings
    const getResponse = await fetch(`${BASE_URL}/api/admin/settings`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include'
    });

    if (!getResponse.ok) {
      console.log('❌ Failed to fetch current settings:', getResponse.status);
      return false;
    }

    const currentData = await getResponse.json();
    console.log('✅ Current settings fetched successfully');

    // Update settings with verification code
    const updatedSettings = {
      ...currentData.settings,
      google_search_console_verification: TEST_VERIFICATION_CODE
    };

    const putResponse = await fetch(`${BASE_URL}/api/admin/settings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ settings: updatedSettings })
    });

    if (putResponse.ok) {
      console.log('✅ Google verification code set successfully');
      return true;
    } else {
      console.log('❌ Failed to set verification code:', putResponse.status);
      return false;
    }

  } catch (error) {
    console.log('❌ Error setting verification code:', error.message);
    return false;
  }
}

/**
 * Test if verification meta tag appears on public pages
 */
async function testPublicPageVerification() {
  console.log('\n🌐 Testing: Verification meta tag on public pages...\n');

  const publicPages = ['/', '/about', '/services', '/gallery', '/shop'];

  for (const page of publicPages) {
    try {
      const response = await fetch(`${BASE_URL}${page}`);

      if (response.ok) {
        const html = await response.text();

        // Check if the verification meta tag is present
        const hasVerificationTag = html.includes(`<meta name="google-site-verification" content="${TEST_VERIFICATION_CODE}"`);

        if (hasVerificationTag) {
          console.log(`✅ ${page}: Verification meta tag found`);
        } else {
          console.log(`❌ ${page}: Verification meta tag NOT found`);
        }
      } else {
        console.log(`❌ ${page}: Failed to load page (${response.status})`);
      }

    } catch (error) {
      console.log(`❌ ${page}: Error - ${error.message}`);
    }
  }
}

/**
 * Test if verification meta tag does NOT appear on admin pages (security isolation)
 */
async function testAdminPageSecurity() {
  console.log('\n🔒 Testing: Security isolation on admin pages...\n');

  const adminPages = ['/admin', '/admin/settings', '/admin/bookings', '/admin/customers'];

  for (const page of adminPages) {
    try {
      const response = await fetch(`${BASE_URL}${page}`);

      if (response.ok) {
        const html = await response.text();

        // Check if the verification meta tag is present (it should NOT be)
        const hasVerificationTag = html.includes(`<meta name="google-site-verification" content="${TEST_VERIFICATION_CODE}"`);

        if (!hasVerificationTag) {
          console.log(`✅ ${page}: Verification meta tag correctly EXCLUDED (security isolation working)`);
        } else {
          console.log(`❌ ${page}: Verification meta tag found (SECURITY ISSUE - should be excluded)`);
        }
      } else {
        console.log(`⚠️  ${page}: Page not accessible (${response.status}) - may require authentication`);
      }

    } catch (error) {
      console.log(`❌ ${page}: Error - ${error.message}`);
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Google Search Console Verification Tests...\n');
  console.log('=' * 60);

  // Test 1: Set verification code
  const codeSet = await testSetVerificationCode();

  if (codeSet) {
    // Wait a moment for the change to propagate
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 2: Check public pages
    await testPublicPageVerification();

    // Test 3: Check admin pages (security)
    await testAdminPageSecurity();
  } else {
    console.log('\n❌ Cannot proceed with verification tests - failed to set verification code');
  }

  console.log('\n' + '=' * 60);
  console.log('🏁 Google verification tests completed!');
  console.log('\nNext steps:');
  console.log('1. Manually check page source of public pages for the meta tag');
  console.log('2. Verify admin pages do not contain the verification meta tag');
  console.log('3. Test with Google Search Console verification');
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment - need to import fetch
  const fetch = require('node-fetch');
  global.fetch = fetch;
  runAllTests().catch(console.error);
} else {
  // Browser environment
  window.runGoogleVerificationTests = runAllTests;
  console.log('Google verification test functions loaded. Run: runGoogleVerificationTests()');
}
