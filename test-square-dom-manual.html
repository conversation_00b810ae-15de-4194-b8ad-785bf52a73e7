<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Square DOM Cleanup Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4ECDC4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45b7b8;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .error-log {
            background: #ffe6e6;
            border: 1px solid #ff9999;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success-log {
            background: #e6ffe6;
            border: 1px solid #99ff99;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .console-log {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.warn { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🧪 Square Payment DOM Cleanup Test</h1>
    
    <div class="test-container">
        <h2>Manual DOM Cleanup Test</h2>
        <p>This test helps verify that the Square payment component DOM cleanup fixes are working correctly.</p>
        
        <div>
            <button class="test-button" onclick="startTest()">Start DOM Test</button>
            <button class="test-button" onclick="clearLogs()">Clear Logs</button>
            <button class="test-button" onclick="openPOSPage()">Open POS Page</button>
        </div>
        
        <div id="testStatus"></div>
        <div id="errorLog"></div>
        <div id="consoleLog"></div>
    </div>
    
    <div class="test-container">
        <h2>Test Instructions</h2>
        <ol>
            <li><strong>Open POS Page:</strong> Click "Open POS Page" to navigate to the POS terminal</li>
            <li><strong>Complete Flow:</strong> Select a service → Choose tier → Enter/skip customer info → Select "Card Payment"</li>
            <li><strong>Watch Console:</strong> Open browser DevTools (F12) and monitor the Console tab</li>
            <li><strong>Test Unmounting:</strong> Click "Back" button to unmount the Square component</li>
            <li><strong>Test Remounting:</strong> Select "Card Payment" again to remount the component</li>
            <li><strong>Check for Errors:</strong> Look for any "NotFoundError" or "removeChild" errors in console</li>
        </ol>
        
        <h3>Expected Behavior (Fixed):</h3>
        <ul>
            <li>✅ Square payment form loads without errors</li>
            <li>✅ Component unmounts without "NotFoundError: Failed to execute 'removeChild'" errors</li>
            <li>✅ Component can be remounted successfully</li>
            <li>✅ Console shows "Square form instance destroyed successfully" messages</li>
            <li>✅ Console shows "Square metadata cleared safely" messages</li>
        </ul>
        
        <h3>Previous Behavior (Before Fix):</h3>
        <ul>
            <li>❌ "NotFoundError: Failed to execute 'removeChild' on 'Node'" errors</li>
            <li>❌ React DOM manipulation conflicts</li>
            <li>❌ Component cleanup interference</li>
        </ul>
    </div>
    
    <div class="test-container">
        <h2>Automated Error Detection</h2>
        <p>This section will automatically detect and log DOM-related errors:</p>
        <div id="errorDetection">
            <div class="status warn">Monitoring for DOM errors...</div>
        </div>
    </div>

    <script>
        let errorCount = 0;
        let testStartTime = null;
        let consoleMessages = [];
        
        // Override console methods to capture messages
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        function captureConsoleMessage(type, args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            
            consoleMessages.push({
                type,
                message,
                timestamp: new Date().toISOString()
            });
            
            updateConsoleLog();
            
            // Check for DOM errors
            if (message.includes('NotFoundError') || 
                message.includes('removeChild') || 
                message.includes('Node to be removed is not a child')) {
                logError(`DOM Error Detected: ${message}`);
            }
            
            // Call original console method
            originalConsole[type].apply(console, args);
        }
        
        console.log = (...args) => captureConsoleMessage('log', args);
        console.warn = (...args) => captureConsoleMessage('warn', args);
        console.error = (...args) => captureConsoleMessage('error', args);
        
        // Capture unhandled errors
        window.addEventListener('error', (event) => {
            const message = `${event.error?.name}: ${event.error?.message}`;
            if (message.includes('NotFoundError') || message.includes('removeChild')) {
                logError(`Unhandled DOM Error: ${message}`);
            }
        });
        
        function startTest() {
            testStartTime = Date.now();
            errorCount = 0;
            consoleMessages = [];
            
            updateStatus('Test started. Monitor console for DOM errors...', 'warn');
            updateConsoleLog();
            
            // Set up periodic error checking
            const checkInterval = setInterval(() => {
                if (Date.now() - testStartTime > 60000) { // Stop after 1 minute
                    clearInterval(checkInterval);
                    finishTest();
                }
            }, 1000);
        }
        
        function finishTest() {
            const domErrors = consoleMessages.filter(msg => 
                msg.message.includes('NotFoundError') || 
                msg.message.includes('removeChild') ||
                msg.message.includes('Node to be removed')
            );
            
            if (domErrors.length === 0) {
                updateStatus('✅ Test completed successfully! No DOM errors detected.', 'pass');
            } else {
                updateStatus(`❌ Test completed with ${domErrors.length} DOM errors detected.`, 'fail');
                domErrors.forEach(error => logError(error.message));
            }
        }
        
        function logError(message) {
            errorCount++;
            const errorLog = document.getElementById('errorLog');
            if (!errorLog.innerHTML.includes('error-log')) {
                errorLog.innerHTML = '<div class="error-log"><strong>DOM Errors Detected:</strong><br></div>';
            }
            errorLog.querySelector('.error-log').innerHTML += `${errorCount}. ${message}<br>`;
        }
        
        function updateStatus(message, type) {
            document.getElementById('testStatus').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }
        
        function updateConsoleLog() {
            const consoleLog = document.getElementById('consoleLog');
            const recentMessages = consoleMessages.slice(-20); // Show last 20 messages
            
            consoleLog.innerHTML = '<div class="console-log"><strong>Recent Console Messages:</strong><br>' +
                recentMessages.map(msg => 
                    `<span style="color: ${getMessageColor(msg.type)}">[${msg.type}] ${msg.message}</span>`
                ).join('<br>') + '</div>';
        }
        
        function getMessageColor(type) {
            switch(type) {
                case 'error': return '#d32f2f';
                case 'warn': return '#f57c00';
                default: return '#333';
            }
        }
        
        function clearLogs() {
            consoleMessages = [];
            document.getElementById('errorLog').innerHTML = '';
            document.getElementById('consoleLog').innerHTML = '';
            document.getElementById('testStatus').innerHTML = '';
            errorCount = 0;
        }
        
        function openPOSPage() {
            window.open('/admin/pos', '_blank');
        }
        
        // Initialize
        updateStatus('Ready to test. Click "Start DOM Test" to begin monitoring.', 'warn');
    </script>
</body>
</html>
