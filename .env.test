# Supabase configuration for testing
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=your-service-api-key

# API URL for testing (use localhost for local testing)
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# OneSignal Configuration
NEXT_PUBLIC_ONESIGNAL_APP_ID=************************************
NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID=web.onesignal.auto.************************************
ONESIGNAL_REST_API_KEY=your-rest-api-key

# Payment gateway configuration (use test credentials)
PAYPAL_CLIENT_ID=your-test-paypal-client-id
PAYPAL_CLIENT_SECRET=your-test-paypal-client-secret
SQUARE_APPLICATION_ID=your-test-square-application-id
SQUARE_ACCESS_TOKEN=your-test-square-access-token