.main {
  width: 100%;
  margin: 0 auto;
  background-image: url('/background.png'); /* Added pink cloud background */
  background-size: cover; /* Ensure background covers the area */
  background-position: center; /* Center the background image */
  background-attachment: fixed; /* Keep background fixed during scroll */
}

/* Hero section */
.hero {
  text-align: center;
  padding: 5rem 2rem;
  background-color: var(--background-color);
  background-image: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('/images/about-hero.jpg');
  background-size: cover;
  background-position: center;
  margin-bottom: 3rem;
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: 3rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.description {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto;
}

/* Story section */
.storySection {
  max-width: var(--max-width);
  margin: 0 auto 4rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.7); /* Translucent white */
  border-radius: 12px;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.storyContent {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  align-items: center;
}

.storyText {
  flex: 2;
  min-width: 300px;
}

.storyText h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.storyText p {
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.storyImage {
  flex: 1;
  min-width: 300px;
  height: 400px;
}

.imagePlaceholder {
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.7;
  border-radius: 8px;
}

.storyImg, .founderImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* Values section */
.valuesSection {
  background-color: #f8f8f8;
  padding: 4rem 2rem;
  margin-bottom: 4rem;
}

.valuesSection h2 {
  text-align: center;
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 3rem;
}

.valueGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

.valueCard {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.valueCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.valueCard h3 {
  font-size: 1.3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.valueCard p {
  color: var(--light-text-color);
  line-height: 1.6;
}

/* Founder section */
.founderSection {
  max-width: var(--max-width);
  margin: 0 auto 4rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.7); /* Translucent white */
  border-radius: 12px;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.founderSection h2 {
  text-align: center;
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 3rem;
}

.founderContent {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  align-items: center;
}

.founderImage {
  flex: 1;
  min-width: 300px;
  height: 400px;
}

.founderText {
  flex: 2;
  min-width: 300px;
}

.founderText p {
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Team section */
.teamSection {
  background-color: #f8f8f8;
  padding: 4rem 2rem;
  margin-bottom: 4rem;
}

.teamSection h2 {
  text-align: center;
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.teamIntro {
  text-align: center;
  color: var(--light-text-color);
  max-width: 800px;
  margin: 0 auto 3rem;
  line-height: 1.6;
}

.teamGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

.teamMember {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Happy Clients Section */
.happyClientsSection {
  max-width: var(--max-width);
  margin: 0 auto 4rem;
  padding: 2rem;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.7); /* Translucent white */
  border-radius: 12px;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.sectionTitle {
  font-size: 2.5rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.divider {
  width: 70px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 0 auto 2rem;
  border-radius: 3px;
}

/* Client Logos Section */
.clientLogosSection {
  padding: 3rem 2rem 5rem;
  background-color: #ffffff;
  text-align: center;
  margin-bottom: 4rem;
}

.clientLogosContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

.clientLogo {
  width: 180px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.clientLogo:hover {
  filter: grayscale(0);
  opacity: 1;
}

.clientLogo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.teamMember {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.teamMember:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.memberImage {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  overflow: hidden;
}

.teamMember h3 {
  font-size: 1.3rem;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.teamMember p {
  color: var(--light-text-color);
}

/* CTA section */
.ctaSection {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.7); /* Translucent white */
  border-radius: 12px;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.ctaSection h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.ctaSection p {
  color: var(--light-text-color);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.outlineButton {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.outlineButton:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive styles */
@media (max-width: 992px) {
  .storyContent, .founderContent {
    flex-direction: column;
  }

  .storyImage, .founderImage {
    order: -1;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .hero {
    padding: 4rem 1rem;
  }

  .storySection, .founderSection, .ctaSection {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .valuesSection, .teamSection {
    padding: 3rem 1rem;
  }

  .valueCard, .teamMember {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .valueGrid, .teamGrid {
    grid-template-columns: 1fr;
  }

  .ctaButtons {
    flex-direction: column;
  }

  .button {
    width: 100%;
    margin-bottom: 1rem;
  }
}
