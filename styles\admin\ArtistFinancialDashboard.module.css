/* Artist Financial Dashboard Styles */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.headerInfo h2 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 2rem;
  font-weight: 700;
}

.headerInfo p {
  margin: 0;
  color: #6b7280;
  font-size: 1.1rem;
}

.yearSelector {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: #374151;
}

.yearSelect {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.yearSelect:focus {
  outline: none;
  border-color: #4ECDC4;
}

/* Summary Section */
.summarySection {
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.summarySection h3 {
  margin: 0 0 1.5rem 0;
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.summaryCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.summaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #4ECDC4;
}

.summaryIcon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summaryContent {
  flex: 1;
}

.summaryValue {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.summaryLabel {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Earnings Section */
.earningsSection {
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.earningsSection h3 {
  margin: 0 0 1.5rem 0;
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
}

.earningsTable {
  overflow-x: auto;
}

.tableHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.2fr 1.2fr 1.2fr 1.2fr;
  gap: 1rem;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
  font-weight: 600;
  color: #475569;
  margin-bottom: 0.5rem;
}

.tableRow {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.2fr 1.2fr 1.2fr 1.2fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.3s ease;
}

.tableRow:hover {
  background: #f8fafc;
}

.tableCell {
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 0.875rem;
}

.eventName {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.eventLocation {
  color: #6b7280;
  font-size: 0.8rem;
}

.netEarnings {
  font-weight: 700;
  color: #059669;
  font-size: 1rem;
}

/* Participation Section */
.participationSection {
  margin-bottom: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.participationSection h3 {
  margin: 0 0 1.5rem 0;
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
}

.participationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.participationCard {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.participationCard:hover {
  border-color: #4ECDC4;
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.15);
}

.participationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.participationHeader h4 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.participationStatus {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.participationStatus.confirmed {
  background: #dcfce7;
  color: #166534;
}

.participationStatus.pending {
  background: #fef3c7;
  color: #92400e;
}

.participationDetails {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.participationDetail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailLabel {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
}

.detailValue {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.participationNotes {
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #4ECDC4;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
}

/* Metrics Section */
.metricsSection {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.metricsSection h3 {
  margin: 0 0 1.5rem 0;
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.metricCard {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-3px);
}

.metricLabel {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.metricValue {
  font-size: 1.75rem;
  font-weight: 700;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
  }

  .tableHeader,
  .tableRow {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .tableCell {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .participationGrid {
    grid-template-columns: 1fr;
  }

  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}
