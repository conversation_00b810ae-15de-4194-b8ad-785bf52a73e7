/* Customer form styles */
.formContainer {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: var(--font-primary);
}

.modeToggle {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modeButton {
  flex: 1;
  padding: 12px 15px;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.modeButton:hover {
  background-color: #e9e9e9;
}

.modeButton.activeMode {
  background-color: var(--color-primary, #6a0dad);
  color: white;
}

.form {
  width: 100%;
}

.formSection {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.formSection:last-child {
  border-bottom: none;
}

.formSection h2 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--color-primary, #6a0dad);
}

.formGroup {
  margin-bottom: 15px;
}

.formRow {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.formRow .formGroup {
  flex: 1;
  margin-bottom: 0;
}

.formGroup label,
.checkboxGroup label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.checkboxGroup label {
  display: inline;
  margin-left: 8px;
  font-weight: normal;
}

.formGroup input,
.formGroup select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.formGroup input:focus,
.formGroup select:focus {
  border-color: var(--color-primary, #6a0dad);
  outline: none;
}

.inputError {
  border-color: #d32f2f !important;
  background-color: rgba(211, 47, 47, 0.05);
}

.errorText {
  display: block;
  color: #d32f2f;
  font-size: 0.8rem;
  margin-top: 4px;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.checkboxGroup input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
}

.required {
  color: #d32f2f;
}

.optional {
  font-size: 0.8rem;
  font-weight: normal;
  color: #666;
}

.errorMessage {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: 500;
}

.successMessage {
  background-color: rgba(76, 175, 80, 0.1);
  color: #388e3c;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-weight: 500;
}

.formActions {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.submitButton {
  background-color: var(--color-primary, #6a0dad);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 14px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;
  width: 100%;
  max-width: 300px;
}

.submitButton:hover {
  background-color: var(--color-primary-dark, #4e0980);
}

.submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.toggleModeButton {
  background: none;
  border: none;
  color: var(--color-primary, #6a0dad);
  margin-top: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: underline;
}

.forgotPasswordButton {
  background: none;
  border: none;
  color: var(--color-primary, #6a0dad);
  margin: 10px 0;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: underline;
}

.privacyConsent {
  font-size: 0.8rem;
  color: #666;
  margin-top: 15px;
  line-height: 1.4;
}

.privacyConsent a {
  color: var(--color-primary, #6a0dad);
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 600px) {
  .formRow {
    flex-direction: column;
    gap: 0;
  }
  
  .submitButton {
    width: 100%;
  }
}
