import { useState, useEffect } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/analytics/SalesDashboard.module.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

/**
 * SalesDashboard component for displaying sales analytics
 * 
 * @param {Object} props - Component props
 * @param {string} props.period - Time period for data (day, week, month, year)
 * @param {boolean} props.customDateRange - Whether to use custom date range
 * @param {Date} props.startDate - Start date for custom range
 * @param {Date} props.endDate - End date for custom range
 * @returns {JSX.Element}
 */
export default function SalesDashboard({ 
  period = 'month',
  customDateRange = false,
  startDate = null,
  endDate = null
}) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [salesData, setSalesData] = useState({
    summary: {
      totalSales: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      conversionRate: 0
    },
    revenueByDate: [],
    salesByProduct: [],
    salesByCategory: []
  });
  const [comparisonData, setComparisonData] = useState(null);
  const [showComparison, setShowComparison] = useState(false);

  // Fetch sales data
  useEffect(() => {
    async function fetchSalesData() {
      try {
        setLoading(true);
        setError(null);
        
        // Build query params
        const queryParams = new URLSearchParams();
        
        if (customDateRange && startDate && endDate) {
          queryParams.append('start_date', startDate.toISOString());
          queryParams.append('end_date', endDate.toISOString());
        } else {
          queryParams.append('period', period);
        }
        
        const response = await fetch(`/api/admin/analytics/sales?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch sales data');
        }
        
        const data = await response.json();
        setSalesData(data);
      } catch (err) {
        console.error('Error fetching sales data:', err);
        setError(err.message || 'Failed to load sales data');
        toast.error('Failed to load sales dashboard');
      } finally {
        setLoading(false);
      }
    }
    
    fetchSalesData();
  }, [period, customDateRange, startDate, endDate]);

  // Fetch comparison data when toggled
  useEffect(() => {
    if (!showComparison) {
      setComparisonData(null);
      return;
    }
    
    async function fetchComparisonData() {
      try {
        // Calculate previous period
        let prevStartDate, prevEndDate;
        
        if (customDateRange && startDate && endDate) {
          const duration = endDate.getTime() - startDate.getTime();
          prevEndDate = new Date(startDate.getTime() - 1);
          prevStartDate = new Date(prevEndDate.getTime() - duration);
        } else {
          // For standard periods, calculate previous period
          const now = new Date();
          
          switch (period) {
            case 'day':
              prevStartDate = new Date(now);
              prevStartDate.setDate(prevStartDate.getDate() - 2);
              prevEndDate = new Date(now);
              prevEndDate.setDate(prevEndDate.getDate() - 1);
              break;
            case 'week':
              prevStartDate = new Date(now);
              prevStartDate.setDate(prevStartDate.getDate() - 14);
              prevEndDate = new Date(now);
              prevEndDate.setDate(prevEndDate.getDate() - 7);
              break;
            case 'month':
              prevStartDate = new Date(now);
              prevStartDate.setMonth(prevStartDate.getMonth() - 2);
              prevEndDate = new Date(now);
              prevEndDate.setMonth(prevEndDate.getMonth() - 1);
              break;
            case 'year':
              prevStartDate = new Date(now);
              prevStartDate.setFullYear(prevStartDate.getFullYear() - 2);
              prevEndDate = new Date(now);
              prevEndDate.setFullYear(prevEndDate.getFullYear() - 1);
              break;
            default:
              prevStartDate = new Date(now);
              prevStartDate.setMonth(prevStartDate.getMonth() - 2);
              prevEndDate = new Date(now);
              prevEndDate.setMonth(prevEndDate.getMonth() - 1);
          }
        }
        
        // Build query params for previous period
        const queryParams = new URLSearchParams({
          start_date: prevStartDate.toISOString(),
          end_date: prevEndDate.toISOString()
        });
        
        const response = await fetch(`/api/admin/analytics/sales?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch comparison data');
        }
        
        const data = await response.json();
        setComparisonData(data);
      } catch (err) {
        console.error('Error fetching comparison data:', err);
        toast.error('Failed to load comparison data');
        setShowComparison(false);
      }
    }
    
    fetchComparisonData();
  }, [showComparison, period, customDateRange, startDate, endDate]);

  // Prepare revenue chart data
  const prepareRevenueChartData = () => {
    const labels = salesData.revenueByDate.map(item => item.date);
    
    const datasets = [
      {
        label: 'Revenue',
        data: salesData.revenueByDate.map(item => item.revenue),
        borderColor: '#4a90e2',
        backgroundColor: 'rgba(74, 144, 226, 0.1)',
        fill: true,
        tension: 0.4
      }
    ];
    
    // Add comparison data if available
    if (showComparison && comparisonData) {
      datasets.push({
        label: 'Previous Period',
        data: comparisonData.revenueByDate.map(item => item.revenue),
        borderColor: '#f5a623',
        backgroundColor: 'rgba(245, 166, 35, 0.1)',
        fill: true,
        tension: 0.4,
        borderDash: [5, 5]
      });
    }
    
    return { labels, datasets };
  };

  // Prepare product sales chart data
  const prepareProductSalesChartData = () => {
    const labels = salesData.salesByProduct.map(item => item.name);
    
    return {
      labels,
      datasets: [
        {
          label: 'Sales',
          data: salesData.salesByProduct.map(item => item.revenue),
          backgroundColor: [
            '#4a90e2',
            '#50e3c2',
            '#f5a623',
            '#d0021b',
            '#9013fe',
            '#417505',
            '#bd10e0',
            '#8b572a'
          ]
        }
      ]
    };
  };

  // Prepare category sales chart data
  const prepareCategorySalesChartData = () => {
    const labels = salesData.salesByCategory.map(item => item.name);
    
    return {
      labels,
      datasets: [
        {
          data: salesData.salesByCategory.map(item => item.revenue),
          backgroundColor: [
            '#4a90e2',
            '#50e3c2',
            '#f5a623',
            '#d0021b',
            '#9013fe',
            '#417505'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Chart options
  const lineOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Revenue Over Time',
        font: {
          size: 16
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: (value) => `$${value}`
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Top Products by Revenue',
        font: {
          size: 16
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: (value) => `$${value}`
        }
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'Sales by Category',
        font: {
          size: 16
        }
      }
    }
  };

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Calculate percentage change
  const calculateChange = (current, previous) => {
    if (!previous) return null;
    return (current - previous) / previous;
  };

  // Render loading state
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading sales data...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorMessage}>{error}</p>
        <button 
          className={styles.retryButton}
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.salesDashboard}>
      <div className={styles.dashboardHeader}>
        <h2>Sales Dashboard</h2>
        <div className={styles.comparisonToggle}>
          <label>
            <input
              type="checkbox"
              checked={showComparison}
              onChange={() => setShowComparison(!showComparison)}
            />
            Show Comparison with Previous Period
          </label>
        </div>
      </div>

      {/* Key Metrics */}
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>
            {formatCurrency(salesData.summary.totalSales)}
            {showComparison && comparisonData && (
              <span className={`${styles.changeIndicator} ${
                calculateChange(salesData.summary.totalSales, comparisonData.summary.totalSales) >= 0
                  ? styles.positive
                  : styles.negative
              }`}>
                {calculateChange(salesData.summary.totalSales, comparisonData.summary.totalSales) >= 0 ? '↑' : '↓'}
                {formatPercentage(Math.abs(calculateChange(salesData.summary.totalSales, comparisonData.summary.totalSales)))}
              </span>
            )}
          </div>
          <div className={styles.metricLabel}>Total Revenue</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>
            {salesData.summary.totalOrders}
            {showComparison && comparisonData && (
              <span className={`${styles.changeIndicator} ${
                calculateChange(salesData.summary.totalOrders, comparisonData.summary.totalOrders) >= 0
                  ? styles.positive
                  : styles.negative
              }`}>
                {calculateChange(salesData.summary.totalOrders, comparisonData.summary.totalOrders) >= 0 ? '↑' : '↓'}
                {formatPercentage(Math.abs(calculateChange(salesData.summary.totalOrders, comparisonData.summary.totalOrders)))}
              </span>
            )}
          </div>
          <div className={styles.metricLabel}>Total Orders</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>
            {formatCurrency(salesData.summary.averageOrderValue)}
            {showComparison && comparisonData && (
              <span className={`${styles.changeIndicator} ${
                calculateChange(salesData.summary.averageOrderValue, comparisonData.summary.averageOrderValue) >= 0
                  ? styles.positive
                  : styles.negative
              }`}>
                {calculateChange(salesData.summary.averageOrderValue, comparisonData.summary.averageOrderValue) >= 0 ? '↑' : '↓'}
                {formatPercentage(Math.abs(calculateChange(salesData.summary.averageOrderValue, comparisonData.summary.averageOrderValue)))}
              </span>
            )}
          </div>
          <div className={styles.metricLabel}>Average Order Value</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>
            {formatPercentage(salesData.summary.conversionRate)}
            {showComparison && comparisonData && (
              <span className={`${styles.changeIndicator} ${
                calculateChange(salesData.summary.conversionRate, comparisonData.summary.conversionRate) >= 0
                  ? styles.positive
                  : styles.negative
              }`}>
                {calculateChange(salesData.summary.conversionRate, comparisonData.summary.conversionRate) >= 0 ? '↑' : '↓'}
                {formatPercentage(Math.abs(calculateChange(salesData.summary.conversionRate, comparisonData.summary.conversionRate)))}
              </span>
            )}
          </div>
          <div className={styles.metricLabel}>Conversion Rate</div>
        </div>
      </div>

      {/* Charts */}
      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <div className={styles.chartContainer}>
            <Line data={prepareRevenueChartData()} options={lineOptions} />
          </div>
        </div>
        <div className={styles.chartRow}>
          <div className={styles.chartCard}>
            <div className={styles.chartContainer}>
              <Bar data={prepareProductSalesChartData()} options={barOptions} />
            </div>
          </div>
          <div className={styles.chartCard}>
            <div className={styles.chartContainer}>
              <Doughnut data={prepareCategorySalesChartData()} options={doughnutOptions} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
