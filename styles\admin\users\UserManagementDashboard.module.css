.dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  margin: 0;
  font-size: 1.8rem;
}

.refreshButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
}

.statIcon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
}

.statContent h3 {
  font-size: 2rem;
  margin: 0;
  color: #333;
  font-weight: 700;
}

.statContent p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
}

.roleBreakdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.roleCard {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.roleCard:hover {
  background: #e9ecef;
}

.roleIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.roleInfo h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.roleInfo p {
  margin: 2px 0 0 0;
  color: #666;
  font-size: 0.85rem;
}

.activityList {
  max-height: 400px;
  overflow-y: auto;
}

.activityItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.activityItem:last-child {
  border-bottom: none;
}

.activityIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.activityContent {
  flex: 1;
}

.activityDescription {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 0.9rem;
}

.activityTime {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
}

.noActivity {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.actionButton {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 15px 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.actionButton:hover {
  border-color: #6e8efb;
  background: #f8f9ff;
  transform: translateY(-1px);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 40px 20px;
  color: #721c24;
  background: #f8d7da;
  border-radius: 8px;
  margin: 20px 0;
}

.error h3 {
  margin: 0 0 10px 0;
  color: #721c24;
}

.retryButton {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-top: 15px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background: #c82333;
}

.accessDenied {
  text-align: center;
  padding: 40px 20px;
  color: #856404;
  background: #fff3cd;
  border-radius: 8px;
  margin: 20px 0;
}

.accessDenied h3 {
  margin: 0 0 10px 0;
  color: #856404;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 15px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .roleBreakdown {
    grid-template-columns: 1fr;
  }
  
  .quickActions {
    grid-template-columns: 1fr;
  }
}
