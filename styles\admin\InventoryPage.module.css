/* InventoryPage.module.css */
.inventoryPage {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: #333;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
}

.addButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.addButton:hover {
  background-color: #5a0c8f;
}

.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statsCard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.2s;
}

.statsCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.statsCard h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #666;
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.tabsContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tabButton {
  flex: 1;
  background-color: transparent;
  border: none;
  padding: 1rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.tabButton:hover:not(.activeTab) {
  background-color: #f5f5f5;
}

.activeTab {
  background-color: #6a0dad;
  color: white;
}

.tabContent {
  padding: 1rem;
  min-height: 400px;
}

.modalContent {
  padding: 1rem;
}

.modalContent h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .statsCard {
    padding: 1rem;
  }
  
  .statValue {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .statsContainer {
    grid-template-columns: 1fr;
  }
  
  .tabs {
    flex-direction: column;
  }
}
