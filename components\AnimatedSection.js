import { useEffect, useRef, useState } from 'react';

/**
 * AnimatedSection component that animates its children when they enter the viewport
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child elements to animate
 * @param {string} props.animation - Animation type: 'fade-in', 'slide-left', 'slide-right', 'scale-in'
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.threshold - Intersection observer threshold (0-1)
 * @param {number} props.delay - Animation delay in milliseconds
 * @param {boolean} props.once - Whether to animate only once or every time element enters viewport
 * @returns {JSX.Element}
 */
const AnimatedSection = ({ 
  children, 
  animation = 'fade-in', 
  className = '', 
  threshold = 0.2,
  delay = 0,
  once = true,
  ...props 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);
  
  useEffect(() => {
    const section = sectionRef.current;
    
    if (!section) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (once) {
            observer.unobserve(section);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      { 
        threshold,
        rootMargin: '0px 0px -50px 0px' // Trigger slightly before element enters viewport
      }
    );
    
    observer.observe(section);
    
    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, [threshold, once]);
  
  const delayStyle = delay ? { transitionDelay: `${delay}ms` } : {};
  
  return (
    <div 
      ref={sectionRef}
      className={`${animation} ${isVisible ? 'visible' : ''} ${className}`}
      style={delayStyle}
      {...props}
    >
      {children}
    </div>
  );
};

export default AnimatedSection;
