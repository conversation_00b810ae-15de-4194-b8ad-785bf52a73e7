import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getTemplate(id, res)
    case 'PUT':
      return updateTemplate(id, req, res)
    case 'DELETE':
      // Only admins can delete templates
      try {
        const { role } = await getCurrentUser(req)
        if (role !== 'admin') {
          return res.status(403).json({ error: 'Forbidden' })
        }
      } catch (error) {
        return res.status(403).json({ error: 'Authorization failed' })
      }
      return deleteTemplate(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single template
async function getTemplate(id, res) {
  try {
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Get template details
    const { data: template, error } = await client
      .from('marketing_templates')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      throw error
    }

    if (!template) {
      return res.status(404).json({ error: 'Template not found' })
    }

    // Get usage count (how many times this template has been used in campaigns)
    const { count, error: countError } = await supabase
      .from('campaign_messages')
      .select('id', { count: 'exact' })
      .ilike('content', `%Template ID: ${id}%`)

    if (countError) {
      console.error('Error getting template usage count:', countError)
    }

    return res.status(200).json({
      template,
      usage_count: count || 0
    })
  } catch (error) {
    console.error('Error fetching template:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Update a template
async function updateTemplate(id, req, res) {
  const {
    name,
    description,
    subject,
    content,
    template_type,
    category,
    is_active
  } = req.body

  try {
    // Validate required fields
    if (!name || !content || !template_type) {
      return res.status(400).json({ error: 'Name, content, and template type are required' })
    }

    // Validate template type
    const validTemplateTypes = ['email', 'sms', 'push']
    if (!validTemplateTypes.includes(template_type)) {
      return res.status(400).json({ error: 'Invalid template type. Must be one of: email, sms, push' })
    }

    // Email templates require a subject
    if (template_type === 'email' && !subject) {
      return res.status(400).json({ error: 'Subject is required for email templates' })
    }

    // Update template
    const { data, error } = await supabase
      .from('marketing_templates')
      .update({
        name,
        description,
        subject,
        content,
        template_type,
        category,
        is_active,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Template not found or you do not have permission to update it' })
    }

    return res.status(200).json(data[0])
  } catch (error) {
    console.error('Error updating template:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Delete a template
async function deleteTemplate(id, res) {
  try {
    // Check if template is being used in any campaigns
    const { count, error: countError } = await supabase
      .from('campaign_messages')
      .select('id', { count: 'exact' })
      .ilike('content', `%Template ID: ${id}%`)

    if (countError) {
      throw countError
    }

    if (count > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete template that is being used in campaigns',
        usage_count: count
      })
    }

    // Delete template
    const { error } = await supabase
      .from('marketing_templates')
      .delete()
      .eq('id', id)

    if (error) {
      throw error
    }

    return res.status(200).json({ success: true })
  } catch (error) {
    console.error('Error deleting template:', error)
    return res.status(500).json({ error: error.message })
  }
}
