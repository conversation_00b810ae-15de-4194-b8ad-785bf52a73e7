import { useState, useEffect } from 'react';
import { getAdminClient, getClient } from '@/lib/supabase';
import styles from '@/styles/admin/AddBookingForm.module.css';

export default function AddBookingForm({ onClose, onBookingAdded }) {
  const [formData, setFormData] = useState({
    customer_id: '',
    service_id: '',
    start_time: '',
    end_time: '',
    status: 'pending',
    location: '',
    notes: ''
  });

  const [customers, setCustomers] = useState([]);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showNewCustomer, setShowNewCustomer] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: ''
  });

  // Fetch customers and services
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get auth token from standardized location
        let authToken = null;
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
            if (cachedToken) {
              const tokenData = JSON.parse(cachedToken);
              if (tokenData && tokenData.token) {
                authToken = tokenData.token;
              }
            }
          } catch (tokenError) {
            console.error('Error getting auth token:', tokenError);
          }
        }

        // Fetch customers using API endpoint instead of direct Supabase access
        const customersResponse = await fetch('/api/admin/customers', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...(authToken ? {
              'Authorization': `Bearer ${authToken}`,
              'X-Auth-Token': authToken
            } : {})
          },
          credentials: 'include'
        });

        if (!customersResponse.ok) {
          throw new Error(`Failed to fetch customers: ${customersResponse.status}`);
        }

        const customersData = await customersResponse.json();

        // Fetch services using API endpoint
        const servicesResponse = await fetch('/api/admin/services', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...(authToken ? {
              'Authorization': `Bearer ${authToken}`,
              'X-Auth-Token': authToken
            } : {})
          },
          credentials: 'include'
        });

        if (!servicesResponse.ok) {
          throw new Error(`Failed to fetch services: ${servicesResponse.status}`);
        }

        const servicesData = await servicesResponse.json();

        setCustomers(customersData.customers || []);
        setServices(servicesData.services || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load customers or services');
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // If service changes, update end time based on duration
    if (name === 'service_id' && value && formData.start_time) {
      const selectedService = services.find(service => service.id === value);
      if (selectedService) {
        const startTime = new Date(formData.start_time);
        const endTime = new Date(startTime.getTime() + selectedService.duration * 60000);
        setFormData(prev => ({
          ...prev,
          end_time: endTime.toISOString().slice(0, 16)
        }));
      }
    }

    // If start time changes and service is selected, update end time
    if (name === 'start_time' && value && formData.service_id) {
      const selectedService = services.find(service => service.id === formData.service_id);
      if (selectedService) {
        const startTime = new Date(value);
        const endTime = new Date(startTime.getTime() + selectedService.duration * 60000);
        setFormData(prev => ({
          ...prev,
          end_time: endTime.toISOString().slice(0, 16)
        }));
      }
    }
  };

  const handleNewCustomerChange = (e) => {
    const { name, value } = e.target;
    setNewCustomer({
      ...newCustomer,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);    try {
      let customerId = formData.customer_id;
      const client = getClient();

      // If creating a new customer
      if (showNewCustomer) {
        // Validate new customer data
        if (!newCustomer.name || !newCustomer.email) {
          throw new Error('Customer name and email are required');
        }

        // Create new customer
        const { data: customerData, error: customerError } = await client
          .from('customers')
          .insert([{
            name: newCustomer.name,
            email: newCustomer.email,
            phone: newCustomer.phone
          }])
          .select();

        if (customerError) throw customerError;

        customerId = customerData[0].id;
      }

      // Create booking
      const { data: bookingData, error: bookingError } = await client
        .from('bookings')
        .insert([{
          customer_id: customerId,
          service_id: formData.service_id,
          start_time: new Date(formData.start_time).toISOString(),
          end_time: new Date(formData.end_time).toISOString(),
          status: formData.status,
          location: formData.location,
          notes: formData.notes
        }])
        .select();

            if (bookingError) throw bookingError;

      // Send notification via API
      try {
        await fetch('/api/notifications/booking', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bookingId: bookingData[0].id,
            customerId: customerId,
            status: formData.status,
            startTime: formData.start_time,
            serviceName: services.find(s => s.id === formData.service_id)?.name || 'Service'
          }),
        });
      } catch (notificationError) {
        console.error('Error sending booking notification:', notificationError);
        // Don't fail the entire booking creation if notification fails
      }

      // Call the callback to refresh the calendar
      if (onBookingAdded) {
        onBookingAdded();
      }

      // Close the form
      onClose();
    } catch (error) {
      console.error('Error creating booking:', error);
      setError(error.message);
      setLoading(false);
    }
  };

  return (
    <div className={styles.addBookingForm}>
      <div className={styles.header}>
        <h2>Add New Booking</h2>
        <button className={styles.closeButton} onClick={onClose}>×</button>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formSection}>
          <h3>Customer Information</h3>

          <div className={styles.customerToggle}>
            <button
              type="button"
              className={`${styles.toggleButton} ${!showNewCustomer ? styles.active : ''}`}
              onClick={() => setShowNewCustomer(false)}
            >
              Existing Customer
            </button>
            <button
              type="button"
              className={`${styles.toggleButton} ${showNewCustomer ? styles.active : ''}`}
              onClick={() => setShowNewCustomer(true)}
            >
              New Customer
            </button>
          </div>

          {showNewCustomer ? (
            <div className={styles.newCustomerFields}>
              <div className={styles.formGroup}>
                <label htmlFor="name">Name *</label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  value={newCustomer.name}
                  onChange={handleNewCustomerChange}
                  className={styles.input}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="email">Email *</label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={newCustomer.email}
                  onChange={handleNewCustomerChange}
                  className={styles.input}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="phone">Phone</label>
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={newCustomer.phone}
                  onChange={handleNewCustomerChange}
                  className={styles.input}
                />
              </div>
            </div>
          ) : (
            <div className={styles.formGroup}>
              <label htmlFor="customer_id">Select Customer *</label>
              <select
                id="customer_id"
                name="customer_id"
                value={formData.customer_id}
                onChange={handleInputChange}
                className={styles.select}
                required={!showNewCustomer}
              >
                <option value="">-- Select Customer --</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} ({customer.email})
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        <div className={styles.formSection}>
          <h3>Booking Details</h3>

          <div className={styles.formGroup}>
            <label htmlFor="service_id">Service *</label>
            <select
              id="service_id"
              name="service_id"
              value={formData.service_id}
              onChange={handleInputChange}
              className={styles.select}
              required
            >
              <option value="">-- Select Service --</option>
              {services.map(service => (
                <option key={service.id} value={service.id}>
                  {service.name} (${service.price} - {service.duration} min)
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="start_time">Start Time *</label>
              <input
                id="start_time"
                name="start_time"
                type="datetime-local"
                value={formData.start_time}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="end_time">End Time *</label>
              <input
                id="end_time"
                name="end_time"
                type="datetime-local"
                value={formData.end_time}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="status">Status</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className={styles.select}
            >
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="canceled">Canceled</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="location">Location</label>
            <input
              id="location"
              name="location"
              type="text"
              value={formData.location}
              onChange={handleInputChange}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="notes">Notes</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              className={styles.textarea}
              rows="3"
            ></textarea>
          </div>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            className={styles.cancelButton}
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading}
          >
            {loading ? 'Creating...' : 'Create Booking'}
          </button>
        </div>
      </form>
    </div>
  );
}
