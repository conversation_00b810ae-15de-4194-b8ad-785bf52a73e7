import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

const BreadcrumbSchema = () => {
  const router = useRouter();
  const [breadcrumbData, setBreadcrumbData] = useState(null);
  const [isClient, setIsClient] = useState(false);

  // Set client-side flag to prevent hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Generate breadcrumb data only on client-side
  useEffect(() => {
    if (!isClient) return;

    const path = router.asPath;

    // Skip breadcrumbs for homepage
    if (path === '/') {
      setBreadcrumbData(null);
      return;
    }

    // Generate breadcrumb items based on the current path
    const pathSegments = path.split('/').filter(segment => segment);
    const breadcrumbItems = [
      { label: 'Home', path: '/' }
    ];

    let currentPath = '';
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;

      // Format the label (capitalize first letter, replace hyphens with spaces)
      // Handle UUIDs by showing a more user-friendly label
      let label;
      if (segment.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        // This is a UUID, use a generic label based on the parent path
        const parentSegment = pathSegments[pathSegments.indexOf(segment) - 1];
        if (parentSegment === 'bookings') {
          label = 'Booking Details';
        } else if (parentSegment === 'customers') {
          label = 'Customer Details';
        } else {
          label = 'Details';
        }
      } else {
        label = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }

      breadcrumbItems.push({ label, path: currentPath });
    });

    const data = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": breadcrumbItems.map((item, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": item.label,
        "item": `https://www.oceansoulsparkles.com.au${item.path}`
      }))
    };

    setBreadcrumbData(data);
  }, [router.asPath, isClient]);

  // Don't render anything on server-side or if no breadcrumb data
  if (!isClient || !breadcrumbData) {
    return null;
  }

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbData) }}
      />
    </Head>
  );
};

export default BreadcrumbSchema;
