import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { updatePassword } from '@/lib/auth' // This already uses supabase
import supabase from '@/lib/supabase';
import styles from '@/styles/admin/Login.module.css'

export default function ResetPassword() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [message, setMessage] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  // Check if user is authenticated with a recovery token
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Use Supabase client directly
        const client = supabase;
        if (!client) {
          console.error("Supabase client not available for session check.");
          // Optionally, set an error state or redirect differently
          router.push('/admin/login');
          return;
        }
        const { data, error } = await client.auth.getSession(); // Use client for getSession
        console.log('Session check:', { data, error })

        if (error) {
          console.error('Error fetching session:', error.message);
          // Potentially handle specific errors, e.g. network error
        }

        if (!data.session) {
          console.log('No session found, redirecting to login')
          router.push('/admin/login')
        } else {
          console.log('Session found:', data.session)
          // Verify it's a recovery session if possible, though Supabase handles this
          // by only allowing password updates with a recovery token.
        }
      } catch (e) {
        console.error("Exception during session check:", e);
        router.push('/admin/login'); // Fallback redirect
      }
    }

    checkSession()
  }, [router])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setMessage(null)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    // Validate password strength
    if (password.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    try {
      console.log('Attempting to update password')
      // updatePassword from @/lib/auth already uses supabase
      const { data, error: updateError } = await updatePassword(password)
      console.log('Update password result:', { data, error: updateError })

      if (updateError) {
        console.error('Password update error:', updateError)
        setError(updateError.message)
        setLoading(false)
        return
      }

      // Sign out the user to clear the recovery session
      const client = supabase; // Use Supabase client directly
      if (!client) {
        console.error("Supabase client not available for sign out.");
        setError("Failed to complete password reset process. Please try logging in.");
        setLoading(false);
        return;
      }
      const { error: signOutError } = await client.auth.signOut(); // Use client for signOut
      if (signOutError) {
        console.error('Error signing out after password reset:', signOutError.message);
        // Non-critical, proceed with success message and redirect
      }

      setMessage('Password has been reset successfully. You will be redirected to login.')
      setLoading(false)

      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push('/admin/login')
      }, 3000)
    } catch (err) {
      console.error('Unexpected error during password update:', err)
      setError(err.message || 'An unexpected error occurred')
      setLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>Reset Password | OceanSoulSparkles Admin</title>
        <meta name="description" content="OceanSoulSparkles Admin Reset Password" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="/images/bannerlogo.PNG"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Reset Password</h1>

          {error && <div className={styles.error}>{error}</div>}
          {message && <div className={styles.success}>{message}</div>}

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="password">New Password</label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className={styles.input}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="confirmPassword">Confirm Password</label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className={styles.input}
              />
            </div>

            <button
              type="submit"
              className={styles.loginButton}
              disabled={loading}
            >
              {loading ? 'Updating...' : 'Update Password'}
            </button>
          </form>

          <div className={styles.backToSite}>
            <Link href="/admin/login" className={styles.link}>
              ← Back to login
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
