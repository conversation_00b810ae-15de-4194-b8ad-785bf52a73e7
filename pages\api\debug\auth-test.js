/**
 * Debug API endpoint to test authentication
 * This endpoint helps debug authentication issues
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';
import authTokenManager from '@/lib/auth-token-manager';

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Auth test API called`);

  try {
    // Check environment variables
    const envCheck = {
      NODE_ENV: process.env.NODE_ENV,
      ENABLE_AUTH_BYPASS: process.env.ENABLE_AUTH_BYPASS,
      NEXT_PUBLIC_ENABLE_AUTH_BYPASS: process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS,
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
    };

    console.log(`[${requestId}] Environment check:`, envCheck);

    // Check headers
    const headers = {
      authorization: req.headers.authorization ? 'Present' : 'Missing',
      'x-auth-token': req.headers['x-auth-token'] ? 'Present' : 'Missing',
      'content-type': req.headers['content-type'],
      userAgent: req.headers['user-agent']
    };

    console.log(`[${requestId}] Headers check:`, headers);

    // Test authentication
    const authResult = await authenticateAdminRequest(req);
    
    console.log(`[${requestId}] Auth result:`, {
      authorized: authResult.authorized,
      hasUser: !!authResult.user,
      role: authResult.role,
      error: authResult.error?.message
    });

    // Return debug information
    return res.status(200).json({
      success: true,
      requestId,
      environment: envCheck,
      headers,
      authentication: {
        authorized: authResult.authorized,
        user: authResult.user ? {
          id: authResult.user.id,
          email: authResult.user.email
        } : null,
        role: authResult.role,
        error: authResult.error?.message || null
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${requestId}] Auth test error:`, error);
    return res.status(500).json({
      success: false,
      requestId,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
