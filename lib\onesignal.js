import { useEffect, useState } from 'react';
import { isClientSide, withClientSideRendering } from './react-hydration-helpers';

/**
 * OneSignal Integration Module
 *
 * This module provides a safe interface to OneSignal that:
 * 1. Prevents hydration issues by ensuring OneSignal is only accessed client-side
 * 2. Provides a consistent mock implementation for development environments
 * 3. Handles errors gracefully to prevent UI issues
 * 4. Uses a custom event listener to detect when OneSignal is initialized
 */

// Check if we're in development mode or a local environment
const isLocalEnvironment = () => {
  if (!isClientSide()) return process.env.NODE_ENV === 'development';

  const hostname = window.location.hostname;
  return process.env.NODE_ENV === 'development' ||
         hostname === 'localhost' ||
         hostname === '127.0.0.1';
};

// Mock implementation for development or when OneSignal fails to initialize
const oneSignalMock = {
  getNotificationPermission: () => Promise.resolve('default'),
  isPushNotificationsEnabled: () => Promise.resolve(false),
  showNativePrompt: () => Promise.resolve(),
  showHttpPrompt: () => Promise.resolve(),
  showCategorySlidedown: () => Promise.resolve(),
  getUserId: () => Promise.resolve('mock-user-id'),
  setExternalUserId: () => Promise.resolve(),
  removeExternalUserId: () => Promise.resolve(),
  setEmail: () => Promise.resolve(),
  sendTag: () => Promise.resolve(),
  getTags: () => Promise.resolve({ role: 'customer', environment: 'development' }),
  on: () => {},
  once: () => {},
  off: () => {},
};

/**
 * Safely get the OneSignal instance
 * Returns mock in development or if OneSignal is not available
 */
export const getOneSignalInstance = () => {
  // Server-side or development environment
  if (!isClientSide() || isLocalEnvironment()) {
    return oneSignalMock;
  }

  // Check if OneSignal is available and initialized
  if (window.OneSignal && typeof window.OneSignal.getNotificationPermission === 'function') {
    return window.OneSignal;
  }

  // If OneSignal is not available or not fully initialized, return mock
  return oneSignalMock;
};

/**
 * Check if OneSignal is properly initialized
 */
export const isOneSignalInitialized = () => {
  if (!isClientSide()) return false;

  if (isLocalEnvironment()) return true;

  return window.OneSignal &&
         typeof window.OneSignal.getNotificationPermission === 'function' &&
         window.__ONESIGNAL_INITIALIZED__ === true;
};

/**
 * Wait for OneSignal to be fully initialized
 * Uses a custom event listener to detect when initialization is complete
 */
export const waitForOneSignal = () => {
  // Server-side or development environment
  if (!isClientSide() || isLocalEnvironment()) {
    return Promise.resolve(oneSignalMock);
  }

  return new Promise((resolve) => {
    // If already initialized, resolve immediately
    if (isOneSignalInitialized()) {
      resolve(getOneSignalInstance());
      return;
    }

    let hasResolved = false;

    // Function to resolve safely
    const safeResolve = (instance) => {
      if (!hasResolved) {
        hasResolved = true;
        resolve(instance || getOneSignalInstance());
      }
    };

    // Listen for the custom initialization event
    const handleInitialized = () => {
      document.removeEventListener('onesignal:initialized', handleInitialized);
      safeResolve();
    };

    // Listen for errors and provide fallback
    const handleError = (event) => {
      console.warn('[OneSignal] Initialization error detected, using mock implementation');
      document.removeEventListener('onesignal:error', handleError);
      safeResolve(oneSignalMock);
    };

    // Add event listeners
    document.addEventListener('onesignal:initialized', handleInitialized);
    document.addEventListener('onesignal:error', handleError);

    // Also check periodically in case the event was missed
    const checkInterval = setInterval(() => {
      if (isOneSignalInitialized()) {
        clearInterval(checkInterval);
        document.removeEventListener('onesignal:initialized', handleInitialized);
        document.removeEventListener('onesignal:error', handleError);
        safeResolve();
      }
    }, 500);

    // Add a timeout to prevent hanging indefinitely
    setTimeout(() => {
      clearInterval(checkInterval);
      document.removeEventListener('onesignal:initialized', handleInitialized);
      document.removeEventListener('onesignal:error', handleError);
      console.warn('[OneSignal] Initialization timed out, using mock implementation');
      safeResolve(oneSignalMock);
    }, 15000); // 15 second timeout
  });
};

/**
 * React hook for OneSignal
 * This is a client-side only hook that safely handles OneSignal initialization
 */
export const useOneSignal = () => {
  // Use state to track initialization
  const [isInitialized, setIsInitialized] = useState(false);

  // Only run on client-side
  useEffect(() => {
    if (!isClientSide()) return;

    // In development, immediately mark as initialized
    if (isLocalEnvironment()) {
      setIsInitialized(true);
      return;
    }

    // Check if already initialized
    if (isOneSignalInitialized()) {
      setIsInitialized(true);
      return;
    }

    // Listen for the custom initialization event
    const handleInitialized = () => {
      setIsInitialized(true);
    };

    // Add event listener
    document.addEventListener('onesignal:initialized', handleInitialized);

    // Also check periodically
    const checkInterval = setInterval(() => {
      if (isOneSignalInitialized()) {
        clearInterval(checkInterval);
        setIsInitialized(true);
      }
    }, 1000);

    // Cleanup
    return () => {
      clearInterval(checkInterval);
      document.removeEventListener('onesignal:initialized', handleInitialized);
    };
  }, []);

  return { isInitialized };
};

/**
 * Safe wrapper for OneSignal methods
 * All OneSignal methods should be wrapped with this to prevent errors
 */
const safeOneSignalCall = async (methodName, callback) => {
  if (!isClientSide()) {
    console.warn(`[OneSignal] ${methodName} called server-side, this is not supported`);
    return { success: false, error: 'Called server-side' };
  }

  if (isLocalEnvironment()) {
    console.log(`[OneSignal] ${methodName} called in development mode (mocked)`);
    return { success: true, development: true };
  }

  try {
    const OneSignal = await waitForOneSignal();
    const result = await callback(OneSignal);
    return { success: true, result };
  } catch (error) {
    console.error(`[OneSignal] Error in ${methodName}:`, error);
    return { success: false, error: error.message };
  }
};

// Wrapped OneSignal methods

export const getNotificationPermissionStatus = () =>
  safeOneSignalCall('getNotificationPermissionStatus',
    async (OneSignal) => await OneSignal.getNotificationPermission());

export const showNativePrompt = () =>
  safeOneSignalCall('showNativePrompt',
    async (OneSignal) => await OneSignal.showNativePrompt());

export const showCategorySlidedown = () =>
  safeOneSignalCall('showCategorySlidedown',
    async (OneSignal) => await OneSignal.showCategorySlidedown());

export const setOneSignalExternalUserId = (userId) =>
  safeOneSignalCall('setExternalUserId',
    async (OneSignal) => await OneSignal.setExternalUserId(userId));

export const setOneSignalEmail = (email) =>
  safeOneSignalCall('setEmail',
    async (OneSignal) => await OneSignal.setEmail(email));

export const addOneSignalTag = (key, value) =>
  safeOneSignalCall('sendTag',
    async (OneSignal) => await OneSignal.sendTag(key, value));

export const getOneSignalTags = () =>
  safeOneSignalCall('getTags',
    async (OneSignal) => await OneSignal.getTags());

export const isPushNotificationsEnabled = () =>
  safeOneSignalCall('isPushNotificationsEnabled',
    async (OneSignal) => await OneSignal.isPushNotificationsEnabled());

// Create client-side only versions of components that use OneSignal
export const withOneSignal = (Component) => {
  return withClientSideRendering(Component);
};
