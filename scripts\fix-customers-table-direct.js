// Alternative approach to fix customers table
// Run with: node scripts/fix-customers-table-direct.js

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Get Supabase credentials from environment
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service key is missing from .env.local file');
  process.exit(1);
}

// Create Supabase admin client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixCustomersTable() {
  try {
    console.log('Starting customers table update...');

    // Step 1: Check if the table exists and create it if not
    console.log('Step 1: Checking if customers table exists...');
    const { data: tableExists, error: tableCheckError } = await supabaseAdmin.rpc('check_table_exists', { 
      table_name: 'customers'
    });

    if (tableCheckError) {
      console.log('Table check failed, will attempt to create table');
    }

    if (!tableExists) {
      console.log('Customers table does not exist, creating it...');
      
      const { error: createTableError } = await supabaseAdmin
        .from('customers')
        .insert([
          { 
            name: 'System Generated',
            email: '<EMAIL>',
            phone: '0000000000' 
          }
        ]);
      
      if (createTableError) {
        console.error('Error creating customers table:', createTableError);
        return;
      }
      console.log('Created customers table');
    }

    // Step 2: Try to add auth_id column if it doesn't exist
    console.log('Step 2: Adding auth_id column...');
    try {
      const { error: alterTableError } = await supabaseAdmin
        .from('customers') 
        .update({ auth_id: null })
        .eq('id', 'does-not-exist');
    
      if (alterTableError && !alterTableError.message.includes('does not exist')) {
        // Column already exists
        console.log('Auth_id column already exists');
      } else {
        // Need to use raw SQL for this - manually do this in the Supabase dashboard
        console.log('Please run the following SQL in your Supabase dashboard:');
        console.log('ALTER TABLE customers ADD COLUMN IF NOT EXISTS auth_id UUID;');
      }
    } catch (err) {
      console.error('Error when trying to check auth_id column:', err);
    }

    // Step 3: Try to add is_guest column if it doesn't exist
    console.log('Step 3: Adding is_guest column...');
    try {
      const { error: updateError } = await supabaseAdmin
        .from('customers')
        .update({ is_guest: true })
        .eq('id', 'does-not-exist');
        
      if (updateError && !updateError.message.includes('does not exist')) {
        console.log('is_guest column already exists');
      } else {
        // Need to use raw SQL for this - manually do this in the Supabase dashboard
        console.log('Please run the following SQL in your Supabase dashboard:');
        console.log('ALTER TABLE customers ADD COLUMN IF NOT EXISTS is_guest BOOLEAN DEFAULT TRUE;');
      }
    } catch (err) {
      console.error('Error when trying to check is_guest column:', err);
    }

    // Step 4: Update RLS policies
    console.log('Step 4: You need to update RLS policies in the Supabase dashboard');
    console.log(`
Please run the following SQL in your Supabase dashboard:

-- Enable RLS on customers table if not already enabled
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Create simplified policies that don't rely on auth_id
CREATE POLICY "Allow read access to authenticated users" ON customers 
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow insert access to all users" ON customers 
  FOR INSERT TO authenticated, anon WITH CHECK (true);

CREATE POLICY "Allow update access to all users" ON customers 
  FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Allow read access to anonymous users" ON customers 
  FOR SELECT TO anon USING (true);
`);

    // Step 5: Verify the structure
    console.log('Step 5: Verifying customers table structure...');
    try {
      const { data, error } = await supabaseAdmin
        .from('customers')
        .select()
        .limit(1);
        
      if (error) {
        console.error('Error fetching from customers table:', error);
      } else {
        console.log('Successfully retrieved customer data:');
        console.log(data);
        console.log('Table structure update process complete!');
      }
    } catch (err) {
      console.error('Error verifying table structure:', err);
    }
  } catch (err) {
    console.error('Unhandled error during table update process:', err);
  }
}

// Run the main function
fixCustomersTable().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
