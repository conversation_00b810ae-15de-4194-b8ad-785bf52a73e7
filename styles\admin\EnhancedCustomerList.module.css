.enhancedCustomerList {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
}

.actions {
  display: flex;
  gap: 15px;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.addButton:hover {
  background: #5a7cfa;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(110, 142, 251, 0.3);
}

.addIcon {
  font-size: 1.2rem;
  font-weight: bold;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #d32f2f;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 1.1rem;
}

.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 15px 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #6e8efb;
}

.selectedCount {
  font-weight: 500;
  color: #333;
}

.bulkActionButtons {
  position: relative;
}

.bulkButton {
  padding: 8px 16px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.bulkActionMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 180px;
}

.bulkActionMenu button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bulkActionMenu button:hover {
  background: #f5f5f5;
}

.bulkActionMenu button:first-child {
  border-radius: 6px 6px 0 0;
}

.bulkActionMenu button:last-child {
  border-radius: 0 0 6px 6px;
}

.tableContainer {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.customerTable {
  width: 100%;
  border-collapse: collapse;
}

.customerTable th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.customerTable th:hover {
  background: #e9ecef;
}

.checkboxColumn {
  width: 40px;
  cursor: default !important;
}

.checkboxColumn:hover {
  background: #f8f9fa !important;
}

.sortIndicator {
  color: #6e8efb;
  font-weight: bold;
}

.customerTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
}

.customerRow:hover {
  background: #fafafa;
}

.customerName {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customerTags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tagMore {
  background: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.75rem;
}

.tierBadge {
  display: inline-block;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.statusBadge {
  display: inline-block;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.healthScore {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  min-width: 50px;
  display: inline-block;
}

.healthGood {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.healthMedium {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.healthPoor {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.actions {
  display: flex;
  gap: 8px;
}

.viewButton,
.editButton {
  padding: 6px 12px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.viewButton {
  background: #e3f2fd;
  color: #1976d2;
}

.viewButton:hover {
  background: #bbdefb;
}

.editButton {
  background: #fff3e0;
  color: #f57c00;
}

.editButton:hover {
  background: #ffe0b2;
}

.noResults {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 1.1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.resultsSummary {
  text-align: center;
  padding: 15px;
  color: #666;
  font-size: 0.9rem;
  background: #f8f9fa;
  border-radius: 6px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .customerTable th:nth-child(6),
  .customerTable td:nth-child(6),
  .customerTable th:nth-child(7),
  .customerTable td:nth-child(7) {
    display: none;
  }
}

@media (max-width: 768px) {
  .enhancedCustomerList {
    padding: 15px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .actions {
    justify-content: center;
  }
  
  .bulkActions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .tableContainer {
    overflow-x: auto;
  }
  
  .customerTable {
    min-width: 800px;
  }
  
  .customerTable th:nth-child(4),
  .customerTable td:nth-child(4),
  .customerTable th:nth-child(5),
  .customerTable td:nth-child(5),
  .customerTable th:nth-child(8),
  .customerTable td:nth-child(8) {
    display: none;
  }
}

@media (max-width: 480px) {
  .customerTable th:nth-child(9),
  .customerTable td:nth-child(9) {
    display: none;
  }
  
  .customerName {
    font-size: 0.9rem;
  }
  
  .actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .viewButton,
  .editButton {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
}
