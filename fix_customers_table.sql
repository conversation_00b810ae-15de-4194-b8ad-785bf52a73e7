-- Fix customers table to add auth_id column and update RLS policies
-- Check if auth_id column exists and add it if not
DO pages\api\admin\analytics 
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.columns 
               WHERE table_name='customers' AND column_name='auth_id') THEN
    ALTER TABLE customers ADD COLUMN auth_id UUID;
    CREATE INDEX IF NOT EXISTS customers_auth_id_idx ON customers(auth_id);
  END IF;
END pages\api\admin\analytics;

-- Check if is_guest column exists and add it if not
DO pages\api\admin\analytics 
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.columns 
               WHERE table_name='customers' AND column_name='is_guest') THEN
    ALTER TABLE customers ADD COLUMN is_guest BOOLEAN DEFAULT TRUE;
  END IF;
END pages\api\admin\analytics;

-- Reset RLS policies
DO pages\api\admin\analytics 
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS customer_select_own ON customers;
  DROP POLICY IF EXISTS customer_update_own ON customers;
  DROP POLICY IF EXISTS customer_all_service_role ON customers;
  DROP POLICY IF EXISTS customer_anon_select ON customers;
EXCEPTION
  WHEN undefined_object THEN
    -- Do nothing
END pages\api\admin\analytics;

-- Enable RLS on customers table
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Create basic policies that don't reference auth_id
CREATE POLICY customer_public_select ON customers 
  FOR SELECT 
  TO authenticated
  USING (true);

CREATE POLICY customer_public_update ON customers 
  FOR UPDATE 
  TO authenticated
  USING (true);

CREATE POLICY customer_public_insert ON customers 
  FOR INSERT 
  TO authenticated
  WITH CHECK (true);

CREATE POLICY customer_anon_access ON customers 
  FOR ALL 
  TO anon
  USING (true);
