/**
 * QR Code Management Library for Event-Based Booking System
 * Handles QR code generation, validation, and analytics tracking
 */

import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from './supabase';

/**
 * Generate a unique QR code for an event
 * @param {Object} eventData - Event information
 * @param {string} eventData.eventId - Event UUID
 * @param {string} eventData.eventName - Event name
 * @param {string} eventData.eventLocation - Event location
 * @param {string} eventData.eventStartDate - Event start date
 * @param {string} eventData.eventEndDate - Event end date
 * @param {Array} eventData.assignedArtists - Array of artist UUIDs
 * @param {Array} eventData.availableServices - Array of service UUIDs
 * @param {Object} options - QR code generation options
 * @returns {Promise<Object>} QR code data and image
 */
export async function generateEventQRCode(eventData, options = {}) {
  try {
    const {
      eventId,
      eventName,
      eventLocation,
      eventStartDate,
      eventEndDate,
      assignedArtists = [],
      availableServices = [],
    } = eventData;

    // Generate unique QR code identifier
    const qrCodeId = uuidv4();
    const qrCode = `OSS-EVENT-${eventId.substring(0, 8)}-${qrCodeId.substring(0, 8)}`;
    
    // Create QR code URL that points to mobile booking interface
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.oceansoulsparkles.com.au';
    const qrUrl = `${baseUrl}/qr/${qrCode}`;

    // Generate QR code image
    const qrCodeImage = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: options.size || 256,
      ...options
    });

    // Initialize analytics and revenue tracking
    const initialAnalytics = {
      created_at: new Date().toISOString(),
      total_scans: 0,
      unique_visitors: 0,
      conversion_rate: 0,
      peak_usage_times: [],
      device_types: {},
      referrer_sources: {}
    };

    const initialRevenueTracking = {
      total_revenue: 0,
      booking_count: 0,
      average_booking_value: 0,
      artist_breakdown: {},
      service_breakdown: {},
      daily_revenue: {},
      hourly_distribution: {}
    };

    // Store QR code in database
    const { data: qrCodeData, error } = await supabase
      .from('event_qr_codes')
      .insert([{
        id: qrCodeId,
        code: qrCode,
        event_id: eventId,
        event_name: eventName,
        event_location: eventLocation,
        event_start_date: eventStartDate,
        event_end_date: eventEndDate,
        assigned_artists: assignedArtists,
        available_services: availableServices,
        is_active: true,
        usage_count: 0,
        max_usage: options.maxUsage || null,
        revenue_tracking: initialRevenueTracking,
        analytics_data: initialAnalytics
      }])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to store QR code: ${error.message}`);
    }

    return {
      success: true,
      qrCode: {
        id: qrCodeId,
        code: qrCode,
        url: qrUrl,
        image: qrCodeImage,
        eventId,
        eventName,
        eventLocation,
        isActive: true,
        createdAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Error generating QR code:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Validate and retrieve QR code data
 * @param {string} qrCode - QR code identifier
 * @returns {Promise<Object>} QR code validation result
 */
export async function validateQRCode(qrCode) {
  try {
    // Fetch QR code data from database
    const { data: qrData, error } = await supabase
      .from('event_qr_codes')
      .select(`
        *,
        events:event_id (
          id,
          name,
          location,
          start_date,
          end_date,
          status,
          max_capacity,
          current_bookings
        )
      `)
      .eq('code', qrCode)
      .single();

    if (error || !qrData) {
      return {
        isValid: false,
        error: 'QR code not found',
        code: 'QR_NOT_FOUND'
      };
    }

    // Check if QR code is active
    if (!qrData.is_active) {
      return {
        isValid: false,
        error: 'QR code has been deactivated',
        code: 'QR_INACTIVE'
      };
    }

    // Check if event has expired
    const now = new Date();
    const eventEndDate = new Date(qrData.event_end_date);
    
    if (now > eventEndDate) {
      // Automatically deactivate expired QR codes
      await supabase
        .from('event_qr_codes')
        .update({ is_active: false })
        .eq('id', qrData.id);

      return {
        isValid: false,
        error: 'Event has ended',
        code: 'EVENT_EXPIRED',
        eventEndDate: qrData.event_end_date
      };
    }

    // Check if event hasn't started yet
    const eventStartDate = new Date(qrData.event_start_date);
    const timeUntilStart = eventStartDate - now;
    const hoursUntilStart = timeUntilStart / (1000 * 60 * 60);

    // Allow bookings up to 24 hours before event starts
    if (hoursUntilStart > 24) {
      return {
        isValid: false,
        error: 'Bookings not yet available for this event',
        code: 'EVENT_NOT_STARTED',
        eventStartDate: qrData.event_start_date,
        hoursUntilStart: Math.ceil(hoursUntilStart)
      };
    }

    // Check usage limits
    if (qrData.max_usage && qrData.usage_count >= qrData.max_usage) {
      return {
        isValid: false,
        error: 'QR code usage limit reached',
        code: 'USAGE_LIMIT_REACHED'
      };
    }

    // Check event capacity
    const event = qrData.events;
    if (event.max_capacity && event.current_bookings >= event.max_capacity) {
      return {
        isValid: false,
        error: 'Event is fully booked',
        code: 'EVENT_FULL',
        maxCapacity: event.max_capacity,
        currentBookings: event.current_bookings
      };
    }

    return {
      isValid: true,
      qrData: {
        id: qrData.id,
        code: qrData.code,
        eventId: qrData.event_id,
        eventName: qrData.event_name,
        eventLocation: qrData.event_location,
        eventStartDate: qrData.event_start_date,
        eventEndDate: qrData.event_end_date,
        assignedArtists: qrData.assigned_artists,
        availableServices: qrData.available_services,
        usageCount: qrData.usage_count,
        maxUsage: qrData.max_usage,
        event: event
      }
    };

  } catch (error) {
    console.error('Error validating QR code:', error);
    return {
      isValid: false,
      error: 'Failed to validate QR code',
      code: 'VALIDATION_ERROR'
    };
  }
}

/**
 * Track QR code scan and update analytics
 * @param {string} qrCodeId - QR code UUID
 * @param {Object} scanData - Scan tracking data
 * @returns {Promise<Object>} Tracking result
 */
export async function trackQRCodeScan(qrCodeId, scanData = {}) {
  try {
    const {
      userAgent = '',
      referrer = '',
      ipAddress = '',
      deviceType = 'unknown',
      timestamp = new Date().toISOString()
    } = scanData;

    // Get current analytics data
    const { data: qrData, error: fetchError } = await supabase
      .from('event_qr_codes')
      .select('analytics_data, usage_count')
      .eq('id', qrCodeId)
      .single();

    if (fetchError) {
      throw new Error(`Failed to fetch QR code data: ${fetchError.message}`);
    }

    // Update analytics
    const currentAnalytics = qrData.analytics_data || {};
    const updatedAnalytics = {
      ...currentAnalytics,
      total_scans: (currentAnalytics.total_scans || 0) + 1,
      last_scan_at: timestamp,
      device_types: {
        ...currentAnalytics.device_types,
        [deviceType]: (currentAnalytics.device_types?.[deviceType] || 0) + 1
      }
    };

    // Track hourly distribution
    const hour = new Date(timestamp).getHours();
    updatedAnalytics.hourly_distribution = {
      ...currentAnalytics.hourly_distribution,
      [hour]: (currentAnalytics.hourly_distribution?.[hour] || 0) + 1
    };

    // Update QR code record
    const { error: updateError } = await supabase
      .from('event_qr_codes')
      .update({
        analytics_data: updatedAnalytics,
        usage_count: qrData.usage_count + 1,
        updated_at: timestamp
      })
      .eq('id', qrCodeId);

    if (updateError) {
      throw new Error(`Failed to update analytics: ${updateError.message}`);
    }

    return {
      success: true,
      scanCount: qrData.usage_count + 1,
      analytics: updatedAnalytics
    };

  } catch (error) {
    console.error('Error tracking QR code scan:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get QR code analytics and revenue data
 * @param {string} qrCodeId - QR code UUID
 * @returns {Promise<Object>} Analytics data
 */
export async function getQRCodeAnalytics(qrCodeId) {
  try {
    const { data: qrData, error } = await supabase
      .from('event_qr_codes')
      .select(`
        *,
        events:event_id (
          name,
          location,
          start_date,
          end_date,
          status
        )
      `)
      .eq('id', qrCodeId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch QR code analytics: ${error.message}`);
    }

    // Get associated bookings for revenue calculation
    const { data: bookings, error: bookingsError } = await supabase
      .from('event_bookings')
      .select('revenue_amount, artist_earnings, platform_earnings, created_at, payment_status')
      .eq('qr_code_id', qrCodeId);

    if (bookingsError) {
      console.warn('Failed to fetch booking data:', bookingsError);
    }

    // Calculate revenue metrics
    const completedBookings = bookings?.filter(b => b.payment_status === 'completed') || [];
    const totalRevenue = completedBookings.reduce((sum, b) => sum + parseFloat(b.revenue_amount || 0), 0);
    const totalArtistEarnings = completedBookings.reduce((sum, b) => sum + parseFloat(b.artist_earnings || 0), 0);
    const totalPlatformEarnings = completedBookings.reduce((sum, b) => sum + parseFloat(b.platform_earnings || 0), 0);

    return {
      success: true,
      analytics: {
        qrCode: {
          id: qrData.id,
          code: qrData.code,
          eventName: qrData.event_name,
          eventLocation: qrData.event_location,
          isActive: qrData.is_active,
          createdAt: qrData.created_at
        },
        usage: {
          totalScans: qrData.usage_count || 0,
          maxUsage: qrData.max_usage,
          usageRate: qrData.max_usage ? (qrData.usage_count / qrData.max_usage * 100) : null
        },
        revenue: {
          totalRevenue,
          totalArtistEarnings,
          totalPlatformEarnings,
          bookingCount: completedBookings.length,
          averageBookingValue: completedBookings.length > 0 ? totalRevenue / completedBookings.length : 0,
          conversionRate: qrData.usage_count > 0 ? (completedBookings.length / qrData.usage_count * 100) : 0
        },
        analytics: qrData.analytics_data || {},
        event: qrData.events
      }
    };

  } catch (error) {
    console.error('Error getting QR code analytics:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Deactivate expired QR codes (utility function for cleanup)
 * @returns {Promise<Object>} Cleanup result
 */
export async function deactivateExpiredQRCodes() {
  try {
    const now = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('event_qr_codes')
      .update({ is_active: false })
      .lt('event_end_date', now)
      .eq('is_active', true)
      .select('id, code, event_name');

    if (error) {
      throw new Error(`Failed to deactivate expired QR codes: ${error.message}`);
    }

    return {
      success: true,
      deactivatedCount: data?.length || 0,
      deactivatedCodes: data || []
    };

  } catch (error) {
    console.error('Error deactivating expired QR codes:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
