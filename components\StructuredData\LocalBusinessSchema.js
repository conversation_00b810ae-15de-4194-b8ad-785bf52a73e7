import React from 'react';

const LocalBusinessSchema = () => {
  const localBusinessData = {
    "@context": "https://schema.org",
    "@type": "EntertainmentBusiness",
    "name": "OceanSoulSparkles",
    "image": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
    "url": "https://www.oceansoulsparkles.com.au",
    "telephone": "+61-XXX-XXX-XXX",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Melbourne",
      "addressRegion": "Victoria",
      "addressCountry": "AU"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "-37.8136",
      "longitude": "144.9631"
    },
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday"
        ],
        "opens": "09:00",
        "closes": "17:00"
      },
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Saturday",
          "Sunday"
        ],
        "opens": "10:00",
        "closes": "16:00"
      }
    ],
    "priceRange": "$$",
    "paymentAccepted": "Cash, Credit Card, PayPal, Square",
    "currenciesAccepted": "AUD",
    "description": "OceanSoulSparkles offers face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available."
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(localBusinessData) }}
    />
  );
};

export default LocalBusinessSchema;
