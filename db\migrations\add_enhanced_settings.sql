-- Enhanced Settings Migration
-- This script adds new settings for Square API, Google integrations, SEO, email, and security

-- Insert enhanced settings if they don't exist
INSERT INTO public.settings (key, value) VALUES
  -- Square API Settings
  ('square_application_id', ''),
  ('square_access_token', ''),
  ('square_environment', 'sandbox'),
  ('square_location_id', ''),
  ('square_webhook_signature_key', ''),
  ('square_currency', 'AUD'),
  ('enable_square_payments', 'false'),
  
  -- Google Integrations
  ('google_analytics_measurement_id', ''),
  ('google_tag_manager_id', ''),
  ('google_search_console_verification', ''),
  ('google_business_profile_id', ''),
  ('google_my_business_api_key', ''),
  ('google_maps_api_key', ''),
  ('google_ads_customer_id', ''),
  ('google_ads_conversion_id', ''),
  ('enable_google_business_integration', 'false'),
  ('enable_google_reviews_widget', 'false'),
  
  -- SEO Settings
  ('seo_meta_title', ''),
  ('seo_meta_description', ''),
  ('seo_keywords', ''),
  ('seo_canonical_url', ''),
  ('enable_schema_markup', 'true'),
  
  -- Email Settings
  ('smtp_host', ''),
  ('smtp_port', '587'),
  ('smtp_username', ''),
  ('smtp_password', ''),
  ('smtp_encryption', 'tls'),
  ('email_from_address', ''),
  ('email_from_name', ''),
  ('enable_email_notifications', 'true'),
  
  -- Backup & Security
  ('enable_auto_backup', 'false'),
  ('backup_frequency', 'weekly'),
  ('enable_two_factor_auth', 'false'),
  ('session_timeout', '60'),
  
  -- Additional Settings
  ('notification_email', ''),
  ('google_analytics_id', ''),
  ('facebook_pixel_id', ''),
  ('logo_url', ''),
  ('favicon_url', ''),
  ('terms_url', ''),
  ('privacy_url', ''),
  ('social_facebook', ''),
  ('social_instagram', ''),
  ('social_twitter', ''),
  ('social_linkedin', ''),
  ('social_youtube', ''),
  ('custom_css', ''),
  ('custom_js', '')
ON CONFLICT (key) DO NOTHING;

-- Create indexes for frequently accessed settings
CREATE INDEX IF NOT EXISTS idx_settings_key ON public.settings(key);

-- Create a function to get multiple settings at once
CREATE OR REPLACE FUNCTION public.get_settings(setting_keys text[])
RETURNS TABLE(key text, value text)
LANGUAGE sql
STABLE
AS $$
  SELECT key, value 
  FROM public.settings 
  WHERE key = ANY(setting_keys);
$$;

-- Create a function to update multiple settings at once
CREATE OR REPLACE FUNCTION public.update_settings(settings_data jsonb)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  setting_key text;
  setting_value text;
BEGIN
  FOR setting_key, setting_value IN SELECT * FROM jsonb_each_text(settings_data)
  LOOP
    INSERT INTO public.settings (key, value, updated_at)
    VALUES (setting_key, setting_value, NOW())
    ON CONFLICT (key) 
    DO UPDATE SET 
      value = EXCLUDED.value,
      updated_at = EXCLUDED.updated_at;
  END LOOP;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_settings(text[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_settings(jsonb) TO authenticated;

-- Add comment
COMMENT ON TABLE public.settings IS 'Application settings for Ocean Soul Sparkles website';
COMMENT ON FUNCTION public.get_settings(text[]) IS 'Get multiple settings by key array';
COMMENT ON FUNCTION public.update_settings(jsonb) IS 'Update multiple settings from JSON object';
