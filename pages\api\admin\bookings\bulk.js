import { supabaseAdmin } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';
import { sendBookingNotification } from '@/lib/notifications-server';

/**
 * Bulk Operations API for Bookings
 * 
 * POST /api/admin/bookings/bulk
 * 
 * Body:
 * {
 *   action: 'confirm' | 'cancel' | 'complete' | 'export' | 'send_reminder' | 'duplicate',
 *   bookingIds: string[],
 *   options?: {
 *     sendNotifications?: boolean,
 *     reason?: string,
 *     newDate?: string (for reschedule)
 *   }
 * }
 */
export default async function handler(req, res) {
  // Generate request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Bulk operations request started`);

  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log(`[${requestId}] Method ${req.method} not allowed`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const authResult = await authTokenManager.verifyToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract and validate request body
    const { action, bookingIds, options = {} } = req.body;

    if (!action || !bookingIds || !Array.isArray(bookingIds)) {
      return res.status(400).json({ 
        error: 'Invalid request body. Required: action, bookingIds (array)' 
      });
    }

    if (bookingIds.length === 0) {
      return res.status(400).json({ error: 'No booking IDs provided' });
    }

    if (bookingIds.length > 100) {
      return res.status(400).json({ error: 'Too many bookings. Maximum 100 per request' });
    }

    const validActions = ['confirm', 'cancel', 'complete', 'export', 'send_reminder', 'duplicate'];
    if (!validActions.includes(action)) {
      return res.status(400).json({ 
        error: `Invalid action. Must be one of: ${validActions.join(', ')}` 
      });
    }

    console.log(`[${requestId}] Processing bulk ${action} for ${bookingIds.length} bookings`);

    // First, fetch the bookings to validate they exist and get customer info
    const { data: bookings, error: fetchError } = await supabaseAdmin
      .from('bookings')
      .select(`
        id,
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        location,
        notes,
        booking_reference,
        customers:customer_id (name, email, phone),
        services:service_id (name, color, price)
      `)
      .in('id', bookingIds);

    if (fetchError) {
      console.error(`[${requestId}] Error fetching bookings:`, fetchError);
      throw fetchError;
    }

    if (!bookings || bookings.length === 0) {
      return res.status(404).json({ error: 'No bookings found with provided IDs' });
    }

    console.log(`[${requestId}] Found ${bookings.length} bookings to process`);

    let results = {
      success: [],
      failed: [],
      total: bookings.length,
      action: action
    };

    // Process each action
    switch (action) {
      case 'confirm':
        results = await bulkConfirm(bookings, options, requestId);
        break;
      
      case 'cancel':
        results = await bulkCancel(bookings, options, requestId);
        break;
      
      case 'complete':
        results = await bulkComplete(bookings, options, requestId);
        break;
      
      case 'export':
        results = await bulkExport(bookings, options, requestId);
        break;
      
      case 'send_reminder':
        results = await bulkSendReminder(bookings, options, requestId);
        break;
      
      case 'duplicate':
        results = await bulkDuplicate(bookings, options, requestId);
        break;
      
      default:
        return res.status(400).json({ error: 'Action not implemented' });
    }

    console.log(`[${requestId}] Bulk operation completed:`, {
      action,
      total: results.total,
      successful: results.success.length,
      failed: results.failed.length
    });

    return res.status(200).json(results);

  } catch (error) {
    console.error(`[${requestId}] Error in bulk operations:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred during bulk operation'
    });
  }
}

// Bulk confirm bookings
async function bulkConfirm(bookings, options, requestId) {
  const results = { success: [], failed: [], total: bookings.length, action: 'confirm' };
  const sendNotifications = options.sendNotifications !== false; // Default to true

  for (const booking of bookings) {
    try {
      // Update booking status
      const { error: updateError } = await supabaseAdmin
        .from('bookings')
        .update({ 
          status: 'confirmed',
          updated_at: new Date().toISOString()
        })
        .eq('id', booking.id);

      if (updateError) throw updateError;

      // Send notification if enabled
      if (sendNotifications && booking.customers?.email) {
        try {
          await sendBookingNotification({
            bookingId: booking.id,
            customerId: booking.customer_id,
            status: 'confirmed',
            startTime: booking.start_time,
            serviceName: booking.services?.name || 'Service'
          });
        } catch (notificationError) {
          console.warn(`[${requestId}] Failed to send notification for booking ${booking.id}:`, notificationError);
          // Don't fail the entire operation for notification errors
        }
      }

      results.success.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        customer_name: booking.customers?.name
      });

    } catch (error) {
      console.error(`[${requestId}] Failed to confirm booking ${booking.id}:`, error);
      results.failed.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        error: error.message
      });
    }
  }

  return results;
}

// Bulk cancel bookings
async function bulkCancel(bookings, options, requestId) {
  const results = { success: [], failed: [], total: bookings.length, action: 'cancel' };
  const sendNotifications = options.sendNotifications !== false; // Default to true
  const reason = options.reason || 'Booking canceled by admin';

  for (const booking of bookings) {
    try {
      // Update booking status
      const { error: updateError } = await supabaseAdmin
        .from('bookings')
        .update({ 
          status: 'canceled',
          notes: booking.notes ? `${booking.notes}\n\nCanceled: ${reason}` : `Canceled: ${reason}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', booking.id);

      if (updateError) throw updateError;

      // Send notification if enabled
      if (sendNotifications && booking.customers?.email) {
        try {
          await sendBookingNotification({
            bookingId: booking.id,
            customerId: booking.customer_id,
            status: 'canceled',
            startTime: booking.start_time,
            serviceName: booking.services?.name || 'Service',
            reason: reason
          });
        } catch (notificationError) {
          console.warn(`[${requestId}] Failed to send notification for booking ${booking.id}:`, notificationError);
        }
      }

      results.success.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        customer_name: booking.customers?.name
      });

    } catch (error) {
      console.error(`[${requestId}] Failed to cancel booking ${booking.id}:`, error);
      results.failed.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        error: error.message
      });
    }
  }

  return results;
}

// Bulk complete bookings
async function bulkComplete(bookings, options, requestId) {
  const results = { success: [], failed: [], total: bookings.length, action: 'complete' };

  for (const booking of bookings) {
    try {
      // Update booking status
      const { error: updateError } = await supabaseAdmin
        .from('bookings')
        .update({ 
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', booking.id);

      if (updateError) throw updateError;

      results.success.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        customer_name: booking.customers?.name
      });

    } catch (error) {
      console.error(`[${requestId}] Failed to complete booking ${booking.id}:`, error);
      results.failed.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        error: error.message
      });
    }
  }

  return results;
}

// Bulk export bookings
async function bulkExport(bookings, options, requestId) {
  const results = { success: [], failed: [], total: bookings.length, action: 'export' };

  try {
    // Generate CSV data
    const csvHeaders = [
      'Booking Reference',
      'Customer Name',
      'Customer Email',
      'Customer Phone',
      'Service Name',
      'Start Time',
      'End Time',
      'Status',
      'Location',
      'Notes'
    ];

    const csvRows = bookings.map(booking => [
      booking.booking_reference || booking.id.slice(-8),
      booking.customers?.name || '',
      booking.customers?.email || '',
      booking.customers?.phone || '',
      booking.services?.name || '',
      new Date(booking.start_time).toLocaleString(),
      new Date(booking.end_time).toLocaleString(),
      booking.status,
      booking.location || '',
      booking.notes || ''
    ]);

    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    results.success = bookings.map(booking => ({
      id: booking.id,
      booking_reference: booking.booking_reference,
      customer_name: booking.customers?.name
    }));

    results.csvData = csvContent;
    results.filename = `bookings_export_${new Date().toISOString().split('T')[0]}.csv`;

  } catch (error) {
    console.error(`[${requestId}] Failed to export bookings:`, error);
    results.failed = bookings.map(booking => ({
      id: booking.id,
      booking_reference: booking.booking_reference,
      error: error.message
    }));
  }

  return results;
}

// Bulk send reminders
async function bulkSendReminder(bookings, options, requestId) {
  const results = { success: [], failed: [], total: bookings.length, action: 'send_reminder' };

  for (const booking of bookings) {
    try {
      if (!booking.customers?.email) {
        throw new Error('No email address available');
      }

      await sendBookingNotification({
        bookingId: booking.id,
        customerId: booking.customer_id,
        status: 'reminder',
        startTime: booking.start_time,
        serviceName: booking.services?.name || 'Service'
      });

      results.success.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        customer_name: booking.customers?.name
      });

    } catch (error) {
      console.error(`[${requestId}] Failed to send reminder for booking ${booking.id}:`, error);
      results.failed.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        error: error.message
      });
    }
  }

  return results;
}

// Bulk duplicate bookings
async function bulkDuplicate(bookings, options, requestId) {
  const results = { success: [], failed: [], total: bookings.length, action: 'duplicate' };

  for (const booking of bookings) {
    try {
      // Create a duplicate booking
      const { data: newBooking, error: createError } = await supabaseAdmin
        .from('bookings')
        .insert([{
          customer_id: booking.customer_id,
          service_id: booking.service_id,
          start_time: booking.start_time,
          end_time: booking.end_time,
          status: 'pending', // New bookings start as pending
          location: booking.location,
          notes: `${booking.notes || ''}\n\nDuplicated from booking ${booking.booking_reference || booking.id.slice(-8)}`
        }])
        .select()
        .single();

      if (createError) throw createError;

      results.success.push({
        id: newBooking.id,
        original_id: booking.id,
        booking_reference: newBooking.booking_reference,
        customer_name: booking.customers?.name
      });

    } catch (error) {
      console.error(`[${requestId}] Failed to duplicate booking ${booking.id}:`, error);
      results.failed.push({
        id: booking.id,
        booking_reference: booking.booking_reference,
        error: error.message
      });
    }
  }

  return results;
}
