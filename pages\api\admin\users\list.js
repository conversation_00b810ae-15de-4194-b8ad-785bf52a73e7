import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for listing users with their roles
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Users list API endpoint called`);

  try {
    // Check if we're in development mode with auth bypass
    const isDevelopment = process.env.NODE_ENV === 'development';
    const authBypass = process.env.ENABLE_AUTH_BYPASS === 'true';

    console.log(`[${requestId}] Environment check: NODE_ENV=${process.env.NODE_ENV}, ENABLE_AUTH_BYPASS=${process.env.ENABLE_AUTH_BYPASS}, isDevelopment=${isDevelopment}, authBypass=${authBypass}`);

    if (isDevelopment && authBypass) {
      console.log(`[${requestId}] Development mode with auth bypass enabled - skipping authentication`);
    } else {
      // Authenticate request using our robust auth module
      const { authorized, error, user, role } = await authenticateAdminRequest(req)

      if (!authorized) {
        console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error')
        return res.status(401).json({
          error: 'Unauthorized access',
          message: error?.message || 'Authentication failed'
        })
      }

      console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`)

      // Only admin users can list all users
      if (role !== 'admin' && role !== 'dev') {
        console.error(`[${requestId}] User ${user?.email} with role ${role} attempted to access admin-only endpoint`)
        return res.status(403).json({ error: 'Forbidden: Admin access required' })
      }
    }

    // Get admin client
    let adminClient;
    try {
      adminClient = getAdminClient();
      if (!adminClient) {
        console.error(`[${requestId}] Failed to initialize admin client`)
        return res.status(500).json({ error: 'Database connection failed' })
      }
    } catch (adminClientError) {
      console.error(`[${requestId}] Error initializing admin client:`, adminClientError)
      return res.status(500).json({ error: 'Database connection failed', message: adminClientError.message })
    }

    // Parse query parameters
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search = '',
      role: roleFilter
    } = req.query

    // Validate numeric parameters
    const validatedPage = Math.max(1, parseInt(page) || 1)
    const validatedLimit = Math.min(Math.max(1, parseInt(limit) || 10), 100) // Between 1 and 100
    const offset = (validatedPage - 1) * validatedLimit

    // Get users from auth schema using the correct approach for Supabase
    // In Supabase JS client, we need to use rpc for auth schema tables
    console.log(`[${requestId}] Fetching users with pagination: page=${validatedPage}, limit=${validatedLimit}`)

    // Fetch users from Supabase auth.users table
    let usersData, usersError, count;

    // Use Supabase Admin API to list users from auth.users table
    console.log(`[${requestId}] Fetching users from auth.users table`);

    // Calculate pagination for auth.listUsers
    const authPage = Math.ceil((offset + 1) / validatedLimit);

    const { data: authData, error: authError } = await adminClient.auth.admin.listUsers({
      page: authPage,
      perPage: validatedLimit
    });

    if (authError) {
      console.error(`[${requestId}] Error fetching auth users:`, authError);
      usersError = authError;
      usersData = [];
      count = 0;
    } else {
      usersData = authData.users || [];
      count = authData.total || usersData.length;
      usersError = null;

      // Apply search filter in memory if needed
      if (search) {
        usersData = usersData.filter(user =>
          user.email && user.email.toLowerCase().includes(search.toLowerCase())
        );
        count = usersData.length;
      }
    }

    if (usersError) {
      console.error(`[${requestId}] Error fetching users:`, usersError)
      throw usersError
    }

    // Get user IDs
    const userIds = usersData.map(user => user.id)

    // If no users found, return empty array
    if (userIds.length === 0) {
      return res.status(200).json({
        users: [],
        total: 0,
        page: validatedPage,
        limit: validatedLimit,
        pages: 0
      })
    }

    // Get roles and profiles for these users
    let rolesData, rolesError, profilesData, profilesError;

    // Build the query to fetch user roles
    let rolesQuery = adminClient
      .from('user_roles')
      .select('id, role')
      .in('id', userIds)

    // Apply role filter if provided
    if (roleFilter && roleFilter !== 'all') {
      rolesQuery = rolesQuery.eq('role', roleFilter)
    }

    const rolesResult = await rolesQuery;
    rolesData = rolesResult.data || [];
    rolesError = rolesResult.error;

    if (rolesError) {
      console.error(`[${requestId}] Error fetching roles:`, rolesError)
      throw rolesError
    }

    // Fetch user profiles for status information
    const profilesResult = await adminClient
      .from('user_profiles')
      .select('id, user_status, deactivated_at, deactivated_by')
      .in('id', userIds)

    profilesData = profilesResult.data || [];
    profilesError = profilesResult.error;

    if (profilesError) {
      console.warn(`[${requestId}] Warning: Error fetching user profiles:`, profilesError)
      // Continue without profiles - status will default to 'active'
      profilesData = [];
    }

    // Create maps of user ID to role and status
    const userRolesMap = {}
    rolesData.forEach(roleData => {
      userRolesMap[roleData.id] = roleData.role
    })

    const userProfilesMap = {}
    profilesData.forEach(profileData => {
      userProfilesMap[profileData.id] = {
        user_status: profileData.user_status || 'active',
        deactivated_at: profileData.deactivated_at,
        deactivated_by: profileData.deactivated_by
      }
    })

    // Combine user data with roles and status
    const users = usersData.map(user => {
      const profile = userProfilesMap[user.id] || {}
      return {
        id: user.id,
        email: user.email,
        role: userRolesMap[user.id] || 'unknown',
        user_status: profile.user_status || 'active',
        deactivated_at: profile.deactivated_at,
        deactivated_by: profile.deactivated_by,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at
      }
    })

    // Filter by role if needed
    const filteredUsers = roleFilter && roleFilter !== 'all'
      ? users.filter(user => user.role === roleFilter)
      : users

    // Calculate total pages
    const totalPages = Math.ceil((roleFilter && roleFilter !== 'all' ? filteredUsers.length : count) / validatedLimit)

    // Return the response
    return res.status(200).json({
      users: filteredUsers,
      total: roleFilter && roleFilter !== 'all' ? filteredUsers.length : count,
      page: validatedPage,
      limit: validatedLimit,
      pages: totalPages
    })
  } catch (error) {
    console.error(`[${requestId}] Error in users list API:`, error)
    return res.status(500).json({
      error: 'Failed to fetch users',
      message: error.message || 'An unexpected error occurred'
    })
  }
}
