.main {
  width: 100%;
  margin: 0 auto;
  background-image: url('/background.png'); /* Added pink cloud background */
  background-size: cover; /* Ensure background covers the area */
  background-position: center; /* Center the background image */
  background-attachment: fixed; /* Keep background fixed during scroll */
}

/* Common section styles */
.sectionTitle {
  font-size: 2.5rem;
  color: var(--text-color);
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: var(--border-radius-full);
}

.sectionDescription {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 2rem auto 4rem;
  text-align: center;
  line-height: 1.6;
}

/* Services section */
.services {
  padding: 6rem 2rem;
  text-align: center;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.7); /* Translucent background */
  /* Removed background image to allow main background to show through */
}

.serviceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-bottom: 4rem;
}

.serviceCard {
  background-color: rgba(255, 209, 220, 0.8); /* Translucent pastel pink */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  padding: 2rem 1.5rem 2.5rem;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(236, 64, 122, 0.2);
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  opacity: 0;
  z-index: -1;
  transition: var(--transition-normal);
}

.serviceCard:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  border-color: transparent;
}

.serviceCard:hover::before {
  opacity: 0.05;
}

.serviceCard:hover h3 {
  color: var(--primary-color);
}

.serviceImage {
  height: 140px;
  width: 140px;
  margin: 0 auto 2rem;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 3px solid var(--primary-light);
  transition: var(--transition-normal);
}

.serviceCard:hover .serviceImage {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.serviceImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.serviceCard:hover .serviceImg {
  transform: scale(1.1);
}

.serviceCard h3 {
  font-size: 1.5rem;
  margin-bottom: 1.25rem;
  color: var(--text-color);
  font-weight: 600;
  transition: var(--transition-normal);
}

.serviceCard p {
  color: var(--light-text-color);
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.serviceLink {
  display: inline-block;
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  position: relative;
  padding-bottom: 3px;
  transition: var(--transition-normal);
}

.serviceLink::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition-normal);
}

.serviceLink:hover {
  color: var(--primary-dark);
}

.serviceLink:hover::after {
  width: 100%;
}

.servicesLink {
  margin-top: 3rem;
}

/* Eco-friendly section */
.ecoFriendly {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 8rem 2rem;
  background: rgba(240, 255, 250, 0.7); /* Translucent light mint background */
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.ecoFriendly::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234ecdc4' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.ecoContent {
  flex: 1;
  min-width: 300px;
  padding: 0 3rem 0 0;
  max-width: 600px;
}

.ecoTitle {
  font-size: 2.2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  font-weight: 700;
  position: relative;
  text-align: left;
}

.ecoTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: var(--border-radius-full);
}

.ecoDescription {
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.7;
  font-size: 1.05rem;
}

.ecoImage {
  flex: 1;
  min-width: 300px;
  height: 450px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  position: relative;
  transform: rotate(2deg);
  transition: var(--transition-normal);
}

.ecoImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  opacity: 0.2;
  z-index: 1;
  transition: var(--transition-normal);
}

.ecoImage:hover {
  transform: rotate(0deg) scale(1.02);
}

.ecoImage:hover::before {
  opacity: 0.1;
}

.ecoImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.ecoImage:hover .ecoImg {
  transform: scale(1.05);
}

/* Gallery section */
.gallery {
  padding: 6rem 2rem;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  text-align: center;
  background-color: rgba(208, 235, 255, 0.7); /* Translucent pastel blue */
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 4rem;
}

.galleryItem {
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 8px;
  position: relative;
  box-shadow: var(--shadow-md);
  cursor: pointer;
}

.galleryImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.galleryOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 60%);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 1.5rem;
  opacity: 0;
  transition: var(--transition-normal);
}

.galleryOverlay span {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  transform: translateY(20px);
  transition: var(--transition-normal);
}

.galleryItem:hover .galleryImg {
  transform: scale(1.1);
}

.galleryItem:hover .galleryOverlay {
  opacity: 1;
}

.galleryItem:hover .galleryOverlay span {
  transform: translateY(0);
}

.galleryLink {
  margin-top: 2rem;
}

/* Contact section */
.contact {
  padding: 6rem 2rem;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  text-align: center;
  position: relative;
  background-color: rgba(230, 208, 255, 0.7); /* Translucent pastel purple */
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.contactContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  margin-top: 4rem;
}

.contactInfo {
  flex: 1;
  min-width: 300px;
}

.contactCard {
  background: rgba(255, 255, 255, 0.85); /* Translucent white */
  border-radius: 12px;
  padding: 3rem;
  height: 100%;
  box-shadow: var(--shadow-lg);
  text-align: left;
  position: relative;
  overflow: hidden;
  z-index: 1;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.contactCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--primary-gradient);
  z-index: 2;
}

.contactCard h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.contactCard p {
  color: var(--light-text-color);
  margin-bottom: 2rem;
  line-height: 1.7;
}

.contactDetail {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.contactDetail svg {
  color: var(--primary-color);
  margin-right: 1rem;
  flex-shrink: 0;
}

.contactDetail a,
.contactDetail span {
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition-normal);
}

.contactDetail a:hover {
  color: var(--primary-color);
}

.contactSocial {
  display: flex;
  gap: 1rem;
  margin-top: 2.5rem;
}

.contactSocial a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  transition: var(--transition-normal);
}

.contactSocial a:hover {
  transform: translateY(-3px);
  background-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.contactSocial img {
  width: 20px;
  height: 20px;
}

.contactFormContainer {
  flex: 1.5;
  min-width: 300px;
}

.contactForm {
  background: rgba(255, 255, 255, 0.85); /* Translucent white */
  border-radius: 12px;
  padding: 3rem;
  box-shadow: var(--shadow-lg);
  text-align: left;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.formRow {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.formGroup {
  flex: 1;
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.formInput,
.formTextarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  font-family: var(--font-secondary);
  font-size: 1rem;
  transition: var(--transition-normal);
  background-color: var(--background-off-white);
}

.formInput:focus,
.formTextarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.formTextarea {
  min-height: 150px;
  resize: vertical;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .services,
  .gallery,
  .contact {
    padding: 5rem 2rem;
  }

  .ecoFriendly {
    padding: 6rem 2rem;
  }

  .ecoContent {
    padding-right: 2rem;
  }

  .ecoImage {
    height: 400px;
  }

  .contactForm {
    padding: 2.5rem;
  }

  .contactCard {
    padding: 2.5rem;
  }
}

@media (max-width: 992px) {
  .services,
  .gallery,
  .contact {
    padding: 4rem 1.5rem;
  }

  .ecoFriendly {
    flex-direction: column;
    padding: 5rem 1.5rem;
  }

  .ecoContent {
    padding-right: 0;
    margin-bottom: 3rem;
    max-width: 100%;
  }

  .ecoImage {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .formRow {
    flex-direction: column;
    gap: 0;
    margin-bottom: 0;
  }

  .sectionTitle {
    font-size: 2.25rem;
  }

  .sectionDescription {
    font-size: 1.1rem;
    margin: 1.5rem auto 3rem;
  }

  .ecoTitle {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .services,
  .gallery,
  .contact {
    padding: 3.5rem 1.25rem;
  }

  .ecoFriendly {
    padding: 4rem 1.25rem;
  }

  .serviceGrid {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }

  .galleryGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .contactContainer {
    flex-direction: column;
    gap: 2rem;
  }

  .contactInfo,
  .contactFormContainer {
    min-width: 100%;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .ecoTitle {
    font-size: 1.8rem;
  }

  .ecoDescription {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .services,
  .gallery,
  .contact {
    padding: 3rem 1rem;
  }

  .ecoFriendly {
    padding: 3.5rem 1rem;
  }

  .galleryGrid {
    grid-template-columns: 1fr;
    max-width: 350px;
    margin-left: auto;
    margin-right: auto;
  }

  .contactForm,
  .contactCard {
    padding: 2rem;
  }

  .sectionTitle {
    font-size: 1.8rem;
  }

  .sectionDescription {
    font-size: 1rem;
  }

  .ecoTitle {
    font-size: 1.6rem;
  }

  .serviceCard h3 {
    font-size: 1.3rem;
  }
}
