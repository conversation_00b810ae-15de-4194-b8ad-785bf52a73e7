import * as fs from 'fs'
import * as path from 'path'
import { promisify } from 'util'

/**
 * Server-side utility to load environment variables from .env.local
 */
export async function loadServerEnv() {
  try {
    const readFile = promisify(fs.readFile)
    const envPath = path.resolve(process.cwd(), '.env.local')
    
    const envData = await readFile(envPath, 'utf8')
    return envData.split('\n').reduce((acc, line) => {
      const match = line.match(/^([^#\s][^=]+)=(.*)$/)
      if (match) {
        acc[match[1].trim()] = match[2].trim()
      }
      return acc
    }, {})
  } catch (error) {
    console.error('Error loading server environment:', error)
    return {}
  }
}

/**
 * Get environment variable with fallback
 */
export function getEnvVar(key, fallback = '') {
  if (typeof window === 'undefined') {
    // Server-side
    return process.env[key] || fallback
  }
  // Client-side
  return process.env[`NEXT_PUBLIC_${key}`] || fallback
}
