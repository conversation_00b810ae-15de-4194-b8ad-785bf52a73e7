import { useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from '@/styles/OceanSparklesShowcase.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * OceanSparklesShowcase component with visual effects and animated content
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Showcase section title
 * @param {string} props.subtitle - Showcase section subtitle
 * @param {Array} props.images - Array of image objects with src, alt, and caption
 * @param {string} props.ctaText - Call to action button text
 * @param {string} props.ctaLink - Call to action button link
 * @returns {JSX.Element}
 */
const OceanSparklesShowcase = ({ 
  title = 'Unleash Your Creativity',
  subtitle = 'A world of vivid colors, glitters, and artistic transformations',
  images = [],
  ctaText = 'Book Your Experience',
  ctaLink = '/book-online',
  ...props 
}) => {
  const showcaseRef = useRef(null);
  const imagesRef = useRef(null);
  
  useEffect(() => {
    // Add parallax effect on scroll
    const handleScroll = () => {
      if (!showcaseRef.current) return;
      
      const scrollPosition = window.scrollY;
      const showcaseOffset = showcaseRef.current.offsetTop;
      const scrollRelative = scrollPosition - showcaseOffset;
      
      // Apply floating effect to images container
      if (imagesRef.current) {
        // Create a subtle floating effect based on scroll position
        const floatY = Math.sin(scrollPosition * 0.002) * 15;
        const floatX = Math.cos(scrollPosition * 0.002) * 5;
        imagesRef.current.style.transform = `translate(${floatX}px, ${floatY}px)`;
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  // Default images if none provided
  const defaultImages = [
    { src: '/Adult Rainbow Leopard.jpg', alt: 'Airbrush Face & Body Painting', caption: 'Airbrush Art' },
    { src: '/images/services/festival-braids.jpg', alt: 'Festival Braids', caption: 'Festival Looks' },
    { src: '/Airbrush Temporary Tattoos.png', alt: 'Airbrush Temporary Tattoos', caption: 'Temporary Tattoos' },
    { src: '/images/services/face-painting-children.jpg', alt: 'Face Painting', caption: 'Face Painting' },
    { src: '/Blue-Sparkles.JPG', alt: 'Eco Glitter', caption: 'Eco Glitter' },
  ];
  
  const showcaseImages = images.length > 0 ? images : defaultImages;
  
  return (
    <section 
      ref={showcaseRef}
      className={styles.showcaseSection}
      {...props}
    >
      <div className={styles.backgroundImage}></div>
      <div className={styles.overlayEffect}></div>
      
      <div className={styles.showcaseContent}>
        <AnimatedSection animation="fade-in" delay={100}>
          <h2 className={styles.showcaseTitle}>{title}</h2>
          <p className={styles.showcaseSubtitle}>{subtitle}</p>
        </AnimatedSection>
        
        <div ref={imagesRef} className={styles.showcaseImagesContainer}>
          {showcaseImages.map((image, index) => (
            <AnimatedSection 
              key={index}
              animation="scale-in"
              delay={200 + (index * 150)}
              className={styles.showcaseImageWrapper}
            >
              <div className={`${styles.showcaseImage} ${styles[`image${index + 1}`]}`}>
                <img src={image.src} alt={image.alt} />
                <div className={styles.imageCaption}>
                  <span>{image.caption}</span>
                </div>
              </div>
            </AnimatedSection>
          ))}
        </div>
        
        <AnimatedSection animation="fade-in" delay={800} className={styles.showcaseCta}>
          <Link href={ctaLink} className="button button--glow">
            {ctaText}
          </Link>
        </AnimatedSection>      </div>
    </section>
  );
};

export default OceanSparklesShowcase; 