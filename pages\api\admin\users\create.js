import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request using our robust auth module
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      console.error('Create user API: Authentication failed:', error?.message || 'Unknown error')
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    console.log('Create user API: Authentication successful. User:', user?.email, 'Role:', role)
    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ error: 'Unauthorized. Only administrators and developers can create users.' })
    }    // Get admin client to bypass RLS policies
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get request body
    const { email, password, role: newUserRole, name, phone, notes, sendWelcomeEmail = true } = req.body

    // Validate required fields
    if (!email) {
      return res.status(400).json({ error: 'Email is required' })
    }

    // Password is only required for non-artist/braider roles
    const requiresPassword = !['artist', 'braider'].includes(newUserRole)
    if (requiresPassword && !password) {
      return res.status(400).json({ error: 'Password is required for this role' })
    }

    // Validate role - Updated to support new 5-role system
    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user']
    if (!validRoles.includes(newUserRole)) {
      return res.status(400).json({
        error: 'Invalid role. Must be one of: dev, admin, artist, braider, user'
      })
    }

    // Check if user already exists in auth.users
    try {
      const { data: existingUsers, error: checkError } = await adminClient.auth.admin.listUsers()
      if (checkError) {
        console.error('Error checking existing users:', checkError)
        return res.status(500).json({ error: 'Failed to verify user uniqueness' })
      }

      const existingUser = existingUsers.users.find(u => u.email === email)
      if (existingUser) {
        console.log('User already exists with email:', email, 'ID:', existingUser.id)
        return res.status(409).json({
          error: 'User already exists',
          message: `A user with email ${email} already exists in the system`,
          existingUserId: existingUser.id
        })
      }
    } catch (error) {
      console.error('Error checking user existence:', error)
      return res.status(500).json({ error: 'Failed to verify user uniqueness' })
    }

    // Create user with Supabase Auth
    const userCreateData = {
      email,
      email_confirm: true, // Auto-confirm email
      user_metadata: name ? { name } : {}
    }

    // Only add password for roles that require it
    if (requiresPassword) {
      userCreateData.password = password
    } else {
      // Generate a secure random password for artist/braider accounts
      userCreateData.password = require('crypto').randomBytes(32).toString('hex')
    }

    const { data: userData, error: userError } = await adminClient.auth.admin.createUser(userCreateData)

    if (userError) {
      console.error('Error creating user:', userError)

      // Provide more specific error messages
      if (userError.message?.includes('already registered')) {
        return res.status(409).json({
          error: 'User already exists',
          message: `A user with email ${email} is already registered`
        })
      }

      return res.status(400).json({ error: userError.message || 'Failed to create user' })
    }

    // Add user role
    const { error: roleError } = await adminClient
      .from('user_roles')
      .insert([
        { id: userData.user.id, role: newUserRole }
      ])

    if (roleError) {
      console.error('Error setting user role:', roleError)

      // Try to delete the user if role assignment fails
      try {
        await adminClient.auth.admin.deleteUser(userData.user.id)
      } catch (deleteError) {
        console.error('Error deleting user after role assignment failure:', deleteError)
      }

      return res.status(500).json({ error: 'Failed to set user role' })
    }

    // Create user profile
    const { error: profileError } = await adminClient
      .from('user_profiles')
      .insert([
        {
          id: userData.user.id,
          name: name || email,
          phone: phone || null,
          notes: notes || null,
          subscription_status: 'pending',
          is_active: true
        }
      ])

    if (profileError) {
      console.error('Error creating user profile:', profileError)
      // Continue anyway - profile creation is not critical for user creation
    }

    // Create application entry and token for artist/braider roles
    let applicationToken = null
    if (newUserRole === 'artist' || newUserRole === 'braider') {
      try {
        // Create application entry
        const { data: applicationData, error: applicationError } = await adminClient
          .from('artist_braider_applications')
          .insert([
            {
              user_id: userData.user.id,
              application_type: newUserRole,
              status: 'pending',
              welcome_email_sent: false
            }
          ])
          .select()
          .single()

        if (applicationError) {
          console.error('Error creating application entry:', applicationError)
          // Continue anyway - application entry creation is not critical for user creation
        } else {
          console.log(`Created application entry for ${newUserRole} user ${userData.user.id}`)

          // Generate secure application token
          const { data: generatedToken, error: tokenError } = await adminClient
            .rpc('generate_application_token')

          if (!tokenError && generatedToken) {
            applicationToken = generatedToken
            console.log(`Generated token for ${newUserRole} user: ${applicationToken.substring(0, 8)}...`)

            // Store token in database
            const expiresAt = new Date()
            expiresAt.setDate(expiresAt.getDate() + 7) // Token expires in 7 days

            // Prepare token data for database insertion
            const tokenInsertData = {
              user_id: userData.user.id,
              application_id: applicationData.id,
              token: applicationToken,
              token_type: 'application_access',
              expires_at: expiresAt.toISOString()
            }

            // Only set created_by if the admin user exists in auth.users table
            // Skip for development admin to avoid foreign key constraint violation
            console.log(`DEBUG: Admin user ID is: ${user.id}`)
            if (user.id !== '00000000-0000-4000-8000-000000000001') {
              tokenInsertData.created_by = user.id
              console.log(`Setting created_by to: ${user.id}`)
            } else {
              console.log(`Skipping created_by field for development admin to avoid foreign key constraint`)
            }

            const { error: tokenInsertError } = await adminClient
              .from('application_tokens')
              .insert([tokenInsertData])

            if (tokenInsertError) {
              console.error('Error storing application token:', tokenInsertError)
              applicationToken = null // Don't include token in email if storage failed
            } else {
              console.log(`Stored application token for ${newUserRole} user ${userData.user.id}`)
            }
          } else {
            console.error('Error generating application token:', tokenError)
            applicationToken = null
          }
        }
      } catch (appError) {
        console.error('Error creating application entry or token:', appError)
        // Continue anyway
      }
    }

    // Log user creation activity
    try {
      await adminClient
        .from('user_activity_log')
        .insert([
          {
            user_id: userData.user.id,
            activity_type: 'user_created',
            activity_description: `User account created with role: ${newUserRole}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          }
        ])
    } catch (activityError) {
      console.error('Error logging user creation activity:', activityError)
      // Continue anyway - activity logging is not critical
    }

    // Send welcome email if requested
    let welcomeEmailResult = null
    if (sendWelcomeEmail) {
      try {
        const { sendWelcomeNotification } = await import('@/lib/notifications-server')

        welcomeEmailResult = await sendWelcomeNotification({
          userId: userData.user.id,
          email: userData.user.email,
          name: name || email,
          role: newUserRole,
          applicationToken: applicationToken
        })

        console.log('Welcome email result:', welcomeEmailResult)
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError)
        // Continue anyway - email sending failure shouldn't fail user creation
        welcomeEmailResult = {
          success: false,
          error: emailError.message,
          emailSent: false
        }
      }
    }

    // Return success response
    return res.status(201).json({
      success: true,
      message: 'User created successfully',
      user: {
        id: userData.user.id,
        email: userData.user.email,
        role: newUserRole
      },
      welcomeEmail: welcomeEmailResult ? {
        sent: welcomeEmailResult.emailSent,
        service: welcomeEmailResult.emailService,
        applicationFormRequired: welcomeEmailResult.applicationFormRequired
      } : null
    })
  } catch (error) {
    console.error('Unexpected error creating user:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
