.diagnosticsPage {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
}

.headerButtons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.refreshButton {
  background-color: #3788d8;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refreshButton:hover {
  background-color: #2c6cb0;
}

.refreshButton:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.errorBox {
  background-color: #fed7d7;
  border: 1px solid #f56565;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.errorBox p {
  color: #c53030;
  margin: 0;
}

.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statusCard {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.statusCard h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 0;
  margin-bottom: 1rem;
}

.statusCard p {
  color: #4a5568;
  margin: 0;
  font-size: 0.875rem;
}

.statusIndicator {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.statusDot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.statusText {
  font-weight: 500;
  font-size: 0.875rem;
}

.healthy {
  border-top: 4px solid #48bb78;
}

.healthy .statusDot {
  background-color: #48bb78;
}

.healthy .statusText {
  color: #48bb78;
}

.error {
  border-top: 4px solid #f56565;
}

.error .statusDot {
  background-color: #f56565;
}

.error .statusText {
  color: #f56565;
}

.checking {
  border-top: 4px solid #ecc94b;
}

.checking .statusDot {
  background-color: #ecc94b;
  animation: pulse 1.5s infinite;
}

.checking .statusText {
  color: #ecc94b;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.logsSection {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.logsSection h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 0;
  margin-bottom: 1rem;
}

.logsContainer {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: #f7fafc;
}

.logEntry {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  font-family: monospace;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.logEntry:last-child {
  border-bottom: none;
}

.logTimestamp {
  color: #718096;
  margin-right: 1rem;
  white-space: nowrap;
}

.logLevel {
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-right: 1rem;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.logMessage {
  flex: 1;
  word-break: break-word;
}

.logEntry.info .logLevel {
  background-color: #bee3f8;
  color: #2b6cb0;
}

.logEntry.warn .logLevel {
  background-color: #feebc8;
  color: #c05621;
}

.logEntry.error .logLevel {
  background-color: #fed7d7;
  color: #c53030;
}

.logEntry.debug .logLevel {
  background-color: #e2e8f0;
  color: #4a5568;
}

.noLogs {
  color: #718096;
  text-align: center;
  padding: 2rem;
  font-style: italic;
}

/* Production Readiness Styles */
.productionReadinessSection {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.productionReadinessSection h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 0;
  margin-bottom: 1rem;
}

.productionCheckButton {
  background-color: #805ad5;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.productionCheckButton:hover {
  background-color: #6b46c1;
}

.productionCheckButton:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.productionCheckButton.ready {
  background-color: #48bb78;
}

.productionCheckButton.ready:hover {
  background-color: #38a169;
}

.productionCheckButton.notReady {
  background-color: #f56565;
}

.productionCheckButton.notReady:hover {
  background-color: #e53e3e;
}

.productionStatusCard {
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.productionStatusCard.ready {
  border-color: #48bb78;
  background-color: #f0fff4;
}

.productionStatusCard.not_ready {
  border-color: #f56565;
  background-color: #fffafa;
}

.productionStatusCard.error {
  border-color: #f56565;
  background-color: #fffafa;
}

.productionStatusCard.checking {
  border-color: #ecc94b;
  background-color: #fffff0;
}

.productionDetails {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.productionDetails h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.categoryResult {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f7fafc;
  border-radius: 0.375rem;
  border-left: 4px solid #e2e8f0;
}

.categoryResult h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.testResults {
  list-style: none;
  padding: 0;
  margin: 0;
}

.testResults li {
  padding: 0.25rem 0;
  font-size: 0.875rem;
  color: #4a5568;
}

.testResults li.passed {
  color: #38a169;
}

.testResults li.failed {
  color: #e53e3e;
}

.testResults li.warning {
  color: #d69e2e;
}
