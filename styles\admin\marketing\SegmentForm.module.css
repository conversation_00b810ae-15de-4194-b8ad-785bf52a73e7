.segmentForm {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.cancelButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.cancelButton:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.formSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.input,
.textarea,
.select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.input:focus,
.textarea:focus,
.select:focus {
  outline: none;
  border-color: #6e8efb;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.submitButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 24px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submitButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .cancelButton {
    align-self: flex-end;
  }
  
  .formActions {
    justify-content: center;
  }
  
  .submitButton {
    width: 100%;
  }
}
