import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for artist earnings data
 * Provides comprehensive earnings breakdown by event and year
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  const { artistId } = req.query;
  const { year } = req.query;
  
  console.log(`[${requestId}] Artist Earnings API called: ${req.method} for artist ${artistId}, year ${year}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetEarnings(req, res, artistId, year, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request - fetch artist earnings
 */
async function handleGetEarnings(req, res, artistId, year, requestId) {
  try {
    console.log(`[${requestId}] Fetching earnings for artist:`, artistId, 'year:', year);

    // Verify artist exists
    const { data: artist, error: artistError } = await supabase
      .from('user_profiles')
      .select('id, name, email')
      .eq('id', artistId)
      .single();

    if (artistError) {
      if (artistError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Artist not found' });
      }
      throw artistError;
    }

    const currentYear = year || new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    const endDate = `${currentYear}-12-31`;

    // Fetch earnings data with event information
    const { data: earnings, error: earningsError } = await supabase
      .from('artist_earnings')
      .select(`
        id,
        gross_revenue,
        commission_rate,
        commission_amount,
        festival_ticket_cost,
        net_earnings,
        payment_status,
        payment_date,
        created_at,
        events!inner(
          id,
          name,
          location,
          start_date,
          end_date
        ),
        bookings!inner(
          id,
          start_time,
          service_id,
          services(
            name,
            price
          )
        )
      `)
      .eq('artist_id', artistId)
      .gte('events.start_date', startDate)
      .lte('events.start_date', endDate)
      .order('events.start_date', { ascending: false });

    if (earningsError) {
      console.error(`[${requestId}] Error fetching earnings:`, earningsError);
      throw earningsError;
    }

    // Group earnings by event
    const earningsByEvent = {};
    earnings.forEach(earning => {
      const eventId = earning.events.id;
      if (!earningsByEvent[eventId]) {
        earningsByEvent[eventId] = {
          event_id: eventId,
          event_name: earning.events.name,
          event_location: earning.events.location,
          event_date: earning.events.start_date,
          event_end_date: earning.events.end_date,
          booking_count: 0,
          gross_revenue: 0,
          commission_amount: 0,
          festival_ticket_cost: 0,
          net_earnings: 0,
          bookings: []
        };
      }

      const eventEarnings = earningsByEvent[eventId];
      eventEarnings.booking_count += 1;
      eventEarnings.gross_revenue += parseFloat(earning.gross_revenue || 0);
      eventEarnings.commission_amount += parseFloat(earning.commission_amount || 0);
      eventEarnings.festival_ticket_cost += parseFloat(earning.festival_ticket_cost || 0);
      eventEarnings.net_earnings += parseFloat(earning.net_earnings || 0);
      eventEarnings.bookings.push({
        id: earning.bookings.id,
        service_name: earning.bookings.services?.name,
        service_price: earning.bookings.services?.price,
        start_time: earning.bookings.start_time
      });
    });

    const formattedEarnings = Object.values(earningsByEvent);

    // Calculate summary statistics
    const summary = {
      total_gross_revenue: formattedEarnings.reduce((sum, e) => sum + e.gross_revenue, 0),
      total_commission_paid: formattedEarnings.reduce((sum, e) => sum + e.commission_amount, 0),
      total_ticket_costs: formattedEarnings.reduce((sum, e) => sum + e.festival_ticket_cost, 0),
      total_net_earnings: formattedEarnings.reduce((sum, e) => sum + e.net_earnings, 0),
      events_participated: formattedEarnings.length,
      total_bookings: formattedEarnings.reduce((sum, e) => sum + e.booking_count, 0),
      avg_booking_value: 0
    };

    if (summary.total_bookings > 0) {
      summary.avg_booking_value = summary.total_gross_revenue / summary.total_bookings;
    }

    console.log(`[${requestId}] Found ${formattedEarnings.length} events with earnings`);
    return res.status(200).json({ 
      earnings: formattedEarnings,
      summary,
      artist: {
        id: artist.id,
        name: artist.name,
        email: artist.email
      },
      year: currentYear
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetEarnings:`, error);
    throw error;
  }
}
