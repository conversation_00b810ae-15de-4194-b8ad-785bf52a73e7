-- Marketing automations table
CREATE TABLE public.marketing_automations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  trigger_type TEXT NOT NULL, -- 'event', 'schedule', 'segment_entry'
  trigger_config J<PERSON>NB NOT NULL, -- Configuration for the trigger
  template_id UUID REFERENCES public.marketing_templates(id),
  message_type TEXT NOT NULL, -- 'email', 'sms', 'push'
  subject TEXT, -- Required for email
  content TEXT NOT NULL,
  segment_id UUID REFERENCES public.customer_segments(id),
  is_active BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automation execution logs table
CREATE TABLE public.automation_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  automation_id UUID REFERENCES public.marketing_automations(id) ON DELETE CASCADE,
  trigger_event TEXT NOT NULL,
  customer_id UUID REFERENCES public.customers(id),
  status TEXT NOT NULL, -- 'success', 'failed'
  message TEXT, -- Error message if failed
  sent_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.marketing_automations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automation_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for marketing_automations
CREATE POLICY "Staff can view all marketing automations" ON public.marketing_automations
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert marketing automations" ON public.marketing_automations
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update their own marketing automations" ON public.marketing_automations
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  ) WITH CHECK (
    get_user_role(auth.uid()) = 'admin' OR 
    (get_user_role(auth.uid()) = 'staff' AND created_by = auth.uid())
  );

CREATE POLICY "Only admins can delete marketing automations" ON public.marketing_automations
  FOR DELETE USING (
    get_user_role(auth.uid()) = 'admin'
  );

-- Create policies for automation_logs
CREATE POLICY "Staff can view all automation logs" ON public.automation_logs
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "System can insert automation logs" ON public.automation_logs
  FOR INSERT WITH CHECK (
    true
  );

-- Create index for faster queries
CREATE INDEX idx_automation_logs_automation_id ON public.automation_logs(automation_id);
CREATE INDEX idx_automation_logs_customer_id ON public.automation_logs(customer_id);
CREATE INDEX idx_marketing_automations_trigger_type ON public.marketing_automations(trigger_type);
CREATE INDEX idx_marketing_automations_is_active ON public.marketing_automations(is_active);

-- Comments for better documentation
COMMENT ON TABLE public.marketing_automations IS 'Stores marketing automation configurations';
COMMENT ON COLUMN public.marketing_automations.trigger_type IS 'Type of trigger: event (e.g., new booking), schedule (e.g., birthday), segment_entry (when customer enters a segment)';
COMMENT ON COLUMN public.marketing_automations.trigger_config IS 'JSON configuration for the trigger, structure depends on trigger_type';
COMMENT ON COLUMN public.marketing_automations.message_type IS 'Type of message to send: email, sms, or push notification';

COMMENT ON TABLE public.automation_logs IS 'Logs of automation executions';
COMMENT ON COLUMN public.automation_logs.trigger_event IS 'The specific event that triggered this automation execution';
COMMENT ON COLUMN public.automation_logs.status IS 'Status of the execution: success or failed';
COMMENT ON COLUMN public.automation_logs.message IS 'Error message if the execution failed';
