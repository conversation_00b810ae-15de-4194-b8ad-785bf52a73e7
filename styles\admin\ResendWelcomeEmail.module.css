/* Resend Welcome Email Component Styles */

.container {
  position: relative;
  display: inline-block;
}

/* Resend <PERSON> */
.resendButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
}

.resendButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.resendButton:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.resendButton:not(:disabled):active {
  transform: translateY(0);
}

/* <PERSON><PERSON> Sizes */
.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.small svg {
  width: 14px;
  height: 14px;
}

.medium {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.medium svg {
  width: 16px;
  height: 16px;
}

.large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.large svg {
  width: 18px;
  height: 18px;
}

/* <PERSON><PERSON>ariants */
.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 1px solid transparent;
}

.primary:not(:disabled):hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.secondary {
  background: white;
  color: #667eea;
  border: 1px solid #667eea;
}

.secondary:not(:disabled):hover {
  background: #667eea;
  color: white;
}

.success {
  background: #48bb78;
  color: white;
  border: 1px solid #48bb78;
}

.success:not(:disabled):hover {
  background: #38a169;
  border-color: #38a169;
}

.warning {
  background: #ed8936;
  color: white;
  border: 1px solid #ed8936;
}

.warning:not(:disabled):hover {
  background: #dd7724;
  border-color: #dd7724;
}

/* Loading State */
.loading {
  position: relative;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Confirmation Dialog */
.confirmationDialog {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  min-width: 320px;
  max-width: 400px;
}

.confirmationContent {
  padding: 1.5rem;
}

.confirmationHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f7fafc;
}

.confirmationHeader svg {
  color: #ed8936;
  flex-shrink: 0;
}

.confirmationHeader h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
}

.confirmationBody {
  margin-bottom: 1.5rem;
}

.confirmationBody p {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  color: #4a5568;
  line-height: 1.5;
}

.confirmationBody ul {
  margin: 0.75rem 0;
  padding-left: 1.25rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.confirmationBody li {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.warning {
  background: #fef5e7;
  border: 1px solid #f6e05e;
  border-radius: 4px;
  padding: 0.75rem;
  margin-top: 0.75rem;
}

.warning strong {
  color: #c05621;
}

.confirmationActions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.cancelButton {
  padding: 0.5rem 1rem;
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.confirmButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #ed8936;
  color: white;
  border: 1px solid #ed8936;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirmButton:hover:not(:disabled) {
  background: #dd7724;
  border-color: #dd7724;
}

.confirmButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.confirmButton .spinner {
  width: 12px;
  height: 12px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .confirmationDialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-top: 0;
    width: 90vw;
    max-width: 350px;
  }

  .confirmationDialog::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }

  .confirmationActions {
    flex-direction: column-reverse;
  }

  .cancelButton,
  .confirmButton {
    width: 100%;
    justify-content: center;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .resendButton {
    border-width: 2px;
  }

  .confirmationDialog {
    border-width: 2px;
    border-color: #1a202c;
  }

  .primary {
    background: #1a202c;
    border-color: #1a202c;
  }

  .secondary {
    color: #1a202c;
    border-color: #1a202c;
  }

  .secondary:not(:disabled):hover {
    background: #1a202c;
  }
}

/* Focus styles for accessibility */
.resendButton:focus,
.cancelButton:focus,
.confirmButton:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Animation for confirmation dialog */
.confirmationDialog {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
