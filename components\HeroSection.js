import { useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from '@/styles/HeroSection.module.css';

/**
 * HeroSection component with parallax effect and animated content
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Hero section title
 * @param {string} props.subtitle - Hero section subtitle
 * @param {string} props.backgroundImage - URL of the background image
 * @param {string} props.ctaText - Call to action button text
 * @param {string} props.ctaLink - Call to action button link
 * @param {string} props.secondaryCtaText - Secondary call to action button text
 * @param {string} props.secondaryCtaLink - Secondary call to action button link
 * @param {string} props.height - Height of the hero section (CSS value)
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element}
 */
const HeroSection = ({
  title,
  subtitle,
  backgroundImage = '/images/hero-image.jpg',
  ctaText = 'Book Now',
  ctaLink = '/book-online',
  secondaryCtaText = '',
  secondaryCtaLink = '',
  height = '100vh',
  className = '',
  ...props
}) => {
  const heroRef = useRef(null);
  const contentRef = useRef(null);

  useEffect(() => {
    // Add animation classes after component mounts
    const content = contentRef.current;
    if (content) {
      // Staggered animation for hero content
      const elements = content.querySelectorAll(`.${styles.animateItem}`);

      elements.forEach((el, index) => {
        setTimeout(() => {
          el.classList.add(styles.visible);
        }, 300 + (index * 200)); // Staggered delay
      });
    }

    // Parallax effect on scroll
    const handleScroll = () => {
      if (!heroRef.current) return;

      const scrollPosition = window.scrollY;
      const parallaxOffset = scrollPosition * 0.4; // Adjust speed as needed

      // Apply parallax effect to background
      heroRef.current.style.backgroundPositionY = `calc(50% + ${parallaxOffset}px)`;
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const heroStyle = {
    height,
    backgroundImage: `url(${backgroundImage})`,
  };

  return (
    <section
      ref={heroRef}
      className={`${styles.heroSection} ${className}`}
      style={heroStyle}
      {...props}
    >
      <div className={styles.overlay}></div>

      <div ref={contentRef} className={styles.heroContent}>
        <h1 className={`${styles.heroTitle} ${styles.animateItem}`}>
          {title}
        </h1>

        {subtitle && (
          <p className={`${styles.heroSubtitle} ${styles.animateItem}`}>
            {subtitle}
          </p>
        )}        <div className={`${styles.heroCta} ${styles.animateItem}`}>
          <Link href={ctaLink} className={`button ${styles.ctaButton}`}>
            {ctaText}
          </Link>

          {secondaryCtaText && (
            <Link href={secondaryCtaLink} className={`button button--outline ${styles.secondaryCtaButton}`}>
              {secondaryCtaText}
            </Link>
          )}
        </div>
      </div>

      <div className={styles.scrollIndicator}>
        <div className={styles.scrollIcon}></div>
        <span>Scroll Down</span>
      </div>
    </section>
  );
};

export default HeroSection;
