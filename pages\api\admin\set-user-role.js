import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client with service role key for admin access
// This bypasses RLS policies
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  // Set proper content type to avoid 406 errors
  res.setHeader('Content-Type', 'application/json');

  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Set user role API endpoint called: ${req.method}`);

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Log request headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));

  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }

  if (req.headers['x-auth-token']) {
    console.log(`[${requestId}] x-auth-token header present`);
  }

  try {
    // Get userId and role from request body
    const { userId, role } = req.body

    if (!userId || !role) {
      return res.status(400).json({ error: 'User ID and role are required' })
    }    // Validate role
    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user']
    if (!validRoles.includes(role)) {
      return res.status(400).json({ error: 'Invalid role. Must be one of: dev, admin, artist, braider, user' })
    }

    // Skip the user check for now - this is causing issues
    // Instead, we'll just try to set the role directly

    // Log the attempt for debugging
    console.log(`[${requestId}] Attempting to set role for user ID: ${userId} to ${role}`)

    // Check if user already has a role
    const { data: existingRole, error: roleError } = await supabaseAdmin
      .from('user_roles')
      .select('*')
      .eq('id', userId)
      .single()

    let result

    if (existingRole) {
      // Update existing role
      result = await supabaseAdmin
        .from('user_roles')
        .update({ role })
        .eq('id', userId)
    } else {
      // Insert new role
      result = await supabaseAdmin
        .from('user_roles')
        .insert([{ id: userId, role }])
    }

    if (result.error) {
      console.error(`[${requestId}] Error setting user role:`, result.error)
      return res.status(500).json({
        error: 'Failed to set user role',
        message: result.error.message,
        requestId
      })
    }

    console.log(`[${requestId}] Successfully set role to ${role} for user ${userId}`)
    return res.status(200).json({
      success: true,
      message: `Role set to ${role} for user ${userId}`,
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({
      error: 'An unexpected error occurred',
      message: error.message,
      requestId
    })
  }
}
