/**
 * Environment Variable Checker for Ocean Soul Sparkles
 *
 * This script checks that all required environment variables are set
 * for development and production environments, with special focus on
 * authentication-related variables that can cause 401 errors.
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env.local
const envLocalPath = path.resolve(process.cwd(), '.env.local');
if (fs.existsSync(envLocalPath)) {
  dotenv.config({ path: envLocalPath });
  console.log('Loaded environment variables from .env.local\n');
} else {
  console.log('Warning: .env.local file not found.\n');
}

// Required environment variables with descriptions
const REQUIRED_VARIABLES = [
  {
    name: 'NEXT_PUBLIC_SUPABASE_URL',
    description: 'The URL of your Supabase project',
    required: true,
    validate: (value) => value.startsWith('https://') && value.includes('.supabase.co'),
    errorMsg: 'Must be a valid Supabase URL (https://your-project-id.supabase.co)'
  },
  {
    name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    description: 'The anonymous key for your Supabase project',
    required: true,
    validate: (value) => value.length > 30,
    errorMsg: 'Appears to be too short for a valid Supabase key'
  },
  {
    name: 'SUPABASE_SERVICE_ROLE_KEY',
    description: 'The service role key for admin operations',
    required: true,
    validate: (value) => value.length > 30,
    errorMsg: 'Appears to be too short for a valid service role key'
  },
  {
    name: 'NEXT_PUBLIC_SITE_URL',
    description: 'The URL of your website (required for auth callbacks)',
    required: process.env.NODE_ENV === 'production',
    validate: (value) => value.startsWith('https://') || value.startsWith('http://'),
    errorMsg: 'Must be a valid URL starting with https:// or http://'
  }
];

/**
 * Check environment variables
 * @param {boolean} silent - Whether to suppress console output
 * @returns {Object} Result of the check
 */
function checkEnvironment(silent = false) {
  const isProd = process.env.NODE_ENV === 'production';
  const missing = [];
  const warnings = [];
  const validationErrors = [];

  if (!silent) {
    console.log(`\n🌊 Ocean Soul Sparkles Environment Check (${isProd ? 'PRODUCTION' : 'DEVELOPMENT'})\n`);
  }

  for (const variable of REQUIRED_VARIABLES) {
    const { name, description, required, validate, errorMsg } = variable;
    const value = process.env[name];

    if (!value) {
      if (required) {
        missing.push({ name, description });
      } else {
        warnings.push({ name, description });
      }
    } else if (validate && !validate(value)) {
      validationErrors.push({ name, description, errorMsg });
    }
  }  if (!silent) {
    if (missing.length === 0 && warnings.length === 0 && validationErrors.length === 0) {
      console.log('✅ All required environment variables are set correctly.\n');
    } else {
      if (missing.length > 0) {
        console.error('❌ Missing required environment variables:');
        missing.forEach(({ name, description }) => {
          console.error(`   - ${name}: ${description}`);
        });
        console.error('\n');
      }

      if (validationErrors.length > 0) {
        console.error('❌ Invalid environment variables:');
        validationErrors.forEach(({ name, errorMsg }) => {
          console.error(`   - ${name}: ${errorMsg}`);
        });
        console.error('\n');
      }

      if (warnings.length > 0) {
        console.warn('⚠️ Missing recommended environment variables:');
        warnings.forEach(({ name, description }) => {
          console.warn(`   - ${name}: ${description}`);
        });
        console.warn('\n');
      }

      if (isProd && (missing.length > 0 || validationErrors.length > 0)) {
        console.error('⛔ Production build will likely fail with authentication errors!\n');
      } else if (missing.length > 0 || validationErrors.length > 0) {
        console.warn('⚠️ Fix these variables in your .env.local file to prevent authentication errors.\n');
      }
    }
  }
    return {
    success: missing.length === 0 && validationErrors.length === 0,
    missing,
    warnings,
    validationErrors,
    isProd
  };
}

// Export for use in other scripts
export default checkEnvironment;

// Run directly if called from the command line
if (import.meta.url === `file://${process.argv[1]}`) {
  const result = checkEnvironment();
  if (!result.success) {
    process.exit(1);
  }
}
