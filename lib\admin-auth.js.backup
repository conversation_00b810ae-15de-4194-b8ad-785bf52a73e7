// This utility centralizes admin authentication for API endpoints
import { getAdminClient } from '@/lib/supabase';

/**
 * Custom error class for authentication errors
 */
export class AuthenticationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

/**
 * Centralized authentication middleware for admin API endpoints
 * Attempts multiple authentication methods for robustness
 * Enhanced with improved error handling, timeout mechanisms, and resource cleanup
 * Further improved to handle authentication failures more gracefully
 *
 * @param {Object} req - HTTP request object
 * @returns {Object} - { user, role, authorized, error }
 */
export async function authenticateAdminRequest(req) {
  // Generate a unique auth ID for tracking
  const authId = Math.random().toString(36).substring(2, 8);

  // Add detailed logging for token extraction
  console.log(`[${authId}] Starting authentication process`);
  console.log(`[${authId}] Request URL: ${req.url}`);
  console.log(`[${authId}] Headers:`, Object.keys(req.headers));
  console.log(`[${authId}] Cookies:`, req.cookies ? Object.keys(req.cookies) : 'No cookies');

  // Check if this is a diagnostics request
  const isDiagnostics = req.url && (
    req.url.includes('/api/admin/diagnostics') ||
    (req.headers.referer && req.headers.referer.includes('/admin/diagnostics'))
  );

  // Set a timeout to prevent hanging authentication
  // Use a shorter timeout for diagnostics requests
  const timeoutDuration = isDiagnostics ? 3000 : 5000;
  let authTimeout = null;
  const timeoutPromise = new Promise((_, reject) => {
    authTimeout = setTimeout(() => {
      reject(new Error('Authentication timeout'));
    }, timeoutDuration);
  });

  try {
    // Development mode fallback for testing
    // Only active when NODE_ENV is development and ENABLE_AUTH_BYPASS is true
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(`[${authId}] DEVELOPMENT MODE: Using auth bypass`);
      return {
        user: { id: 'dev-admin', email: '<EMAIL>' },
        role: 'admin',
        authorized: true,
        error: null
      };
    }

    // First try with MCP client (more robust with retry logic)
    try {
      // Extract auth token from request with improved extraction
      let token = null;

      // Method 1: Check Authorization header
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }

      // Method 2: Check cookies (for browser-based requests)
      if (!token) {
        try {
          // Try to get token from cookies object
          if (req.cookies) {
            if (req.cookies.oss_auth_token) {
              token = req.cookies.oss_auth_token;
            } else if (req.cookies.sb_auth_token) {
              token = req.cookies.sb_auth_token;
            } else if (req.cookies.mcp_auth_token) {
              token = req.cookies.mcp_auth_token;
            }
          }
          // Try to parse cookies from cookie header
          else if (req.headers.cookie) {
            const cookieHeader = req.headers.cookie;
            const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
              const [key, value] = cookie.trim().split('=');
              acc[key] = value;
              return acc;
            }, {});

            if (cookies.oss_auth_token) {
              token = cookies.oss_auth_token;
            } else if (cookies.sb_auth_token) {
              token = cookies.sb_auth_token;
            } else if (cookies.mcp_auth_token) {
              token = cookies.mcp_auth_token;
            }
          }
        } catch (cookieError) {
          console.warn(`[${authId}] Error parsing cookies:`, cookieError);
        }
      }

      // Method 3: Check query parameters (for special cases)
      if (!token && req.query && req.query.token) {
        token = req.query.token;
      }

      if (token) {
        // Log token extraction method in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[${authId}] Token extracted from ${
            authHeader ? 'Authorization header' :
            req.cookies?.oss_auth_token ? 'oss_auth_token cookie' :
            req.cookies?.sb_auth_token ? 'sb_auth_token cookie' :
            req.cookies?.mcp_auth_token ? 'mcp_auth_token cookie' :
            'query parameter'
          }`);
        }

        // Race the auth check against a timeout
        const authResult = await Promise.race([
          mcpClient.getCurrentUserWithToken(token),
          timeoutPromise
        ]);

        if (authResult && !authResult.error && authResult.user &&
            (authResult.role === 'admin' || authResult.role === 'staff')) {
          // Clear the timeout since we've completed successfully
          if (authTimeout) {
            clearTimeout(authTimeout);
            authTimeout = null;
          }

          return {
            user: authResult.user,
            role: authResult.role,
            authorized: true,
            error: null
          };
        } else if (authResult && authResult.error) {
          // Log specific error for debugging
          console.warn(`[${authId}] Token validation failed: ${authResult.error.message || 'Unknown error'}`);
        }
      } else if (isDiagnostics) {
        // For diagnostics requests, log that no token was found
        console.warn(`[${authId}] No authentication token found in diagnostics request`);
      }
    } catch (mcpError) {
      console.warn(`[${authId}] MCP authentication failed, falling back to direct Supabase:`, mcpError);
      // Continue to fallback methods
    }

    // Second, try with Supabase's built-in getSession method
    try {
      // Get admin client with timeout
      const adminClientPromise = mcpClient.getAdminClient();

      // Create a new timeout for this specific operation
      let adminClientTimeoutId = null;
      const adminClientTimeoutPromise = new Promise((_, reject) => {
        adminClientTimeoutId = setTimeout(() => {
          reject(new Error('Admin client fetch timeout'));
        }, 5000);
      });

      try {
        const adminClient = await Promise.race([adminClientPromise, adminClientTimeoutPromise]);

        // Clear the timeout since we got a response
        if (adminClientTimeoutId) {
          clearTimeout(adminClientTimeoutId);
          adminClientTimeoutId = null;
        }

        if (!adminClient) {
          console.error(`[${authId}] MCP admin client not available.`);
          throw new Error('Admin client not available');
        }

        // Get session with timeout
        let sessionTimeoutId = null;
        const sessionTimeoutPromise = new Promise((_, reject) => {
          sessionTimeoutId = setTimeout(() => {
            reject(new Error('Session fetch timeout'));
          }, 5000);
        });

        try {
          const sessionPromise = adminClient.auth.getSession({ req });
          const { data, error } = await Promise.race([sessionPromise, sessionTimeoutPromise]);

          // Clear the timeout since we got a response
          if (sessionTimeoutId) {
            clearTimeout(sessionTimeoutId);
            sessionTimeoutId = null;
          }

          if (error || !data || !data.session) {
            throw new Error('No valid session found');
          }

          const session = data.session;

          // Check user role in database with timeout
          let roleTimeoutId = null;
          const roleTimeoutPromise = new Promise((_, reject) => {
            roleTimeoutId = setTimeout(() => {
              reject(new Error('Role verification timeout'));
            }, 5000);
          });

          try {
            const rolePromise = adminClient
              .from('user_roles')
              .select('role')
              .eq('id', session.user.id)
              .single();

            const { data: roleData, error: roleError } = await Promise.race([rolePromise, roleTimeoutPromise]);

            // Clear the timeout since we got a response
            if (roleTimeoutId) {
              clearTimeout(roleTimeoutId);
              roleTimeoutId = null;
            }

            if (roleError || !roleData) {
              throw new Error('Role verification failed');
            }

            if (roleData.role === 'admin' || roleData.role === 'staff') {
              // Clear the main timeout since we've completed successfully
              if (authTimeout) {
                clearTimeout(authTimeout);
                authTimeout = null;
              }

              return {
                user: session.user,
                role: roleData.role,
                authorized: true,
                error: null
              };
            }
          } catch (roleError) {
            // Clear the timeout to prevent memory leaks
            if (roleTimeoutId) {
              clearTimeout(roleTimeoutId);
            }

            console.warn(`[${authId}] Role verification failed:`, roleError);
            throw roleError;
          }
        } catch (sessionError) {
          // Clear the timeout to prevent memory leaks
          if (sessionTimeoutId) {
            clearTimeout(sessionTimeoutId);
          }

          console.warn(`[${authId}] Session fetch failed:`, sessionError);
          throw sessionError;
        }
      } catch (adminClientError) {
        // Clear the timeout to prevent memory leaks
        if (adminClientTimeoutId) {
          clearTimeout(adminClientTimeoutId);
        }

        console.warn(`[${authId}] Admin client fetch failed:`, adminClientError);
        throw adminClientError;
      }
    } catch (supabaseError) {
      console.warn(`[${authId}] Supabase session authentication failed:`, supabaseError);
      // Continue to last fallback method
    }

    // Final attempt - check for API key in headers (for system integrations)
    const apiKey = req.headers['x-api-key'];
    if (apiKey && apiKey === process.env.ADMIN_API_KEY) {
      // Clear the timeout since we've completed successfully
      if (authTimeout) {
        clearTimeout(authTimeout);
        authTimeout = null;
      }

      return {
        user: { id: 'system', email: '<EMAIL>' },
        role: 'system',
        authorized: true,
        error: null
      };
    }

    // All authentication methods failed
    // Clear the timeout since we've completed
    if (authTimeout) {
      clearTimeout(authTimeout);
      authTimeout = null;
    }

    return {
      user: null,
      role: null,
      authorized: false,
      error: new Error('Unauthorized access')
    };
  } catch (error) {
    // Clear timeout to prevent memory leaks
    if (authTimeout) {
      clearTimeout(authTimeout);
      authTimeout = null;
    }

    console.error(`[${authId}] Admin authentication error:`, error);
    return {
      user: null,
      role: null,
      authorized: false,
      error: error
    };
  }
}
