<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Management & Service Visibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .category-list, .service-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            padding: 10px;
            margin-top: 10px;
        }
        .item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Category Management & Service Visibility Test</h1>
        <p>This test verifies the implementation of category dropdowns and service visibility controls.</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testServiceCategories()">Test Service Categories API</button>
            <button onclick="testProductCategories()">Test Product Categories API</button>
            <button onclick="testServiceVisibility()">Test Service Visibility</button>
            <button onclick="testCompleteFlow()">Test Complete Implementation</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="info status">Click a test button to start testing...</div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>Service Categories</h3>
                <div id="service-categories" class="loading">Not loaded yet</div>
            </div>

            <div class="card">
                <h3>Product Categories</h3>
                <div id="product-categories" class="loading">Not loaded yet</div>
            </div>

            <div class="card">
                <h3>Public Services (visible_on_public=true)</h3>
                <div id="public-services" class="loading">Not loaded yet</div>
            </div>

            <div class="card">
                <h3>All Admin Services</h3>
                <div id="admin-services" class="loading">Not loaded yet</div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="${result.type} status">[${result.timestamp}] ${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResults();
            document.getElementById('service-categories').innerHTML = '<div class="loading">Not loaded yet</div>';
            document.getElementById('product-categories').innerHTML = '<div class="loading">Not loaded yet</div>';
            document.getElementById('public-services').innerHTML = '<div class="loading">Not loaded yet</div>';
            document.getElementById('admin-services').innerHTML = '<div class="loading">Not loaded yet</div>';
        }

        async function testServiceCategories() {
            addResult('Testing Service Categories API...', 'info');
            
            try {
                const response = await fetch('/api/admin/service-categories');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const categories = data.categories || [];
                
                addResult(`✅ Service Categories API: Found ${categories.length} categories`, 'success');
                
                // Display categories
                displayCategories(categories, 'service-categories');
                
                // Check for expected categories
                const expectedCategories = ['Face Painting', 'Airbrush', 'Hair & Braiding', 'Glitter & Gems', 'Special Events', 'UV & Glow'];
                const foundCategories = categories.map(c => c.name);
                const missingCategories = expectedCategories.filter(cat => !foundCategories.includes(cat));
                
                if (missingCategories.length > 0) {
                    addResult(`⚠️ Missing expected categories: ${missingCategories.join(', ')}`, 'warning');
                } else {
                    addResult(`✅ All expected service categories found`, 'success');
                }
                
            } catch (error) {
                addResult(`❌ Service Categories API Error: ${error.message}`, 'error');
            }
        }

        async function testProductCategories() {
            addResult('Testing Product Categories API...', 'info');
            
            try {
                const response = await fetch('/api/admin/inventory/categories');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const categories = data.categories || [];
                
                addResult(`✅ Product Categories API: Found ${categories.length} categories`, 'success');
                
                // Display categories
                displayCategories(categories, 'product-categories');
                
            } catch (error) {
                addResult(`❌ Product Categories API Error: ${error.message}`, 'error');
            }
        }

        async function testServiceVisibility() {
            addResult('Testing Service Visibility Controls...', 'info');
            
            try {
                // Test public services API
                const publicResponse = await fetch('/api/public/services');
                if (!publicResponse.ok) {
                    throw new Error(`Public Services API failed: ${publicResponse.status}`);
                }
                
                const publicData = await publicResponse.json();
                const publicServices = publicData.services || [];
                
                addResult(`✅ Public Services API: Found ${publicServices.length} services`, 'success');
                displayServices(publicServices, 'public-services');
                
                // Test admin services API
                const adminResponse = await fetch('/api/admin/services/index');
                if (!adminResponse.ok) {
                    throw new Error(`Admin Services API failed: ${adminResponse.status}`);
                }
                
                const adminData = await adminResponse.json();
                const adminServices = adminData.services || [];
                
                addResult(`✅ Admin Services API: Found ${adminServices.length} services`, 'success');
                displayServices(adminServices, 'admin-services');
                
                // Compare visibility
                if (adminServices.length >= publicServices.length) {
                    addResult(`✅ Visibility control working: Admin sees ${adminServices.length} services, Public sees ${publicServices.length}`, 'success');
                } else {
                    addResult(`⚠️ Unexpected visibility: Admin sees fewer services than public`, 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Service Visibility Test Error: ${error.message}`, 'error');
            }
        }

        async function testCompleteFlow() {
            addResult('Testing Complete Implementation...', 'info');
            
            await testServiceCategories();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testProductCategories();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testServiceVisibility();
            
            addResult('✅ Complete implementation test finished', 'success');
        }

        function displayCategories(categories, containerId) {
            const container = document.getElementById(containerId);
            
            if (categories.length === 0) {
                container.innerHTML = '<div class="warning status">No categories found</div>';
                return;
            }
            
            const categoryItems = categories.map(category => `
                <div class="item">
                    <strong>${category.name}</strong>
                    ${category.description ? `<br><small>${category.description}</small>` : ''}
                </div>
            `).join('');
            
            container.innerHTML = `
                <div class="category-list">
                    ${categoryItems}
                </div>
                <p><em>Total: ${categories.length} categories</em></p>
            `;
        }

        function displayServices(services, containerId) {
            const container = document.getElementById(containerId);
            
            if (services.length === 0) {
                container.innerHTML = '<div class="warning status">No services found</div>';
                return;
            }
            
            const serviceItems = services.slice(0, 10).map(service => {
                const name = service.name || service.title || 'Unnamed Service';
                const category = service.category || 'No Category';
                const visibility = service.visible_on_public !== undefined ? 
                    `Public: ${service.visible_on_public}, POS: ${service.visible_on_pos}, Events: ${service.visible_on_events}` : 
                    'Visibility info not available';
                
                return `
                    <div class="item">
                        <strong>${name}</strong><br>
                        <small>Category: ${category}</small><br>
                        <small>${visibility}</small>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                <div class="service-list">
                    ${serviceItems}
                </div>
                <p><em>Showing first 10 of ${services.length} services</em></p>
            `;
        }

        // Auto-run initial test
        window.addEventListener('load', () => {
            setTimeout(testCompleteFlow, 1000);
        });
    </script>
</body>
</html>
