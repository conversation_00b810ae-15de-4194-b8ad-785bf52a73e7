/**
 * Test API credentials endpoint
 * POST /api/admin/secure-settings/test - Test specific service credentials
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { testEmailService } from '@/lib/secure-settings-manager'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Settings test API called`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.error?.message || 'Authentication required'
      })
    }

    const { user: adminUser, role: adminRole } = authResult

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        message: 'Admin or developer role required'
      })
    }

    const { serviceType, credentials, testEmail } = req.body

    if (!serviceType || !credentials) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'serviceType and credentials are required'
      })
    }

    console.log(`[${requestId}] Testing ${serviceType} service for user: ${adminUser.email}`)

    let testResult

    switch (serviceType) {
      case 'gmail':
        testResult = await testGmailService(credentials, testEmail)
        break
      case 'workspace':
        testResult = await testWorkspaceService(credentials, testEmail)
        break
      case 'onesignal':
        testResult = await testOneSignalService(credentials, testEmail)
        break
      case 'email_flow':
        testResult = await testCompleteEmailFlow(credentials, testEmail)
        break
      default:
        return res.status(400).json({
          error: 'Invalid service type',
          message: `Unknown service type: ${serviceType}`
        })
    }

    console.log(`[${requestId}] Test result for ${serviceType}:`, testResult.success ? 'SUCCESS' : 'FAILED')

    return res.status(200).json({
      success: testResult.success,
      serviceType,
      result: testResult,
      requestId
    })

  } catch (error) {
    console.error(`[${requestId}] Test error:`, error)
    return res.status(500).json({
      error: 'Test failed',
      message: error.message,
      requestId
    })
  }
}

/**
 * Test Gmail SMTP service
 */
async function testGmailService(credentials, testEmail) {
  try {
    if (!credentials.gmail_smtp_user || !credentials.gmail_smtp_password) {
      throw new Error('Gmail SMTP user and password are required')
    }

    const nodemailer = await import('nodemailer')
    
    const transporter = nodemailer.default.createTransporter({
      service: 'gmail',
      auth: {
        user: credentials.gmail_smtp_user,
        pass: credentials.gmail_smtp_password
      },
      timeout: 10000 // 10 second timeout
    })

    // Test connection
    await transporter.verify()

    let emailResult = null
    
    // Send test email if email address provided
    if (testEmail) {
      const info = await transporter.sendMail({
        from: `"Ocean Soul Sparkles" <${credentials.gmail_smtp_user}>`,
        to: testEmail,
        subject: 'Gmail SMTP Test - Ocean Soul Sparkles',
        text: 'This is a test email to verify Gmail SMTP configuration.',
        html: `
          <h2>Gmail SMTP Test</h2>
          <p>This is a test email to verify Gmail SMTP configuration.</p>
          <p>If you received this, your Gmail SMTP settings are working correctly!</p>
          <p><strong>Service:</strong> Gmail SMTP</p>
          <p><strong>Time:</strong> ${new Date().toISOString()}</p>
        `
      })

      emailResult = {
        messageId: info.messageId,
        recipient: testEmail
      }
    }

    return {
      success: true,
      message: 'Gmail SMTP connection verified successfully',
      details: {
        connectionTest: 'PASSED',
        emailTest: emailResult ? 'SENT' : 'SKIPPED',
        emailResult
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      details: {
        connectionTest: 'FAILED',
        emailTest: 'SKIPPED'
      }
    }
  }
}

/**
 * Test Workspace SMTP service
 */
async function testWorkspaceService(credentials, testEmail) {
  try {
    if (!credentials.workspace_smtp_user || !credentials.workspace_smtp_password) {
      throw new Error('Workspace SMTP user and password are required')
    }

    const nodemailer = await import('nodemailer')
    
    const transporter = nodemailer.default.createTransporter({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: credentials.workspace_smtp_user,
        pass: credentials.workspace_smtp_password
      },
      timeout: 10000
    })

    // Test connection
    await transporter.verify()

    let emailResult = null
    
    // Send test email if email address provided
    if (testEmail) {
      const info = await transporter.sendMail({
        from: `"Ocean Soul Sparkles" <${credentials.workspace_smtp_user}>`,
        to: testEmail,
        subject: 'Workspace SMTP Test - Ocean Soul Sparkles',
        text: 'This is a test email to verify Workspace SMTP configuration.',
        html: `
          <h2>Workspace SMTP Test</h2>
          <p>This is a test email to verify Workspace SMTP configuration.</p>
          <p>If you received this, your Workspace SMTP settings are working correctly!</p>
          <p><strong>Service:</strong> Google Workspace SMTP</p>
          <p><strong>Time:</strong> ${new Date().toISOString()}</p>
        `
      })

      emailResult = {
        messageId: info.messageId,
        recipient: testEmail
      }
    }

    return {
      success: true,
      message: 'Workspace SMTP connection verified successfully',
      details: {
        connectionTest: 'PASSED',
        emailTest: emailResult ? 'SENT' : 'SKIPPED',
        emailResult
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      details: {
        connectionTest: 'FAILED',
        emailTest: 'SKIPPED'
      }
    }
  }
}

/**
 * Test OneSignal service
 */
async function testOneSignalService(credentials, testEmail) {
  try {
    if (!credentials.onesignal_app_id || !credentials.onesignal_rest_api_key) {
      throw new Error('OneSignal App ID and REST API Key are required')
    }

    // Test API connection
    const appsResponse = await fetch('https://onesignal.com/api/v1/apps', {
      headers: {
        'Authorization': `Basic ${credentials.onesignal_rest_api_key}`
      }
    })

    if (!appsResponse.ok) {
      throw new Error(`OneSignal API error: ${appsResponse.status} - ${appsResponse.statusText}`)
    }

    const apps = await appsResponse.json()
    const targetApp = apps.find(app => app.id === credentials.onesignal_app_id)

    if (!targetApp) {
      throw new Error('OneSignal App ID not found in your account')
    }

    let emailResult = null

    // Send test email if email address provided
    if (testEmail) {
      const emailResponse = await fetch('https://onesignal.com/api/v1/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials.onesignal_rest_api_key}`
        },
        body: JSON.stringify({
          app_id: credentials.onesignal_app_id,
          include_email_tokens: [testEmail],
          email_subject: 'OneSignal Test - Ocean Soul Sparkles',
          email_body: `
            <h2>OneSignal Test</h2>
            <p>This is a test email to verify OneSignal configuration.</p>
            <p>If you received this, your OneSignal settings are working correctly!</p>
            <p><strong>Service:</strong> OneSignal</p>
            <p><strong>Time:</strong> ${new Date().toISOString()}</p>
          `
        })
      })

      if (emailResponse.ok) {
        const result = await emailResponse.json()
        emailResult = {
          messageId: result.id,
          recipient: testEmail
        }
      } else {
        const errorData = await emailResponse.text()
        throw new Error(`OneSignal email error: ${emailResponse.status} - ${errorData}`)
      }
    }

    return {
      success: true,
      message: 'OneSignal connection verified successfully',
      details: {
        connectionTest: 'PASSED',
        appFound: targetApp.name,
        emailTest: emailResult ? 'SENT' : 'SKIPPED',
        emailResult
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      details: {
        connectionTest: 'FAILED',
        emailTest: 'SKIPPED'
      }
    }
  }
}

/**
 * Test complete email flow using the email service manager
 */
async function testCompleteEmailFlow(credentials, testEmail) {
  try {
    if (!testEmail) {
      throw new Error('Test email address is required for email flow test')
    }

    // Temporarily update settings for testing
    const { sendEmailWithFallback } = await import('@/lib/email-service-manager')
    
    const result = await sendEmailWithFallback({
      to: testEmail,
      subject: 'Complete Email Flow Test - Ocean Soul Sparkles',
      text: 'This is a test of the complete email flow system.',
      html: `
        <h2>Complete Email Flow Test</h2>
        <p>This is a test of the complete email flow system.</p>
        <p>If you received this, your email configuration is working correctly!</p>
        <p><strong>Time:</strong> ${new Date().toISOString()}</p>
      `,
      fromName: 'Ocean Soul Sparkles'
    })

    return {
      success: true,
      message: 'Complete email flow test successful',
      details: {
        service: result.service,
        messageId: result.messageId,
        recipient: result.recipient
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      details: {
        flowTest: 'FAILED'
      }
    }
  }
}
