import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for managing event expenses
 * Handles GET (list expenses) and POST (create expense) operations
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  const { eventId } = req.query;
  
  console.log(`[${requestId}] Event Expenses API called: ${req.method} for event ${eventId}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetExpenses(req, res, eventId, requestId);
    } else if (req.method === 'POST') {
      return await handleCreateExpense(req, res, eventId, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request - fetch event expenses
 */
async function handleGetExpenses(req, res, eventId, requestId) {
  try {
    console.log(`[${requestId}] Fetching expenses for event:`, eventId);

    // Verify event exists
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('id, name')
      .eq('id', eventId)
      .single();

    if (eventError) {
      if (eventError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Event not found' });
      }
      throw eventError;
    }

    // Fetch expenses with category information
    const { data: expenses, error: expensesError } = await supabase
      .from('event_expenses')
      .select(`
        id,
        expense_name,
        amount,
        description,
        vendor_name,
        expense_date,
        payment_method,
        is_reimbursable,
        reimbursed,
        reimbursed_date,
        created_at,
        updated_at,
        category_id,
        expense_categories!inner(
          id,
          name,
          icon,
          color
        )
      `)
      .eq('event_id', eventId)
      .order('expense_date', { ascending: false });

    if (expensesError) {
      console.error(`[${requestId}] Error fetching expenses:`, expensesError);
      throw expensesError;
    }

    // Format expenses with category information
    const formattedExpenses = expenses.map(expense => ({
      ...expense,
      category_name: expense.expense_categories.name,
      category_icon: expense.expense_categories.icon,
      category_color: expense.expense_categories.color
    }));

    console.log(`[${requestId}] Found ${formattedExpenses.length} expenses`);
    return res.status(200).json({ 
      expenses: formattedExpenses,
      event: event
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetExpenses:`, error);
    throw error;
  }
}

/**
 * Handle POST request - create new expense
 */
async function handleCreateExpense(req, res, eventId, user, requestId) {
  try {
    const {
      category_id,
      expense_name,
      amount,
      description,
      vendor_name,
      expense_date,
      payment_method,
      is_reimbursable
    } = req.body;

    console.log(`[${requestId}] Creating expense:`, { eventId, expense_name, amount });

    // Validate required fields
    if (!category_id || !expense_name || !amount || !expense_date) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['category_id', 'expense_name', 'amount', 'expense_date']
      });
    }

    // Validate amount
    const expenseAmount = parseFloat(amount);
    if (isNaN(expenseAmount) || expenseAmount < 0) {
      return res.status(400).json({ 
        error: 'Amount must be a valid positive number' 
      });
    }

    // Verify event exists
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('id, name')
      .eq('id', eventId)
      .single();

    if (eventError) {
      if (eventError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Event not found' });
      }
      throw eventError;
    }

    // Verify category exists
    const { data: category, error: categoryError } = await supabase
      .from('expense_categories')
      .select('id, name')
      .eq('id', category_id)
      .single();

    if (categoryError) {
      if (categoryError.code === 'PGRST116') {
        return res.status(400).json({ error: 'Invalid expense category' });
      }
      throw categoryError;
    }

    // Create expense
    const { data: expense, error: createError } = await supabase
      .from('event_expenses')
      .insert([{
        event_id: eventId,
        category_id,
        expense_name,
        amount: expenseAmount,
        description: description || '',
        vendor_name: vendor_name || '',
        expense_date,
        payment_method: payment_method || 'card',
        is_reimbursable: is_reimbursable || false,
        created_by: user.id
      }])
      .select(`
        id,
        expense_name,
        amount,
        description,
        vendor_name,
        expense_date,
        payment_method,
        is_reimbursable,
        reimbursed,
        reimbursed_date,
        created_at,
        updated_at,
        category_id,
        expense_categories!inner(
          id,
          name,
          icon,
          color
        )
      `)
      .single();

    if (createError) {
      console.error(`[${requestId}] Error creating expense:`, createError);
      throw createError;
    }

    // Format response with category information
    const formattedExpense = {
      ...expense,
      category_name: expense.expense_categories.name,
      category_icon: expense.expense_categories.icon,
      category_color: expense.expense_categories.color
    };

    console.log(`[${requestId}] Expense created successfully:`, expense.id);
    return res.status(201).json({ 
      expense: formattedExpense,
      message: 'Expense created successfully'
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleCreateExpense:`, error);
    throw error;
  }
}
