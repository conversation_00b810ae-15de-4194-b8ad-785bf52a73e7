.usersPage {
  width: 100%;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.actions {
  display: flex;
  gap: 12px;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  color: #333;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  border-color: #6e8efb;
  background: #f8f9ff;
  transform: translateY(-1px);
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.95rem;
  border-left: 4px solid #f44336;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.95rem;
  border-left: 4px solid #4caf50;
}

.filterContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.searchInput {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 10px;
  width: 300px;
}

.searchInput input {
  flex: 1;
  border: none;
  padding: 8px;
  font-size: 0.9rem;
}

.searchInput input:focus {
  outline: none;
}

.searchIcon {
  color: #6c757d;
}

.filterOptions {
  display: flex;
  gap: 10px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.paginationInfo {
  font-size: 0.9rem;
  color: #6c757d;
}

.paginationButtons {
  display: flex;
  gap: 8px;
}

.paginationButton {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #f0f0f0;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationButton.active {
  background-color: #6e8efb;
  color: white;
  border-color: #6e8efb;
}

/* Tab Navigation Styles */
.tabNavigation {
  display: flex;
  gap: 4px;
  margin: 20px 0;
  border-bottom: 2px solid #f0f0f0;
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.2s ease;
  position: relative;
}

.tab:hover {
  background: #f8f9ff;
  color: #333;
}

.tab.activeTab {
  background: white;
  color: #6e8efb;
  border-bottom: 2px solid #6e8efb;
  margin-bottom: -2px;
}

.tabIcon {
  font-size: 1.1rem;
}

.tabContent {
  margin-top: 20px;
}

.comingSoon {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.comingSoon h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.5rem;
}

.comingSoon p {
  color: #666;
  margin: 0 0 25px 0;
  font-size: 1rem;
}

/* Applications Section */
.applicationsSection {
  margin: 1rem 0;
}
