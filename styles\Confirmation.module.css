.main {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
  background-color: rgba(250, 250, 255, 0.8);
}

.confirmationSection {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(26, 115, 232, 0.2);
  border-radius: 50%;
  border-top-color: #1A73E8;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.confirmationContainer {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
}

.confirmationHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.checkmarkCircle {
  width: 80px;
  height: 80px;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.pageTitle {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.thankYouMessage {
  font-size: 1.25rem;
  color: #555;
}

.orderDetails {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.orderInfo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.orderInfoItem {
  display: flex;
  flex-direction: column;
}

.orderInfoLabel {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.orderInfoValue {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.orderMessage {
  color: #555;
  line-height: 1.6;
}

.orderMessage a {
  color: #1A73E8;
  text-decoration: none;
}

.orderMessage a:hover {
  text-decoration: underline;
}

.nextSteps {
  margin-bottom: 2rem;
}

.nextStepsTitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.stepsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.step {
  display: flex;
  align-items: flex-start;
}

.stepIcon {
  width: 48px;
  height: 48px;
  background-color: rgba(26, 115, 232, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.stepContent {
  flex: 1;
}

.stepTitle {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.stepDescription {
  color: #555;
  line-height: 1.5;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.primaryButton {
  background-color: #1A73E8;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.primaryButton:hover {
  background-color: #1557b0;
}

.secondaryButton {
  background-color: transparent;
  color: #1A73E8;
  border: 1px solid #1A73E8;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.secondaryButton:hover {
  background-color: rgba(26, 115, 232, 0.05);
}

.paymentInfo {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.paymentLogos {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.securePaymentNote {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #4CAF50;
}

.securePaymentNote svg {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .orderInfo {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .paymentLogos {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}
