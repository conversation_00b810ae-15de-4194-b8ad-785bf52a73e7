# Gmail API Integration Setup Guide
# Ocean Soul Sparkles Website

## ✅ COMPLETED SETUP

### 1. Dependencies Installed
- `googleapis` - Google APIs client library
- `next-auth` - OAuth flow management

### 2. OAuth Credentials Configured
Your OAuth credentials are already set up in `.env.local`:
- `GOOGLE_CLIENT_ID`: ************-bdaro6svl47787ec21bi11251eoolj9n.apps.googleusercontent.com
- `GOOGLE_CLIENT_SECRET`: 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

### 3. Files Created/Updated

#### New Files:
- `lib/gmail-api.js` - Gmail API service with OAuth token management
- `pages/api/auth/gmail/authorize.js` - Authorization endpoint
- `pages/api/auth/gmail/callback.js` - OAuth callback handler
- `scripts/test-gmail-api.js` - Test script for verification

#### Updated Files:
- `lib/google-cloud-email.js` - Integrated Gmail API into priority system
- `pages/api/admin/google-cloud-email.js` - Added Gmail API management endpoints
- `components/admin/SettingsForm.js` - Added Gmail API controls to admin panel
- `styles/admin/SettingsForm.module.css` - Added Gmail API styling

### 4. Email Service Priority Order
Now configured as:
1. **Gmail API** (Most reliable, OAuth-based)
2. **Google Workspace SMTP** (Custom domain emails)
3. **Gmail SMTP** (App password-based)
4. **OneSignal** (Fallback service)

## 🚀 NEXT STEPS TO COMPLETE SETUP

### Step 1: Google Cloud Console Configuration

Since you mentioned you're currently setting up Google Cloud Console, ensure these steps are completed:

1. **Enable Gmail API**:
   - Go to Google Cloud Console → APIs & Services → Library
   - Search for "Gmail API" and enable it

2. **Configure OAuth Consent Screen**:
   - Go to APIs & Services → OAuth consent screen
   - Add your domain: `oceansoulsparkles.com.au`
   - Add scopes: `gmail.send` and `gmail.compose`

3. **Verify Redirect URIs** (already configured in your image):
   - Production: `https://oceansoulsparkles.com.au/api/auth/gmail/callback`
   - Development: `http://localhost:3000/api/auth/gmail/callback`

### Step 2: Authorize Gmail API Access

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Access Admin Panel**:
   - Navigate to `/admin/settings`
   - Click on "Email Settings" tab
   - Find the "Gmail API (Most Reliable)" section

3. **Check Gmail API Status**:
   - Click "Check Gmail API Status"
   - Should show OAuth is configured but not authorized

4. **Authorize Gmail API**:
   - Click "Authorize Gmail API"
   - Complete Google OAuth flow
   - Grant permissions for Gmail access

### Step 3: Test the Integration

1. **In Admin Panel**:
   - Click "Test Gmail API" to verify connection
   - Send a test email to verify functionality

2. **Using Test Script**:
   - Open browser console on admin page
   - Run: `testGmailAPI()`
   - Follow prompts to test complete workflow

3. **Check Email Service Status**:
   - In Email Settings, click "Check Email Service Status"
   - Verify Gmail API is listed as primary service

## 📋 VERIFICATION CHECKLIST

- [ ] Google Cloud Console: Gmail API enabled
- [ ] Google Cloud Console: OAuth consent screen configured
- [ ] Gmail API authorized through admin panel
- [ ] Test email sent successfully via Gmail API
- [ ] Email service status shows Gmail API as primary
- [ ] All existing email functionality still works

## 🔧 ADMIN PANEL FEATURES

Your admin panel now includes:

1. **Gmail API Status Check** - Monitor authorization status
2. **One-Click Authorization** - Easy OAuth setup
3. **Token Management** - Revoke/re-authorize as needed
4. **Connection Testing** - Verify API connectivity
5. **Service Priority Display** - See which service is active

## 🛡️ SECURITY NOTES

- OAuth tokens are securely stored in Supabase `admin_settings` table
- Tokens automatically refresh when needed
- No app passwords required (more secure than SMTP)
- Granular permissions (only send/compose access)

## 📞 TROUBLESHOOTING

### Common Issues:

1. **"OAuth credentials not configured"**:
   - Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in .env.local

2. **"Gmail API not authorized"**:
   - Complete authorization flow in admin panel
   - Check redirect URIs match exactly

3. **"Token expired"**:
   - Click "Re-authorize" in admin panel
   - System should auto-refresh tokens

4. **"Gmail API connection failed"**:
   - Verify Gmail API is enabled in Google Cloud Console
   - Check OAuth consent screen configuration

### Debug Information:
- Check browser console for detailed error messages
- Monitor server logs for API call details
- Use test script for comprehensive diagnostics

## 🎯 BENEFITS OF COMPLETED INTEGRATION

1. **Higher Delivery Rates** - Gmail API has better deliverability
2. **No App Passwords** - More secure OAuth-based authentication  
3. **Automatic Failover** - Falls back to SMTP if API fails
4. **Better Monitoring** - Real-time status checks in admin panel
5. **Future-Proof** - OAuth tokens don't expire like app passwords

Your Gmail API integration is now ready for testing and production use!
