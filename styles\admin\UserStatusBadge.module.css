/* User Status Badge Styles */

.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
  transition: all 0.2s ease;
}

/* Badge Sizes */
.small {
  padding: 0.125rem 0.5rem;
  font-size: 0.625rem;
}

.medium {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.large {
  padding: 0.375rem 1rem;
  font-size: 0.875rem;
}

/* Status Colors */
.statusActive {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.statusInactive {
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.statusDeactivated {
  background: #e2e8f0;
  color: #4a5568;
  border: 1px solid #cbd5e0;
}

.statusDefault {
  background: #f7fafc;
  color: #718096;
  border: 1px solid #e2e8f0;
}

/* Status Icon */
.statusIcon {
  font-size: 0.875em;
  font-weight: bold;
  line-height: 1;
}

.statusLabel {
  line-height: 1;
}

/* Hover Effects */
.statusBadge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statusActive:hover {
  background: #9ae6b4;
  border-color: #68d391;
}

.statusInactive:hover {
  background: #feb2b2;
  border-color: #fc8181;
}

.statusDeactivated:hover {
  background: #cbd5e0;
  border-color: #a0aec0;
}

.statusDefault:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

/* Accessibility */
.statusBadge:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .statusBadge {
    border-width: 2px;
    font-weight: 700;
  }

  .statusActive {
    background: #22543d;
    color: white;
    border-color: #22543d;
  }

  .statusInactive {
    background: #c53030;
    color: white;
    border-color: #c53030;
  }

  .statusDeactivated {
    background: #4a5568;
    color: white;
    border-color: #4a5568;
  }

  .statusDefault {
    background: #718096;
    color: white;
    border-color: #718096;
  }
}

/* Print styles */
@media print {
  .statusBadge {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .statusBadge {
    font-size: 0.625rem;
    padding: 0.125rem 0.5rem;
  }

  .statusLabel {
    display: none;
  }

  .statusIcon {
    font-size: 1rem;
  }
}
