.main {
  padding: 0;
  width: 100%;
}

/* Hero Section */
.heroSection {
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('/images/services-hero.jpg');
  background-size: cover;
  background-position: center;
  height: 40vh;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  padding: 2rem;
}

.heroContent {
  max-width: 800px;
}

.heroTitle {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.heroSubtitle {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Products Section */
.productsSection {
  padding: 5rem 2rem;
  background-color: var(--background-color, #f9f9f9);
}

.sectionContainer {
  max-width: var(--max-width);
  margin: 0 auto;
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.sectionSubtitle {
  text-align: center;
  font-size: 1.1rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto 3rem;
  line-height: 1.6;
}

/* Filter tabs */
.filterTabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filterTab {
  padding: 0.8rem 1.5rem;
  border: 2px solid var(--primary-color);
  border-radius: 30px;
  background: transparent;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filterTab:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.activeTab {
  background-color: var(--primary-color);
  color: white;
}

/* Products section */
.productsSection {
  margin-bottom: 3rem;
}

.productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-bottom: 3rem;
}

.productCard {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.productCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.productImage {
  height: 200px;
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.imagePlaceholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}

.productImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productImg:hover {
  transform: scale(1.05);
}

.productCard h3 {
  padding: 1rem 1rem 0.5rem;
  font-size: 1.2rem;
}

.price {
  padding: 0 1rem 0.5rem;
  color: var(--primary-color);
  font-weight: 600;
}

.description {
  padding: 0 1rem 1rem;
  color: var(--light-text-color);
  font-size: 0.9rem;
  flex-grow: 1;
}

.button {
  display: block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  text-align: center;
  font-weight: 500;
  transition: background-color 0.3s ease;
  margin: 0 1rem 1rem;
  border-radius: var(--border-radius);
}

.button:hover {
  background-color: #156a87;
  text-decoration: none;
}

/* Call to Action Section */
.cta {
  background-color: var(--primary-color);
  color: white;
  padding: 5rem 2rem;
  text-align: center;
}

.cta h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta p {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto 2rem;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.ctaButtons .button {
  display: inline-block;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  margin: 0;
}

.ctaButtons .button {
  background-color: white;
  color: var(--primary-color);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.ctaButtons .button:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
}

.outlineButton {
  background-color: transparent !important;
  border: 2px solid white;
  color: white !important;
}

.outlineButton:hover {
  background-color: white !important;
  color: var(--primary-color) !important;
}

/* Responsive styles */
@media (max-width: 992px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 1rem;
  }

  .sectionTitle {
    font-size: 1.8rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .filterTabs {
    gap: 0.8rem;
    margin-bottom: 2rem;
  }

  .filterTab {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .cta h2 {
    font-size: 1.8rem;
  }

  .cta p {
    font-size: 1rem;
  }

  .ctaButtons .button {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .productsGrid {
    grid-template-columns: 1fr;
    max-width: 320px;
    margin: 0 auto;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 1rem;
    max-width: 250px;
    margin: 0 auto;
  }
}
