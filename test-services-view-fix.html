<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services View Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .visibility-status {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        .visibility-item {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .visible { background-color: #d4edda; color: #155724; }
        .hidden { background-color: #f8d7da; color: #721c24; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Services View Fix Test</h1>
        <p>Testing that the services_with_pricing view now includes visibility fields.</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testPublicServices()">Test Public Services API</button>
            <button onclick="testPOSServices()">Test POS Services (Admin)</button>
            <button onclick="testAllServices()">Test All Admin Services</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="info status">Click a test button to start testing...</div>
            </div>
        </div>

        <div class="grid">
            <div class="container">
                <h3>Public Services (visible_on_public=true)</h3>
                <div id="public-services">
                    <div class="info status">Click "Test Public Services API" to load...</div>
                </div>
            </div>

            <div class="container">
                <h3>POS Services (visible_on_pos=true)</h3>
                <div id="pos-services">
                    <div class="info status">Click "Test POS Services" to load...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="${result.type} status">[${result.timestamp}] ${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResults();
        }

        async function testPublicServices() {
            addResult('🧪 Testing Public Services API...', 'info');
            
            try {
                const response = await fetch('/api/public/services');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`✅ Public Services API: Successfully loaded ${services.length} services`, 'success');
                
                // Display services
                displayServices(services, 'public-services', 'Public');
                
                // Check if services have visibility info
                if (services.length > 0) {
                    const firstService = services[0];
                    if (firstService.visible_on_public !== undefined) {
                        addResult(`✅ Visibility data available in public services`, 'success');
                    } else {
                        addResult(`⚠️ Visibility data not available in public services response`, 'warning');
                    }
                }
                
            } catch (error) {
                addResult(`❌ Public Services API Error: ${error.message}`, 'error');
                document.getElementById('public-services').innerHTML = 
                    `<div class="error status">Error: ${error.message}</div>`;
            }
        }

        async function testPOSServices() {
            addResult('🧪 Testing POS Services (simulating POS query)...', 'info');
            
            try {
                // Simulate the POS query by directly querying the services with POS filter
                const response = await fetch('/api/admin/services/index');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const allServices = data.services || [];
                
                // Filter for POS visible services (simulating what POS terminal does)
                const posServices = allServices.filter(service => service.visible_on_pos === true);
                
                addResult(`✅ POS Services: Found ${posServices.length} POS-visible services out of ${allServices.length} total`, 'success');
                
                // Display services
                displayServices(posServices, 'pos-services', 'POS');
                
            } catch (error) {
                addResult(`❌ POS Services Error: ${error.message}`, 'error');
                document.getElementById('pos-services').innerHTML = 
                    `<div class="error status">Error: ${error.message}</div>`;
            }
        }

        async function testAllServices() {
            addResult('🧪 Testing All Admin Services...', 'info');
            
            try {
                const response = await fetch('/api/admin/services/index');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`✅ Admin Services API: Successfully loaded ${services.length} services`, 'success');
                
                // Check visibility field availability
                if (services.length > 0) {
                    const firstService = services[0];
                    const hasVisibility = firstService.visible_on_public !== undefined && 
                                         firstService.visible_on_pos !== undefined && 
                                         firstService.visible_on_events !== undefined;
                    
                    if (hasVisibility) {
                        addResult(`✅ All visibility fields available in admin services`, 'success');
                        
                        // Count visibility settings
                        const publicCount = services.filter(s => s.visible_on_public === true).length;
                        const posCount = services.filter(s => s.visible_on_pos === true).length;
                        const eventsCount = services.filter(s => s.visible_on_events === true).length;
                        
                        addResult(`📊 Visibility counts: Public=${publicCount}, POS=${posCount}, Events=${eventsCount}`, 'info');
                    } else {
                        addResult(`❌ Visibility fields missing in admin services`, 'error');
                    }
                }
                
            } catch (error) {
                addResult(`❌ Admin Services API Error: ${error.message}`, 'error');
            }
        }

        function displayServices(services, containerId, type) {
            const container = document.getElementById(containerId);
            
            if (services.length === 0) {
                container.innerHTML = `<div class="warning status">No ${type} services found</div>`;
                return;
            }
            
            const serviceCards = services.slice(0, 5).map(service => {
                const name = service.name || service.title || 'Unnamed Service';
                const hasVisibility = service.visible_on_public !== undefined;
                
                return `
                    <div class="service-card">
                        <h4>${name}</h4>
                        ${hasVisibility ? `
                            <div class="visibility-status">
                                <div class="visibility-item ${service.visible_on_public ? 'visible' : 'hidden'}">
                                    Public: ${service.visible_on_public ? 'Visible' : 'Hidden'}
                                </div>
                                <div class="visibility-item ${service.visible_on_pos ? 'visible' : 'hidden'}">
                                    POS: ${service.visible_on_pos ? 'Visible' : 'Hidden'}
                                </div>
                                <div class="visibility-item ${service.visible_on_events ? 'visible' : 'hidden'}">
                                    Events: ${service.visible_on_events ? 'Visible' : 'Hidden'}
                                </div>
                            </div>
                        ` : '<div class="warning status">No visibility data</div>'}
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                ${serviceCards}
                ${services.length > 5 ? `<p><em>Showing first 5 of ${services.length} services</em></p>` : ''}
            `;
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testPublicServices();
                setTimeout(testAllServices, 1000);
            }, 1000);
        });
    </script>
</body>
</html>
