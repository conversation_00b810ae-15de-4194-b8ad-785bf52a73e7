#!/usr/bin/env node

/**
 * Performance and CSP Verification Script
 * Verifies that the performance and CSP fixes are working correctly
 */

import https from 'https';
import { URL } from 'url';

const DOMAIN = 'www.oceansoulsparkles.com.au';
const TIMEOUT = 10000;

console.log('🔍 Performance and CSP Verification for Ocean Soul Sparkles\n');

let testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

/**
 * Log test result
 */
function logResult(test, status, message, details = null) {
  const symbols = { pass: '✅', fail: '❌', warn: '⚠️' };
  console.log(`${symbols[status]} ${test}: ${message}`);
  
  if (details) {
    console.log(`   ${details}`);
  }
  
  testResults.tests.push({ test, status, message, details });
  testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

/**
 * Make HTTPS request
 */
function makeHttpsRequest(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, { timeout: TIMEOUT }, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data
        });
      });
    });
    
    request.on('error', reject);
    request.on('timeout', () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Test CSP headers
 */
async function testCSPHeaders() {
  console.log('🛡️ Testing Content Security Policy...\n');
  
  try {
    const response = await makeHttpsRequest(`https://${DOMAIN}`);
    const csp = response.headers['content-security-policy'];
    
    if (!csp) {
      logResult('CSP Header', 'fail', 'Content-Security-Policy header not found');
      return;
    }
    
    logResult('CSP Header', 'pass', 'Content-Security-Policy header present');
    
    // Check for OneSignal domains
    if (csp.includes('onesignal.com')) {
      logResult('OneSignal CSP', 'pass', 'OneSignal domains whitelisted in CSP');
    } else {
      logResult('OneSignal CSP', 'fail', 'OneSignal domains not found in CSP');
    }
    
    // Check for Google Fonts
    if (csp.includes('fonts.googleapis.com')) {
      logResult('Google Fonts CSP', 'pass', 'Google Fonts domains whitelisted in CSP');
    } else {
      logResult('Google Fonts CSP', 'fail', 'Google Fonts domains not found in CSP');
    }
    
    // Check for script-src-elem directive
    if (csp.includes('script-src-elem')) {
      logResult('Script-Src-Elem', 'pass', 'script-src-elem directive present');
    } else {
      logResult('Script-Src-Elem', 'warn', 'script-src-elem directive not found');
    }
    
    // Check for style-src-elem directive
    if (csp.includes('style-src-elem')) {
      logResult('Style-Src-Elem', 'pass', 'style-src-elem directive present');
    } else {
      logResult('Style-Src-Elem', 'warn', 'style-src-elem directive not found');
    }
    
    // Check for Square domains
    if (csp.includes('squareup.com')) {
      logResult('Square CSP', 'pass', 'Square domains whitelisted in CSP');
    } else {
      logResult('Square CSP', 'fail', 'Square domains not found in CSP');
    }
    
  } catch (error) {
    logResult('CSP Headers', 'fail', `Failed to check CSP: ${error.message}`);
  }
}

/**
 * Test performance CSS inclusion
 */
async function testPerformanceCSS() {
  console.log('\n🚀 Testing Performance CSS...\n');
  
  try {
    const response = await makeHttpsRequest(`https://${DOMAIN}`);
    const html = response.data;
    
    // Check if the page loads successfully
    if (response.statusCode === 200) {
      logResult('Page Load', 'pass', 'Website loads successfully');
    } else {
      logResult('Page Load', 'fail', `Unexpected status code: ${response.statusCode}`);
      return;
    }
    
    // Check for react-toastify CSS
    if (html.includes('react-toastify') || html.includes('Toastify')) {
      logResult('Toastify CSS', 'pass', 'React-toastify CSS detected');
    } else {
      logResult('Toastify CSS', 'warn', 'React-toastify CSS not detected in HTML');
    }
    
    // Check for performance optimization indicators
    if (html.includes('will-change') || html.includes('transform3d')) {
      logResult('Performance CSS', 'pass', 'Performance optimizations detected');
    } else {
      logResult('Performance CSS', 'warn', 'Performance optimizations not detected in HTML');
    }
    
  } catch (error) {
    logResult('Performance CSS', 'fail', `Failed to check performance CSS: ${error.message}`);
  }
}

/**
 * Test Google Fonts loading
 */
async function testGoogleFonts() {
  console.log('\n🔤 Testing Google Fonts...\n');
  
  try {
    // Test Google Fonts CSS endpoint
    const fontsUrl = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Dancing+Script:wght@400;700&display=swap';
    const response = await makeHttpsRequest(fontsUrl);
    
    if (response.statusCode === 200) {
      logResult('Google Fonts CSS', 'pass', 'Google Fonts CSS loads successfully');
      
      // Check if CSS contains font definitions
      if (response.data.includes('@font-face')) {
        logResult('Font Definitions', 'pass', 'Font definitions found in CSS');
      } else {
        logResult('Font Definitions', 'warn', 'Font definitions not found in CSS');
      }
    } else {
      logResult('Google Fonts CSS', 'fail', `Google Fonts CSS failed to load: ${response.statusCode}`);
    }
    
  } catch (error) {
    logResult('Google Fonts', 'fail', `Failed to test Google Fonts: ${error.message}`);
  }
}

/**
 * Test OneSignal accessibility
 */
async function testOneSignal() {
  console.log('\n🔔 Testing OneSignal Accessibility...\n');
  
  try {
    // Test OneSignal API endpoint (this might be blocked by CORS, but we can check if it's reachable)
    const oneSignalUrl = 'https://onesignal.com/api/v1/apps';
    
    // We can't actually test the API call due to CORS, but we can check if the domain is reachable
    const response = await makeHttpsRequest('https://onesignal.com/');
    
    if (response.statusCode === 200 || response.statusCode === 301 || response.statusCode === 302) {
      logResult('OneSignal Domain', 'pass', 'OneSignal domain is reachable');
    } else {
      logResult('OneSignal Domain', 'warn', `OneSignal domain returned: ${response.statusCode}`);
    }
    
  } catch (error) {
    logResult('OneSignal Domain', 'warn', `OneSignal domain test inconclusive: ${error.message}`);
  }
}

/**
 * Test overall security headers
 */
async function testSecurityHeaders() {
  console.log('\n🔒 Testing Security Headers...\n');
  
  try {
    const response = await makeHttpsRequest(`https://${DOMAIN}`);
    const headers = response.headers;
    
    // Check HSTS
    if (headers['strict-transport-security']) {
      logResult('HSTS Header', 'pass', 'HSTS header present');
    } else {
      logResult('HSTS Header', 'warn', 'HSTS header missing');
    }
    
    // Check X-Frame-Options
    if (headers['x-frame-options']) {
      logResult('X-Frame-Options', 'pass', `X-Frame-Options: ${headers['x-frame-options']}`);
    } else {
      logResult('X-Frame-Options', 'warn', 'X-Frame-Options header missing');
    }
    
    // Check X-Content-Type-Options
    if (headers['x-content-type-options']) {
      logResult('X-Content-Type-Options', 'pass', 'X-Content-Type-Options header present');
    } else {
      logResult('X-Content-Type-Options', 'warn', 'X-Content-Type-Options header missing');
    }
    
  } catch (error) {
    logResult('Security Headers', 'fail', `Failed to check security headers: ${error.message}`);
  }
}

/**
 * Generate final report
 */
function generateReport() {
  console.log('\n📊 Performance and CSP Verification Report\n');
  console.log(`Tests run: ${testResults.tests.length}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️  Warnings: ${testResults.warnings}`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ VERIFICATION FAILED');
    console.log('Some critical tests failed. Please review the issues above.\n');
    
    console.log('Failed tests:');
    testResults.tests
      .filter(test => test.status === 'fail')
      .forEach(test => console.log(`  - ${test.test}: ${test.message}`));
    
    return false;
  } else if (testResults.warnings > 0) {
    console.log('\n⚠️  VERIFICATION PASSED WITH WARNINGS');
    console.log('Most tests passed, but some warnings were found.\n');
    
    console.log('Warnings:');
    testResults.tests
      .filter(test => test.status === 'warn')
      .forEach(test => console.log(`  - ${test.test}: ${test.message}`));
    
    return true;
  } else {
    console.log('\n✅ VERIFICATION PASSED');
    console.log('All tests passed! Your performance and CSP fixes are working correctly.\n');
    return true;
  }
}

/**
 * Main execution
 */
async function main() {
  console.log(`Testing domain: ${DOMAIN}\n`);
  
  await testCSPHeaders();
  await testPerformanceCSS();
  await testGoogleFonts();
  await testOneSignal();
  await testSecurityHeaders();
  
  const success = generateReport();
  
  if (success) {
    console.log('🎉 Your performance and CSP fixes are working correctly!');
    console.log('\nNext steps:');
    console.log('1. Test toast notifications in your browser');
    console.log('2. Check browser console for any CSP violations');
    console.log('3. Verify OneSignal notifications work');
    console.log('4. Confirm Google Fonts are loading properly');
  } else {
    console.log('🔧 Please fix the issues above and run the test again.');
  }
  
  process.exit(success ? 0 : 1);
}

// Run the verification
main().catch(error => {
  console.error('❌ Verification failed:', error);
  process.exit(1);
});
