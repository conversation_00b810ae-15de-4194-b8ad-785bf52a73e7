import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import CustomerForm from '@/components/admin/CustomerForm'
import { useAuth } from '@/contexts/AuthContext'
import supabase from '@/lib/supabase'
import styles from '@/styles/admin/CustomersPage.module.css'

export default function EditCustomerPage() {
  const router = useRouter()
  const { id } = router.query
  const { user, isAuthenticated } = useAuth()
  const [customer, setCustomer] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (id && isAuthenticated) {
      fetchCustomer()
    }
  }, [id, isAuthenticated])

  const fetchCustomer = async () => {
    setLoading(true)
    setError(null)

    try {
      // Get the current session token
      const { data: sessionData } = await supabase.auth.getSession();
      const token = sessionData?.session?.access_token;

      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Make the request with authentication headers
      const response = await fetch(`/api/customers/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch customer');
      }

      const data = await response.json();
      setCustomer(data.customer);
    } catch (error) {
      console.error('Error fetching customer:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }

  const handleCancel = () => {
    router.push(`/admin/customers/${id}`)
  }

  const handleSave = (savedCustomer) => {
    router.push(`/admin/customers/${savedCustomer.id}`)
  }

  return (
    <ProtectedRoute>
      <AdminLayout title={customer ? `Edit Customer: ${customer.name}` : 'Edit Customer'}>
        <div className={styles.customersPage}>
          <div className={styles.header}>
            <h2>{customer ? `Edit Customer: ${customer.name}` : 'Edit Customer'}</h2>
          </div>

          {error && <div className={styles.error}>{error}</div>}

          {loading ? (
            <div className={styles.loading}>Loading customer data...</div>
          ) : (
            <CustomerForm
              customer={customer}
              onCancel={handleCancel}
              onSave={handleSave}
            />
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
