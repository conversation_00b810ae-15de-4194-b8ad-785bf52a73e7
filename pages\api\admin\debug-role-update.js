import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

export default async function handler(req, res) {
  // Set proper content type
  res.setHeader('Content-Type', 'application/json');

  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Debug role update endpoint called: ${req.method}`);

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Step 1: Log all request details
    console.log(`[${requestId}] === REQUEST DEBUGGING ===`);
    console.log(`[${requestId}] Method: ${req.method}`);
    console.log(`[${requestId}] URL: ${req.url}`);
    console.log(`[${requestId}] Headers:`, Object.keys(req.headers));
    console.log(`[${requestId}] Content-Type: ${req.headers['content-type']}`);
    console.log(`[${requestId}] Authorization present: ${!!req.headers.authorization}`);
    console.log(`[${requestId}] X-Auth-Token present: ${!!req.headers['x-auth-token']}`);

    // Step 2: Log raw request body
    console.log(`[${requestId}] === BODY DEBUGGING ===`);
    console.log(`[${requestId}] Raw body:`, req.body);
    console.log(`[${requestId}] Body type:`, typeof req.body);
    console.log(`[${requestId}] Body stringified:`, JSON.stringify(req.body, null, 2));

    // Step 3: Extract and validate data
    const { userId, role: newRole } = req.body || {};
    
    console.log(`[${requestId}] === DATA EXTRACTION ===`);
    console.log(`[${requestId}] Extracted userId: "${userId}" (type: ${typeof userId})`);
    console.log(`[${requestId}] Extracted role: "${newRole}" (type: ${typeof newRole})`);

    // Step 4: Validate required fields
    const validationResults = {
      userIdPresent: !!userId,
      rolePresent: !!newRole,
      userIdType: typeof userId,
      roleType: typeof newRole,
      userIdValue: userId,
      roleValue: newRole
    };

    console.log(`[${requestId}] === VALIDATION RESULTS ===`);
    console.log(`[${requestId}] Validation:`, validationResults);    // Step 5: Role validation
    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user'];
    const roleValidation = {
      validRoles: validRoles,
      providedRole: newRole,
      isValid: validRoles.includes(newRole),
      exactMatch: validRoles.find(r => r === newRole),
      trimmedMatch: validRoles.includes(String(newRole).trim()),
      lowercaseMatch: validRoles.includes(String(newRole).toLowerCase())
    };

    console.log(`[${requestId}] === ROLE VALIDATION ===`);
    console.log(`[${requestId}] Role validation:`, roleValidation);

    // Step 6: Test authentication
    console.log(`[${requestId}] === AUTHENTICATION TEST ===`);
    let authResult;
    try {
      authResult = await authenticateAdminRequest(req);
      console.log(`[${requestId}] Auth result:`, {
        authorized: authResult.authorized,
        userEmail: authResult.user?.email,
        userRole: authResult.role,
        error: authResult.error?.message
      });
    } catch (authError) {
      console.error(`[${requestId}] Auth error:`, authError);
      authResult = { authorized: false, error: authError };
    }

    // Step 7: Test admin client
    console.log(`[${requestId}] === ADMIN CLIENT TEST ===`);
    let adminClientTest;
    try {
      const adminClient = getAdminClient();
      adminClientTest = {
        clientExists: !!adminClient,
        clientType: typeof adminClient
      };
      console.log(`[${requestId}] Admin client test:`, adminClientTest);
    } catch (clientError) {
      console.error(`[${requestId}] Admin client error:`, clientError);
      adminClientTest = { error: clientError.message };
    }

    // Return comprehensive debug information
    return res.status(200).json({
      success: true,
      requestId,
      debug: {
        request: {
          method: req.method,
          url: req.url,
          hasAuth: !!req.headers.authorization,
          hasXAuthToken: !!req.headers['x-auth-token'],
          contentType: req.headers['content-type']
        },
        body: {
          raw: req.body,
          type: typeof req.body,
          stringified: JSON.stringify(req.body)
        },
        extraction: {
          userId,
          newRole,
          userIdType: typeof userId,
          roleType: typeof newRole
        },
        validation: validationResults,
        roleValidation,
        authentication: authResult,
        adminClient: adminClientTest
      }
    });

  } catch (error) {
    console.error(`[${requestId}] Debug endpoint error:`, error);
    return res.status(500).json({
      error: 'Debug endpoint failed',
      message: error.message,
      requestId
    });
  }
}
