/**
 * Enable Authentication Bypass Script
 * 
 * This script adds the ENABLE_AUTH_BYPASS=true setting to your .env.local file
 * to enable the development mode authentication bypass.
 * 
 * Usage:
 * 1. Run with: node scripts/enable-auth-bypass.js
 * 2. Restart the server
 */

const fs = require('fs');
const path = require('path');

// Path to the .env.local file
const envFilePath = path.join(__dirname, '..', '.env.local');

// Read the current file
console.log(`Reading ${envFilePath}...`);
let envFileContent;
try {
  envFileContent = fs.readFileSync(envFilePath, 'utf8');
  console.log('File read successfully.');
} catch (error) {
  console.error(`Error reading file: ${error.message}`);
  process.exit(1);
}

// Create a backup
const backupPath = `${envFilePath}.backup`;
console.log(`Creating backup at ${backupPath}...`);
try {
  fs.writeFileSync(backupPath, envFileContent);
  console.log('Backup created successfully.');
} catch (error) {
  console.error(`Error creating backup: ${error.message}`);
  process.exit(1);
}

// Check if ENABLE_AUTH_BYPASS is already set
if (envFileContent.includes('ENABLE_AUTH_BYPASS=')) {
  console.log('ENABLE_AUTH_BYPASS is already set in .env.local');
  
  // Make sure it's set to true
  if (!envFileContent.includes('ENABLE_AUTH_BYPASS=true')) {
    console.log('Updating ENABLE_AUTH_BYPASS to true...');
    envFileContent = envFileContent.replace(/ENABLE_AUTH_BYPASS=.*/, 'ENABLE_AUTH_BYPASS=true');
  } else {
    console.log('ENABLE_AUTH_BYPASS is already set to true');
    process.exit(0);
  }
} else {
  // Add ENABLE_AUTH_BYPASS=true to the file
  console.log('Adding ENABLE_AUTH_BYPASS=true to .env.local...');
  envFileContent += '\n\n# Enable authentication bypass for development\nENABLE_AUTH_BYPASS=true\n';
}

// Write the modified file
console.log('Writing modified file...');
try {
  fs.writeFileSync(envFilePath, envFileContent);
  console.log('File updated successfully.');
} catch (error) {
  console.error(`Error writing file: ${error.message}`);
  console.log('Restoring from backup...');
  try {
    fs.copyFileSync(backupPath, envFilePath);
    console.log('Restored from backup.');
  } catch (restoreError) {
    console.error(`Error restoring from backup: ${restoreError.message}`);
  }
  process.exit(1);
}

console.log('\nAuthentication bypass enabled!');
console.log('Remember to restart the server for changes to take effect.');
console.log('\nWARNING: This bypass should only be used in development.');
console.log('Set ENABLE_AUTH_BYPASS=false or remove it before deploying to production!');
