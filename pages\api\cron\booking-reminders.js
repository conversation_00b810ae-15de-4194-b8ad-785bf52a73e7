import supabase from '@/lib/supabase';
import { sendBookingReminderNotification } from '@/lib/notifications-server';

/**
 * API endpoint for scheduled task to send booking reminders
 * This endpoint is designed to be called by a cron job or scheduled task
 * It should be secured with an API key or other authentication mechanism
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify API key for security
  const apiKey = req.headers['x-api-key'];
  if (!apiKey || apiKey !== process.env.CRON_API_KEY) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Calculate tomorrow's date range
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const tomorrowStart = new Date(tomorrow);
    tomorrowStart.setHours(0, 0, 0, 0);

    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(23, 59, 59, 999);

    // Use Supabase client directly
    const client = supabase;

    // Get all confirmed bookings for tomorrow that haven't had reminders sent
    const { data: bookings, error: bookingsError } = await client
      .from('bookings')
      .select(`
        id,
        customer_id,
        service_id,
        start_time,
        location,
        services:service_id (name)
      `)
      .eq('status', 'confirmed')
      .gte('start_time', tomorrowStart.toISOString())
      .lte('start_time', tomorrowEnd.toISOString())
      .is('reminder_sent', null); // Only get bookings where reminder hasn't been sent

    if (bookingsError) {
      console.error('Error fetching bookings:', bookingsError);
      return res.status(500).json({ error: 'Failed to fetch bookings' });
    }

    // If no bookings found, return success with count 0
    if (!bookings || bookings.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No bookings found for tomorrow that need reminders',
        count: 0
      });
    }

    // Send reminders for each booking
    const results = [];
    for (const booking of bookings) {
      const result = await sendBookingReminderNotification({
        bookingId: booking.id,
        customerId: booking.customer_id,
        startTime: booking.start_time,
        serviceName: booking.services.name,
        location: booking.location
      });

      // Mark the booking as having had a reminder sent
      if (result.success) {
        await client
          .from('bookings')
          .update({ reminder_sent: new Date().toISOString() })
          .eq('id', booking.id);
      }

      results.push({
        bookingId: booking.id,
        success: result.success,
        error: result.error
      });
    }

    // Count successful and failed reminders
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    return res.status(200).json({
      success: true,
      message: `Sent ${successCount} booking reminders, ${failedCount} failed`,
      count: bookings.length,
      successCount,
      failedCount,
      results
    });
  } catch (error) {
    console.error('Error sending booking reminders:', error);
    return res.status(500).json({ error: 'Failed to send booking reminders' });
  }
}
