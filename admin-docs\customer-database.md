# Customer Database

This document provides detailed implementation instructions for the OceanSoulSparkles admin panel customer database system.

## Overview

The customer database system allows administrators to manage customer information, view booking history, and track customer preferences. It provides a searchable interface with filtering capabilities and supports data export for marketing purposes.

## Features

- Customer information management (contact details, preferences)
- Booking history tracking
- Searchable customer interface
- Filtering by name, email, service history, and spend amount
- Data export functionality (CSV/Excel)
- GDPR compliance features (data deletion, consent management)

## Database Schema

Create the following tables in Supabase:

```sql
-- Customers table
CREATE TABLE public.customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Australia',
  notes TEXT,
  marketing_consent BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer preferences table
CREATE TABLE public.customer_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  preference_key TEXT NOT NULL,
  preference_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id, preference_key)
);

-- Enable RLS
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all customers" ON public.customers
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert customers" ON public.customers
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update customers" ON public.customers
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Admins can delete customers" ON public.customers
  FOR DELETE USING (
    get_user_role(auth.uid()) = 'admin'
  );

-- Similar policies for customer_preferences table
```

## Implementation Steps

### 1. Create Customer API Endpoints

Create the following API endpoints:

```javascript
// pages/api/customers/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCustomers(req, res)
    case 'POST':
      return createCustomer(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get customers with optional filters
async function getCustomers(req, res) {
  const { search, sort_by, sort_order, limit, offset } = req.query

  let query = supabase
    .from('customers')
    .select('*')

  // Apply search filter
  if (search) {
    query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,phone.ilike.%${search}%`)
  }

  // Apply sorting
  if (sort_by) {
    const order = sort_order === 'desc' ? false : true
    query = query.order(sort_by, { ascending: order })
  } else {
    query = query.order('created_at', { ascending: false })
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit)
  }
  if (offset) {
    query = query.offset(offset)
  }

  const { data, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    customers: data,
    total: count
  })
}

// Create a new customer
async function createCustomer(req, res) {
  const { name, email, phone, address, city, state, postal_code, country, notes, marketing_consent } = req.body

  // Validate required fields
  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' })
  }

  // Check if email already exists
  const { data: existingCustomer, error: checkError } = await supabase
    .from('customers')
    .select('id')
    .eq('email', email)
    .single()

  if (checkError && checkError.code !== 'PGRST116') {
    return res.status(500).json({ error: checkError.message })
  }

  if (existingCustomer) {
    return res.status(409).json({ error: 'A customer with this email already exists' })
  }

  // Create customer
  const { data, error } = await supabase
    .from('customers')
    .insert([
      { 
        name, 
        email, 
        phone, 
        address, 
        city, 
        state, 
        postal_code, 
        country, 
        notes, 
        marketing_consent 
      }
    ])
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(201).json(data[0])
}
```

Create additional API endpoints for individual customer operations:

```javascript
// pages/api/customers/[id].js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin, isAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCustomer(id, res)
    case 'PUT':
      return updateCustomer(id, req, res)
    case 'DELETE':
      // Only admins can delete customers
      const isAdminUser = await isAdmin()
      if (!isAdminUser) {
        return res.status(403).json({ error: 'Forbidden' })
      }
      return deleteCustomer(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single customer with their booking history
async function getCustomer(id, res) {
  // Get customer details
  const { data: customer, error } = await supabase
    .from('customers')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!customer) {
    return res.status(404).json({ error: 'Customer not found' })
  }

  // Get customer booking history
  const { data: bookings, error: bookingsError } = await supabase
    .from('bookings')
    .select(`
      id,
      start_time,
      end_time,
      status,
      location,
      services:service_id (name, price)
    `)
    .eq('customer_id', id)
    .order('start_time', { ascending: false })

  if (bookingsError) {
    return res.status(500).json({ error: bookingsError.message })
  }

  // Get customer preferences
  const { data: preferences, error: preferencesError } = await supabase
    .from('customer_preferences')
    .select('*')
    .eq('customer_id', id)

  if (preferencesError) {
    return res.status(500).json({ error: preferencesError.message })
  }

  // Return combined data
  return res.status(200).json({
    ...customer,
    bookings: bookings || [],
    preferences: preferences || []
  })
}

// Update a customer
async function updateCustomer(id, req, res) {
  const { 
    name, 
    email, 
    phone, 
    address, 
    city, 
    state, 
    postal_code, 
    country, 
    notes, 
    marketing_consent,
    preferences 
  } = req.body

  // Validate required fields
  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' })
  }

  // Check if email already exists (for a different customer)
  const { data: existingCustomer, error: checkError } = await supabase
    .from('customers')
    .select('id')
    .eq('email', email)
    .neq('id', id)
    .single()

  if (checkError && checkError.code !== 'PGRST116') {
    return res.status(500).json({ error: checkError.message })
  }

  if (existingCustomer) {
    return res.status(409).json({ error: 'Another customer with this email already exists' })
  }

  // Update customer
  const { data, error } = await supabase
    .from('customers')
    .update({
      name,
      email,
      phone,
      address,
      city,
      state,
      postal_code,
      country,
      notes,
      marketing_consent,
      updated_at: new Date()
    })
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Customer not found' })
  }

  // Update preferences if provided
  if (preferences && Array.isArray(preferences)) {
    // First delete existing preferences
    await supabase
      .from('customer_preferences')
      .delete()
      .eq('customer_id', id)

    // Then insert new preferences
    if (preferences.length > 0) {
      const preferencesData = preferences.map(pref => ({
        customer_id: id,
        preference_key: pref.key,
        preference_value: pref.value
      }))

      await supabase
        .from('customer_preferences')
        .insert(preferencesData)
    }
  }

  return res.status(200).json(data[0])
}

// Delete a customer
async function deleteCustomer(id, res) {
  // Delete customer (preferences will be deleted via cascade)
  const { data, error } = await supabase
    .from('customers')
    .delete()
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Customer not found' })
  }

  return res.status(200).json({ message: 'Customer deleted successfully' })
}
```

### 2. Create Customer List Component

Create a component for displaying and searching customers:

```javascript
// components/admin/CustomerList.js
import { useState, useEffect } from 'react'
import Link from 'next/link'
import styles from '@/styles/admin/CustomerList.module.css'

export default function CustomerList() {
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCustomers, setTotalCustomers] = useState(0)
  const limit = 20

  // Fetch customers when search, sort, or page changes
  useEffect(() => {
    const fetchCustomers = async () => {
      setLoading(true)
      setError(null)

      try {
        const offset = (page - 1) * limit
        const queryParams = new URLSearchParams({
          limit,
          offset,
          sort_by: sortBy,
          sort_order: sortOrder
        })

        if (search) {
          queryParams.append('search', search)
        }

        const response = await fetch(`/api/customers?${queryParams.toString()}`)

        if (!response.ok) {
          throw new Error('Failed to fetch customers')
        }

        const data = await response.json()
        setCustomers(data.customers)
        setTotalCustomers(data.total)
        setTotalPages(Math.ceil(data.total / limit))
      } catch (error) {
        console.error('Error fetching customers:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchCustomers()
  }, [search, sortBy, sortOrder, page])

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    setPage(1) // Reset to first page on new search
  }

  // Handle sort change
  const handleSort = (field) => {
    if (sortBy === field) {
      // Toggle sort order if clicking the same field
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // Default to ascending for new sort field
      setSortBy(field)
      setSortOrder('asc')
    }
    setPage(1) // Reset to first page on new sort
  }

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? '↑' : '↓'
  }

  if (error) {
    return <div className={styles.error}>{error}</div>
  }

  return (
    <div className={styles.customerListContainer}>
      <div className={styles.searchBar}>
        <input
          type="text"
          placeholder="Search customers..."
          value={search}
          onChange={handleSearchChange}
          className={styles.searchInput}
        />
      </div>

      {loading && customers.length === 0 ? (
        <div className={styles.loading}>Loading customers...</div>
      ) : (
        <>
          <div className={styles.customerCount}>
            Showing {customers.length} of {totalCustomers} customers
          </div>

          <table className={styles.customerTable}>
            <thead>
              <tr>
                <th onClick={() => handleSort('name')}>
                  Name {renderSortIndicator('name')}
                </th>
                <th onClick={() => handleSort('email')}>
                  Email {renderSortIndicator('email')}
                </th>
                <th onClick={() => handleSort('phone')}>
                  Phone {renderSortIndicator('phone')}
                </th>
                <th onClick={() => handleSort('created_at')}>
                  Created {renderSortIndicator('created_at')}
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {customers.map((customer) => (
                <tr key={customer.id}>
                  <td>{customer.name}</td>
                  <td>{customer.email}</td>
                  <td>{customer.phone || '-'}</td>
                  <td>{new Date(customer.created_at).toLocaleDateString()}</td>
                  <td>
                    <Link href={`/admin/customers/${customer.id}`}>
                      <a className={styles.viewButton}>View</a>
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {customers.length === 0 && (
            <div className={styles.noResults}>No customers found</div>
          )}

          <div className={styles.pagination}>
            <button
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={styles.paginationButton}
            >
              Previous
            </button>
            <span className={styles.pageInfo}>
              Page {page} of {totalPages}
            </span>
            <button
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className={styles.paginationButton}
            </button>
          </div>
        </>
      )}
    </div>
  )
}
```

## Data Export Functionality

Implement data export functionality for marketing purposes:

```javascript
// pages/api/customers/export.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isAdmin } from '@/lib/auth'
import { Parser } from 'json2csv'

export default async function handler(req, res) {
  // Only admins can export customer data
  const isAdminUser = await isAdmin()
  if (!isAdminUser) {
    return res.status(403).json({ error: 'Forbidden' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get only customers who have consented to marketing
    const { data, error } = await supabase
      .from('customers')
      .select('name, email, phone, city, state, country, created_at')
      .eq('marketing_consent', true)

    if (error) {
      return res.status(500).json({ error: error.message })
    }

    // Convert to CSV
    const fields = ['name', 'email', 'phone', 'city', 'state', 'country', 'created_at']
    const json2csvParser = new Parser({ fields })
    const csv = json2csvParser.parse(data)

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv')
    res.setHeader('Content-Disposition', 'attachment; filename=customers.csv')

    // Send CSV data
    res.status(200).send(csv)
  } catch (error) {
    console.error('Error exporting customers:', error)
    res.status(500).json({ error: 'Failed to export customers' })
  }
}
```

## GDPR Compliance Features

Implement GDPR compliance features:

```javascript
// pages/api/customers/[id]/gdpr-delete.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Only admins can perform GDPR deletions
  const isAdminUser = await isAdmin()
  if (!isAdminUser) {
    return res.status(403).json({ error: 'Forbidden' })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { id } = req.query

  try {
    // Get customer details for logging
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('email')
      .eq('id', id)
      .single()

    if (customerError) {
      return res.status(500).json({ error: customerError.message })
    }

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' })
    }

    // Anonymize customer data
    const { error: updateError } = await supabase
      .from('customers')
      .update({
        name: 'Anonymized User',
        email: `anonymized-${Date.now()}@deleted.oceansoulsparkles.com.au`,
        phone: null,
        address: null,
        city: null,
        state: null,
        postal_code: null,
        country: null,
        notes: 'Data deleted per GDPR request',
        marketing_consent: false,
        updated_at: new Date()
      })
      .eq('id', id)

    if (updateError) {
      return res.status(500).json({ error: updateError.message })
    }

    // Delete customer preferences
    await supabase
      .from('customer_preferences')
      .delete()
      .eq('customer_id', id)

    // Log the GDPR deletion request
    await supabase
      .from('gdpr_deletion_log')
      .insert([
        {
          customer_id: id,
          original_email: customer.email,
          requested_by: req.body.requested_by || 'admin',
          reason: req.body.reason || 'Customer request'
        }
      ])

    return res.status(200).json({ message: 'Customer data anonymized successfully' })
  } catch (error) {
    console.error('Error processing GDPR deletion:', error)
    return res.status(500).json({ error: 'Failed to process GDPR deletion request' })
  }
}
```

## Testing

1. Test customer creation, editing, and deletion
2. Test search and filtering functionality
3. Test data export
4. Test GDPR compliance features
5. Test integration with booking system

## Security Considerations

- Implement proper authentication and authorization
- Validate all input data
- Protect against SQL injection
- Implement rate limiting for API endpoints
- Log all customer data changes for audit purposes
- Ensure GDPR compliance with data handling
