// POS Square Payment Component Runtime Test
// Paste this in the browser console at /admin/pos

console.log('🧪 POS SQUARE PAYMENT TEST STARTING...');

// Test 1: Check if POSSquarePayment component exists
setTimeout(() => {
  console.log('\n📋 TEST 1: Component DOM Check');
  const posContainer = document.querySelector('[data-testid="pos-container"], .pos-container, #pos-container');
  console.log('POS Container found:', !!posContainer);
  
  const squareContainer = document.querySelector('[data-square-container]');
  console.log('Square container found:', !!squareContainer);
  if (squareContainer) {
    console.log('Square container ID:', squareContainer.id);
    console.log('Square container data attribute:', squareContainer.getAttribute('data-square-container'));
  }
  
  const cardInputs = document.querySelectorAll('iframe[name*="card"], input[name*="card"], div[id*="card"]');
  console.log('Card input elements found:', cardInputs.length);
  cardInputs.forEach((input, i) => {
    console.log(`Card input ${i + 1}:`, input.tagName, input.id || input.name);
  });
}, 2000);

// Test 2: Check Square SDK loading
setTimeout(() => {
  console.log('\n📋 TEST 2: Square SDK Check');
  console.log('window.Square exists:', !!window.Square);
  if (window.Square) {
    console.log('Square.payments exists:', !!window.Square.payments);
  }
  
  // Check for multiple Square SDK loads
  const squareScripts = document.querySelectorAll('script[src*="square"]');
  console.log('Square scripts loaded:', squareScripts.length);
  if (squareScripts.length > 1) {
    console.warn('⚠️ Multiple Square scripts detected - potential duplicate loading');
  }
}, 3000);

// Test 3: Check console monitoring
setTimeout(() => {
  console.log('\n📋 TEST 3: Console Monitor Check');
  console.log('window.getConsoleErrors exists:', !!window.getConsoleErrors);
  if (window.getConsoleErrors) {
    const consoleData = window.getConsoleErrors();
    console.log('Console errors captured:', consoleData.errorsCount);
    console.log('Console warnings captured:', consoleData.warningsCount);
    console.log('Console logs captured:', consoleData.logsCount);
    
    if (consoleData.errorsCount > 0) {
      console.log('Recent errors:', consoleData.errors.slice(-3));
    }
  }
}, 4000);

// Test 4: Check Supabase connection
setTimeout(() => {
  console.log('\n📋 TEST 4: Supabase Connection Check');
  console.log('window.supabase exists:', !!window.supabase);
  console.log('window.__SUPABASE_CLIENT_INSTANCE exists:', !!window.__SUPABASE_CLIENT_INSTANCE);
  console.log('window.__SUPABASE_CLIENT_COUNT:', window.__SUPABASE_CLIENT_COUNT);
  
  if (window.__SUPABASE_CLIENT_COUNT > 1) {
    console.warn('⚠️ Multiple Supabase client instances detected');
  }
}, 5000);

// Test 5: Memory Management Check
setTimeout(() => {
  console.log('\n📋 TEST 5: Memory Management Check');
  
  // Check for potential memory leaks
  const allElements = document.querySelectorAll('*');
  console.log('Total DOM elements:', allElements.length);
  
  // Check for duplicate IDs (potential container management issues)
  const allIds = [];
  document.querySelectorAll('[id]').forEach(el => {
    if (allIds.includes(el.id)) {
      console.warn('⚠️ Duplicate ID found:', el.id);
    }
    allIds.push(el.id);
  });
  
  // Check event listeners (rough estimate)
  const elementsWithListeners = document.querySelectorAll('[onclick], [onchange], [onsubmit]');
  console.log('Elements with inline event handlers:', elementsWithListeners.length);
  
}, 6000);

// Test 6: Environment Configuration
setTimeout(() => {
  console.log('\n📋 TEST 6: Environment Check');
  console.log('Current hostname:', window.location.hostname);
  console.log('Current port:', window.location.port);
  console.log('Protocol:', window.location.protocol);
  
  // Check if environment matches expected
  const expectedPort = '3002';
  if (window.location.port === expectedPort) {
    console.log('✅ Running on expected port:', expectedPort);
  } else {
    console.warn('⚠️ Port mismatch. Expected:', expectedPort, 'Actual:', window.location.port);
  }
}, 7000);

console.log('⏳ Test will complete in 8 seconds...');
setTimeout(() => {
  console.log('\n✅ POS SQUARE PAYMENT TEST COMPLETED');
  console.log('📊 Review results above for any issues');
}, 8000);
