/**
 * Admin User Verification Script
 * 
 * This script checks if the admin user exists in the user_roles table
 * and creates it if missing. It also verifies the user has the correct role.
 * 
 * Usage:
 * 1. Run with: node scripts/verify-admin-user.js
 * 2. Follow the prompts to enter your admin email
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Create Supabase admin client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase credentials in .env.local file');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function verifyAdminUser() {
  try {
    // Prompt for admin email
    rl.question('Enter the email of your admin user: ', async (email) => {
      if (!email) {
        console.error('Error: Email is required');
        rl.close();
        return;
      }

      console.log(`\nVerifying admin user for email: ${email}`);
      
      // Step 1: Check if user exists in auth.users
      const { data: userData, error: userError } = await supabaseAdmin.auth.admin.listUsers();
      
      if (userError) {
        console.error('Error fetching users:', userError.message);
        rl.close();
        return;
      }

      // Find user by email
      const user = userData.users.find(u => u.email === email);
      
      if (!user) {
        console.error(`\nError: User with email ${email} not found in auth.users`);
        console.log('\nWould you like to create this user? (y/n)');
        
        rl.question('', async (answer) => {
          if (answer.toLowerCase() === 'y') {
            await createAdminUser(email);
          } else {
            console.log('User creation skipped.');
            rl.close();
          }
        });
        return;
      }
      
      console.log(`\nUser found: ${user.id} (${user.email})`);
      
      // Step 2: Check if user has role in user_roles table
      const { data: roleData, error: roleError } = await supabaseAdmin
        .from('user_roles')
        .select('*')
        .eq('id', user.id);
      
      if (roleError) {
        console.error('Error checking user role:', roleError.message);
        rl.close();
        return;
      }
      
      if (!roleData || roleData.length === 0) {
        console.log('\nUser does not have a role assigned in user_roles table');
        console.log('Adding admin role...');
        
        const { error: insertError } = await supabaseAdmin
          .from('user_roles')
          .insert([{ id: user.id, role: 'admin' }]);
        
        if (insertError) {
          console.error('Error assigning admin role:', insertError.message);
        } else {
          console.log('✅ Admin role successfully assigned!');
        }
      } else {
        const userRole = roleData[0];
        console.log(`\nCurrent role: ${userRole.role}`);
        
        if (userRole.role !== 'admin') {
          console.log('Updating role to admin...');
          
          const { error: updateError } = await supabaseAdmin
            .from('user_roles')
            .update({ role: 'admin' })
            .eq('id', user.id);
          
          if (updateError) {
            console.error('Error updating role:', updateError.message);
          } else {
            console.log('✅ Role successfully updated to admin!');
          }
        } else {
          console.log('✅ User already has admin role');
        }
      }
      
      // Verify RLS policies
      console.log('\nVerifying Row Level Security (RLS) policies...');
      const { data: policies, error: policiesError } = await supabaseAdmin.rpc('get_policies');
      
      if (policiesError) {
        console.log('Could not verify RLS policies:', policiesError.message);
      } else {
        const userRolesPolicies = policies.filter(p => p.tablename === 'user_roles');
        console.log(`Found ${userRolesPolicies.length} policies for user_roles table`);
        
        if (userRolesPolicies.length === 0) {
          console.warn('⚠️ Warning: No RLS policies found for user_roles table');
          console.log('This may cause authentication issues. Consider adding appropriate policies.');
        }
      }
      
      console.log('\nAdmin user verification complete!');
      rl.close();
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    rl.close();
  }
}

async function createAdminUser(email) {
  try {
    rl.question('Enter password for new admin user: ', async (password) => {
      if (!password || password.length < 6) {
        console.error('Error: Password must be at least 6 characters');
        rl.close();
        return;
      }
      
      // Create user
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true
      });
      
      if (error) {
        console.error('Error creating user:', error.message);
        rl.close();
        return;
      }
      
      console.log(`\nUser created: ${data.user.id} (${data.user.email})`);
      
      // Assign admin role
      const { error: roleError } = await supabaseAdmin
        .from('user_roles')
        .insert([{ id: data.user.id, role: 'admin' }]);
      
      if (roleError) {
        console.error('Error assigning admin role:', roleError.message);
      } else {
        console.log('✅ Admin role successfully assigned!');
      }
      
      console.log('\nAdmin user creation complete!');
      rl.close();
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    rl.close();
  }
}

// Start the verification process
verifyAdminUser();
