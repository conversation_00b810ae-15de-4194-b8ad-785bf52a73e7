-- CRITICAL PRODUCTION FIX for Artist/Braider Onboarding System
-- Copy and paste this entire script into Supabase SQL Editor and run it

-- =============================================================================
-- FIX 1: Create admin_settings table (resolves "relation does not exist" error)
-- =============================================================================

CREATE TABLE IF NOT EXISTS public.admin_settings (
  setting_key TEXT PRIMARY KEY,
  setting_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.admin_settings ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access
DROP POLICY IF EXISTS "Admin users can manage admin settings" ON public.admin_settings;
CREATE POLICY "Admin users can manage admin settings" 
  ON public.admin_settings 
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON public.admin_settings(setting_key);

-- Insert default admin settings
INSERT INTO public.admin_settings (setting_key, setting_value) VALUES
  ('gmail_api_tokens', '{}'),
  ('gmail_smtp_user', ''),
  ('gmail_smtp_password', ''),
  ('gmail_from_email', ''),
  ('gmail_from_name', 'Ocean Soul Sparkles'),
  ('workspace_smtp_user', ''),
  ('workspace_smtp_password', ''),
  ('workspace_from_email', ''),
  ('workspace_from_name', 'Ocean Soul Sparkles'),
  ('onesignal_app_id', ''),
  ('onesignal_rest_api_key', ''),
  ('email_service_priority', 'gmail,workspace,onesignal'),
  ('system_maintenance_mode', 'false'),
  ('system_debug_mode', 'false'),
  ('system_email_enabled', 'true'),
  ('system_notifications_enabled', 'true')
ON CONFLICT (setting_key) DO NOTHING;

-- =============================================================================
-- FIX 2: Create/Fix token validation functions (resolves token validation API failures)
-- =============================================================================

-- Create or replace the validate_application_token function
CREATE OR REPLACE FUNCTION public.validate_application_token(token_value text)
RETURNS TABLE(
  is_valid boolean,
  user_id uuid,
  application_id uuid,
  error_message text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_record RECORD;
  app_record RECORD;
BEGIN
  -- Initialize return values
  is_valid := false;
  user_id := NULL;
  application_id := NULL;
  error_message := NULL;

  -- Check if token exists and is not expired
  SELECT 
    at.user_id,
    at.application_id,
    at.expires_at,
    at.used_at,
    at.token_type
  INTO token_record
  FROM public.application_tokens at
  WHERE at.token = token_value;

  -- If token not found
  IF NOT FOUND THEN
    error_message := 'Invalid or expired token';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if token has expired
  IF token_record.expires_at < NOW() THEN
    error_message := 'Token has expired';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if token has already been used (for one-time tokens)
  IF token_record.used_at IS NOT NULL AND token_record.token_type = 'application_access' THEN
    error_message := 'Token has already been used';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if associated application exists
  SELECT 
    aba.id,
    aba.application_type,
    aba.status
  INTO app_record
  FROM public.artist_braider_applications aba
  WHERE aba.id = token_record.application_id;

  -- If application not found
  IF NOT FOUND THEN
    error_message := 'Associated application not found';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if application is in a valid state for token usage
  IF app_record.status NOT IN ('pending', 'under_review') THEN
    error_message := 'Application is not in a valid state for token access';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Token is valid
  is_valid := true;
  user_id := token_record.user_id;
  application_id := token_record.application_id;
  error_message := NULL;

  RETURN NEXT;
  RETURN;
END;
$$;

-- Create or replace the mark_token_as_used function
CREATE OR REPLACE FUNCTION public.mark_token_as_used(token_value text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  rows_updated integer;
BEGIN
  -- Mark the token as used
  UPDATE public.application_tokens
  SET 
    used_at = NOW(),
    updated_at = NOW()
  WHERE token = token_value
    AND used_at IS NULL;

  GET DIAGNOSTICS rows_updated = ROW_COUNT;
  
  RETURN rows_updated > 0;
END;
$$;

-- Create or replace the generate_application_token function
CREATE OR REPLACE FUNCTION public.generate_application_token()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_value text;
  token_exists boolean;
BEGIN
  -- Generate a secure random token
  LOOP
    -- Generate a 32-character random string
    token_value := encode(gen_random_bytes(24), 'base64');
    -- Remove URL-unsafe characters
    token_value := replace(replace(replace(token_value, '+', '-'), '/', '_'), '=', '');
    
    -- Check if token already exists
    SELECT EXISTS(
      SELECT 1 FROM public.application_tokens 
      WHERE token = token_value
    ) INTO token_exists;
    
    -- Exit loop if token is unique
    EXIT WHEN NOT token_exists;
  END LOOP;
  
  RETURN token_value;
END;
$$;

-- =============================================================================
-- FIX 3: Create helper functions for admin settings
-- =============================================================================

-- Create a function to safely get admin settings
CREATE OR REPLACE FUNCTION public.get_admin_setting(setting_key_param text)
RETURNS text
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT setting_value 
  FROM public.admin_settings 
  WHERE setting_key = setting_key_param;
$$;

-- Create a function to safely set admin settings
CREATE OR REPLACE FUNCTION public.set_admin_setting(setting_key_param text, setting_value_param text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.admin_settings (setting_key, setting_value, updated_at)
  VALUES (setting_key_param, setting_value_param, NOW())
  ON CONFLICT (setting_key) 
  DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_at = EXCLUDED.updated_at;
END;
$$;

-- =============================================================================
-- FIX 4: Grant necessary permissions
-- =============================================================================

-- Grant permissions for admin_settings
GRANT SELECT ON public.admin_settings TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_setting(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_admin_setting(text, text) TO authenticated;

-- Grant permissions for token functions
GRANT EXECUTE ON FUNCTION public.validate_application_token(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_token_as_used(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.generate_application_token() TO authenticated;

-- =============================================================================
-- FIX 5: Add comments for documentation
-- =============================================================================

COMMENT ON TABLE public.admin_settings IS 'Administrative settings for Ocean Soul Sparkles system';
COMMENT ON FUNCTION public.get_admin_setting(text) IS 'Safely retrieve an admin setting value';
COMMENT ON FUNCTION public.set_admin_setting(text, text) IS 'Safely set an admin setting value';
COMMENT ON FUNCTION public.validate_application_token(text) IS 'Validate an application token and return validation details';
COMMENT ON FUNCTION public.mark_token_as_used(text) IS 'Mark an application token as used';
COMMENT ON FUNCTION public.generate_application_token() IS 'Generate a secure unique application token';

-- =============================================================================
-- VERIFICATION QUERIES (run these to verify the fixes worked)
-- =============================================================================

-- Test 1: Check if admin_settings table exists and has data
-- SELECT COUNT(*) as admin_settings_count FROM public.admin_settings;

-- Test 2: Test token generation
-- SELECT public.generate_application_token() as test_token;

-- Test 3: Test token validation with invalid token
-- SELECT * FROM public.validate_application_token('invalid-token-test');

-- Test 4: Check if application tables exist
-- SELECT COUNT(*) as applications_count FROM public.artist_braider_applications;
-- SELECT COUNT(*) as tokens_count FROM public.application_tokens;

-- =============================================================================
-- SUCCESS MESSAGE
-- =============================================================================

DO $$
BEGIN
  RAISE NOTICE '✅ CRITICAL PRODUCTION FIX COMPLETED SUCCESSFULLY!';
  RAISE NOTICE '📧 Email service issues should now be resolved';
  RAISE NOTICE '🔐 Token validation API should now work correctly';
  RAISE NOTICE '🗄️ Database schema issues have been fixed';
  RAISE NOTICE '🎯 Artist/Braider onboarding system is ready for testing';
END $$;
