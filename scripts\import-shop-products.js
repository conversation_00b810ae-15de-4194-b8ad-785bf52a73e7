/**
 * Import hardcoded shop products into the database
 * This script imports the product data from the shop page into the Supabase database
 * to establish the correct data flow: admin edits → public shop page
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Hardcoded product data from shop.js
const shopProducts = [
  {
    id: 'split-cake-cosmic',
    name: 'Split Cake - Cosmic Tide',
    description: 'Highly pigmented water-activated split cake palette with cosmic-inspired colors. Perfect for face and body art with seamless blending and bold coverage.',
    price: 40.00,
    image: '/images/products/splitcake-cosmic-pak.jpg',
    additionalImage: '/images/products/splitcake-cosmic-prod.jpg',
    category: 'split-cakes',
    features: [
      'Smudge-proof & Transfer-resistant',
      'Strong Pigment Shades',
      'Blendable Formula',
      'Skin Safe',
      'Cruelty-Free & Vegan-Friendly'
    ],
    details: [
      'Product Volume: 60g split cake',
      'Water-Based Formula',
      'Not waterproof'
    ],
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-ocean',
    name: 'Split Cake - Ocean Sounds',
    description: 'Water-activated split cake palette with ocean-inspired blues and teals. Creates stunning ocean-themed designs with highly pigmented colors that blend seamlessly.',
    price: 40.00,
    image: '/images/products/splitcake-ocean-pak.jpg',
    additionalImage: '/images/products/splitcake-ocean-prod.jpg',
    category: 'split-cakes',
    features: [
      'Smudge-proof & Transfer-resistant',
      'Strong Pigment Shades',
      'Blendable Formula',
      'Skin Safe',
      'Cruelty-Free & Vegan-Friendly'
    ],
    details: [
      'Product Volume: 60g split cake',
      'Water-Based Formula',
      'Not waterproof'
    ],
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-neutral',
    name: 'Split Cake - Neutral Colour',
    description: 'Water-activated split cake palette with neutral earth tones. Perfect for creating natural looks, animal designs, and subtle face painting with highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-neutral-pak.jpg',
    additionalImage: '/images/products/splitcake-neutral-prod.jpg',
    category: 'split-cakes',
    features: [
      'Smudge-proof & Transfer-resistant',
      'Strong Pigment Shades',
      'Blendable Formula',
      'Skin Safe',
      'Cruelty-Free & Vegan-Friendly'
    ],
    details: [
      'Product Volume: 60g split cake',
      'Water-Based Formula',
      'Not waterproof'
    ],
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-fire',
    name: 'Split Cake - Flaming Tiger',
    description: 'Water-activated split cake palette with fiery reds, oranges and yellows. Create dramatic flame effects and bold designs with these highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-flaimingtiger-pak.jpg',
    additionalImage: '/images/products/splitcake-flaimingtiger-prod.jpg',
    category: 'split-cakes',
    features: [
      'Smudge-proof & Transfer-resistant',
      'Strong Pigment Shades',
      'Blendable Formula',
      'Skin Safe',
      'Cruelty-Free & Vegan-Friendly'
    ],
    details: [
      'Product Volume: 60g split cake',
      'Water-Based Formula',
      'Not waterproof'
    ],
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-pearl',
    name: 'Split Cake - Pearlescent Rose',
    description: 'Water-activated split cake palette with pearlescent rose tones. Creates elegant, shimmering designs with a beautiful pearlescent finish and highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-pearl-pak.jpg',
    additionalImage: '/images/products/splitcake-pearl-prod.jpg',
    category: 'split-cakes',
    features: [
      'Smudge-proof & Transfer-resistant',
      'Strong Pigment Shades',
      'Blendable Formula',
      'Skin Safe',
      'Cruelty-Free & Vegan-Friendly'
    ],
    details: [
      'Product Volume: 60g split cake',
      'Water-Based Formula',
      'Not waterproof'
    ],
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-tropical',
    name: 'Split Cake - Tropical Paradise',
    description: 'Water-activated split cake palette with vibrant tropical colors. Perfect for creating exotic flowers, tropical designs, and festival looks with highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-tropical-pak.jpg',
    additionalImage: '/images/products/splitcake-tropical-prod.jpg',
    category: 'split-cakes',
    features: [
      'Smudge-proof & Transfer-resistant',
      'Strong Pigment Shades',
      'Blendable Formula',
      'Skin Safe',
      'Cruelty-Free & Vegan-Friendly'
    ],
    details: [
      'Product Volume: 60g split cake',
      'Water-Based Formula',
      'Not waterproof'
    ],
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-galaxy',
    name: 'Split Cake - Galaxy Dreams',
    description: 'Water-activated split cake palette with cosmic galaxy colors. Create stunning space-themed designs with these highly pigmented colors that blend seamlessly.',
    price: 40.00,
    image: '/images/products/splitcake-galaxy-pak.jpg',
    additionalImage: '/images/products/splitcake-galaxy-prod.jpg',
    category: 'split-cakes',
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-aurora',
    name: 'Split Cake - Aurora Borealis',
    description: 'Water-activated split cake palette with aurora borealis-inspired colors. Create magical northern lights effects with these highly pigmented colors that blend beautifully.',
    price: 40.00,
    image: '/images/products/splitcake-aurora-pak.jpg',
    additionalImage: '/images/products/splitcake-aurora-prod.jpg',
    category: 'split-cakes',
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-forest',
    name: 'Split Cake - Forest Fairy',
    description: 'Water-activated split cake palette with forest-inspired greens. Perfect for creating woodland creatures, fairy designs, and nature-themed art with highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-forest-pak.jpg',
    additionalImage: '/images/products/splitcake-forest-prod.jpg',
    category: 'split-cakes',
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-rainbow-uv',
    name: 'Split Cake - Rainbow UV GLOW',
    description: 'Water-activated split cake palette with UV-reactive rainbow colors. Create vibrant designs that glow under blacklight with these highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-rainbowuv-pak.jpg',
    additionalImage: '/images/products/splitcake-rainbowuv-prod.jpg',
    category: 'uv-products',
    badge: 'New Arrival'
  },
  {
    id: 'split-cake-pastel-uv',
    name: 'Split Cake - Pastel UV GLOW',
    description: 'Water-activated split cake palette with UV-reactive pastel colors. Create soft, ethereal designs that glow under blacklight with these highly pigmented colors.',
    price: 40.00,
    image: '/images/products/splitcake-pasteluv-pak.jpg',
    additionalImage: '/images/products/splitcake-pasteluv-prod.jpg',
    category: 'uv-products',
    badge: 'New Arrival'
  },
  {
    id: 'uv-liner-palette',
    name: 'UV LINER Water Activated Pallette',
    description: 'Water-activated UV liner palette with 6 neon blacklight/UV glow shades and 2 classic shades. Perfect for creating bold eyeliner looks, intricate body art, and creative makeup designs.',
    price: 55.00,
    image: '/images/products/splitcake-uvliner-water activated-pak.jpg',
    additionalImage: '',
    category: 'uv-products',
    badge: 'New Arrival'
  }
];

async function importProducts() {
  console.log('🔄 Starting product import...');
  
  try {
    for (const product of shopProducts) {
      console.log(`📦 Processing product: ${product.name}`);
      
      // Check if product already exists
      const { data: existingProduct } = await supabaseAdmin
        .from('products')
        .select('id, name')
        .eq('name', product.name)
        .single();
      
      // Prepare product data for database
      const productData = {
        name: product.name,
        description: product.description,
        price: product.price,
        category: product.category,
        image_url: product.image,
        gallery_images: product.additionalImage ? [product.additionalImage] : [],
        status: 'active',
        is_active: true,
        featured: product.badge === 'New Arrival',
        stock: 40, // Default stock
        low_stock_threshold: 5,
        meta_title: product.name,
        meta_description: product.description.substring(0, 160)
      };
      
      if (existingProduct) {
        // Update existing product
        const { error } = await supabaseAdmin
          .from('products')
          .update(productData)
          .eq('id', existingProduct.id);
        
        if (error) {
          console.error(`❌ Error updating ${product.name}:`, error);
        } else {
          console.log(`✅ Updated: ${product.name}`);
        }
      } else {
        // Create new product
        const { error } = await supabaseAdmin
          .from('products')
          .insert([productData]);
        
        if (error) {
          console.error(`❌ Error creating ${product.name}:`, error);
        } else {
          console.log(`✅ Created: ${product.name}`);
        }
      }
    }
    
    console.log('🎉 Product import completed!');
  } catch (error) {
    console.error('💥 Import failed:', error);
  }
}

// Run the import
if (require.main === module) {
  importProducts();
}

module.exports = { importProducts, shopProducts };
