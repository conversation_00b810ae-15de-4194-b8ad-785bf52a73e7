-- Enhanced User Management System Migration
-- This migration adds comprehensive user management features including:
-- - 5-role system (DEV, Admin, Artist, Braider, User)
-- - Extended user profiles
-- - Activity tracking
-- - Artist/Braider onboarding workflow
-- - Service assignments

-- First, update the existing user_roles table to support new roles
ALTER TABLE public.user_roles 
DROP CONSTRAINT IF EXISTS user_roles_role_check;

ALTER TABLE public.user_roles 
ADD CONSTRAINT user_roles_role_check 
CHECK (role IN ('dev', 'admin', 'artist', 'braider', 'user'));

-- Update default role
ALTER TABLE public.user_roles 
ALTER COLUMN role SET DEFAULT 'user';

-- Create extended user profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL PRIMARY KEY,
  name TEXT,
  phone TEXT,
  subscription_status TEXT CHECK (subscription_status IN ('successful', 'failed', 'pending')) DEFAULT 'pending',
  last_login_at TIMESTAMPTZ,
  login_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user activity tracking table
CREATE TABLE IF NOT EXISTS public.user_activity_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_description TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create artist/braider application system
CREATE TABLE IF NOT EXISTS public.artist_braider_applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  application_type TEXT CHECK (application_type IN ('artist', 'braider')) NOT NULL,
  experience_years INTEGER,
  portfolio_url TEXT,
  availability_preferences JSONB,
  service_specializations TEXT[],
  previous_experience TEXT,
  references TEXT,
  status TEXT CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')) DEFAULT 'pending',
  reviewed_by UUID REFERENCES auth.users,
  reviewed_at TIMESTAMPTZ,
  review_notes TEXT,
  welcome_email_sent BOOLEAN DEFAULT FALSE,
  welcome_email_sent_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user service assignments table
CREATE TABLE IF NOT EXISTS public.user_service_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  service_id UUID REFERENCES public.services ON DELETE CASCADE,
  is_primary_provider BOOLEAN DEFAULT FALSE,
  skill_level TEXT CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')) DEFAULT 'intermediate',
  hourly_rate DECIMAL(10, 2),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, service_id)
);

-- Enable RLS on new tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_braider_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_service_assignments ENABLE ROW LEVEL SECURITY;

-- Update the get_user_role function to handle new roles
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  role_name TEXT;
BEGIN
  SELECT role INTO role_name FROM public.user_roles WHERE id = $1;
  RETURN COALESCE(role_name, 'user');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user has admin privileges
CREATE OR REPLACE FUNCTION public.is_admin_or_dev(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) IN ('dev', 'admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user has staff privileges
CREATE OR REPLACE FUNCTION public.is_staff_or_above(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) IN ('dev', 'admin', 'artist', 'braider');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing policies that need to be updated
DROP POLICY IF EXISTS "Admins can view all roles" ON public.user_roles;
DROP POLICY IF EXISTS "Admins can update roles" ON public.user_roles;

-- Create new policies for user_roles
CREATE POLICY "Admins and devs can view all roles" ON public.user_roles
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can update roles" ON public.user_roles
  FOR UPDATE USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can insert roles" ON public.user_roles
  FOR INSERT WITH CHECK (is_admin_or_dev(auth.uid()));

-- Create policies for user_profiles
CREATE POLICY "Users can view their own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins and devs can view all profiles" ON public.user_profiles
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Users can update their own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins and devs can update all profiles" ON public.user_profiles
  FOR UPDATE USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can insert profiles" ON public.user_profiles
  FOR INSERT WITH CHECK (is_admin_or_dev(auth.uid()));

-- Create policies for user_activity_log
CREATE POLICY "Users can view their own activity" ON public.user_activity_log
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and devs can view all activity" ON public.user_activity_log
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "System can insert activity logs" ON public.user_activity_log
  FOR INSERT WITH CHECK (true);

-- Create policies for artist_braider_applications
CREATE POLICY "Users can view their own applications" ON public.artist_braider_applications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and devs can view all applications" ON public.artist_braider_applications
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Users can create their own applications" ON public.artist_braider_applications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins and devs can update applications" ON public.artist_braider_applications
  FOR UPDATE USING (is_admin_or_dev(auth.uid()));

-- Create policies for user_service_assignments
CREATE POLICY "Users can view their own service assignments" ON public.user_service_assignments
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Staff can view all service assignments" ON public.user_service_assignments
  FOR SELECT USING (is_staff_or_above(auth.uid()));

CREATE POLICY "Admins and devs can manage service assignments" ON public.user_service_assignments
  FOR ALL USING (is_admin_or_dev(auth.uid()));

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_name ON public.user_profiles(name);
CREATE INDEX IF NOT EXISTS idx_user_profiles_phone ON public.user_profiles(phone);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON public.user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON public.user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_artist_braider_applications_user_id ON public.artist_braider_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_artist_braider_applications_status ON public.artist_braider_applications(status);
CREATE INDEX IF NOT EXISTS idx_user_service_assignments_user_id ON public.user_service_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_service_assignments_service_id ON public.user_service_assignments(service_id);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON public.user_profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_artist_braider_applications_updated_at
  BEFORE UPDATE ON public.artist_braider_applications
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_service_assignments_updated_at
  BEFORE UPDATE ON public.user_service_assignments
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default user profiles for existing users
INSERT INTO public.user_profiles (id, name, created_at, updated_at)
SELECT 
  au.id,
  COALESCE(au.raw_user_meta_data->>'name', au.email) as name,
  au.created_at,
  NOW()
FROM auth.users au
WHERE NOT EXISTS (
  SELECT 1 FROM public.user_profiles up WHERE up.id = au.id
);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.user_profiles TO authenticated;
GRANT SELECT, INSERT ON public.user_activity_log TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.artist_braider_applications TO authenticated;
GRANT SELECT ON public.user_service_assignments TO authenticated;
