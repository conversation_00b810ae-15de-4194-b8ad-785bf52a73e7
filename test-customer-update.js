/**
 * Test script to verify customer update API is working
 * This script tests the fixed authentication and customer update functionality
 */

const customerId = 'ada32544-90c7-4386-83fe-7bd16103b645';
const apiUrl = 'http://localhost:3000';

// Test data for customer update
const testUpdateData = {
  name: '<PERSON> (Updated)',
  email: '<EMAIL>',
  phone: '+61 400 123 456',
  city: 'Sydney',
  state: 'NSW',
  notes: 'Test update from API fix verification'
};

async function testCustomerUpdate() {
  console.log('🧪 Testing Customer Update API Fix...\n');

  try {
    // First, let's test the GET endpoint to make sure the customer exists
    console.log('1. Testing GET customer endpoint...');
    const getResponse = await fetch(`${apiUrl}/api/customers/${customerId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // This would be a real token in production
      }
    });

    console.log(`   GET Status: ${getResponse.status}`);

    if (getResponse.ok) {
      const customerData = await getResponse.json();
      console.log(`   ✅ Customer found: ${customerData.customer?.name}`);
      console.log(`   📧 Email: ${customerData.customer?.email}`);
    } else {
      const errorData = await getResponse.json();
      console.log(`   ❌ GET failed: ${errorData.error}`);
      return;
    }

    console.log('\n2. Testing PUT customer endpoint...');

    // Now test the PUT endpoint (the one that was failing)
    const putResponse = await fetch(`${apiUrl}/api/customers/${customerId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // This would be a real token in production
      },
      body: JSON.stringify(testUpdateData)
    });

    console.log(`   PUT Status: ${putResponse.status}`);

    if (putResponse.ok) {
      const updatedCustomer = await putResponse.json();
      console.log(`   ✅ Customer updated successfully!`);
      console.log(`   📝 Updated name: ${updatedCustomer.name}`);
      console.log(`   📧 Updated email: ${updatedCustomer.email}`);
      console.log(`   📱 Updated phone: ${updatedCustomer.phone}`);
      console.log(`   📍 Updated city: ${updatedCustomer.city}`);
      console.log(`   📋 Updated notes: ${updatedCustomer.notes}`);

      if (updatedCustomer.requestId) {
        console.log(`   🔍 Request ID: ${updatedCustomer.requestId}`);
      }
    } else {
      const errorData = await putResponse.json();
      console.log(`   ❌ PUT failed: ${errorData.error}`);
      console.log(`   📝 Error message: ${errorData.message || 'No additional message'}`);

      if (errorData.requestId) {
        console.log(`   🔍 Request ID: ${errorData.requestId}`);
      }
    }

    console.log('\n3. Testing Admin Customer endpoint...');

    // Test the new admin endpoint as well
    const adminResponse = await fetch(`${apiUrl}/api/admin/customers/${customerId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }
    });

    console.log(`   Admin GET Status: ${adminResponse.status}`);

    if (adminResponse.ok) {
      const adminCustomerData = await adminResponse.json();
      console.log(`   ✅ Admin endpoint working: ${adminCustomerData.customer?.name}`);
      console.log(`   📊 Health Score: ${adminCustomerData.customer?.customer_health_score || 'N/A'}`);
      console.log(`   🏷️ Tags: ${adminCustomerData.customer?.tags?.length || 0} tags`);
    } else {
      const errorData = await adminResponse.json();
      console.log(`   ❌ Admin GET failed: ${errorData.error}`);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }

  console.log('\n🏁 Test completed!');
}

// Run the test
testCustomerUpdate();

console.log(`
📋 Test Summary:
- Customer ID: ${customerId}
- API Base URL: ${apiUrl}
- Testing endpoints:
  * GET /api/customers/${customerId}
  * PUT /api/customers/${customerId}
  * GET /api/admin/customers/${customerId}

🔧 What was fixed:
1. Updated authentication from getCurrentUserFromRequest to authTokenManager.verifyToken()
2. Changed from supabase client to supabaseAdmin client
3. Added proper error handling and request tracking
4. Created new admin customer endpoint with enhanced features

🎯 Expected results:
- All endpoints should return 200 status codes
- Customer data should be retrieved and updated successfully
- Enhanced admin endpoint should show additional customer analytics
`);
