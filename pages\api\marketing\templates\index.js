import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getTemplates(req, res)
    case 'POST':
      return createTemplate(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get marketing templates with optional filters
async function getTemplates(req, res) {
  const {
    search,
    template_type,
    category,
    is_active,
    sort_by = 'created_at',
    sort_order = 'desc',
    limit = 50,
    offset = 0
  } = req.query

  try {
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    let query = client
      .from('marketing_templates')
      .select('*', { count: 'exact' })

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,subject.ilike.%${search}%`)
    }

    // Apply template type filter
    if (template_type) {
      query = query.eq('template_type', template_type)
    }

    // Apply category filter
    if (category) {
      query = query.eq('category', category)
    }

    // Apply active status filter
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true')
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    if (limit) {
      query = query.limit(limit)
    }
    if (offset) {
      query = query.offset(offset)
    }

    // Execute query
    const { data, error, count } = await query

    if (error) {
      throw error
    }

    return res.status(200).json({
      templates: data || [],
      total: count || 0
    })
  } catch (error) {
    console.error('Error fetching templates:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Create a new marketing template
async function createTemplate(req, res) {
  const {
    name,
    description,
    subject,
    content,
    template_type,
    category,
    is_active = true
  } = req.body

  try {
    // Get current user
    const { user } = await getCurrentUser(req)

    // Validate required fields
    if (!name || !content || !template_type) {
      return res.status(400).json({ error: 'Name, content, and template type are required' })
    }

    // Validate template type
    const validTemplateTypes = ['email', 'sms', 'push']
    if (!validTemplateTypes.includes(template_type)) {
      return res.status(400).json({ error: 'Invalid template type. Must be one of: email, sms, push' })
    }

    // Email templates require a subject
    if (template_type === 'email' && !subject) {
      return res.status(400).json({ error: 'Subject is required for email templates' })
    }

    // Create template
    const { data, error } = await supabase
      .from('marketing_templates')
      .insert([
        {
          name,
          description,
          subject,
          content,
          template_type,
          category,
          is_active,
          created_by: user.id
        }
      ])
      .select()

    if (error) {
      throw error
    }

    return res.status(201).json(data[0])
  } catch (error) {
    console.error('Error creating template:', error)
    return res.status(500).json({ error: error.message })
  }
}
