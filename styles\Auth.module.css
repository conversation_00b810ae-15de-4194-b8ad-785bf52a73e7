/* Authentication pages styles */
.main {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 500px;
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  text-align: center;
  padding: 2rem 2rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header h1 {
  margin: 0 0 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.formWrapper {
  padding: 2rem;
}

.authLinks {
  padding: 1rem 2rem 2rem;
  text-align: center;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.authLinks p {
  margin: 0.5rem 0;
  color: #6b7280;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.link:hover {
  color: #5a67d8;
  text-decoration: underline;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  font-size: 1.2rem;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 640px) {
  .main {
    padding: 1rem;
    min-height: 70vh;
  }
  
  .container {
    margin: 0;
  }
  
  .header {
    padding: 1.5rem 1rem 1rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .formWrapper {
    padding: 1.5rem;
  }
  
  .authLinks {
    padding: 1rem 1.5rem 1.5rem;
  }
}
