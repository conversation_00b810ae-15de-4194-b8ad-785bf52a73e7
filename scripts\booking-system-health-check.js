/**
 * Booking System Health Check Script
 * 
 * This script performs a comprehensive health check of the booking system
 * to verify all components are working correctly.
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY
};

// Test results tracking
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  errors: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message, isWarning = false) {
  if (condition) {
    results.passed++;
    log(`PASS: ${message}`, 'success');
  } else {
    if (isWarning) {
      results.warnings++;
      log(`WARNING: ${message}`, 'warning');
    } else {
      results.failed++;
      results.errors.push(message);
      log(`FAIL: ${message}`, 'error');
    }
  }
}

// Simple fetch implementation
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const lib = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    if (options.body) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(options.body);
    }

    const req = lib.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            json: () => Promise.resolve(jsonData),
            text: () => Promise.resolve(data)
          });
        } catch (parseError) {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            json: () => Promise.reject(parseError),
            text: () => Promise.resolve(data)
          });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function checkEnvironmentVariables() {
  log('Checking environment variables...');
  
  assert(config.supabaseUrl, 'NEXT_PUBLIC_SUPABASE_URL is configured');
  assert(config.supabaseAnonKey, 'NEXT_PUBLIC_SUPABASE_ANON_KEY is configured');
  assert(config.supabaseServiceKey, 'SUPABASE_SERVICE_ROLE_KEY is configured');
  
  if (config.supabaseUrl) {
    assert(config.supabaseUrl.includes('supabase.co'), 'Supabase URL format is valid');
  }
}

async function checkDatabaseConnectivity() {
  log('Checking database connectivity...');
  
  try {
    // Test Supabase REST API directly
    const response = await fetch(`${config.supabaseUrl}/rest/v1/services?select=count`, {
      headers: {
        'apikey': config.supabaseAnonKey,
        'Authorization': `Bearer ${config.supabaseAnonKey}`
      }
    });
    
    assert(response.ok, 'Database connection successful');
    
    if (response.ok) {
      const data = await response.json();
      assert(Array.isArray(data), 'Database returns valid data structure');
    }
  } catch (error) {
    assert(false, `Database connectivity failed: ${error.message}`);
  }
}

async function checkAPIEndpoints() {
  log('Checking API endpoints...');
  
  // Test health endpoint
  try {
    const healthResponse = await fetch(`${config.baseUrl}/api/health`);
    assert(healthResponse.ok, 'Health endpoint is accessible', true);
  } catch (error) {
    assert(false, `Health endpoint failed: ${error.message}`, true);
  }
  
  // Test booking validation with invalid data
  try {
    const invalidBookingResponse = await fetch(`${config.baseUrl}/api/public/bookings`, {
      method: 'POST',
      body: JSON.stringify({
        name: 'Test'
        // Missing required fields
      })
    });
    
    assert(invalidBookingResponse.status === 400, 'Booking API validates required fields');
  } catch (error) {
    assert(false, `Booking validation test failed: ${error.message}`, true);
  }
}

async function checkTableStructure() {
  log('Checking database table structure...');
  
  const tables = ['bookings', 'customers', 'services'];
  
  for (const table of tables) {
    try {
      const response = await fetch(`${config.supabaseUrl}/rest/v1/${table}?select=*&limit=1`, {
        headers: {
          'apikey': config.supabaseAnonKey,
          'Authorization': `Bearer ${config.supabaseAnonKey}`
        }
      });
      
      assert(response.ok, `${table} table is accessible`);
      
      if (response.ok) {
        const data = await response.json();
        assert(Array.isArray(data), `${table} table returns valid data structure`);
      }
    } catch (error) {
      assert(false, `${table} table check failed: ${error.message}`);
    }
  }
}

async function checkDataIntegrity() {
  log('Checking data integrity...');
  
  try {
    // Check if there are services available
    const servicesResponse = await fetch(`${config.supabaseUrl}/rest/v1/services?select=id,name,duration,price`, {
      headers: {
        'apikey': config.supabaseAnonKey,
        'Authorization': `Bearer ${config.supabaseAnonKey}`
      }
    });
    
    if (servicesResponse.ok) {
      const services = await servicesResponse.json();
      assert(services.length > 0, 'Services are available for booking');
      
      if (services.length > 0) {
        const service = services[0];
        assert(service.name && service.duration && service.price, 'Service data is complete');
      }
    }
    
    // Check booking-customer relationships
    const bookingsResponse = await fetch(`${config.supabaseUrl}/rest/v1/bookings?select=id,customer_id,service_id&limit=5`, {
      headers: {
        'apikey': config.supabaseAnonKey,
        'Authorization': `Bearer ${config.supabaseAnonKey}`
      }
    });
    
    if (bookingsResponse.ok) {
      const bookings = await bookingsResponse.json();
      if (bookings.length > 0) {
        const booking = bookings[0];
        assert(booking.customer_id && booking.service_id, 'Booking relationships are properly maintained');
      }
    }
  } catch (error) {
    assert(false, `Data integrity check failed: ${error.message}`, true);
  }
}

async function checkValidationLibrary() {
  log('Checking validation library...');
  
  try {
    // This would require the validation library to be available
    // For now, we'll just check if the API properly validates data
    const testData = {
      name: '', // Invalid: empty name
      email: 'invalid-email', // Invalid: bad email format
      phone: '+61400000000',
      date: '2024-12-25',
      time: '10:00',
      location: 'Test Location',
      service: { name: 'Test Service' },
      option: { hours: 2, price: 100 }
    };
    
    const response = await fetch(`${config.baseUrl}/api/public/bookings`, {
      method: 'POST',
      body: JSON.stringify(testData)
    });
    
    assert(response.status === 400, 'API properly validates invalid data');
    
    if (response.status === 400) {
      const errorData = await response.json();
      assert(errorData.errors || errorData.error, 'API returns proper error messages');
    }
  } catch (error) {
    assert(false, `Validation library check failed: ${error.message}`, true);
  }
}

async function runHealthCheck() {
  log('Starting booking system health check...');
  log(`Base URL: ${config.baseUrl}`);
  log(`Supabase URL: ${config.supabaseUrl}`);
  
  try {
    await checkEnvironmentVariables();
    await checkDatabaseConnectivity();
    await checkTableStructure();
    await checkDataIntegrity();
    await checkAPIEndpoints();
    await checkValidationLibrary();
    
  } catch (error) {
    assert(false, `Health check failed: ${error.message}`);
  }
  
  // Report results
  log('\n=== HEALTH CHECK RESULTS ===');
  log(`✅ Passed: ${results.passed}`);
  log(`❌ Failed: ${results.failed}`);
  log(`⚠️ Warnings: ${results.warnings}`);
  
  if (results.failed > 0) {
    log('\n❌ Failed checks:');
    results.errors.forEach(error => log(`- ${error}`, 'error'));
    
    log('\n🔧 RECOMMENDED ACTIONS:');
    log('1. Check environment variables are properly configured');
    log('2. Verify Supabase database is accessible');
    log('3. Ensure all required tables exist with proper structure');
    log('4. Test API endpoints manually if needed');
    
    process.exit(1);
  } else {
    log('\n🎉 All critical checks passed!', 'success');
    
    if (results.warnings > 0) {
      log('\n⚠️ Some warnings were found, but the system should function correctly.');
    }
    
    log('\n✅ BOOKING SYSTEM STATUS: HEALTHY');
    process.exit(0);
  }
}

// Run health check if called directly
if (require.main === module) {
  runHealthCheck().catch(error => {
    log(`Health check runner failed: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runHealthCheck, results };
