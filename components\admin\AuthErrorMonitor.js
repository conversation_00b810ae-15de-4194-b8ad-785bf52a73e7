/**
 * Authentication Error Monitor Component
 * 
 * This component captures and logs authentication-related errors
 * to help debug session expiration and token issues.
 * It provides real-time monitoring of authentication failures.
 */

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

const AuthErrorMonitor = () => {
  const { user, error, loading } = useAuth();
  const [authErrors, setAuthErrors] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  // Capture console errors related to authentication
  useEffect(() => {
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    // Override console.error to capture auth-related errors
    console.error = (...args) => {
      originalConsoleError.apply(console, args);
      
      const errorMessage = args.join(' ');
      const isAuthError = 
        errorMessage.includes('401') ||
        errorMessage.includes('Unauthorized') ||
        errorMessage.includes('Authentication') ||
        errorMessage.includes('Token') ||
        errorMessage.includes('JWT') ||
        errorMessage.includes('session expired') ||
        errorMessage.includes('GoTrueClient');

      if (isAuthError) {
        const errorEntry = {
          timestamp: new Date().toISOString(),
          type: 'error',
          message: errorMessage,
          stack: args.find(arg => arg?.stack)?.stack || null
        };

        setAuthErrors(prev => [...prev.slice(-9), errorEntry]); // Keep last 10 errors
        
        // Send to server for logging
        fetch('/api/admin/diagnostics/client-error', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'auth_error',
            message: errorMessage,
            timestamp: errorEntry.timestamp,
            userAgent: navigator.userAgent,
            url: window.location.href
          })
        }).catch(err => originalConsoleError('Failed to log auth error to server:', err));
      }
    };

    // Override console.warn to capture auth-related warnings
    console.warn = (...args) => {
      originalConsoleWarn.apply(console, args);
      
      const warnMessage = args.join(' ');
      const isAuthWarning = 
        warnMessage.includes('Multiple GoTrueClient') ||
        warnMessage.includes('Token') ||
        warnMessage.includes('Authentication') ||
        warnMessage.includes('Session');

      if (isAuthWarning) {
        const warnEntry = {
          timestamp: new Date().toISOString(),
          type: 'warning',
          message: warnMessage
        };

        setAuthErrors(prev => [...prev.slice(-9), warnEntry]);
      }
    };

    // Capture unhandled promise rejections
    const handleUnhandledRejection = (event) => {
      const errorMessage = event.reason?.message || event.reason || 'Unknown error';
      const isAuthError = 
        errorMessage.includes('401') ||
        errorMessage.includes('Unauthorized') ||
        errorMessage.includes('Authentication') ||
        errorMessage.includes('Token');

      if (isAuthError) {
        const errorEntry = {
          timestamp: new Date().toISOString(),
          type: 'unhandled_rejection',
          message: `Unhandled Promise Rejection: ${errorMessage}`
        };

        setAuthErrors(prev => [...prev.slice(-9), errorEntry]);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup
    return () => {
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // Monitor auth context errors
  useEffect(() => {
    if (error) {
      const errorEntry = {
        timestamp: new Date().toISOString(),
        type: 'auth_context_error',
        message: error.message || error.toString()
      };

      setAuthErrors(prev => [...prev.slice(-9), errorEntry]);
    }
  }, [error]);

  // Auto-show monitor when errors are detected
  useEffect(() => {
    if (authErrors.length > 0) {
      setIsVisible(true);
    }
  }, [authErrors]);

  // Don't render anything if no errors and not visible
  if (!isVisible && authErrors.length === 0) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '400px',
      maxHeight: '300px',
      backgroundColor: '#1a1a1a',
      color: '#fff',
      border: '1px solid #ff4444',
      borderRadius: '4px',
      padding: '10px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 10000,
      overflow: 'auto',
      boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '10px',
        borderBottom: '1px solid #444',
        paddingBottom: '5px'
      }}>
        <strong style={{ color: '#ff4444' }}>🚨 Auth Error Monitor</strong>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ×
        </button>
      </div>

      <div style={{ marginBottom: '10px', fontSize: '11px', color: '#ccc' }}>
        User: {user ? user.email : 'Not authenticated'} | 
        Loading: {loading ? 'Yes' : 'No'} | 
        Errors: {authErrors.length}
      </div>

      {authErrors.length === 0 ? (
        <div style={{ color: '#4CAF50' }}>✅ No authentication errors detected</div>
      ) : (
        <div>
          {authErrors.map((error, index) => (
            <div
              key={index}
              style={{
                marginBottom: '8px',
                padding: '5px',
                backgroundColor: error.type === 'error' ? '#2d1b1b' : '#2d2d1b',
                border: `1px solid ${error.type === 'error' ? '#ff4444' : '#ffaa44'}`,
                borderRadius: '2px'
              }}
            >
              <div style={{ 
                fontSize: '10px', 
                color: '#888',
                marginBottom: '2px'
              }}>
                {new Date(error.timestamp).toLocaleTimeString()} - {error.type}
              </div>
              <div style={{ 
                wordBreak: 'break-word',
                color: error.type === 'error' ? '#ff6666' : '#ffcc66'
              }}>
                {error.message}
              </div>
            </div>
          ))}
        </div>
      )}

      <div style={{
        marginTop: '10px',
        paddingTop: '5px',
        borderTop: '1px solid #444',
        fontSize: '10px',
        color: '#888'
      }}>
        <button
          onClick={() => setAuthErrors([])}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '2px 6px',
            borderRadius: '2px',
            cursor: 'pointer',
            fontSize: '10px',
            marginRight: '5px'
          }}
        >
          Clear
        </button>
        <button
          onClick={() => {
            const report = {
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent,
              url: window.location.href,
              user: user ? { id: user.id, email: user.email } : null,
              errors: authErrors
            };
            console.log('Auth Error Report:', JSON.stringify(report, null, 2));
          }}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '2px 6px',
            borderRadius: '2px',
            cursor: 'pointer',
            fontSize: '10px'
          }}
        >
          Export Report
        </button>
      </div>
    </div>
  );
};

export default AuthErrorMonitor;
