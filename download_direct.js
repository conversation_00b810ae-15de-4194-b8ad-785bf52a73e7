const https = require('https');
const fs = require('fs');
const path = require('path');

// Function to download an image
const downloadImage = (url, filename) => {
  return new Promise((resolve, reject) => {
    // Create directory if it doesn't exist
    const dir = path.dirname(filename);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    const file = fs.createWriteStream(filename);
    
    https.get(url, response => {
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${filename}`);
        resolve();
      });
      
      file.on('error', err => {
        fs.unlink(filename, () => {});
        console.error(`Error downloading ${url}: ${err.message}`);
        reject(err);
      });
    }).on('error', err => {
      fs.unlink(filename, () => {});
      console.error(`Error downloading ${url}: ${err.message}`);
      reject(err);
    });
  });
};

// Main function
const main = async () => {
  try {
    // List of images to download
    const images = [
      {
        url: 'https://static.wixstatic.com/media/debcf9_cc1f0559f1da41b0a03466a8aae96df4~mv2.jpg',
        filename: 'public/images/hero-image.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/3a85ea796b814e3dbf5cfe3c7b494c46.jpg',
        filename: 'public/images/hero-background.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_4db0e235f56943e6942da2eb1ed6127b~mv2.jpeg',
        filename: 'public/images/services/airbrush-painting.jpeg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_3372857712bc43fdb43fb97da6b71d65~mv2.jpg',
        filename: 'public/images/services/face-paint.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_bca56433c8484b318b12f074efc92bc9~mv2.jpg',
        filename: 'public/images/services/festival-braids.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_de3ab13843be484dbdf3668a3e92297e~mv2.jpg',
        filename: 'public/images/products/biodegradable-glitter.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_d2d9bde88b414c778ab89f2e8de46ff8~mv2.jpg',
        filename: 'public/images/gallery/gallery-1.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_eb7bc4fd3e6b4f569078a8dd071e7de8~mv2.jpg',
        filename: 'public/images/gallery/gallery-2.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_d43fcde06742418e8fbfa11391c62812~mv2.jpg',
        filename: 'public/images/gallery/gallery-3.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_34d65a5a20b84faaa064b1d55a866ae8~mv2.jpg',
        filename: 'public/images/gallery/gallery-4.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_345a8834dbb44a7e9f31d1c982a7d207~mv2.jpg',
        filename: 'public/images/gallery/gallery-5.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_5e4e73f72f0d4134b9433ad12dfbf786~mv2.jpg',
        filename: 'public/images/gallery/gallery-6.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/debcf9_d0e2b66bbfac478580850120ea7d7be7~mv2.jpg',
        filename: 'public/images/gallery/gallery-7.jpg'
      },
      {
        url: 'https://static.wixstatic.com/media/11062b_603340b7bcb14e7785c7b65b233cd9f9~mv2.png',
        filename: 'public/images/social/instagram-icon.png'
      },
      {
        url: 'https://static.wixstatic.com/media/11062b_ef6a6ac194704911951645990055c2ce~mv2.png',
        filename: 'public/images/social/facebook-icon.png'
      }
    ];
    
    // Download all images
    console.log('Starting to download images...');
    for (const image of images) {
      await downloadImage(image.url, image.filename);
    }
    
    console.log('All images downloaded successfully!');
  } catch (error) {
    console.error('Error:', error.message);
  }
};

// Run the main function
main();
