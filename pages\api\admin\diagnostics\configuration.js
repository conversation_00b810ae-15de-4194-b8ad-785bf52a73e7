/**
 * Configuration Diagnostics API
 * 
 * This endpoint checks the configuration status for production readiness
 */

import { withAdminAuth } from '@/lib/admin-auth';

async function configurationHandler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const requestId = Math.random().toString(36).substring(2, 8);
    console.log(`[${requestId}] Configuration diagnostics request`);

    // Check required environment variables
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NEXT_PUBLIC_SITE_URL'
    ];

    const missingEnvVars = [];
    const presentEnvVars = [];

    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        presentEnvVars.push(envVar);
      } else {
        missingEnvVars.push(envVar);
      }
    }

    // Check production-specific configuration
    const productionEnvVars = [
      'NEXT_PUBLIC_SECURE_COOKIES',
      'NEXT_PUBLIC_COOKIE_DOMAIN'
    ];

    const missingProdVars = productionEnvVars.filter(envVar => !process.env[envVar]);

    // Check for development mode in production
    const devModeEnabled = process.env.NEXT_PUBLIC_DEV_MODE === 'true';
    const isProduction = process.env.NODE_ENV === 'production';

    // Calculate overall status
    const hasAllRequired = missingEnvVars.length === 0;
    const hasProductionConfig = missingProdVars.length === 0 || !isProduction;
    const devModeOk = !devModeEnabled || !isProduction;

    const allHealthy = hasAllRequired && hasProductionConfig && devModeOk;

    const details = {
      requiredVariables: {
        present: presentEnvVars,
        missing: missingEnvVars,
        status: hasAllRequired ? 'passed' : 'failed'
      },
      productionVariables: {
        missing: missingProdVars,
        status: hasProductionConfig ? 'passed' : 'warning'
      },
      developmentMode: {
        enabled: devModeEnabled,
        environment: process.env.NODE_ENV,
        status: devModeOk ? 'passed' : 'failed'
      }
    };

    let message = 'Configuration check completed';
    if (!hasAllRequired) {
      message = `Missing required environment variables: ${missingEnvVars.join(', ')}`;
    } else if (!hasProductionConfig && isProduction) {
      message = `Missing production environment variables: ${missingProdVars.join(', ')}`;
    } else if (!devModeOk) {
      message = 'Development mode is enabled in production environment';
    } else {
      message = 'All configuration checks passed';
    }

    return res.status(200).json({
      status: allHealthy ? 'healthy' : 'error',
      message,
      details,
      requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Configuration diagnostics error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Configuration diagnostics failed',
      error: error.message
    });
  }
}

export default withAdminAuth(configurationHandler);
