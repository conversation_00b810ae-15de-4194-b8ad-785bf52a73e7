{"analysis_date": "2025-06-01T23:13:13.583584", "summary": {"total_contacts": 1022, "contacts_with_square_id": 864, "valid_emails": 833, "valid_phones": 806, "duplicates_found": 9, "data_quality_issues": [{"index": 30, "issue": "Missing email address", "contact": "<PERSON><PERSON><PERSON>"}, {"index": 65, "issue": "Missing email address", "contact": ""}, {"index": 87, "issue": "Missing email address", "contact": "<PERSON>"}, {"index": 174, "issue": "Missing email address", "contact": "curran"}, {"index": 177, "issue": "Missing email address", "contact": "<PERSON><PERSON>"}, {"index": 192, "issue": "Missing email address", "contact": "<PERSON><PERSON><PERSON>"}, {"index": 213, "issue": "Missing email address", "contact": ""}, {"index": 218, "issue": "Missing email address", "contact": "Grayson"}, {"index": 250, "issue": "Missing email address", "contact": "Redzepagic"}, {"index": 251, "issue": "Missing email address", "contact": "<PERSON><PERSON>"}, {"index": 261, "issue": "Missing email address", "contact": "<PERSON>"}, {"index": 276, "issue": "Missing email address", "contact": "<PERSON>"}, {"index": 316, "issue": "Missing email address", "contact": "<PERSON><PERSON><PERSON><PERSON>"}, {"index": 319, "issue": "Missing email address", "contact": "<PERSON><PERSON>"}, {"index": 320, "issue": "Missing email address", "contact": "Agis"}, {"index": 337, "issue": "Missing email address", "contact": "frost"}, {"index": 343, "issue": "Missing email address", "contact": "<PERSON><PERSON><PERSON>"}, {"index": 344, "issue": "Missing email address", "contact": "Lyons"}, {"index": 407, "issue": "Missing email address", "contact": "LOUISE"}, {"index": 425, "issue": "Invalid phone format: '+44 7932 773139", "contact": "<PERSON><PERSON>"}, {"index": 494, "issue": "Missing email address", "contact": "Ke<PERSON>m"}, {"index": 517, "issue": "Missing email address", "contact": ""}, {"index": 543, "issue": "Missing email address", "contact": "critsilis"}, {"index": 558, "issue": "Missing email address", "contact": "dyson"}, {"index": 600, "issue": "Missing email address", "contact": "<PERSON>"}, {"index": 615, "issue": "Invalid phone format: '+81 45-319-6127", "contact": "<PERSON><PERSON><PERSON><PERSON>"}, {"index": 653, "issue": "Missing email address", "contact": "Bentley"}, {"index": 656, "issue": "Missing email address", "contact": "<PERSON><PERSON><PERSON>"}, {"index": 688, "issue": "Missing email address", "contact": "<PERSON><PERSON>"}, {"index": 692, "issue": "Invalid phone format: '******-927-0090", "contact": "<PERSON><PERSON><PERSON>"}, {"index": 700, "issue": "Missing email address", "contact": "<PERSON><PERSON>"}, {"index": 702, "issue": "Invalid phone format: '******-318-0355", "contact": "<PERSON>"}, {"index": 719, "issue": "Missing email address", "contact": "Stracham"}, {"index": 843, "issue": "Missing email address", "contact": "<PERSON>"}, {"index": 848, "issue": "Missing email address", "contact": "<PERSON><PERSON>"}]}, "square_customers_sample": [{"index": 0, "name": "Endsor", "email": "<EMAIL>", "square_id": "3K8A23VXSNT0SHYAYZ6M6J4WPW", "created_at": "2024-06-28 03:24"}, {"index": 1, "name": "<PERSON>", "email": "<EMAIL>", "square_id": "DNXYJHJZEVBH69KY0BYMAE4W5C", "created_at": "2024-02-07 05:08"}, {"index": 2, "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "square_id": "YW2F8ZWQT7CP9PJPZ9CC8MJ2MC", "created_at": "2024-07-27 08:39"}, {"index": 3, "name": "gray", "email": "<EMAIL>", "square_id": "Y7VS0EM06AWV5G7G3N64PXJWPC", "created_at": "2024-07-27 08:39"}, {"index": 4, "name": "<PERSON>", "email": "<EMAIL>", "square_id": "413VNJ385121X18WYNEZB19NH4", "created_at": "2024-07-27 08:39"}], "recommendations": [{"priority": "HIGH", "category": "Data Quality", "issue": "35 data quality issues found", "action": "Review and clean data before import"}, {"priority": "HIGH", "category": "Duplicates", "issue": "9 potential duplicates found", "action": "Review and merge duplicate records"}, {"priority": "INFO", "category": "Import Strategy", "issue": "Square Customer ID integration", "action": "Ensure Square Customer IDs are preserved during import for payment integration"}]}