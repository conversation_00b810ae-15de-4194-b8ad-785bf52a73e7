.automationList {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.actions {
  display: flex;
  gap: 10px;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
}

.searchContainer {
  flex: 1;
  min-width: 200px;
}

.searchInput {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 2px rgba(110, 142, 251, 0.2);
}

.filterControls {
  display: flex;
  gap: 10px;
}

.filterSelect {
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s ease;
}

.filterSelect:focus {
  outline: none;
  border-color: #6e8efb;
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.automationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.automationCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.automationCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.automationHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.automationName {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.automationStatus {
  display: flex;
  align-items: center;
}

.statusToggle {
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.statusActive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.statusActive:hover:not(:disabled) {
  background-color: rgba(76, 175, 80, 0.2);
}

.statusInactive {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.statusInactive:hover:not(:disabled) {
  background-color: rgba(158, 158, 158, 0.2);
}

.statusLoading {
  display: inline-block;
  width: 16px;
  text-align: center;
}

.automationTrigger,
.automationMessage,
.automationSegment {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
}

.triggerDetail {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  background-color: rgba(110, 142, 251, 0.1);
  border-radius: 4px;
  font-size: 0.8rem;
  color: #6e8efb;
}

.automationDescription {
  font-size: 0.9rem;
  color: #666;
  margin: 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.automationStats {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 12px 0;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 4px;
}

.statLabel {
  font-size: 0.8rem;
  color: #666;
}

.statValue {
  font-size: 0.85rem;
  font-weight: 500;
  color: #333;
}

.automationActions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.viewButton,
.editButton {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewButton {
  background-color: #6e8efb;
  color: white;
}

.viewButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.editButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.noResults {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
}

.paginationButton {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.paginationButton:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 0.9rem;
  color: #666;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .actions {
    width: 100%;
  }
  
  .addButton {
    width: 100%;
    justify-content: center;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .filterControls {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .filterSelect {
    flex: 1;
    min-width: 120px;
  }
  
  .automationGrid {
    grid-template-columns: 1fr;
  }
}
