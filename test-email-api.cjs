/**
 * Email Configuration Test via API
 * Tests the email system through the running Next.js application
 */

const fetch = require('node-fetch');

async function testEmailAPI() {
  console.log('🧪 Testing Email Configuration via API...\n');

  const baseUrl = 'http://localhost:3000';

  // Test 1: Check if server is running
  console.log('1️⃣ Checking if Next.js server is running...');
  try {
    const response = await fetch(`${baseUrl}/api/health`);
    if (response.ok) {
      console.log('   ✅ Server is running');
    } else {
      console.log('   ❌ Server responded with error:', response.status);
    }
  } catch (error) {
    console.log('   ❌ Server is not responding. Make sure to run: npm run dev');
    console.log('   ❌ Error:', error.message);
    return;
  }

  // Test 2: Test Gmail connection via API
  console.log('\n2️⃣ Testing Gmail Connection via API...');
  try {
    const response = await fetch(`${baseUrl}/api/admin/test-connections`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'gmail'
      })
    });

    const result = await response.json();
    console.log(`   ${result.success ? '✅' : '❌'} Gmail SMTP: ${result.message || 'Test completed'}`);
    if (result.details) {
      console.log('   Details:', JSON.stringify(result.details, null, 2));
    }
  } catch (error) {
    console.log(`   ❌ Gmail test failed: ${error.message}`);
  }

  // Test 3: Test Workspace connection via API
  console.log('\n3️⃣ Testing Workspace Connection via API...');
  try {
    const response = await fetch(`${baseUrl}/api/admin/test-connections`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'workspace'
      })
    });

    const result = await response.json();
    console.log(`   ${result.success ? '✅' : '❌'} Workspace SMTP: ${result.message || 'Test completed'}`);
    if (result.details) {
      console.log('   Details:', JSON.stringify(result.details, null, 2));
    }
  } catch (error) {
    console.log(`   ❌ Workspace test failed: ${error.message}`);
  }

  // Test 4: Check email service status
  console.log('\n4️⃣ Checking Email Service Status via API...');
  try {
    const response = await fetch(`${baseUrl}/api/admin/test-connections`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'email-status'
      })
    });

    const result = await response.json();
    console.log(`   Status Check: ${result.success ? '✅' : '❌'}`);
    if (result.primary) {
      console.log(`   Primary Service: ${result.primary}`);
    }
    if (result.available && result.available.length > 0) {
      console.log(`   Available Services: ${result.available.join(', ')}`);
    }
    if (result.services) {
      console.log('   Service Details:');
      Object.entries(result.services).forEach(([service, details]) => {
        console.log(`     ${details.success ? '✅' : '❌'} ${service}: ${details.message}`);
      });
    }
  } catch (error) {
    console.log(`   ❌ Email status check failed: ${error.message}`);
  }

  console.log('\n🎯 Summary:');
  console.log('='.repeat(50));
  console.log('✅ API tests completed');
  console.log('\n📋 Next Steps:');
  console.log('1. If tests passed: Configure real Gmail/Workspace credentials');
  console.log('2. If tests failed: Check environment variables in .env.local');
  console.log('3. Visit admin panel: http://localhost:3000/admin/settings');
  console.log('4. Go to "Email Settings" tab to test interactively');
  console.log('\n🔧 Quick Setup:');
  console.log('- Gmail: Update GMAIL_SMTP_USER and GMAIL_SMTP_APP_PASSWORD');
  console.log('- Workspace: Update WORKSPACE_SMTP_USER and WORKSPACE_SMTP_APP_PASSWORD');
  console.log('- See GOOGLE_CLOUD_EMAIL_SETUP_GUIDE.md for detailed instructions');
}

// Run the test
testEmailAPI().catch(error => {
  console.error('❌ API test failed:', error);
});
