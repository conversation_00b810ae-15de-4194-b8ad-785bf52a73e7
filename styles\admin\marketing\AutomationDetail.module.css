.automationDetail {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.headerLeft h2 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  color: #333;
}

.meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.statusToggle {
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.statusActive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.statusActive:hover:not(:disabled) {
  background-color: rgba(76, 175, 80, 0.2);
}

.statusInactive {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.statusInactive:hover:not(:disabled) {
  background-color: rgba(158, 158, 158, 0.2);
}

.statusLoading {
  display: inline-block;
  width: 16px;
  text-align: center;
}

.type,
.trigger {
  font-size: 0.9rem;
  color: #666;
}

.headerActions {
  display: flex;
  gap: 10px;
}

.triggerButton,
.editButton,
.deleteButton,
.backButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.triggerButton {
  background-color: #ff9800;
  color: white;
  border: none;
  cursor: pointer;
}

.triggerButton:hover {
  background-color: #f57c00;
  transform: translateY(-1px);
}

.editButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.deleteButton {
  background-color: transparent;
  color: #f44336;
  border: 1px solid #f44336;
  cursor: pointer;
}

.deleteButton:hover {
  background-color: rgba(244, 67, 54, 0.1);
  transform: translateY(-1px);
}

.backButton {
  background-color: #6e8efb;
  color: white;
  border: none;
  cursor: pointer;
  margin-top: 16px;
}

.backButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.loading,
.error,
.notFound {
  text-align: center;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.error {
  color: #f44336;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.notFound {
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.automationInfo {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.infoSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 0.9rem;
  color: #666;
}

.infoValue {
  font-size: 1rem;
  color: #333;
}

.segmentLink,
.templateLink,
.customerLink {
  color: #6e8efb;
  text-decoration: none;
  transition: color 0.2s ease;
}

.segmentLink:hover,
.templateLink:hover,
.customerLink:hover {
  color: #5a7df9;
  text-decoration: underline;
}

.contentItem {
  margin-bottom: 16px;
}

.contentItem:last-child {
  margin-bottom: 0;
}

.contentLabel {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
}

.contentValue {
  display: block;
  font-size: 1rem;
  color: #333;
}

.contentPre {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  max-height: 300px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.statCard {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s ease;
}

.statCard:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.statValue {
  font-size: 1.8rem;
  font-weight: 600;
  color: #6e8efb;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
}

.noLogs {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.logsTable {
  overflow-x: auto;
}

.logsTable table {
  width: 100%;
  border-collapse: collapse;
}

.logsTable th {
  text-align: left;
  padding: 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-size: 0.9rem;
  color: #333;
}

.logsTable td {
  padding: 12px;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
}

.successRow {
  background-color: rgba(76, 175, 80, 0.05);
}

.failedRow {
  background-color: rgba(244, 67, 54, 0.05);
}

.successStatus {
  color: #4caf50;
}

.failedStatus {
  color: #f44336;
}

.deleteModal,
.triggerModal {
  padding: 20px;
}

.deleteModal h3,
.triggerModal h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.deleteModal p,
.triggerModal p {
  margin-bottom: 20px;
  color: #666;
}

.deleteError,
.triggerError {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.triggerSuccess {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.deleteActions,
.triggerActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancelDeleteButton,
.confirmDeleteButton,
.cancelTriggerButton,
.confirmTriggerButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelDeleteButton,
.cancelTriggerButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelDeleteButton:hover:not(:disabled),
.cancelTriggerButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.confirmDeleteButton {
  background-color: #f44336;
  color: white;
  border: none;
}

.confirmDeleteButton:hover:not(:disabled) {
  background-color: #e53935;
}

.confirmTriggerButton {
  background-color: #ff9800;
  color: white;
  border: none;
}

.confirmTriggerButton:hover:not(:disabled) {
  background-color: #f57c00;
}

.cancelDeleteButton:disabled,
.confirmDeleteButton:disabled,
.cancelTriggerButton:disabled,
.confirmTriggerButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
  }
  
  .headerActions {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .triggerButton,
  .editButton,
  .deleteButton {
    flex: 1;
    text-align: center;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .deleteActions,
  .triggerActions {
    flex-direction: column;
  }
  
  .cancelDeleteButton,
  .confirmDeleteButton,
  .cancelTriggerButton,
  .confirmTriggerButton {
    width: 100%;
  }
}
