import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for system logs
 * This endpoint retrieves system logs
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Logs endpoint called: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request using our robust auth module
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  // Only allow admin users to access logs
  if (role !== 'admin') {
    console.error(`[${requestId}] User ${user?.email} with role ${role} attempted to access logs`);
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Only admin users can access logs',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);

  try {
    // Set a timeout to prevent hanging requests
    let timeoutId = null;
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error(`[${requestId}] Logs fetch timed out`);
        reject(new Error('Logs fetch timeout'));
      }, 10000); // 10 second timeout
    });

    let adminClient;
    try {
      // Get admin client with timeout protection
      adminClient = getAdminClient();

      // Clear the timeout since we got a response
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (!adminClient) {
        console.error(`[${requestId}] Supabase admin client not available.`);
        return res.status(500).json({
          error: 'Database connection failed',
          message: 'Could not establish database connection',
          requestId
        });
      }

      console.log(`[${requestId}] Admin client obtained successfully`);
    } catch (adminClientError) {
      // Clear the timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      console.error(`[${requestId}] Admin client fetch error:`, adminClientError);
      throw adminClientError;
    }

    // Get query parameters
    const { limit = 50, level } = req.query;

    try {
      // First check if the system_logs table exists
      const { data: tableExists, error: tableCheckError } = await adminClient
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'system_logs')
        .single();

      // If there's an error or the table doesn't exist, generate mock data
      if (tableCheckError || !tableExists) {
        console.log(`[${requestId}] system_logs table does not exist, returning mock data`);
        // Generate mock logs for development
        const mockLogs = [
          {
            id: '1',
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'info',
            message: 'Application started successfully',
            source: 'system'
          },
          {
            id: '2',
            timestamp: new Date(Date.now() - 120000).toISOString(),
            level: 'warn',
            message: 'High memory usage detected (85%)',
            source: 'system'
          },
          {
            id: '3',
            timestamp: new Date(Date.now() - 180000).toISOString(),
            level: 'error',
            message: 'Failed to connect to payment gateway: timeout',
            source: 'payment'
          },
          {
            id: '4',
            timestamp: new Date(Date.now() - 240000).toISOString(),
            level: 'info',
            message: 'User login successful: <EMAIL>',
            source: 'auth'
          },
          {
            id: '5',
            timestamp: new Date(Date.now() - 300000).toISOString(),
            level: 'debug',
            message: 'Cache refreshed: products',
            source: 'cache'
          }
        ];

        return res.status(200).json({
          logs: mockLogs,
          count: mockLogs.length,
          note: 'Using mock data as no logs were found',
          requestId,
          timestamp: new Date().toISOString()
        });
      }

      // If the table exists, proceed with the query
      // Start building the query
      let query = adminClient
        .from('system_logs')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(parseInt(limit, 10));

      // Filter by log level if provided
      if (level) {
        query = query.eq('level', level);
      }

      // Execute the query
      const { data, error: logsError } = await query;

      if (logsError) {
        console.error(`[${requestId}] Logs fetch failed:`, logsError);
        return res.status(500).json({
          error: 'Failed to fetch logs',
          message: logsError.message || 'An error occurred while fetching logs',
          requestId
        });
      }

      // Return logs
      return res.status(200).json({
        logs: data || [],
        count: data ? data.length : 0,
        requestId,
        timestamp: new Date().toISOString()
      });
    } catch (queryError) {
      console.error(`[${requestId}] Error checking or querying logs table:`, queryError);
      throw queryError;
    }
  } catch (error) {
    console.error(`[${requestId}] Logs API error:`, error);
    return res.status(500).json({
      error: 'Failed to fetch logs',
      message: error.message || 'An error occurred while fetching logs',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
