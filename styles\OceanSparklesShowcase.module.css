.showcaseSection {
  position: relative;
  min-height: 800px;
  overflow: hidden;
  padding: 6rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Removed background-image to allow main background to show through */
  z-index: -2;
  opacity: 0.9;
  transform: scale(1.05);
}

.overlayEffect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(98, 0, 234, 0.2) 0%, rgba(236, 64, 122, 0.2) 50%, rgba(0, 188, 212, 0.2) 100%);
  z-index: -1;
  mix-blend-mode: overlay;
  backdrop-filter: blur(3px); /* Add slight blur effect */
}

.showcaseContent {
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.showcaseTitle {
  font-size: 3rem;
  color: white;
  margin-bottom: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to right, #ec407a, #6200ea, #00bcd4);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.showcaseSubtitle {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto 4rem;
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.showcaseImagesContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 4rem;
  perspective: 1000px;
}

.showcaseImageWrapper {
  flex: 0 0 auto;
  max-width: 250px;
  transform-style: preserve-3d;
  transition: transform 0.5s ease;
}

.showcaseImage {
  position: relative;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.5s ease;
  transform: translateZ(0);
  cursor: pointer;
}

.showcaseImage::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, #ec407a, #6200ea, #00bcd4);
  z-index: -1;
  border-radius: 50%;
  opacity: 0.7;
  animation: rotateBorder 10s infinite linear;
}

@keyframes rotateBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.showcaseImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: saturate(1.2);
}

.imageCaption {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 1.5rem 1rem 1rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.imageCaption span {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.showcaseImage:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.showcaseImage:hover img {
  transform: scale(1.2);
}

.showcaseImage:hover .imageCaption {
  transform: translateY(0);
}

/* Position variations for image layout */
.image1 {
  transform: rotate(-5deg);
}

.image2 {
  transform: translateY(-20px) rotate(3deg);
}

.image3 {
  transform: translateY(10px) rotate(-2deg);
}

.image4 {
  transform: rotate(6deg);
}

.image5 {
  transform: translateY(-15px) rotate(-4deg);
}

.showcaseCta {
  margin-top: 2rem;
}

.showcaseCta a.button--glow {
  background: linear-gradient(135deg, #ec407a, #6200ea);
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(98, 0, 234, 0.3);
}

.showcaseCta a.button--glow:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(98, 0, 234, 0.4);
}

.showcaseCta a.button--glow::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  background: linear-gradient(135deg, #ec407a, #6200ea, #00bcd4, #6200ea, #ec407a);
  background-size: 400% 400%;
  z-index: -1;
  border-radius: 40px;
  animation: gradientAnimation 5s ease infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.showcaseCta a.button--glow:hover::before {
  opacity: 1;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .showcaseSection {
    padding: 5rem 2rem;
  }

  .showcaseTitle {
    font-size: 2.7rem;
  }

  .showcaseSubtitle {
    font-size: 1.3rem;
  }

  .showcaseImage {
    width: 230px;
    height: 230px;
  }
}

@media (max-width: 992px) {
  .showcaseTitle {
    font-size: 2.5rem;
  }

  .showcaseSubtitle {
    font-size: 1.2rem;
    max-width: 600px;
  }

  .showcaseImage {
    width: 200px;
    height: 200px;
  }
}

@media (max-width: 768px) {
  .showcaseSection {
    padding: 4rem 1.5rem;
    min-height: 700px;
  }

  .showcaseTitle {
    font-size: 2.2rem;
  }

  .showcaseSubtitle {
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }

  .showcaseImage {
    width: 180px;
    height: 180px;
  }

  .imageCaption span {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .showcaseSection {
    padding: 3rem 1rem;
  }

  .showcaseTitle {
    font-size: 2rem;
  }

  .showcaseSubtitle {
    font-size: 1rem;
    margin-bottom: 2.5rem;
  }

  .showcaseImagesContainer {
    gap: 1.5rem;
  }

  .showcaseImage {
    width: 150px;
    height: 150px;
  }

  .showcaseCta a.button--glow {
    font-size: 1rem;
    padding: 0.8rem 2rem;
  }
}