import { useState, useEffect } from 'react';
import Link from 'next/link';
import { createClient } from '@supabase/supabase-js';
import { STATUS_DISPLAY_NAMES } from '@/lib/booking-status';
import styles from '@/styles/admin/CustomerBookingHistory.module.css';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Enhanced component for displaying customer booking history
 *
 * @param {Object} props - Component props
 * @param {Array} props.bookings - Customer bookings array
 * @param {string} props.customerId - Customer ID (optional, if direct fetching is needed)
 * @returns {JSX.Element}
 */
export default function CustomerBookingHistory({ bookings = [], customerId }) {
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date_desc');
  const [localBookings, setLocalBookings] = useState(bookings);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [bookingStats, setBookingStats] = useState({
    totalBookings: 0,
    totalSpent: 0,
    upcomingBookings: 0,
    canceledBookings: 0,
    mostFrequentService: '',
    lastBookingDate: null,
    firstBookingDate: null
  });

  // Format date and time
  const formatDateTime = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleString('en-AU', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get status class for styling
  const getStatusClass = (status) => {
    switch (status) {
      case 'confirmed':
        return styles.confirmed;
      case 'pending':
        return styles.pending;
      case 'canceled':
        return styles.canceled;
      case 'in_progress':
        return styles.inProgress;
      case 'completed':
        return styles.completed;
      case 'no_show':
        return styles.noShow;
      case 'rescheduled':
        return styles.rescheduled;
      default:
        return '';
    }
  }

  // Fetch bookings if customerId is provided and bookings array is empty
  useEffect(() => {
    const fetchBookings = async () => {
      if (!customerId || bookings.length > 0) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch bookings from Supabase
        const { data, error } = await supabase
          .from('bookings')
          .select(`
            id,
            start_time,
            end_time,
            status,
            location,
            notes,
            services:service_id (id, name, price, duration, color)
          `)
          .eq('customer_id', customerId)
          .order('start_time', { ascending: false });

        if (error) throw error;

        setLocalBookings(data || []);
      } catch (error) {
        console.error('Error fetching customer bookings:', error);
        setError('Failed to load booking history');
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, [customerId, bookings.length]);

  // Update local bookings when props bookings change
  useEffect(() => {
    if (bookings.length > 0) {
      setLocalBookings(bookings);
    }
  }, [bookings]);

  // Filter bookings based on selected filter
  const filteredBookings = localBookings.filter(booking => {
    if (filter === 'all') return true;
    if (filter === 'upcoming') {
      return new Date(booking.start_time) >= new Date() && booking.status !== 'canceled';
    }
    if (filter === 'past') {
      return new Date(booking.start_time) < new Date() || booking.status === 'canceled';
    }
    if (filter === 'completed') {
      return booking.status === 'completed';
    }
    return booking.status === filter;
  });

  // Sort bookings
  const sortedBookings = [...filteredBookings].sort((a, b) => {
    if (sortBy === 'date_asc') {
      return new Date(a.start_time) - new Date(b.start_time);
    }
    if (sortBy === 'date_desc') {
      return new Date(b.start_time) - new Date(a.start_time);
    }
    if (sortBy === 'service') {
      return (a.services?.name || '').localeCompare(b.services?.name || '');
    }
    if (sortBy === 'price_asc') {
      return (a.services?.price || 0) - (b.services?.price || 0);
    }
    if (sortBy === 'price_desc') {
      return (b.services?.price || 0) - (a.services?.price || 0);
    }
    return 0;
  });

  // Calculate booking statistics
  useEffect(() => {
    if (localBookings.length === 0) return;

    // Sort bookings by date
    const sortedBookings = [...localBookings].sort((a, b) =>
      new Date(a.start_time) - new Date(b.start_time)
    );

    // Calculate total spent
    const totalSpent = localBookings
      .filter(booking => booking.status !== 'canceled')
      .reduce((total, booking) => {
        const price = booking.services?.price || 0;
        return total + parseFloat(price);
      }, 0);

    // Count upcoming bookings
    const now = new Date();
    const upcomingBookings = localBookings.filter(booking =>
      new Date(booking.start_time) >= now && booking.status !== 'canceled'
    ).length;

    // Count completed bookings
    const completedBookings = localBookings.filter(booking =>
      booking.status === 'completed'
    ).length;

    // Count canceled bookings
    const canceledBookings = localBookings.filter(booking =>
      booking.status === 'canceled'
    ).length;

    // Find most frequent service
    const serviceCount = {};
    localBookings.forEach(booking => {
      const serviceName = booking.services?.name || 'Unknown';
      serviceCount[serviceName] = (serviceCount[serviceName] || 0) + 1;
    });

    let mostFrequentService = '';
    let maxCount = 0;

    Object.entries(serviceCount).forEach(([service, count]) => {
      if (count > maxCount) {
        mostFrequentService = service;
        maxCount = count;
      }
    });

    // Get first and last booking dates
    const firstBookingDate = sortedBookings.length > 0 ?
      new Date(sortedBookings[0].start_time) : null;

    const lastBookingDate = sortedBookings.length > 0 ?
      new Date(sortedBookings[sortedBookings.length - 1].start_time) : null;

    setBookingStats({
      totalBookings: localBookings.length,
      totalSpent: totalSpent.toFixed(2),
      upcomingBookings,
      completedBookings,
      canceledBookings,
      mostFrequentService,
      firstBookingDate,
      lastBookingDate
    });
  }, [localBookings]);

  // Format date only
  const formatDate = (date) => {
    if (!date) return 'N/A'
    return date.toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className={styles.bookingHistory}>
      <div className={styles.header}>
        <h3 className={styles.title}>Booking History</h3>
        <div className={styles.bookingActions}>
          <Link
            href={`/admin/bookings/new?customer_id=${localBookings[0]?.customer_id || customerId || ''}`}
            className={styles.newBookingButton}
          >
            New Booking
          </Link>
        </div>
      </div>

      {error && (
        <div className={styles.error}>{error}</div>
      )}

      {loading && localBookings.length === 0 ? (
        <div className={styles.loading}>Loading booking history...</div>
      ) : (
        <>
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{bookingStats.totalBookings}</div>
              <div className={styles.statLabel}>Total Bookings</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>${bookingStats.totalSpent}</div>
              <div className={styles.statLabel}>Total Spent</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{bookingStats.upcomingBookings}</div>
              <div className={styles.statLabel}>Upcoming</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{formatDate(bookingStats.lastBookingDate)}</div>
              <div className={styles.statLabel}>Last Booking</div>
            </div>
          </div>

          <div className={styles.additionalStats}>
            <div className={styles.statItem}>
              <span className={styles.statItemLabel}>First Booking:</span>
              <span className={styles.statItemValue}>{formatDate(bookingStats.firstBookingDate)}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statItemLabel}>Most Frequent Service:</span>
              <span className={styles.statItemValue}>{bookingStats.mostFrequentService || 'N/A'}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statItemLabel}>Completed Bookings:</span>
              <span className={styles.statItemValue}>{bookingStats.completedBookings || 0}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statItemLabel}>Canceled Bookings:</span>
              <span className={styles.statItemValue}>{bookingStats.canceledBookings}</span>
            </div>
          </div>

          <div className={styles.controlsRow}>
            <div className={styles.filters}>
              <button
                className={`${styles.filterButton} ${filter === 'all' ? styles.active : ''}`}
                onClick={() => setFilter('all')}
              >
                All
              </button>
              <button
                className={`${styles.filterButton} ${filter === 'upcoming' ? styles.active : ''}`}
                onClick={() => setFilter('upcoming')}
              >
                Upcoming
              </button>
              <button
                className={`${styles.filterButton} ${filter === 'past' ? styles.active : ''}`}
                onClick={() => setFilter('past')}
              >
                Past
              </button>
              <button
                className={`${styles.filterButton} ${filter === 'confirmed' ? styles.active : ''}`}
                onClick={() => setFilter('confirmed')}
              >
                Confirmed
              </button>
              <button
                className={`${styles.filterButton} ${filter === 'completed' ? styles.active : ''}`}
                onClick={() => setFilter('completed')}
              >
                Completed
              </button>
              <button
                className={`${styles.filterButton} ${filter === 'canceled' ? styles.active : ''}`}
                onClick={() => setFilter('canceled')}
              >
                Canceled
              </button>
            </div>

            <div className={styles.sortControl}>
              <label htmlFor="sortBy" className={styles.sortLabel}>Sort by:</label>
              <select
                id="sortBy"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className={styles.sortSelect}
              >
                <option value="date_desc">Date (Newest First)</option>
                <option value="date_asc">Date (Oldest First)</option>
                <option value="service">Service Name</option>
                <option value="price_desc">Price (High to Low)</option>
                <option value="price_asc">Price (Low to High)</option>
              </select>
            </div>
          </div>
        </>
      )}

      {!loading && sortedBookings.length === 0 ? (
        <div className={styles.noBookings}>
          {filter === 'all'
            ? 'No bookings found for this customer.'
            : `No ${filter} bookings found.`}
        </div>
      ) : (
        <div className={styles.bookingsList}>
          {sortedBookings.map((booking) => (
            <div key={booking.id} className={styles.bookingItem}>
              <div className={styles.bookingHeader}>
                <div className={styles.serviceInfo}>
                  {booking.services?.color && (
                    <div
                      className={styles.serviceColor}
                      style={{ backgroundColor: booking.services.color }}
                    ></div>
                  )}
                  <span className={styles.serviceName}>
                    {booking.services?.name || 'Unknown Service'}
                  </span>
                </div>
                <span className={`${styles.status} ${getStatusClass(booking.status)}`}>
                  {STATUS_DISPLAY_NAMES[booking.status] || booking.status}
                </span>
              </div>

              <div className={styles.bookingDetails}>
                <div className={styles.bookingTime}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                  <span>{formatDateTime(booking.start_time)}</span>
                </div>

                {booking.location && (
                  <div className={styles.bookingLocation}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    <span>{booking.location}</span>
                  </div>
                )}

                {booking.services?.price && (
                  <div className={styles.bookingPrice}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="1" x2="12" y2="23"></line>
                      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                    <span>${parseFloat(booking.services.price).toFixed(2)}</span>
                  </div>
                )}
              </div>

              {booking.notes && (
                <div className={styles.bookingNotes}>
                  <strong>Notes:</strong> {booking.notes}
                </div>
              )}

              <div className={styles.bookingActions}>
                <Link
                  href={`/admin/bookings/${booking.id}`}
                  className={styles.viewButton}
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
