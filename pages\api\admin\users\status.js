import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * Determine if a user is "fully active" and should not be deleted
 * A user is considered fully active if they have:
 * - Logged in at least once, OR
 * - Have an approved application (for artist/braider), OR
 * - Have an active profile with successful subscription status
 */
function determineIfUserIsFullyActive(authUser, userRole, userProfile, applicationStatus) {
  // If user has logged in, they are considered active
  if (userProfile?.login_count > 0) {
    return true
  }

  // If user has an approved application, they are considered active
  if (applicationStatus === 'approved') {
    return true
  }

  // If user has successful subscription status, they are considered active
  if (userProfile?.subscription_status === 'successful') {
    return true
  }

  // If user profile is explicitly marked as active and they have a role beyond 'user'
  if (userProfile?.is_active && userRole?.role && userRole.role !== 'user') {
    return true
  }

  // Check if user was created more than 7 days ago and has a profile
  // This indicates they've completed the signup process
  if (userProfile && authUser?.created_at) {
    const createdDate = new Date(authUser.created_at)
    const daysSinceCreation = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceCreation > 7) {
      return true
    }
  }

  // Otherwise, user is not considered fully active
  return false
}

/**
 * User Status Check API
 * Determines if a user can be deleted or should be deactivated
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ error: 'Unauthorized. Only administrators and developers can check user status.' })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    const { userId } = req.query

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    // Check if user exists in auth.users (the authoritative source)
    let targetUser = null
    try {
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers()
      if (authError) {
        console.error('Error fetching auth users:', authError)
        return res.status(500).json({ error: 'Failed to fetch user data' })
      }

      targetUser = authData.users.find(u => u.id === userId)
      if (!targetUser) {
        return res.status(404).json({ error: 'User not found' })
      }
    } catch (error) {
      console.error('Error checking user existence:', error)
      return res.status(500).json({ error: 'Failed to verify user existence' })
    }

    // Get user role
    const { data: userRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', userId)
      .single()

    // Note: roleError is acceptable here as user might not have a role record
    if (roleError && roleError.code !== 'PGRST116') {
      console.error('Error fetching user role:', roleError)
      return res.status(500).json({ error: 'Failed to fetch user role' })
    }

    // Get user profile
    const { data: userProfile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('is_active, subscription_status, login_count, last_login_at, notes')
      .eq('id', userId)
      .single()

    // Note: profileError is acceptable here as user might not have a profile record
    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error fetching user profile:', profileError)
      return res.status(500).json({ error: 'Failed to fetch user profile' })
    }

    // Get application status for artist/braider users
    let applicationStatus = null
    let applicationData = null
    if (userRole?.role === 'artist' || userRole?.role === 'braider') {
      const { data: application, error: appError } = await adminClient
        .from('artist_braider_applications')
        .select('status, reviewed_at, review_notes, created_at')
        .eq('user_id', userId)
        .eq('application_type', userRole.role)
        .single()

      if (appError && appError.code !== 'PGRST116') {
        console.error('Error fetching application status:', appError)
        return res.status(500).json({ error: 'Failed to fetch application status' })
      }

      applicationStatus = application?.status || null
      applicationData = application || null
    }

    // Determine if user is fully active
    const isFullyActive = determineIfUserIsFullyActive(targetUser, userRole, userProfile, applicationStatus)

    // Determine what actions are allowed
    const canDelete = !isFullyActive || role === 'dev'
    const canDeactivate = true // Admins can always deactivate
    const canReactivate = userProfile?.is_active === false

    // Determine recommended action
    let recommendedAction = 'none'
    if (isFullyActive) {
      recommendedAction = userProfile?.is_active === false ? 'reactivate' : 'deactivate'
    } else {
      recommendedAction = 'delete'
    }

    // Generate status summary
    const statusSummary = []
    if (userProfile?.login_count > 0) {
      statusSummary.push(`Has logged in ${userProfile.login_count} times`)
    }
    if (applicationStatus === 'approved') {
      statusSummary.push('Has approved application')
    }
    if (userProfile?.subscription_status === 'successful') {
      statusSummary.push('Has successful subscription')
    }
    if (userProfile?.is_active === false) {
      statusSummary.push('Account is currently deactivated')
    }
    if (targetUser?.created_at) {
      const createdDate = new Date(targetUser.created_at)
      const daysSinceCreation = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24))
      statusSummary.push(`Account created ${daysSinceCreation} days ago`)
    }

    return res.status(200).json({
      success: true,
      user: {
        id: userId,
        email: targetUser.email,
        created_at: targetUser.created_at,
        last_sign_in_at: targetUser.last_sign_in_at
      },
      role: userRole?.role || 'unknown',
      profile: userProfile || null,
      application: applicationData || null,
      status: {
        isFullyActive,
        isCurrentlyActive: userProfile?.is_active !== false,
        hasLoggedIn: (userProfile?.login_count || 0) > 0,
        applicationStatus: applicationStatus,
        subscriptionStatus: userProfile?.subscription_status || 'unknown'
      },
      permissions: {
        canDelete,
        canDeactivate,
        canReactivate,
        currentUserRole: role
      },
      recommendations: {
        action: recommendedAction,
        reason: isFullyActive 
          ? 'User is fully active - use deactivate instead of delete'
          : 'User has not completed signup/application process - safe to delete',
        statusSummary
      }
    })

  } catch (error) {
    console.error('Unexpected error in user status check:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
