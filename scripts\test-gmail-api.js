/**
 * Gmail API Integration Test Script
 * Tests the complete Gmail API workflow for Ocean Soul Sparkles
 */

const testGmailAPI = async () => {
  console.log('🔍 Testing Gmail API Integration...\n');

  // Test 1: Check Gmail API Service Status
  console.log('1. Checking Gmail API Status...');
  try {
    const response = await fetch('/api/admin/google-cloud-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'gmail-api-status' })
    });
    
    const result = await response.json();
    console.log('✅ Gmail API Status:', result);
    
    if (!result.oauth_configured) {
      console.log('⚠️  OAuth credentials not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET');
      return;
    }
    
    if (!result.authorized) {
      console.log('⚠️  Gmail API not authorized. Please authorize first.');
      console.log('   Navigate to: /api/auth/gmail/authorize');
      return;
    }
    
  } catch (error) {
    console.error('❌ Error checking Gmail API status:', error);
    return;
  }

  // Test 2: Test Gmail API Connection
  console.log('\n2. Testing Gmail API Connection...');
  try {
    const response = await fetch('/api/admin/google-cloud-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        action: 'test-email',
        service: 'gmail-api'
      })
    });
    
    const result = await response.json();
    console.log('✅ Gmail API Connection Test:', result);
    
  } catch (error) {
    console.error('❌ Error testing Gmail API connection:', error);
  }

  // Test 3: Check Email Service Priority
  console.log('\n3. Checking Email Service Priority...');
  try {
    const response = await fetch('/api/admin/google-cloud-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'get-status' })
    });
    
    const result = await response.json();
    console.log('✅ Email Service Status:', result);
    console.log('   Primary Service:', result.primary);
    console.log('   Available Services:', result.available);
    
  } catch (error) {
    console.error('❌ Error checking email service status:', error);
  }

  // Test 4: Send Test Email (optional)
  const sendTestEmail = confirm('Would you like to send a test email via Gmail API?');
  if (sendTestEmail) {
    const testEmail = prompt('Enter test email address:');
    if (testEmail) {
      console.log('\n4. Sending Test Email...');
      try {
        const response = await fetch('/api/admin/google-cloud-email', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            action: 'send-test-email',
            to: testEmail,
            subject: 'Gmail API Test - Ocean Soul Sparkles',
            message: 'This is a test email sent via Gmail API to verify the integration is working correctly.'
          })
        });
        
        const result = await response.json();
        console.log('✅ Test Email Result:', result);
        
      } catch (error) {
        console.error('❌ Error sending test email:', error);
      }
    }
  }

  console.log('\n🎉 Gmail API Integration Test Complete!');
};

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  window.testGmailAPI = testGmailAPI;
  console.log('Gmail API test function loaded. Run testGmailAPI() to test the integration.');
} else if (typeof module !== 'undefined') {
  module.exports = { testGmailAPI };
}

export { testGmailAPI };
