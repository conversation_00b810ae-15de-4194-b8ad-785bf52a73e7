// Gmail OAuth authorization endpoint
import { getGmailAuthUrl } from '../../../../lib/gmail-api.js';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Generate authorization URL
    const authUrl = getGmailAuthUrl();
    
    // Redirect to Google OAuth
    return res.redirect(authUrl);
  } catch (error) {
    console.error('Gmail OAuth authorization error:', error);
    return res.status(500).json({ 
      error: 'Failed to generate authorization URL',
      details: error.message 
    });
  }
}
