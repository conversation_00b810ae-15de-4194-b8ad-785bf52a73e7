# API Authentication

This document describes how API authentication works in the Ocean Soul Sparkles application.

## Overview

API authentication in Ocean Soul Sparkles is based on JWT tokens and follows a consistent approach across all API endpoints. The authentication system provides:

- Secure token-based authentication
- Role-based access control
- Consistent error handling
- Protection against common security vulnerabilities

## Authentication Methods

The API supports the following authentication methods:

### 1. Bearer Token Authentication (Preferred)

The client includes the JWT token in the `Authorization` header:

```
Authorization: Bearer <token>
```

Example:
```javascript
fetch('/api/admin/customers', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
```

### 2. X-Auth-Token Header (Alternative)

For cross-origin requests or when the `Authorization` header is not available, the client can include the JWT token in the `X-Auth-Token` header:

```
X-Auth-Token: <token>
```

Example:
```javascript
fetch('/api/admin/customers', {
  headers: {
    'X-Auth-Token': token
  }
})
```

## Authentication Middleware

API authentication is handled by the `authenticateAdmin` middleware in `lib/admin-auth.js`. This middleware:

1. Extracts the token from the request headers
2. Verifies the token signature and expiration
3. Checks the user's role in the database
4. Adds user and role information to the request object
5. Allows or denies access based on the user's role

```javascript
// lib/admin-auth.js
export const authenticateAdmin = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Missing or invalid Authorization header'
      })
    }

    const token = authHeader.substring(7)

    // Verify token with admin client
    const adminClient = getAdminClient()
    const { data, error } = await adminClient.auth.getUser(token)

    if (error || !data.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid authentication token'
      })
    }

    // Check user role
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single()

    if (roleError || !roleData || roleData.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      })
    }

    // Add user and role to request object
    req.user = data.user
    req.role = roleData.role

    // Continue to the next middleware or route handler
    next()
  } catch (error) {
    console.error('Authentication error:', error)
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication process failed'
    })
  }
}
```

## Using Authentication in API Routes

API routes can use the `withAdminAuth` higher-order function to protect endpoints:

```javascript
// pages/api/admin/customers/index.js
import { withAdminAuth } from '@/lib/admin-auth'

const handler = async (req, res) => {
  // This code only runs if the user is authenticated and has the admin role
  const { method } = req

  switch (method) {
    case 'GET':
      // Handle GET request
      return res.status(200).json([/* customer data */])
    case 'POST':
      // Handle POST request
      return res.status(201).json({ /* created customer */ })
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Wrap the handler with authentication middleware
export default withAdminAuth(handler)
```

## Error Responses

The authentication system returns consistent error responses:

### 401 Unauthorized

Returned when:
- No token is provided
- The token is invalid or expired
- The user does not exist

```json
{
  "error": "Unauthorized",
  "message": "Invalid authentication token"
}
```

### 403 Forbidden

Returned when:
- The user does not have the required role
- The user is not authorized to access the resource

```json
{
  "error": "Forbidden",
  "message": "Insufficient permissions"
}
```

### 500 Internal Server Error

Returned when:
- An unexpected error occurs during authentication

```json
{
  "error": "Internal Server Error",
  "message": "Authentication process failed"
}
```

## Client-Side Authentication

On the client side, the `authenticatedFetch` function in `lib/auth.js` can be used to make authenticated API requests:

```javascript
// lib/auth.js
export const authenticatedFetch = async (url, options = {}) => {
  // Get the current session from Supabase
  const { data } = await supabase.auth.getSession();
  const token = data?.session?.access_token;

  // If token is about to expire, refresh it
  if (token && isTokenExpiringSoon(token)) {
    const { data: refreshData } = await supabase.auth.refreshSession();
    token = refreshData?.session?.access_token || token;
  }

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    }
  });
};

// Helper function to check if token is expiring soon
const isTokenExpiringSoon = (token) => {
  try {
    // Decode the JWT token (without verification)
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map((c) => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const { exp } = JSON.parse(jsonPayload);
    const expirationTime = exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();

    // Token is expiring soon if it expires in less than 5 minutes
    return expirationTime - currentTime < 5 * 60 * 1000;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return false;
  }
};
```

Example usage:
```javascript
import { authenticatedFetch } from '@/lib/auth'

const fetchCustomers = async () => {
  const response = await authenticatedFetch('/api/admin/customers')
  if (!response.ok) {
    throw new Error('Failed to fetch customers')
  }
  return response.json()
}
```

## Security Considerations

The API authentication system includes several security features:

1. **JWT Token Security**
   - Tokens are signed with a secret key
   - Tokens have a limited lifetime (60 minutes)
   - Tokens are automatically refreshed before expiration
   - Supabase handles token rotation securely

2. **HTTPS Only**
   - All API requests must use HTTPS
   - Cookies are set with the `secure` flag in production
   - Supabase enforces HTTPS for all API requests

3. **CORS Protection**
   - Cross-Origin Resource Sharing (CORS) is configured to allow only specific origins
   - Preflight requests are handled correctly
   - Supabase provides built-in CORS protection

4. **Row Level Security (RLS)**
   - Supabase's Row Level Security provides database-level access control
   - RLS policies ensure users can only access authorized data
   - Service role key is only used server-side for admin operations

5. **Rate Limiting**
   - API requests are rate-limited to prevent abuse
   - Failed authentication attempts are rate-limited more aggressively
   - Supabase provides built-in rate limiting for authentication endpoints

6. **Error Handling**
   - Error messages do not reveal sensitive information
   - Authentication failures are logged for monitoring
   - Consistent error responses across all endpoints

## Testing API Authentication

To test API authentication, you can use the `scripts/run-auth-tests.js` script:

```bash
node scripts/run-auth-tests.js --token=<your_auth_token>
```

This script tests various authentication scenarios:
- Valid token authentication
- Expired token handling
- Missing token handling
- Role-based access control
- Alternative authentication methods
