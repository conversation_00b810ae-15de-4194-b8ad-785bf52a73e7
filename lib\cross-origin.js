/**
 * Cross-Origin Helper
 *
 * This module provides utilities for handling cross-origin requests
 * in development environments.
 */
import { supabase } from './supabase';

// Track if we're currently refreshing the token
let isRefreshingToken = false;
let refreshPromise = null;

/**
 * Get the base URL for the current environment
 * @returns {string} The base URL
 */
export function getBaseUrl() {
  if (typeof window === 'undefined') {
    // Server-side, use environment variables
    return process.env.NEXT_PUBLIC_SITE_URL || '';
  }

  // Client-side, use window.location
  const isDev = process.env.NODE_ENV === 'development';

  if (isDev && process.env.NEXT_PUBLIC_DEV_URL) {
    return process.env.NEXT_PUBLIC_DEV_URL;
  }

  return window.location.origin;
}

/**
 * Check if cross-origin requests are allowed
 * @returns {boolean} True if cross-origin requests are allowed
 */
export function isCrossOriginAllowed() {
  return process.env.NEXT_PUBLIC_ALLOW_CROSS_ORIGIN === 'true';
}

/**
 * Get the full URL for an API endpoint
 * @param {string} path - The API path (e.g., '/api/admin/users')
 * @returns {string} The full URL
 */
export function getApiUrl(path) {
  const isDev = process.env.NODE_ENV === 'development';
  const allowCrossOrigin = isCrossOriginAllowed();

  // If we're in development and cross-origin is allowed, use the full URL
  if (isDev && allowCrossOrigin && process.env.NEXT_PUBLIC_DEV_URL) {
    return `${process.env.NEXT_PUBLIC_DEV_URL}${path}`;
  }

  return path;
}

/**
 * Create fetch options with CORS support
 * @param {Object} options - The fetch options
 * @returns {Object} The updated fetch options
 */
export function createFetchOptions(options = {}) {
  return {
    ...options,
    credentials: 'include',
    headers: {
      ...(options.headers || {}),
      'Content-Type': 'application/json',
    },
  };
}

/**
 * Get the authentication token
 * @returns {Promise<string|null>} The token or null if not available
 */
export async function getAuthToken() {
  // First try to get token from sessionStorage
  if (typeof window !== 'undefined' && window.sessionStorage) {
    try {
      const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
      if (cachedToken) {
        try {
          const tokenData = JSON.parse(cachedToken);
          if (tokenData && tokenData.token && tokenData.expires > Date.now()) {
            console.log('Using token from sessionStorage');
            return tokenData.token;
          } else if (tokenData && tokenData.token) {
            console.log('Token in sessionStorage is expired, refreshing');
            // Token is expired, try to refresh
          }
        } catch (parseError) {
          console.warn('Error parsing token from sessionStorage:', parseError);
        }
      }
    } catch (storageError) {
      console.warn('Error accessing sessionStorage:', storageError);
    }
  }

  // If no valid token in sessionStorage, try to get from current session
  try {
    const client = supabase;
    const { data: sessionData } = await client.auth.getSession();
    const token = sessionData?.session?.access_token;

    if (token) {
      console.log('Using token from current session');

      // Store token in sessionStorage for future use
      if (typeof window !== 'undefined' && window.sessionStorage) {
        try {
          const tokenCache = JSON.stringify({
            token,
            expires: Date.now() + 3600000, // 1 hour expiry
          });

          sessionStorage.setItem('oss_auth_token_cache', tokenCache);
          console.log('Token stored in sessionStorage');
        } catch (storageError) {
          console.warn('Error storing token in sessionStorage:', storageError);
        }
      }

      return token;
    }
  } catch (sessionError) {
    console.warn('Error getting session:', sessionError);
  }

  // If still no token, try to refresh
  return refreshAuthToken();
}

/**
 * Refresh the authentication token
 * @returns {Promise<string|null>} The new token or null if refresh failed
 */
export async function refreshAuthToken() {
  // If we're already refreshing, return the existing promise
  if (isRefreshingToken && refreshPromise) {
    return refreshPromise;
  }

  // Set refreshing state
  isRefreshingToken = true;

  // Create a new refresh promise
  refreshPromise = new Promise(async (resolve) => {
    try {
      console.log('Starting token refresh');
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Token refresh failed:', error);
        resolve(null);
        return;
      }

      if (data?.session?.access_token) {
        const token = data.session.access_token;
        console.log('Token refreshed successfully');

        // Store token in sessionStorage for future use
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            const tokenCache = JSON.stringify({
              token,
              expires: Date.now() + 3600000, // 1 hour expiry
            });

            sessionStorage.setItem('oss_auth_token_cache', tokenCache);
            console.log('Refreshed token stored in sessionStorage');
          } catch (storageError) {
            console.warn('Error storing refreshed token in sessionStorage:', storageError);
          }
        }

        resolve(token);
      } else {
        console.error('No token in refresh response');
        resolve(null);
      }
    } catch (err) {
      console.error('Error refreshing token:', err);
      resolve(null);
    } finally {
      console.log('Token refresh process completed');
      isRefreshingToken = false;
      refreshPromise = null;
    }
  });

  return refreshPromise;
}

/**
 * Create fetch options with authentication
 * @param {Object} options - The fetch options
 * @returns {Promise<Object>} The updated fetch options with authentication
 */
export async function createAuthFetchOptions(options = {}) {
  const token = await getAuthToken();

  const headers = {
    ...(options.headers || {}),
    'Content-Type': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return {
    ...options,
    credentials: 'include',
    headers,
  };
}

export default {
  getBaseUrl,
  isCrossOriginAllowed,
  getApiUrl,
  createFetchOptions,
  getAuthToken,
  refreshAuthToken,
  createAuthFetchOptions,
};
