.triggerBuilder {
  margin-bottom: 20px;
}

.triggerTypeSection {
  margin-bottom: 16px;
}

.configSection {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup:last-child {
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.select,
.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.select:focus,
.input:focus {
  outline: none;
  border-color: #6e8efb;
}

.helpText {
  margin-top: 4px;
  font-size: 0.8rem;
  color: #666;
}

@media (max-width: 768px) {
  .configSection {
    padding: 12px;
  }
}
