/**
 * OceanSoulSparkles Payment Gateway Test Script
 * 
 * This script helps test payment gateway integration without processing real payments.
 * It can be included in development/testing environments to simulate payment processing.
 */

// Mock PayPal SDK
class MockPayPalSDK {
  constructor() {
    console.log('🔄 Initializing Mock PayPal SDK');
    this.initialized = true;
  }
  
  // Mock PayPal Buttons component
  Buttons({ createOrder, onApprove, onError, onCancel }) {
    return {
      render: (selector) => {
        console.log(`🔄 Rendering PayPal buttons to ${selector}`);
        
        // Create mock buttons in the DOM
        const container = document.querySelector(selector);
        if (container) {
          // Clear existing content
          container.innerHTML = '';
          
          // Create test buttons
          const successBtn = document.createElement('button');
          successBtn.textContent = 'Test Successful Payment';
          successBtn.className = 'mock-paypal-button mock-success';
          successBtn.addEventListener('click', async () => {
            try {
              console.log('🔄 Creating test order...');
              const orderId = await createOrder();
              console.log(`✅ Test order created: ${orderId}`);
              
              console.log('🔄 Approving test payment...');
              await onApprove({
                orderID: orderId,
              }, {
                order: {
                  capture: async () => {
                    return {
                      id: orderId,
                      status: 'COMPLETED',
                      payer: {
                        email_address: '<EMAIL>',
                        name: {
                          given_name: 'Test',
                          surname: 'User'
                        }
                      },
                      purchase_units: [{
                        amount: {
                          value: '100.00',
                          currency_code: 'AUD'
                        }
                      }]
                    };
                  }
                }
              });
              console.log('✅ Test payment approved');
            } catch (error) {
              console.error('❌ Test payment error:', error);
              onError(error);
            }
          });
          
          const errorBtn = document.createElement('button');
          errorBtn.textContent = 'Test Failed Payment';
          errorBtn.className = 'mock-paypal-button mock-error';
          errorBtn.addEventListener('click', () => {
            console.log('🔄 Simulating payment error...');
            onError(new Error('Test payment error'));
          });
          
          const cancelBtn = document.createElement('button');
          cancelBtn.textContent = 'Test Cancelled Payment';
          cancelBtn.className = 'mock-paypal-button mock-cancel';
          cancelBtn.addEventListener('click', () => {
            console.log('🔄 Simulating payment cancellation...');
            onCancel();
          });
          
          // Add buttons to container
          container.appendChild(successBtn);
          container.appendChild(errorBtn);
          container.appendChild(cancelBtn);
          
          // Add some basic styling
          const style = document.createElement('style');
          style.textContent = `
            .mock-paypal-button {
              display: block;
              width: 100%;
              padding: 10px;
              margin: 5px 0;
              border: none;
              border-radius: 4px;
              font-weight: bold;
              cursor: pointer;
            }
            .mock-success {
              background-color: #0070ba;
              color: white;
            }
            .mock-error {
              background-color: #e74c3c;
              color: white;
            }
            .mock-cancel {
              background-color: #95a5a6;
              color: white;
            }
          `;
          document.head.appendChild(style);
        }
        
        return {
          close: () => {
            console.log('🔄 Closing PayPal buttons');
            if (container) {
              container.innerHTML = '';
            }
          }
        };
      }
    };
  }
}

// Mock Square SDK
class MockSquareSDK {
  constructor() {
    console.log('🔄 Initializing Mock Square SDK');
    this.initialized = true;
  }
  
  payments(applicationId, locationId) {
    console.log(`🔄 Initializing Square Payments with applicationId: ${applicationId}, locationId: ${locationId}`);
    
    return {
      card: async () => {
        console.log('🔄 Creating Square Card payment method');
        
        return {
          attach: async (selector) => {
            console.log(`🔄 Attaching Square Card to ${selector}`);
            
            // Create mock card form
            const container = document.querySelector(selector);
            if (container) {
              // Clear existing content
              container.innerHTML = '';
              
              // Create test card form
              const form = document.createElement('div');
              form.className = 'mock-square-card-form';
              form.innerHTML = `
                <div class="mock-card-field">
                  <label>Card Number</label>
                  <input type="text" value="4111 1111 1111 1111" readonly />
                </div>
                <div class="mock-card-field">
                  <label>Expiration</label>
                  <input type="text" value="12/25" readonly />
                </div>
                <div class="mock-card-field">
                  <label>CVV</label>
                  <input type="text" value="123" readonly />
                </div>
                <div class="mock-card-field">
                  <label>ZIP Code</label>
                  <input type="text" value="12345" readonly />
                </div>
                <div class="mock-card-note">
                  This is a test card form. In production, this would be replaced with the actual Square card form.
                </div>
              `;
              
              container.appendChild(form);
              
              // Add some basic styling
              const style = document.createElement('style');
              style.textContent = `
                .mock-square-card-form {
                  border: 1px solid #ddd;
                  padding: 15px;
                  border-radius: 4px;
                  background-color: #f9f9f9;
                }
                .mock-card-field {
                  margin-bottom: 10px;
                }
                .mock-card-field label {
                  display: block;
                  margin-bottom: 5px;
                  font-weight: bold;
                }
                .mock-card-field input {
                  width: 100%;
                  padding: 8px;
                  border: 1px solid #ddd;
                  border-radius: 4px;
                  background-color: #fff;
                }
                .mock-card-note {
                  margin-top: 15px;
                  font-size: 12px;
                  color: #666;
                  font-style: italic;
                }
              `;
              document.head.appendChild(style);
            }
          },
          
          tokenize: async () => {
            console.log('🔄 Tokenizing Square Card payment');
            
            // Simulate processing delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Return success by default
            return {
              status: 'OK',
              token: 'mock_square_token_' + Math.random().toString(36).substring(2, 10)
            };
          },
          
          destroy: async () => {
            console.log('🔄 Destroying Square Card payment method');
            
            // Clear the form
            const container = document.querySelector('#square-card-container');
            if (container) {
              container.innerHTML = '';
            }
          }
        };
      }
    };
  }
}

// Initialize mock payment SDKs
function initMockPaymentSDKs() {
  console.log('🚀 Initializing mock payment SDKs for testing');
  
  // Check if we're in a development/testing environment
  const isDevelopment = process.env.NODE_ENV === 'development' || 
                        window.location.hostname === 'localhost' ||
                        window.location.hostname === '127.0.0.1';
  
  if (isDevelopment) {
    // Mock PayPal SDK
    if (!window.paypal) {
      window.paypal = new MockPayPalSDK();
      console.log('✅ Mock PayPal SDK initialized');
    }
    
    // Mock Square SDK
    if (!window.Square) {
      window.Square = {
        payments: (applicationId, locationId) => {
          const mockSquare = new MockSquareSDK();
          return mockSquare.payments(applicationId, locationId);
        }
      };
      console.log('✅ Mock Square SDK initialized');
    }
    
    console.log('✅ Payment gateway test environment ready');
    console.log('⚠️ This is a TEST environment. No real payments will be processed.');
  } else {
    console.log('⚠️ Production environment detected. Using real payment SDKs.');
  }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { initMockPaymentSDKs };
} else {
  // Auto-initialize when included directly in a script tag
  window.addEventListener('DOMContentLoaded', initMockPaymentSDKs);
}

// Add a visual indicator for test mode
function addTestModeIndicator() {
  const indicator = document.createElement('div');
  indicator.className = 'payment-test-mode-indicator';
  indicator.innerHTML = '🧪 PAYMENT TEST MODE';
  
  const style = document.createElement('style');
  style.textContent = `
    .payment-test-mode-indicator {
      position: fixed;
      bottom: 10px;
      right: 10px;
      background-color: #f39c12;
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-weight: bold;
      font-size: 12px;
      z-index: 9999;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
  `;
  
  document.head.appendChild(style);
  document.body.appendChild(indicator);
}

// Call this function to add the indicator
if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.addEventListener('DOMContentLoaded', addTestModeIndicator);
}
