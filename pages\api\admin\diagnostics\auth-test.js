/**
 * API endpoint for testing authentication
 * This endpoint helps diagnose authentication issues by providing detailed information
 * about the current authentication state.
 *
 * Uses the new authentication middleware for consistent authentication.
 */

import { withAdminAuth } from '@/lib/admin-auth';
import supabase from '@/lib/supabase';

// Handler function that will be wrapped with authentication
async function authTestHandler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Auth test request received`);

  try {
    // Get additional session information for diagnostics
    let sessionInfo = null;
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.warn(`[${requestId}] Error getting session:`, error.message);
      } else if (data && data.session) {
        // Extract non-sensitive session info
        sessionInfo = {
          user: {
            id: data.session.user.id,
            email: data.session.user.email,
            lastSignInAt: data.session.user.last_sign_in_at
          },
          expires_at: data.session.expires_at,
          token_type: data.session.token_type
        };
      }
    } catch (sessionError) {
      console.error(`[${requestId}] Error fetching session info:`, sessionError);
    }

    // Return success with user info
    // User and role are attached to req by the middleware
    return res.status(200).json({
      status: 'success',
      message: 'Authentication successful',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.role
      },
      session: sessionInfo,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in auth test:`, error);

    return res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      error: error.message,
      requestId
    });
  }
}

// Export the handler wrapped with authentication middleware
export default withAdminAuth(authTestHandler);
