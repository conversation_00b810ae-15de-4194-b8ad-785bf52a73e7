.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease-out;
}

.toolbar.disabled {
  opacity: 0.6;
  pointer-events: none;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.selectionInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selectionCount {
  display: flex;
  align-items: center;
  gap: 8px;
}

.count {
  background: #3788d8;
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 14px;
  min-width: 24px;
  text-align: center;
}

.countLabel {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.clearSelection {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.clearSelection:hover {
  background: #f5f5f5;
  border-color: #bbb;
}

.clearSelection:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  min-width: 120px;
  justify-content: center;
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Action button color variants */
.actionButton.primary {
  background: #3788d8;
  color: white;
}

.actionButton.primary:hover:not(:disabled) {
  background: #2c6bb8;
}

.actionButton.success {
  background: #27ae60;
  color: white;
}

.actionButton.success:hover:not(:disabled) {
  background: #229954;
}

.actionButton.danger {
  background: #e74c3c;
  color: white;
}

.actionButton.danger:hover:not(:disabled) {
  background: #c0392b;
}

.actionButton.secondary {
  background: #6c757d;
  color: white;
}

.actionButton.secondary:hover:not(:disabled) {
  background: #5a6268;
}

.actionIcon {
  font-size: 16px;
}

.actionLabel {
  white-space: nowrap;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 4px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.quickStats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.statLabel {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.statValue {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.confirmationModal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
}

.modalHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background: #f5f5f5;
}

.modalContent {
  padding: 24px;
}

.warningIcon {
  font-size: 48px;
  text-align: center;
  margin-bottom: 16px;
}

.confirmationMessage {
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 16px;
  line-height: 1.5;
  text-align: center;
}

.additionalWarning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #856404;
}

.selectedBookingsList {
  margin-top: 20px;
}

.selectedBookingsList h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.bookingsList {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
}

.bookingItem {
  display: grid;
  grid-template-columns: 100px 1fr 1fr;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.bookingItem:last-child {
  border-bottom: none;
}

.bookingRef {
  font-family: monospace;
  color: #666;
  font-weight: 500;
}

.bookingCustomer {
  font-weight: 500;
  color: #2c3e50;
}

.bookingService {
  color: #666;
}

.moreBookings {
  padding: 8px 12px;
  text-align: center;
  font-style: italic;
  color: #666;
  font-size: 13px;
}

.modalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid #e1e5e9;
}

.cancelButton {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #bbb;
}

.confirmButton {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.confirmButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .selectionInfo {
    justify-content: center;
  }

  .actions {
    justify-content: center;
  }

  .quickStats {
    justify-content: center;
  }

  .actionButton {
    min-width: auto;
    flex: 1;
  }

  .confirmationModal {
    width: 95%;
    margin: 10px;
  }

  .modalActions {
    flex-direction: column;
  }

  .cancelButton,
  .confirmButton {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .actions {
    flex-direction: column;
  }

  .actionButton {
    width: 100%;
  }

  .bookingItem {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .bookingRef,
  .bookingCustomer,
  .bookingService {
    text-align: center;
  }
}

/* Focus indicators for accessibility */
.actionButton:focus,
.clearSelection:focus,
.cancelButton:focus,
.confirmButton:focus,
.closeButton:focus {
  outline: 2px solid #3788d8;
  outline-offset: 2px;
}
