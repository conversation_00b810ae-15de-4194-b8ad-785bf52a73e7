import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { validateQRCode, trackQRCodeScan } from '@/lib/qr-code-manager';
import { supabase } from '@/lib/supabase';
import styles from '@/styles/mobile/QRLanding.module.css';

/**
 * QR Code Landing Page
 * This page is displayed when customers scan an event QR code
 * Provides event information and directs to mobile booking interface
 */
export default function QRCodeLanding({ qrData, validationError, services }) {
  const router = useRouter();
  const { code } = router.query;
  const [isLoading, setIsLoading] = useState(false);
  const [scanTracked, setScanTracked] = useState(false);

  // Track QR code scan on page load
  useEffect(() => {
    if (qrData && !scanTracked) {
      trackScan();
      setScanTracked(true);
    }
  }, [qrData, scanTracked]);

  const trackScan = async () => {
    try {
      const scanData = {
        userAgent: navigator.userAgent,
        referrer: document.referrer,
        deviceType: getDeviceType(),
        timestamp: new Date().toISOString()
      };

      await trackQRCodeScan(qrData.id, scanData);
    } catch (error) {
      console.warn('Failed to track QR scan:', error);
    }
  };

  const getDeviceType = () => {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  };

  const handleStartBooking = () => {
    setIsLoading(true);
    router.push(`/mobile-booking?qr=${code}`);
  };

  // Handle validation errors
  if (validationError) {
    return (
      <div className={styles.container}>
        <Head>
          <title>QR Code - Ocean Soul Sparkles</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>⚠️</div>
          <h1 className={styles.errorTitle}>QR Code Issue</h1>
          <p className={styles.errorMessage}>{validationError.error}</p>
          
          {validationError.code === 'EVENT_EXPIRED' && (
            <div className={styles.expiredInfo}>
              <p>This event ended on {new Date(validationError.eventEndDate).toLocaleDateString()}</p>
            </div>
          )}
          
          {validationError.code === 'EVENT_NOT_STARTED' && (
            <div className={styles.notStartedInfo}>
              <p>Bookings will be available {validationError.hoursUntilStart} hours before the event starts</p>
            </div>
          )}

          <div className={styles.alternativeActions}>
            <Link href="/services" className={styles.alternativeButton}>
              View Our Services
            </Link>
            <Link href="/contact" className={styles.alternativeButton}>
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Main QR code landing page
  return (
    <div className={styles.container}>
      <Head>
        <title>{qrData.eventName} - Ocean Soul Sparkles</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content={`Book your ${qrData.eventName} appointment instantly with Ocean Soul Sparkles`} />
        <meta name="robots" content="noindex, nofollow" />
        
        {/* Mobile-specific meta tags */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="theme-color" content="#4ECDC4" />
      </Head>

      <div className={styles.header}>
        <div className={styles.logo}>
          <img src="/publicimages/bannerlogo.PNG" alt="Ocean Soul Sparkles" />
        </div>
        <div className={styles.eventBadge}>
          <span className={styles.eventLabel}>Event</span>
        </div>
      </div>

      <div className={styles.eventInfo}>
        <h1 className={styles.eventTitle}>{qrData.eventName}</h1>
        <div className={styles.eventDetails}>
          <div className={styles.eventDetail}>
            <span className={styles.detailIcon}>📍</span>
            <span className={styles.detailText}>{qrData.eventLocation}</span>
          </div>
          <div className={styles.eventDetail}>
            <span className={styles.detailIcon}>📅</span>
            <span className={styles.detailText}>
              {new Date(qrData.eventStartDate).toLocaleDateString()} - {new Date(qrData.eventEndDate).toLocaleDateString()}
            </span>
          </div>
          <div className={styles.eventDetail}>
            <span className={styles.detailIcon}>⏰</span>
            <span className={styles.detailText}>
              {new Date(qrData.eventStartDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(qrData.eventEndDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        </div>
      </div>

      <div className={styles.servicesPreview}>
        <h2 className={styles.servicesTitle}>Available Services</h2>
        <div className={styles.servicesList}>
          {services.slice(0, 3).map((service, index) => (
            <div key={service.id} className={styles.serviceItem}>
              <div className={styles.serviceIcon}>{service.icon || '🎨'}</div>
              <div className={styles.serviceInfo}>
                <h3 className={styles.serviceName}>{service.title}</h3>
                <p className={styles.servicePrice}>From ${service.pricing?.[0]?.price || service.price}</p>
              </div>
            </div>
          ))}
          {services.length > 3 && (
            <div className={styles.moreServices}>
              <span>+{services.length - 3} more services</span>
            </div>
          )}
        </div>
      </div>

      <div className={styles.bookingInfo}>
        <div className={styles.infoCard}>
          <h3 className={styles.infoTitle}>Quick & Easy Booking</h3>
          <ul className={styles.infoList}>
            <li>✨ Instant confirmation</li>
            <li>💳 Secure payment</li>
            <li>📱 Mobile-friendly</li>
            <li>⚡ No waiting in line</li>
          </ul>
        </div>
      </div>

      <div className={styles.actions}>
        <button 
          className={styles.bookButton}
          onClick={handleStartBooking}
          disabled={isLoading}
        >
          {isLoading ? (
            <span className={styles.loadingSpinner}>⏳</span>
          ) : (
            <>
              <span className={styles.buttonIcon}>🎨</span>
              Book Your Appointment Now
            </>
          )}
        </button>
        
        <div className={styles.alternativeActions}>
          <Link href="/services" className={styles.linkButton}>
            View All Services
          </Link>
          <Link href="/contact" className={styles.linkButton}>
            Contact Us
          </Link>
        </div>
      </div>

      <div className={styles.footer}>
        <p className={styles.footerText}>
          Powered by Ocean Soul Sparkles
        </p>
        <p className={styles.securityText}>
          🔒 Secure payment processing
        </p>
      </div>
    </div>
  );
}

/**
 * Server-side props to validate QR code and fetch event data
 */
export async function getServerSideProps({ params, req }) {
  const { code } = params;

  try {
    // Validate QR code
    const validationResult = await validateQRCode(code);
    
    if (!validationResult.isValid) {
      return {
        props: {
          qrData: null,
          validationError: validationResult,
          services: []
        }
      };
    }

    const qrData = validationResult.qrData;

    // Fetch available services for this event
    let services = [];
    if (qrData.availableServices && qrData.availableServices.length > 0) {
      const { data: serviceData, error: serviceError } = await supabase
        .from('services')
        .select('*')
        .in('id', qrData.availableServices)
        .eq('status', 'active');

      if (!serviceError && serviceData) {
        // Transform services to match frontend format
        services = serviceData.map(service => ({
          id: service.id,
          title: service.name,
          description: service.description,
          price: service.price,
          duration: service.duration,
          category: service.category,
          icon: getCategoryIcon(service.category),
          pricing: [{ price: service.price, duration: service.duration }]
        }));
      }
    } else {
      // If no specific services assigned, get all active services
      const { data: allServices, error: allServicesError } = await supabase
        .from('services')
        .select('*')
        .eq('status', 'active')
        .limit(6);

      if (!allServicesError && allServices) {
        services = allServices.map(service => ({
          id: service.id,
          title: service.name,
          description: service.description,
          price: service.price,
          duration: service.duration,
          category: service.category,
          icon: getCategoryIcon(service.category),
          pricing: [{ price: service.price, duration: service.duration }]
        }));
      }
    }

    return {
      props: {
        qrData,
        validationError: null,
        services
      }
    };

  } catch (error) {
    console.error('Error in QR code page:', error);
    return {
      props: {
        qrData: null,
        validationError: {
          error: 'Failed to load QR code data',
          code: 'SYSTEM_ERROR'
        },
        services: []
      }
    };
  }
}

/**
 * Get category icon for service
 */
function getCategoryIcon(category) {
  const iconMap = {
    'painting': '🎨',
    'airbrush': '🎨',
    'braiding': '💇',
    'hair': '💇',
    'glitter': '✨',
    'sparkle': '✨',
    'special': '🎭',
    'uv': '🌟'
  };
  return iconMap[category] || '🎨';
}
