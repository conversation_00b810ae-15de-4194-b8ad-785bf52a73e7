.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.statusIcon {
  font-size: 0.9rem;
}

.statusLabel {
  text-transform: capitalize;
}

/* Status-specific styles */
.pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.underReview {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.approved {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.rejected {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.unknown {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
}

/* Hover effects for interactive badges */
.statusBadge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .statusBadge {
    font-size: 0.7rem;
    padding: 3px 6px;
  }

  .statusIcon {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .statusBadge {
    font-size: 0.6rem;
    padding: 2px 4px;
  }

  .statusIcon {
    font-size: 0.7rem;
  }
}
