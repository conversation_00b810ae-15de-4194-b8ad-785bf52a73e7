import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { period = 'month', start_date, end_date, limit = 10 } = req.query

  try {
    // Calculate date range based on period
    const today = new Date()
    let startDate = new Date()
    let endDate = new Date()

    if (start_date && end_date) {
      // Custom date range
      startDate = new Date(start_date)
      endDate = new Date(end_date)
    } else {
      // Predefined periods
      switch (period) {
        case 'week':
          startDate.setDate(today.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(today.getMonth() - 1)
          break
        case 'quarter':
          startDate.setMonth(today.getMonth() - 3)
          break
        case 'year':
          startDate.setFullYear(today.getFullYear() - 1)
          break
        default:
          startDate.setMonth(today.getMonth() - 1) // Default to last month
      }
    }

    // Format dates for Supabase queries
    const formattedStartDate = startDate.toISOString()
    const formattedEndDate = endDate.toISOString()

    // Get client from Supabase
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available for analytics campaigns.");
      return res.status(503).json({ error: 'Service temporarily unavailable. Please try again later.' });
    }

    // Get campaigns
    const { data: campaigns, error: campaignsError } = await client
      .from('marketing_campaigns')
      .select('*')
      .gte('created_at', formattedStartDate)
      .lte('created_at', formattedEndDate)
      .order('created_at', { ascending: false })

    if (campaignsError) throw campaignsError

    // Get campaign messages
    const { data: messages, error: messagesError } = await client
      .from('campaign_messages')
      .select(`
        id,
        campaign_id,
        status,
        message_type,
        sent_date,
        campaigns:campaign_id (name, campaign_type)
      `)
      .gte('sent_date', formattedStartDate)
      .lte('sent_date', formattedEndDate)

    if (messagesError) throw messagesError

    // Calculate campaign performance metrics
    const campaignPerformance = []

    if (campaigns && campaigns.length > 0) {
      for (const campaign of campaigns) {
        // Get messages for this campaign
        const campaignMessages = messages ? messages.filter(msg => msg.campaign_id === campaign.id) : []

        // Calculate metrics
        const totalMessages = campaignMessages.length
        const sentMessages = campaignMessages.filter(msg => msg.status === 'sent').length
        const openedMessages = campaignMessages.filter(msg => msg.opened).length
        const clickedMessages = campaignMessages.filter(msg => msg.clicked).length

        campaignPerformance.push({
          id: campaign.id,
          name: campaign.name,
          status: campaign.status,
          campaign_type: campaign.campaign_type,
          start_date: campaign.start_date,
          end_date: campaign.end_date,
          metrics: {
            total_messages: totalMessages,
            sent: sentMessages,
            opened: openedMessages,
            clicked: clickedMessages,
            open_rate: calculatePercentage(openedMessages, sentMessages),
            click_rate: calculatePercentage(clickedMessages, openedMessages),
            click_through_rate: calculatePercentage(clickedMessages, sentMessages)
          }
        })
      }
    }

    // Sort campaigns by performance (click-through rate)
    campaignPerformance.sort((a, b) =>
      b.metrics.click_through_rate - a.metrics.click_through_rate
    )

    // Calculate overall metrics
    const totalCampaigns = campaigns ? campaigns.length : 0
    const activeCampaigns = campaigns
      ? campaigns.filter(c => c.status === 'active').length
      : 0

    const totalMessages = messages ? messages.length : 0
    const sentMessages = messages
      ? messages.filter(msg => msg.status === 'sent').length
      : 0
    const openedMessages = messages
      ? messages.filter(msg => msg.opened).length
      : 0
    const clickedMessages = messages
      ? messages.filter(msg => msg.clicked).length
      : 0

    // Calculate campaign type distribution
    const campaignTypeDistribution = {}
    campaigns?.forEach(campaign => {
      campaignTypeDistribution[campaign.campaign_type] =
        (campaignTypeDistribution[campaign.campaign_type] || 0) + 1
    })

    // Prepare response
    const response = {
      period,
      date_range: {
        start_date: formattedStartDate,
        end_date: formattedEndDate
      },
      summary: {
        total_campaigns: totalCampaigns,
        active_campaigns: activeCampaigns,
        total_messages: totalMessages,
        sent_messages: sentMessages,
        opened_messages: openedMessages,
        clicked_messages: clickedMessages,
        open_rate: calculatePercentage(openedMessages, sentMessages),
        click_rate: calculatePercentage(clickedMessages, openedMessages),
        click_through_rate: calculatePercentage(clickedMessages, sentMessages)
      },
      campaign_types: campaignTypeDistribution,
      top_campaigns: campaignPerformance.slice(0, parseInt(limit))
    }

    return res.status(200).json(response)
  } catch (error) {
    console.error('Error fetching campaign analytics:', error)
    return res.status(500).json({ error: 'Failed to fetch campaign analytics' })
  }
}

// Helper function to calculate percentage
function calculatePercentage(numerator, denominator) {
  if (!denominator) return 0
  return Math.round((numerator / denominator) * 100)
}
