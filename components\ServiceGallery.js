import { useState, useEffect } from 'react';
import styles from '@/styles/ServiceGallery.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * ServiceGallery component with masonry layout and lightbox functionality
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Gallery section title
 * @param {string} props.subtitle - Gallery section subtitle
 * @param {Array} props.images - Array of image objects with src, alt, and category properties
 * @returns {JSX.Element}
 */
const ServiceGallery = ({
  title = 'Our Work',
  subtitle = 'Explore our creative portfolio of recent projects',
  images = [],
  ...props
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');
  const [columnsCount, setColumnsCount] = useState(3);
  
  // Default images if none provided
  const defaultImages = [
    {
      src: '/images/services/gallery/face-paint-1.jpg',
      alt: 'Colorful butterfly face paint design',
      category: 'painting'
    },
    {
      src: '/images/services/gallery/braids-1.jpg',
      alt: 'Festival braids with colorful extensions',
      category: 'hair'
    },
    {
      src: '/images/services/gallery/airbrush-1.jpg',
      alt: 'Airbrush body art design with glitter',
      category: 'painting'
    },
    {
      src: '/images/services/gallery/glitter-1.jpg',
      alt: 'Festival glitter design on face',
      category: 'sparkle'
    },
    {
      src: '/images/services/gallery/face-paint-2.jpg',
      alt: 'Superhero face paint for kids',
      category: 'painting'
    },
    {
      src: '/images/services/gallery/braids-2.jpg',
      alt: 'Intricate braiding pattern with beads',
      category: 'hair'
    },
    {
      src: '/images/services/gallery/airbrush-2.jpg',
      alt: 'Airbrush tattoo design for arm',
      category: 'painting'
    },
    {
      src: '/images/services/gallery/glitter-2.jpg',
      alt: 'Sparkle design with gems application',
      category: 'sparkle'
    }
  ];
  
  const galleryImages = images.length > 0 ? images : defaultImages;
  
  // Filter images based on active category
  const filteredImages = activeFilter === 'all' 
    ? galleryImages 
    : galleryImages.filter(image => image.category === activeFilter);
  
  // Get unique categories for filter
  const categories = ['all', ...new Set(galleryImages.map(image => image.category))];
  
  // Create masonry layout columns
  const createColumns = (imagesList, columnCount) => {
    const columns = Array.from({ length: columnCount }, () => []);
    
    imagesList.forEach((image, index) => {
      columns[index % columnCount].push(image);
    });
    
    return columns;
  };
  
  // Responsive column count
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 600) {
        setColumnsCount(1);
      } else if (window.innerWidth < 900) {
        setColumnsCount(2);
      } else {
        setColumnsCount(3);
      }
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Create columns for the masonry layout
  const columns = createColumns(filteredImages, columnsCount);
  
  return (
    <section className={styles.gallerySection} {...props}>
      <AnimatedSection animation="fade-up">
        <h2 className={styles.galleryTitle}>{title}</h2>
        <p className={styles.gallerySubtitle}>{subtitle}</p>
      </AnimatedSection>
      
      <div className={styles.categoryFilters}>
        {categories.map((category, index) => (
          <button
            key={index}
            className={`${styles.filterButton} ${activeFilter === category ? styles.activeFilter : ''}`}
            onClick={() => setActiveFilter(category)}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </button>
        ))}
      </div>
      
      <div className={styles.galleryGrid} style={{ gridTemplateColumns: `repeat(${columnsCount}, 1fr)` }}>
        {columns.map((column, columnIndex) => (
          <div key={columnIndex} className={styles.galleryColumn}>
            {column.map((image, imageIndex) => (
              <AnimatedSection
                key={imageIndex}
                animation="fade-in"
                delay={100 * (columnIndex + imageIndex)}
              >
                <div 
                  className={styles.galleryItem}
                  onClick={() => setSelectedImage(image)}
                >
                  <img 
                    src={image.src} 
                    alt={image.alt} 
                    className={styles.galleryImage} 
                  />
                  <div className={styles.galleryItemOverlay}>
                    <span className={styles.viewPrompt}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                      </svg>
                      View
                    </span>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        ))}
      </div>
      
      {/* Lightbox Modal */}
      {selectedImage && (
        <div className={styles.lightbox} onClick={() => setSelectedImage(null)}>
          <div className={styles.lightboxContent} onClick={e => e.stopPropagation()}>
            <img src={selectedImage.src} alt={selectedImage.alt} className={styles.lightboxImage} />
            <div className={styles.lightboxCaption}>{selectedImage.alt}</div>
            <button className={styles.closeButton} onClick={() => setSelectedImage(null)}>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default ServiceGallery; 