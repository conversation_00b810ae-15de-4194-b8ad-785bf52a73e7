.main {
  width: 100%;
  margin: 0 auto;
  min-height: 70vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.errorSection {
  text-align: center;
  max-width: 600px;
  margin-bottom: 3rem;
}

.title {
  font-size: 8rem;
  color: var(--primary-color);
  margin-bottom: 0;
  line-height: 1;
}

.subtitle {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.description {
  font-size: 1.2rem;
  color: var(--light-text-color);
  margin-bottom: 2rem;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.outlineButton {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.outlineButton:hover {
  background-color: var(--primary-color);
  color: white;
}

.suggestionsSection {
  text-align: center;
}

.suggestionsSection h3 {
  font-size: 1.3rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.suggestionLinks {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

.suggestionLink {
  color: var(--primary-color);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.suggestionLink:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .title {
    font-size: 6rem;
  }
  
  .subtitle {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 4rem;
  }
  
  .subtitle {
    font-size: 1.5rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
}
