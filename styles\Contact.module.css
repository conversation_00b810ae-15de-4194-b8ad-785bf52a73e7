.main {
  width: 100%;
  margin: 0 auto;
}

/* Hero section */
.hero {
  text-align: center;
  padding: 5rem 2rem;
  background-color: var(--background-color);
  background-image: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('/images/contact-hero.jpg');
  background-size: cover;
  background-position: center;
  margin-bottom: 3rem;
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: 3rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.description {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto;
}

/* Contact section */
.contactSection {
  max-width: var(--max-width);
  margin: 0 auto 4rem;
  padding: 0 2rem;
}

.contactContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
}

.contactInfo {
  flex: 1;
  min-width: 300px;
}

.contactInfo h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.contactInfo p {
  color: var(--light-text-color);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.contactDetails {
  margin-top: 3rem;
}

.contactItem {
  margin-bottom: 2rem;
}

.contactItem h3 {
  font-size: 1.3rem;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.contactItem p {
  margin-bottom: 0.5rem;
}

.contactItem a {
  color: var(--primary-color);
  text-decoration: none;
}

.contactItem a:hover {
  text-decoration: underline;
}

.socialLinks {
  display: flex;
  gap: 1rem;
}

.socialLink {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.socialLink:hover {
  color: var(--secondary-color);
}

.formSection {
  flex: 2;
  min-width: 300px;
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.formSection h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.contactForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup:nth-child(5) {
  grid-column: span 2;
}

.formInput, .formTextarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 1rem;
}

.formInput:focus, .formTextarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.formTextarea {
  min-height: 150px;
  resize: vertical;
}

.submitButton {
  grid-column: span 2;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.submitButton:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.submitButton:disabled {
  background-color: var(--light-text-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.errorMessage {
  grid-column: span 2;
  color: var(--error-color);
  margin-bottom: 1rem;
}

.successMessage {
  text-align: center;
  padding: 2rem;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.successMessage h3 {
  color: var(--success-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.successMessage p {
  margin-bottom: 2rem;
  color: var(--light-text-color);
}

.button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
}

.button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

/* Booking CTA section */
.bookingCta {
  background-color: #f8f8f8;
  padding: 4rem 2rem;
  text-align: center;
  margin-bottom: 4rem;
}

.bookingCta h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.bookingCta p {
  color: var(--light-text-color);
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButton {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.ctaButton:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

/* Responsive styles */
@media (max-width: 992px) {
  .contactContainer {
    flex-direction: column;
  }

  .contactForm {
    grid-template-columns: 1fr;
  }

  .formGroup:nth-child(5) {
    grid-column: span 1;
  }

  .submitButton, .errorMessage {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .hero {
    padding: 4rem 1rem;
  }

  .contactSection, .bookingCta {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .formSection {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .button, .ctaButton, .submitButton {
    width: 100%;
  }

  .formSection {
    padding: 1rem;
  }
}
