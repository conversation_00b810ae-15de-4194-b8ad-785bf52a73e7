const fs = require('fs');
const path = require('path');

// Load the service data
const serviceData = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'hair-treatment-service.json'), 'utf8'));

async function addHairTreatmentService() {
  console.log('🔍 Adding Hair Treatment Services...');
  
  try {
    // You'll need to set your admin auth token here
    const authToken = process.env.ADMIN_AUTH_TOKEN;
    
    if (!authToken) {
      console.error('❌ ADMIN_AUTH_TOKEN environment variable is required');
      process.exit(1);
    }

    const response = await fetch('http://localhost:3000/api/admin/services', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(serviceData)
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorData}`);
    }

    const result = await response.json();
    console.log('✅ Hair Treatment Services created successfully!');
    console.log('Service ID:', result.service?.id);
    console.log('Pricing Tiers:', result.service?.pricing_tiers?.length || 0);
    
    return result;
    
  } catch (error) {
    console.error('❌ Error creating Hair Treatment Services:', error.message);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  addHairTreatmentService()
    .then(() => {
      console.log('🎉 Hair Treatment Services setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error);
      process.exit(1);
    });
}

module.exports = { addHairTreatmentService };
