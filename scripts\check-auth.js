/**
 * Authentication Configuration Check for Ocean Soul Sparkles
 * 
 * This script performs a comprehensive check of the authentication
 * system configuration and identifies potential issues that could
 * cause 401 errors.
 */

const fs = require('fs');
const path = require('path');
const checkEnvironment = require('./check-env');

// Debug mode
const DEBUG = process.env.DEBUG === 'auth';

// Helper functions
function log(message) {
  console.log(message);
}

function debug(message) {
  if (DEBUG) {
    console.log(`[DEBUG] ${message}`);
  }
}

function warn(message) {
  console.warn(`[WARNING] ${message}`);
}

function error(message) {
  console.error(`[ERROR] ${message}`);
}

function success(message) {
  console.log(`[SUCCESS] ${message}`);
}

/**
 * Check the presence and validity of .env files
 */
function checkEnvFiles() {
  log('\nChecking environment configuration files...');
  
  // Check if .env.local exists
  const envLocalPath = path.resolve(process.cwd(), '.env.local');
  const hasEnvLocal = fs.existsSync(envLocalPath);
  
  if (!hasEnvLocal) {
    error('Missing .env.local file. This file is necessary for authentication.');
    warn('Create a .env.local file based on the .env.local.example template.');
    return false;
  }
  
  // Check environment variables
  const envResult = checkEnvironment(true); // silent mode
  
  if (!envResult.success) {
    error('Environment configuration is incomplete:');
    
    envResult.missing.forEach(item => {
      error(` - Missing required variable: ${item.name}`);
    });
    
    envResult.validationErrors.forEach(item => {
      error(` - Invalid variable: ${item.name} - ${item.errorMsg}`);
    });
    
    return false;
  }
  
  success('Environment configuration appears to be valid.');
  return true;
}

/**
 * Check NextJS configuration
 */
function checkNextJsConfig() {
  log('\nChecking Next.js configuration...');
  
  const configPath = path.resolve(process.cwd(), 'next.config.js');
  
  if (!fs.existsSync(configPath)) {
    error('Missing next.config.js file.');
    return false;
  }
  
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  // Check for redirects and rewrites that might affect authentication
  const hasRedirects = configContent.includes('redirects');
  const hasRewrites = configContent.includes('rewrites');
  
  if (hasRedirects) {
    warn('Found redirects in next.config.js - ensure they don\'t interfere with authentication endpoints.');
  }
  
  if (hasRewrites) {
    warn('Found rewrites in next.config.js - ensure they don\'t interfere with authentication endpoints.');
  }
  
  success('Next.js configuration appears to be valid.');
  return true;
}

/**
 * Check API route configuration
 */
function checkApiRoutes() {
  log('\nChecking API route configuration...');
  
  const apiDir = path.resolve(process.cwd(), 'pages/api');
  
  if (!fs.existsSync(apiDir)) {
    error('API directory not found at pages/api.');
    return false;
  }
  
  // Check for auth middleware usage
  let foundAuthImports = 0;
  let totalApiFiles = 0;
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        scanDirectory(itemPath);
      } else if (stats.isFile() && itemPath.endsWith('.js')) {
        totalApiFiles++;
        const content = fs.readFileSync(itemPath, 'utf8');
        if (content.includes('withAuth') && content.includes('api-auth')) {
          foundAuthImports++;
          debug(`Found auth middleware in: ${itemPath}`);
        }
      }
    }
  }
  
  scanDirectory(apiDir);
  
  debug(`Found ${foundAuthImports} files using auth middleware out of ${totalApiFiles} total API files`);
  
  if (foundAuthImports === 0) {
    warn('No API routes found using authentication middleware.');
    warn('Ensure protected routes use withAuth from api-auth.js');
  } else {
    success(`Found ${foundAuthImports} API routes using authentication middleware.`);
  }
  
  return true;
}

/**
 * Check middleware configuration
 */
function checkMiddleware() {
  log('\nChecking middleware configuration...');
  
  const middlewarePath = path.resolve(process.cwd(), 'middleware.js');
  
  if (!fs.existsSync(middlewarePath)) {
    warn('No middleware.js file found. This might be fine if not using Edge middleware.');
    return true;
  }
  
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  // Check for API routes matcher 
  if (!content.includes('/api/admin/') && !content.includes('/api/auth/')) {
    warn('Middleware does not appear to be configured for API authentication paths.');
    warn('Consider adding matchers for /api/admin/ and /api/auth/ paths.');
  }
  
  // Check for authentication headers
  if (!content.includes('Authorization')) {
    warn('Middleware does not appear to handle Authorization headers.');
  }
  
  success('Middleware configuration appears to be set up for authentication.');
  return true;
}

/**
 * Run all checks
 */
function runAuthChecks() {
  log('\n🔐 Ocean Soul Sparkles Authentication Configuration Check');
  log('==================================================');
  
  const checks = [
    { name: 'Environment Files', fn: checkEnvFiles },
    { name: 'Next.js Config', fn: checkNextJsConfig },
    { name: 'API Routes', fn: checkApiRoutes },
    { name: 'Middleware', fn: checkMiddleware }
  ];
  
  const results = {};
  let allPassed = true;
  
  for (const check of checks) {
    const result = check.fn();
    results[check.name] = result;
    if (!result) allPassed = false;
  }
  
  log('\n==================================================');
  log('Authentication Configuration Check Summary:');
  
  for (const [name, result] of Object.entries(results)) {
    if (result) {
      success(`✅ ${name}: Passed`);
    } else {
      error(`❌ ${name}: Failed`);
    }
  }
  
  log('\n==================================================');
  
  if (allPassed) {
    success('Authentication configuration appears to be valid.');
    log('\nIf you are still experiencing 401 errors:');
    log('1. Check browser console for specific error messages');
    log('2. Verify that tokens are being properly passed in requests');
    log('3. Ensure the Supabase service is running and accessible');
    log('4. Test both development and production environments');
  } else {
    error('Some authentication configuration checks failed.');
    log('\nPlease fix the issues above to resolve 401 unauthorized errors.');
  }
}

// Run the checks
runAuthChecks();
