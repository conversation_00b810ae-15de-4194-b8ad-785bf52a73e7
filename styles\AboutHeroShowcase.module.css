.aboutShowcase {
  position: relative;
  height: 100vh;
  min-height: 800px;
  width: 100%;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
  color: white;
}

.overlayGradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.4), rgba(85, 98, 255, 0.4)); /* Added translucent gradient */
  z-index: -1;
}

.sparkleContainer { /* New container for sparkles */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden; /* Ensure sparkles don't go outside bounds */
  z-index: 0; /* Above background, below content */
}

.sparkle { /* Style for individual sparkles */
  position: absolute;
  font-size: 20px; /* Adjust size as needed */
  color: rgba(255, 255, 255, 0.9); /* Increased opacity for better visibility */
  user-select: none; /* Prevent text selection */
  pointer-events: none; /* Allow mouse events to pass through */
  will-change: transform, opacity;
  transition: transform 0.2s ease-out, opacity 0.5s ease-out; /* Smooth movement and fading */
  text-shadow: 0 0 6px rgba(0, 0, 0, 0.7), /* Darker shadow for contrast */
               0 0 10px rgba(255, 255, 255, 0.7),
               0 0 15px rgba(255, 215, 0, 0.6); /* Gold glow */
}

.showcaseContent {
  max-width: 1200px;
  width: 100%;
  text-align: center;
  z-index: 2;
  padding: 2rem;
}

.showcaseTitle {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5); /* Enhanced text shadow for readability */
  position: relative;
  display: inline-block;
  color: #fff; /* Ensure text is white or a light color */
}

.titleUnderline {
  height: 3px;
  width: 80px;
  background: linear-gradient(to right, #FFC107, #FF5722);
  margin: 0 auto 2rem;
  position: relative;
  border-radius: 3px;
}

.titleUnderline::before,
.titleUnderline::after {
  content: '';
  position: absolute;
  top: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #FF5722;
  animation: pulseEffect 2s infinite;
}

.titleUnderline::before {
  left: -4px;
  animation-delay: 0s;
}

.titleUnderline::after {
  right: -4px;
  animation-delay: 0.5s;
}

@keyframes pulseEffect {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.showcaseSubtitle {
  font-size: 1.5rem;
  max-width: 800px;
  margin: 0 auto 4rem;
  line-height: 1.6;
  text-shadow: 0 1px 8px rgba(0, 0, 0, 0.6); /* Enhanced text shadow for readability */
  color: rgba(255, 255, 255, 0.95); /* Ensure high contrast */
}

.teamShowcase {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 4rem;
}

.teamMemberCard {
  flex: 0 0 280px;
  background: rgba(0, 0, 0, 0.2); /* More translucent background for cards */
  backdrop-filter: blur(8px) saturate(120%); /* Adjusted blur for better card visibility */
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25); /* Slightly stronger shadow */
  border: 1px solid rgba(255, 255, 255, 0.25); /* Brighter border */
  position: relative;
  overflow: hidden;
}

.teamMemberCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  transition: all 0.5s ease;
}

.teamMemberCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.35);
  background: rgba(0, 0, 0, 0.3); /* More translucent hover state for cards */
}

.teamMemberCard:hover::before {
  left: 100%;
  transition: all 0.8s ease;
}

.memberImageContainer {
  margin-bottom: 1.5rem;
}

.memberImageBorder {
  width: 180px; /* Increased from 140px */
  height: 180px; /* Increased from 140px */
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  position: relative;
  background: linear-gradient(135deg, #FFC107, #FF5722, #F44336);
  padding: 3px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.memberImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: transform 0.5s ease;
}

.teamMemberCard:hover .memberImage {
  transform: scale(1.1);
}

.memberInfo {
  text-align: center;
}

.memberName {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
  color: #ffffff; /* Ensure member name is white */
  text-shadow: 0 1px 3px rgba(0,0,0,0.4);
}

.memberRole {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.85); /* Ensure role text is light */
  font-weight: 500;
  position: relative;
  display: inline-block;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.memberRole::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: linear-gradient(to right, #FFC107, #FF5722);
  border-radius: 2px;
}

.memberDescription {
  color: rgba(255, 255, 255, 0.8); /* Ensure description text is light */
  line-height: 1.6;
  font-size: 0.95rem;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.teamNote {
  max-width: 800px;
  margin: 0 auto 3rem;
  background: rgba(0, 0, 0, 0.25); /* More translucent background */
  backdrop-filter: blur(6px) saturate(110%);
  border-radius: 12px;
  padding: 1.5rem 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.teamNote p {
  color: rgba(255, 255, 255, 0.95); /* Ensure text is light */
  line-height: 1.6;
  font-size: 1.1rem;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.aboutCta {
  margin-top: 2rem;
}

.ctaText {
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.95); /* Ensure text is light */
  text-shadow: 0 1px 5px rgba(0,0,0,0.5);
}

.scrollIndicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  transition: opacity 0.3s ease;
  opacity: 0.8;
  cursor: pointer;
}

.scrollIndicator:hover {
  opacity: 1;
}

.scrollCircle {
  width: 30px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  margin-bottom: 8px;
  position: relative;
}

.scrollCircle::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: scrollDown 2s infinite;
}

@keyframes scrollDown {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  80% {
    transform: translateX(-50%) translateY(24px);
    opacity: 0;
  }
  81% {
    transform: translateX(-50%) translateY(0);
    opacity: 0;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .showcaseTitle {
    font-size: 3rem;
  }

  .showcaseSubtitle {
    font-size: 1.3rem;
  }
}

@media (max-width: 992px) {
  .aboutShowcase {
    min-height: 700px;
  }

  .showcaseTitle {
    font-size: 2.7rem;
  }

  .showcaseSubtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
  }

  .teamShowcase {
    gap: 1.5rem;
  }

  .teamMemberCard {
    flex: 0 0 250px;
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .aboutShowcase {
    height: auto;
    padding: 6rem 1rem;
  }

  .showcaseTitle {
    font-size: 2.3rem;
  }

  .showcaseSubtitle {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }

  .teamMemberCard {
    flex: 0 0 100%;
    max-width: 320px;
  }

  .scrollIndicator {
    display: none;
  }
}

@media (max-width: 480px) {
  .showcaseTitle {
    font-size: 2rem;
  }

  .showcaseSubtitle {
    font-size: 1rem;
  }

  .memberImageBorder {
    width: 150px; /* Increased from 120px */
    height: 150px; /* Increased from 120px */
  }

  .memberName {
    font-size: 1.3rem;
  }

  .ctaText {
    font-size: 1.1rem;
  }
}