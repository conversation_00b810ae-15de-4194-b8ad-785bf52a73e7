import { useState, useEffect } from 'react'
import GlitterExplosion from '@/components/GlitterExplosion'
import styles from '@/styles/admin/users/ArtistBraiderApplicationForm.module.css'

export default function ArtistBraiderApplicationForm({ onSubmit, onCancel, initialData = null, tokenUser = null }) {
  // Get application type from initialData (set by URL path)
  const applicationType = initialData?.application_type || 'artist'

  const [formData, setFormData] = useState({
    application_type: applicationType,
    experience_years: '',
    portfolio_url: '',
    availability_preferences: {
      weekdays: [],
      weekends: false,
      evenings: false,
      flexible: false
    },
    service_specializations: [],
    previous_experience: '',
    references: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({})
  const [showConfetti, setShowConfetti] = useState(false)

  // Service specialization options
  const serviceOptions = {
    artist: [
      'Makeup Application',
      'Eyebrow Styling',
      'Eyelash Extensions',
      'Facial Treatments',
      'Nail Art',
      'Special Event Makeup',
      'Bridal Makeup',
      'Photography Makeup'
    ],
    braider: [
      'Box Braids',
      'Cornrows',
      'French Braids',
      'Dutch Braids',
      'Fishtail Braids',
      'Twist Braids',
      'Protective Styles',
      'Hair Extensions',
      'Wedding Hairstyles',
      'Special Occasion Styles'
    ]
  }

  const weekdayOptions = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'
  ]

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        // Ensure arrays are properly initialized
        service_specializations: initialData.service_specializations || [],
        availability_preferences: {
          weekdays: [],
          weekends: false,
          evenings: false,
          flexible: false,
          ...initialData.availability_preferences
        }
      }))
    }
  }, [initialData])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    
    if (name.startsWith('availability_')) {
      const availabilityField = name.replace('availability_', '')
      setFormData(prev => ({
        ...prev,
        availability_preferences: {
          ...prev.availability_preferences,
          [availabilityField]: type === 'checkbox' ? checked : value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }))
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleWeekdayChange = (day) => {
    setFormData(prev => {
      const currentWeekdays = prev.availability_preferences?.weekdays || []
      return {
        ...prev,
        availability_preferences: {
          ...prev.availability_preferences,
          weekdays: currentWeekdays.includes(day)
            ? currentWeekdays.filter(d => d !== day)
            : [...currentWeekdays, day]
        }
      }
    })
  }

  const handleSpecializationChange = (specialization) => {
    setFormData(prev => {
      const currentSpecializations = prev.service_specializations || []
      return {
        ...prev,
        service_specializations: currentSpecializations.includes(specialization)
          ? currentSpecializations.filter(s => s !== specialization)
          : [...currentSpecializations, specialization]
      }
    })
  }

  const validateForm = () => {
    const newErrors = {}

    // Application type is now automatically set from URL, no validation needed

    if (!formData.experience_years || formData.experience_years < 0) {
      newErrors.experience_years = 'Please enter valid years of experience'
    }

    if (formData.portfolio_url && !isValidUrl(formData.portfolio_url)) {
      newErrors.portfolio_url = 'Please enter a valid URL'
    }

    if (!formData.service_specializations || formData.service_specializations.length === 0) {
      newErrors.service_specializations = 'Please select at least one specialization'
    }

    if (!formData.previous_experience || formData.previous_experience.trim().length < 50) {
      newErrors.previous_experience = 'Please provide at least 50 characters describing your experience'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (string) => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // Trigger confetti animation
    setShowConfetti(true)

    setLoading(true)
    try {
      await onSubmit({
        ...formData,
        user_id: tokenUser?.id
      })
    } catch (error) {
      console.error('Error submitting application:', error)
    } finally {
      setLoading(false)
    }
  }

  const currentSpecializations = serviceOptions[applicationType] || []

  // Role-specific content
  const roleContent = {
    artist: {
      title: "Join Our Creative Team as a Beauty Artist!",
      description: "Ready to sparkle with us? Share your beauty expertise and become part of the Ocean Soul Sparkles family.",
      icon: "🎨"
    },
    braider: {
      title: "Join Our Creative Team as a Hair Braider!",
      description: "Ready to sparkle with us? Share your braiding skills and become part of the Ocean Soul Sparkles family.",
      icon: "💫"
    }
  }

  const currentRoleContent = roleContent[applicationType] || roleContent.artist

  return (
    <div className={styles.formContainer}>
      {/* Confetti Animation */}
      {showConfetti && (
        <GlitterExplosion
          active={showConfetti}
          particleCount={100}
          colors={['#4ECDC4', '#FF6B6B', '#FFE66D', '#FFD700', '#1A73E8', '#9C27B0']}
          duration={2000}
          onComplete={() => setShowConfetti(false)}
        />
      )}

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.header}>
          <div className={styles.headerIcon}>{currentRoleContent.icon}</div>
          <h2>{currentRoleContent.title}</h2>
          <p>{currentRoleContent.description}</p>
        </div>

        {/* Application type is now automatically determined from URL - no user selection needed */}

        <div className={styles.formGroup}>
          <label htmlFor="experience_years" className={styles.requiredLabel}>
            Years of Experience <span className={styles.required}>*</span>
          </label>
          <div className={styles.inputWrapper}>
            <input
              type="number"
              id="experience_years"
              name="experience_years"
              value={formData.experience_years}
              onChange={handleChange}
              className={styles.input}
              min="0"
              max="50"
              required
              placeholder="Enter years of experience"
            />
            <span className={styles.inputIcon}>📅</span>
          </div>
          {errors.experience_years && <div className={styles.error}>{errors.experience_years}</div>}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="portfolio_url">
            Portfolio URL <span className={styles.optional}>(Optional)</span>
          </label>
          <div className={styles.inputWrapper}>
            <input
              type="url"
              id="portfolio_url"
              name="portfolio_url"
              value={formData.portfolio_url}
              onChange={handleChange}
              className={styles.input}
              placeholder="https://your-portfolio.com"
            />
            <span className={styles.inputIcon}>🔗</span>
          </div>
          {errors.portfolio_url && <div className={styles.error}>{errors.portfolio_url}</div>}
        </div>

        <div className={styles.formGroup}>
          <label>
            {applicationType === 'artist' ? 'Beauty Service Specializations' : 'Hair Braiding Specializations'} <span className={styles.required}>*</span>
          </label>
          <div className={styles.checkboxGrid}>
            {currentSpecializations.map(specialization => (
              <label key={specialization} className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={formData.service_specializations?.includes(specialization) || false}
                  onChange={() => handleSpecializationChange(specialization)}
                />
                {specialization}
              </label>
            ))}
          </div>
          {errors.service_specializations && <div className={styles.error}>{errors.service_specializations}</div>}
        </div>

        <div className={styles.formGroup}>
          <label>Availability Preferences</label>
          
          <div className={styles.availabilitySection}>
            <h4>Weekdays</h4>
            <div className={styles.checkboxGrid}>
              {weekdayOptions.map(day => (
                <label key={day} className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.availability_preferences?.weekdays?.includes(day) || false}
                    onChange={() => handleWeekdayChange(day)}
                  />
                  {day}
                </label>
              ))}
            </div>
          </div>

          <div className={styles.availabilityOptions}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="availability_weekends"
                checked={formData.availability_preferences?.weekends || false}
                onChange={handleChange}
              />
              Available weekends
            </label>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="availability_evenings"
                checked={formData.availability_preferences?.evenings || false}
                onChange={handleChange}
              />
              Available evenings (after 6 PM)
            </label>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="availability_flexible"
                checked={formData.availability_preferences?.flexible || false}
                onChange={handleChange}
              />
              Flexible schedule
            </label>
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="previous_experience">Previous Experience *</label>
          <textarea
            id="previous_experience"
            name="previous_experience"
            value={formData.previous_experience}
            onChange={handleChange}
            className={styles.textarea}
            rows="4"
            placeholder="Please describe your previous experience, training, and qualifications..."
            required
          />
          <div className={styles.charCount}>
            {formData.previous_experience?.length || 0} characters (minimum 50)
          </div>
          {errors.previous_experience && <div className={styles.error}>{errors.previous_experience}</div>}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="references">References</label>
          <textarea
            id="references"
            name="references"
            value={formData.references}
            onChange={handleChange}
            className={styles.textarea}
            rows="3"
            placeholder="Please provide contact information for professional references..."
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            className={styles.cancelButton}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={loading}
          >
            {loading ? 'Submitting...' : 'Submit Application'}
          </button>
        </div>
      </form>
    </div>
  )
}
