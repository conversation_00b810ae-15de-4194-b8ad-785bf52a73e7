import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import authTokenManager from '@/lib/auth-token-manager';
import Modal from '@/components/admin/Modal';
import styles from '@/styles/admin/EventExpenseTracker.module.css';

/**
 * Event Expense Tracker Component
 * Manages expense tracking for events with categories and financial analytics
 */
export default function EventExpenseTracker({ eventId, event, onExpenseUpdate }) {
  const [expenses, setExpenses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingExpense, setEditingExpense] = useState(null);

  useEffect(() => {
    if (eventId) {
      fetchExpenses();
      fetchCategories();
    }
  }, [eventId]);

  const fetchExpenses = async () => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();
      const response = await fetch(`/api/admin/events/${eventId}/expenses`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setExpenses(data.expenses || []);
      }
    } catch (error) {
      console.error('Error fetching expenses:', error);
      toast.error('Failed to load expenses');
    }
  };

  const fetchCategories = async () => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();
      const response = await fetch('/api/admin/expense-categories', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddExpense = async (expenseData) => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();
      const response = await fetch(`/api/admin/events/${eventId}/expenses`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(expenseData)
      });

      if (response.ok) {
        const data = await response.json();
        setExpenses(prev => [data.expense, ...prev]);
        setShowAddModal(false);
        toast.success('Expense added successfully');
        if (onExpenseUpdate) onExpenseUpdate();
      } else {
        throw new Error('Failed to add expense');
      }
    } catch (error) {
      console.error('Error adding expense:', error);
      toast.error('Failed to add expense');
    }
  };

  const handleEditExpense = async (expenseData) => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();
      const response = await fetch(`/api/admin/events/${eventId}/expenses/${editingExpense.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(expenseData)
      });

      if (response.ok) {
        const data = await response.json();
        setExpenses(prev => prev.map(exp => 
          exp.id === editingExpense.id ? data.expense : exp
        ));
        setEditingExpense(null);
        toast.success('Expense updated successfully');
        if (onExpenseUpdate) onExpenseUpdate();
      } else {
        throw new Error('Failed to update expense');
      }
    } catch (error) {
      console.error('Error updating expense:', error);
      toast.error('Failed to update expense');
    }
  };

  const handleDeleteExpense = async (expenseId) => {
    if (!confirm('Are you sure you want to delete this expense?')) return;

    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();
      const response = await fetch(`/api/admin/events/${eventId}/expenses/${expenseId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        setExpenses(prev => prev.filter(exp => exp.id !== expenseId));
        toast.success('Expense deleted successfully');
        if (onExpenseUpdate) onExpenseUpdate();
      } else {
        throw new Error('Failed to delete expense');
      }
    } catch (error) {
      console.error('Error deleting expense:', error);
      toast.error('Failed to delete expense');
    }
  };

  const totalExpenses = expenses.reduce((sum, exp) => sum + parseFloat(exp.amount || 0), 0);
  const expensesByCategory = expenses.reduce((acc, exp) => {
    const categoryName = exp.category_name || 'Uncategorized';
    acc[categoryName] = (acc[categoryName] || 0) + parseFloat(exp.amount || 0);
    return acc;
  }, {});

  if (loading) {
    return <div className={styles.loading}>Loading expenses...</div>;
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerInfo}>
          <h3>💰 Event Expenses</h3>
          <div className={styles.totalExpenses}>
            Total: ${totalExpenses.toFixed(2)}
          </div>
        </div>
        <button
          className={styles.addButton}
          onClick={() => setShowAddModal(true)}
        >
          + Add Expense
        </button>
      </div>

      {/* Expense Summary */}
      <div className={styles.summary}>
        <div className={styles.summaryCard}>
          <div className={styles.summaryLabel}>Total Expenses</div>
          <div className={styles.summaryValue}>${totalExpenses.toFixed(2)}</div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.summaryLabel}>Budget</div>
          <div className={styles.summaryValue}>
            ${event?.expense_budget ? parseFloat(event.expense_budget).toFixed(2) : '0.00'}
          </div>
        </div>
        <div className={styles.summaryCard}>
          <div className={styles.summaryLabel}>Remaining</div>
          <div className={`${styles.summaryValue} ${
            event?.expense_budget && totalExpenses > parseFloat(event.expense_budget) 
              ? styles.overBudget 
              : styles.underBudget
          }`}>
            ${event?.expense_budget 
              ? (parseFloat(event.expense_budget) - totalExpenses).toFixed(2)
              : '-'
            }
          </div>
        </div>
      </div>

      {/* Category Breakdown */}
      {Object.keys(expensesByCategory).length > 0 && (
        <div className={styles.categoryBreakdown}>
          <h4>Expenses by Category</h4>
          <div className={styles.categoryList}>
            {Object.entries(expensesByCategory).map(([category, amount]) => (
              <div key={category} className={styles.categoryItem}>
                <span className={styles.categoryName}>{category}</span>
                <span className={styles.categoryAmount}>${amount.toFixed(2)}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Expenses List */}
      <div className={styles.expensesList}>
        {expenses.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No expenses recorded yet</p>
            <button
              className={styles.addFirstButton}
              onClick={() => setShowAddModal(true)}
            >
              Add First Expense
            </button>
          </div>
        ) : (
          expenses.map(expense => (
            <div key={expense.id} className={styles.expenseItem}>
              <div className={styles.expenseInfo}>
                <div className={styles.expenseName}>{expense.expense_name}</div>
                <div className={styles.expenseDetails}>
                  <span className={styles.expenseCategory}>
                    {expense.category_name || 'Uncategorized'}
                  </span>
                  <span className={styles.expenseDate}>
                    {new Date(expense.expense_date).toLocaleDateString()}
                  </span>
                  {expense.vendor_name && (
                    <span className={styles.expenseVendor}>{expense.vendor_name}</span>
                  )}
                </div>
                {expense.description && (
                  <div className={styles.expenseDescription}>{expense.description}</div>
                )}
              </div>
              <div className={styles.expenseActions}>
                <div className={styles.expenseAmount}>${parseFloat(expense.amount).toFixed(2)}</div>
                <div className={styles.actionButtons}>
                  <button
                    className={styles.editButton}
                    onClick={() => setEditingExpense(expense)}
                  >
                    Edit
                  </button>
                  <button
                    className={styles.deleteButton}
                    onClick={() => handleDeleteExpense(expense.id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Add/Edit Expense Modal */}
      {(showAddModal || editingExpense) && (
        <ExpenseModal
          expense={editingExpense}
          categories={categories}
          onClose={() => {
            setShowAddModal(false);
            setEditingExpense(null);
          }}
          onSubmit={editingExpense ? handleEditExpense : handleAddExpense}
        />
      )}
    </div>
  );
}

/**
 * Expense Modal Component
 */
function ExpenseModal({ expense, categories, onClose, onSubmit }) {
  const [formData, setFormData] = useState({
    category_id: expense?.category_id || '',
    expense_name: expense?.expense_name || '',
    amount: expense?.amount || '',
    description: expense?.description || '',
    vendor_name: expense?.vendor_name || '',
    expense_date: expense?.expense_date || new Date().toISOString().split('T')[0],
    payment_method: expense?.payment_method || 'card',
    is_reimbursable: expense?.is_reimbursable || false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting expense:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <Modal onClose={onClose} title={expense ? 'Edit Expense' : 'Add Expense'}>
      <form onSubmit={handleSubmit} className={styles.expenseForm}>
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Category *</label>
            <select
              name="category_id"
              value={formData.category_id}
              onChange={handleChange}
              className={styles.input}
              required
            >
              <option value="">Select category</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>Amount ($) *</label>
            <input
              type="number"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              className={styles.input}
              min="0"
              step="0.01"
              required
              placeholder="0.00"
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Expense Name *</label>
          <input
            type="text"
            name="expense_name"
            value={formData.expense_name}
            onChange={handleChange}
            className={styles.input}
            required
            placeholder="e.g., Booth rental fee"
          />
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Vendor/Supplier</label>
            <input
              type="text"
              name="vendor_name"
              value={formData.vendor_name}
              onChange={handleChange}
              className={styles.input}
              placeholder="e.g., Festival Organizers"
            />
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>Date *</label>
            <input
              type="date"
              name="expense_date"
              value={formData.expense_date}
              onChange={handleChange}
              className={styles.input}
              required
            />
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>Payment Method</label>
            <select
              name="payment_method"
              value={formData.payment_method}
              onChange={handleChange}
              className={styles.input}
            >
              <option value="card">Credit/Debit Card</option>
              <option value="cash">Cash</option>
              <option value="bank_transfer">Bank Transfer</option>
              <option value="paypal">PayPal</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="is_reimbursable"
                checked={formData.is_reimbursable}
                onChange={handleChange}
                className={styles.checkbox}
              />
              <span>Reimbursable expense</span>
            </label>
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Description</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            className={styles.textarea}
            rows="3"
            placeholder="Additional details about this expense..."
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onClose}
            className={styles.cancelButton}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : (expense ? 'Update' : 'Add')} Expense
          </button>
        </div>
      </form>
    </Modal>
  );
}
