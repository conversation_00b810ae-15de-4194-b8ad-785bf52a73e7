/**
 * POS Session Manager
 * Handles session persistence and refresh for POS operations
 */

import { supabase } from './supabase'

let sessionRefreshInterval = null
let isRefreshing = false

/**
 * Start session monitoring for POS operations
 */
export function startPOSSessionMonitoring() {
  if (sessionRefreshInterval) {
    console.log('POS session monitoring already active')
    return
  }

  console.log('🔄 Starting POS session monitoring...')

  // Refresh session every 10 minutes during POS operations
  sessionRefreshInterval = setInterval(async () => {
    if (isRefreshing) return

    try {
      isRefreshing = true
      console.log('🔄 POS session refresh check...')

      const { data: { session }, error } = await supabase.auth.refreshSession()

      if (error) {
        console.warn('POS session refresh failed:', error.message)
      } else if (session) {
        console.log('✅ POS session refreshed successfully')

        // Update cached auth state
        if (typeof window !== 'undefined') {
          try {
            const authState = {
              user: session.user,
              role: session.user?.user_metadata?.role || 'user',
              timestamp: Date.now(),
              sessionId: session.access_token?.substring(0, 10)
            }
            sessionStorage.setItem('oss_auth_state', JSON.stringify(authState))
          } catch (cacheError) {
            console.warn('Error updating cached auth state:', cacheError)
          }
        }
      }
    } catch (error) {
      console.error('POS session refresh error:', error)
    } finally {
      isRefreshing = false
    }
  }, 10 * 60 * 1000) // 10 minutes

  // Validate existing session without forcing refresh
  setTimeout(async () => {
    try {
      // First check if we have an existing session
      const { data: { session }, error } = await supabase.auth.getSession()

      if (session && session.user) {
        console.log('✅ Initial POS session validation successful - existing session found')
        console.log('Session user:', session.user.email)

        // Update cached auth state with current session
        if (typeof window !== 'undefined') {
          try {
            const authState = {
              user: session.user,
              role: session.user?.user_metadata?.role || 'user',
              timestamp: Date.now(),
              sessionId: session.access_token?.substring(0, 10)
            }
            sessionStorage.setItem('oss_auth_state', JSON.stringify(authState))
            console.log('✅ POS session state cached successfully')
          } catch (cacheError) {
            console.warn('Error caching POS session state:', cacheError)
          }
        }
      } else if (error) {
        console.warn('Initial POS session validation failed:', error.message)
      } else {
        console.warn('Initial POS session validation failed: No active session found')
      }
    } catch (error) {
      console.error('Initial POS session validation error:', error)
    }
  }, 500) // Reduced delay for faster validation
}

/**
 * Stop session monitoring
 */
export function stopPOSSessionMonitoring() {
  if (sessionRefreshInterval) {
    console.log('🛑 Stopping POS session monitoring')
    clearInterval(sessionRefreshInterval)
    sessionRefreshInterval = null
  }
}

/**
 * Get a valid session for POS operations with automatic refresh
 */
export async function getPOSSession() {
  try {
    console.log('🔍 Getting POS session...')

    // First try to get current session
    let { data: { session }, error } = await supabase.auth.getSession()

    console.log('Current session check:', {
      hasSession: !!session,
      hasUser: !!session?.user,
      userEmail: session?.user?.email,
      error: error?.message
    })

    if (!session && !error) {
      console.log('No current session found, attempting refresh...')
      // Try to refresh session only if there's no session but no error
      const refreshResult = await supabase.auth.refreshSession()
      session = refreshResult.data?.session
      error = refreshResult.error

      console.log('Refresh attempt result:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        error: error?.message
      })
    }

    if (!session) {
      const errorMsg = error?.message || 'No active session found'
      console.error('❌ POS session validation failed:', errorMsg)
      throw new Error(`Authentication required: ${errorMsg}. Please refresh the page and log in again.`)
    }

    console.log('✅ POS session validated successfully for user:', session.user?.email)

    // Check if session is close to expiring (within 5 minutes)
    const expiresAt = session.expires_at * 1000 // Convert to milliseconds
    const now = Date.now()
    const fiveMinutes = 5 * 60 * 1000

    if (expiresAt - now < fiveMinutes) {
      console.log('Session expires soon, refreshing...')
      const refreshResult = await supabase.auth.refreshSession()
      if (refreshResult.data?.session) {
        session = refreshResult.data.session
        console.log('✅ Session refreshed due to upcoming expiration')
      }
    }

    return session
  } catch (error) {
    console.error('Error getting POS session:', error)
    throw error
  }
}

/**
 * Validate that user has admin/staff access for POS operations
 */
export async function validatePOSAccess() {
  try {
    const session = await getPOSSession()

    if (!session?.user) {
      throw new Error('No authenticated user')
    }

    const userRole = session.user.user_metadata?.role || 'user'
    const hasAccess = ['admin', 'staff'].includes(userRole)

    if (!hasAccess) {
      // Check known admin emails as fallback
      const knownAdmins = ['<EMAIL>', '<EMAIL>']
      const isKnownAdmin = knownAdmins.includes(session.user.email)

      if (!isKnownAdmin) {
        throw new Error('Admin or staff privileges required for POS operations')
      }
    }

    return {
      session,
      user: session.user,
      role: userRole,
      hasAccess: true
    }
  } catch (error) {
    console.error('POS access validation failed:', error)
    throw error
  }
}
