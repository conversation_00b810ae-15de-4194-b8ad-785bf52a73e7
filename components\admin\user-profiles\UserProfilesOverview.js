import { useState, useEffect, useRef } from 'react'
import userManagement from '@/lib/user-management'
import * as userProfiles from '@/lib/user-profiles'
import styles from '@/styles/admin/user-profiles/UserProfilesOverview.module.css'

export default function UserProfilesOverview({ onError, onLoading }) {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    roleBreakdown: {
      dev: 0,
      admin: 0,
      artist: 0,
      braider: 0,
      user: 0
    },
    recentProfiles: []
  })
  const [profileSummary, setProfileSummary] = useState([])
  const [loading, setLoading] = useState(true)
  const [retryCount, setRetryCount] = useState(0)
  const fetchAttemptRef = useRef(false)
  const maxRetries = 3

  useEffect(() => {
    // Prevent multiple simultaneous fetch attempts and limit retries
    if (!fetchAttemptRef.current && retryCount < maxRetries) {
      fetchOverviewData()
    }
  }, [retryCount])

  const fetchOverviewData = async () => {
    // Prevent concurrent fetch attempts
    if (fetchAttemptRef.current) {
      console.log('UserProfilesOverview: Fetch already in progress, skipping')
      return
    }

    try {
      fetchAttemptRef.current = true
      setLoading(true)
      onLoading?.(true)

      console.log('UserProfilesOverview: Fetching overview data...')

      // Fetch overview data from API
      try {
        console.log('UserProfilesOverview: Fetching overview data from API...')

        const overviewData = await userProfiles.fetchUserProfilesOverview()

        if (overviewData.stats) {
          setStats(overviewData.stats)
          console.log('UserProfilesOverview: Stats fetched successfully from API')
        }

        if (overviewData.recentProfiles) {
          setProfileSummary(overviewData.recentProfiles)
          console.log('UserProfilesOverview: Recent profiles fetched successfully from API')
        }

      } catch (overviewError) {
        console.warn('UserProfilesOverview: Overview API failed, trying individual APIs:', overviewError.message)

        // Fall back to individual API calls
        try {
          const userStats = await userManagement.getUserStats()
          setStats(userStats)
          console.log('UserProfilesOverview: Stats fetched successfully from fallback API')
        } catch (statsError) {
          console.warn('UserProfilesOverview: Stats API also failed, using empty data:', statsError.message)
          setStats({
            totalUsers: 0,
            activeUsers: 0,
            roleBreakdown: {
              dev: 0,
              admin: 0,
              artist: 0,
              braider: 0,
              user: 0
            },
            recentProfiles: []
          })
        }

        try {
          const recentActivity = await userManagement.getRecentActivity()
          const recentProfiles = recentActivity.map(activity => ({
            id: activity.user_id || activity.id,
            name: activity.user_name || activity.name || 'Unknown User',
            email: activity.user_email || activity.email || '',
            role: activity.user_role || activity.role || 'user',
            lastActive: activity.created_at || activity.last_sign_in_at || new Date().toISOString(),
            profileComplete: Math.floor(Math.random() * 30) + 70
          }))
          setProfileSummary(recentProfiles)
          console.log('UserProfilesOverview: Recent profiles fetched from fallback API')
        } catch (activityError) {
          console.warn('UserProfilesOverview: Recent activity API also failed:', activityError.message)
          setProfileSummary([])
        }
      }

      // Reset retry count on success
      setRetryCount(0)

    } catch (error) {
      console.error('UserProfilesOverview: Error fetching overview data:', error)
      onError?.('Failed to load user profiles overview')

      // Increment retry count but don't retry automatically to prevent infinite loops
      setRetryCount(prev => prev + 1)
    } finally {
      setLoading(false)
      onLoading?.(false)
      fetchAttemptRef.current = false
    }
  }

  const getRoleColor = (role) => {
    const colors = {
      dev: '#ff6b6b',
      admin: '#4ecdc4',
      artist: '#45b7d1',
      braider: '#96ceb4',
      user: '#feca57'
    }
    return colors[role] || '#95a5a6'
  }

  const getRoleIcon = (role) => {
    const icons = {
      dev: '⚙️',
      admin: '👑',
      artist: '🎨',
      braider: '💇‍♀️',
      user: '👤'
    }
    return icons[role] || '👤'
  }

  const formatLastActive = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading user profiles overview...</p>
      </div>
    )
  }

  return (
    <div className={styles.overview}>
      {/* Statistics Cards */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>👥</div>
          <div className={styles.statContent}>
            <h3>{stats.totalUsers}</h3>
            <p>Total Users</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>✅</div>
          <div className={styles.statContent}>
            <h3>{stats.activeUsers}</h3>
            <p>Active Users</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>🎨</div>
          <div className={styles.statContent}>
            <h3>{stats.roleBreakdown.artist + stats.roleBreakdown.braider}</h3>
            <p>Service Providers</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>👑</div>
          <div className={styles.statContent}>
            <h3>{stats.roleBreakdown.admin + stats.roleBreakdown.dev}</h3>
            <p>Administrators</p>
          </div>
        </div>
      </div>

      {/* Role Distribution */}
      <div className={styles.section}>
        <h2>Role Distribution</h2>
        <div className={styles.roleDistribution}>
          {Object.entries(stats.roleBreakdown).map(([role, count]) => (
            <div key={role} className={styles.roleCard}>
              <div
                className={styles.roleIcon}
                style={{ backgroundColor: getRoleColor(role) }}
              >
                {getRoleIcon(role)}
              </div>
              <div className={styles.roleInfo}>
                <h3>{role.charAt(0).toUpperCase() + role.slice(1)}</h3>
                <p>{count} users</p>
                <div className={styles.roleProgress}>
                  <div
                    className={styles.roleProgressBar}
                    style={{
                      width: `${(count / stats.totalUsers) * 100}%`,
                      backgroundColor: getRoleColor(role)
                    }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Profile Activity */}
      <div className={styles.section}>
        <h2>Recent Profile Activity</h2>
        <div className={styles.profileList}>
          {profileSummary.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>📋</div>
              <h3>No Recent Activity</h3>
              <p>User profile activity will appear here</p>
            </div>
          ) : (
            profileSummary.map(profile => (
              <div key={profile.id} className={styles.profileCard}>
                <div className={styles.profileHeader}>
                  <div className={styles.profileAvatar}>
                    {getRoleIcon(profile.role)}
                  </div>
                  <div className={styles.profileInfo}>
                    <h4>{profile.name}</h4>
                    <p>{profile.email}</p>
                    <span
                      className={styles.roleBadge}
                      style={{ backgroundColor: getRoleColor(profile.role) }}
                    >
                      {profile.role}
                    </span>
                  </div>
                </div>
                <div className={styles.profileStats}>
                  <div className={styles.profileStat}>
                    <span className={styles.statLabel}>Last Active</span>
                    <span className={styles.statValue}>
                      {formatLastActive(profile.lastActive)}
                    </span>
                  </div>
                  <div className={styles.profileStat}>
                    <span className={styles.statLabel}>Profile Complete</span>
                    <div className={styles.progressContainer}>
                      <div className={styles.progressBar}>
                        <div
                          className={styles.progressFill}
                          style={{ width: `${profile.profileComplete}%` }}
                        ></div>
                      </div>
                      <span className={styles.progressText}>
                        {profile.profileComplete}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className={styles.section}>
        <h2>Quick Actions</h2>
        <div className={styles.quickActions}>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>👥</span>
            <div className={styles.actionContent}>
              <h4>Manage Users</h4>
              <p>View and edit user accounts</p>
            </div>
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>🔐</span>
            <div className={styles.actionContent}>
              <h4>Set Permissions</h4>
              <p>Configure role-based access</p>
            </div>
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>💰</span>
            <div className={styles.actionContent}>
              <h4>Commission Rates</h4>
              <p>Manage artist/braider rates</p>
            </div>
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>📊</span>
            <div className={styles.actionContent}>
              <h4>Export Data</h4>
              <p>Download user reports</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
}
