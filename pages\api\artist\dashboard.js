import { getAdminClient } from '@/lib/supabase'
import { verifyAuthToken } from '@/lib/auth-utils'

/**
 * API endpoint for artist/braider dashboard data
 * GET /api/artist/dashboard - Get dashboard data for authenticated artist/braider
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Artist dashboard API called`)

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify authentication and role
    const authResult = await verifyAuthToken(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error || 'Unauthorized' })
    }

    const { user, role } = authResult

    // Ensure user is artist or braider
    if (!['artist', 'braider'].includes(role)) {
      return res.status(403).json({ error: 'Access denied. Artist or Braider role required.' })
    }

    console.log(`[${requestId}] Fetching dashboard data for ${role}: ${user.email}`)

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get artist profile
    const { data: artistProfile, error: profileError } = await adminClient
      .from('artist_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error(`[${requestId}] Error fetching artist profile:`, profileError)
    }

    // Get upcoming bookings (next 30 days)
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)

    const { data: upcomingBookings, error: bookingsError } = await adminClient
      .from('artist_booking_assignments')
      .select(`
        id,
        booking_id,
        status,
        bookings (
          id,
          start_time,
          end_time,
          status,
          services (
            name
          ),
          customers (
            name
          )
        )
      `)
      .eq('artist_id', artistProfile?.id)
      .gte('bookings.start_time', new Date().toISOString())
      .lte('bookings.start_time', thirtyDaysFromNow.toISOString())
      .order('bookings.start_time', { ascending: true })
      .limit(10)

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching upcoming bookings:`, bookingsError)
    }

    // Get recent payments (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const { data: recentPayments, error: paymentsError } = await adminClient
      .from('payments')
      .select(`
        id,
        amount,
        created_at,
        booking_id,
        bookings (
          services (
            name
          ),
          customers (
            name
          )
        )
      `)
      .eq('artist_id', artistProfile?.id)
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(10)

    if (paymentsError) {
      console.error(`[${requestId}] Error fetching recent payments:`, paymentsError)
    }

    // Calculate stats
    const stats = {
      totalBookings: 0,
      totalEarnings: 0,
      averageRating: 0,
      completedServices: 0
    }

    // Get total bookings count
    if (artistProfile?.id) {
      const { count: totalBookingsCount } = await adminClient
        .from('artist_booking_assignments')
        .select('*', { count: 'exact', head: true })
        .eq('artist_id', artistProfile.id)

      stats.totalBookings = totalBookingsCount || 0

      // Get completed services count
      const { count: completedServicesCount } = await adminClient
        .from('artist_booking_assignments')
        .select('*', { count: 'exact', head: true })
        .eq('artist_id', artistProfile.id)
        .eq('status', 'completed')

      stats.completedServices = completedServicesCount || 0

      // Calculate total earnings
      const { data: allPayments } = await adminClient
        .from('payments')
        .select('amount, commission_amount')
        .eq('artist_id', artistProfile.id)

      if (allPayments) {
        stats.totalEarnings = allPayments.reduce((total, payment) => {
          return total + (payment.commission_amount || 0)
        }, 0)
      }

      // Get average rating
      const { data: ratings } = await adminClient
        .from('artist_booking_assignments')
        .select('customer_rating')
        .eq('artist_id', artistProfile.id)
        .not('customer_rating', 'is', null)

      if (ratings && ratings.length > 0) {
        const totalRating = ratings.reduce((sum, r) => sum + r.customer_rating, 0)
        stats.averageRating = totalRating / ratings.length
      }
    }

    // Format the response data
    const formattedUpcomingBookings = (upcomingBookings || []).map(assignment => ({
      id: assignment.booking_id,
      start_time: assignment.bookings?.start_time,
      end_time: assignment.bookings?.end_time,
      status: assignment.bookings?.status,
      service_name: assignment.bookings?.services?.name,
      customer_name: assignment.bookings?.customers?.name,
      assignment_status: assignment.status
    }))

    const formattedRecentPayments = (recentPayments || []).map(payment => ({
      id: payment.id,
      amount: payment.amount,
      commission: payment.amount * (artistProfile?.commission_rate || 15) / 100,
      created_at: payment.created_at,
      service_name: payment.bookings?.services?.name,
      customer_name: payment.bookings?.customers?.name
    }))

    console.log(`[${requestId}] Dashboard data fetched successfully`)

    return res.status(200).json({
      profile: artistProfile || {
        artist_name: user.email,
        display_name: user.email,
        bio: '',
        specializations: [],
        skill_level: 'intermediate',
        is_active: true,
        is_available_today: true
      },
      upcomingBookings: formattedUpcomingBookings,
      recentPayments: formattedRecentPayments,
      stats
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in artist dashboard:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
