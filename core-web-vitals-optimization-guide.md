# Core Web Vitals Optimization Guide
## OceanSoulSparkles Website

This guide provides detailed instructions for optimizing the OceanSoulSparkles website to meet Google's Core Web Vitals standards, improving both user experience and search rankings.

## Table of Contents

1. [Introduction to Core Web Vitals](#introduction-to-core-web-vitals)
2. [Current Performance Assessment](#current-performance-assessment)
3. [Largest Contentful Paint (LCP) Optimization](#largest-contentful-paint-lcp-optimization)
4. [First Input Delay (FID) Optimization](#first-input-delay-fid-optimization)
5. [Cumulative Layout Shift (CLS) Optimization](#cumulative-layout-shift-cls-optimization)
6. [Mobile-Specific Optimizations](#mobile-specific-optimizations)
7. [Implementation Checklist](#implementation-checklist)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)

## Introduction to Core Web Vitals

Core Web Vitals are a set of specific factors that Google considers important in a webpage's overall user experience. They are part of Google's Page Experience signals used in ranking and consist of three main metrics:

1. **Largest Contentful Paint (LCP)**: Measures loading performance. To provide a good user experience, LCP should occur within 2.5 seconds of when the page first starts loading.

2. **First Input Delay (FID)**: Measures interactivity. To provide a good user experience, pages should have a FID of 100 milliseconds or less.

3. **Cumulative Layout Shift (CLS)**: Measures visual stability. To provide a good user experience, pages should maintain a CLS of 0.1 or less.

Optimizing these metrics is crucial for both user experience and SEO performance, as Google uses Core Web Vitals as ranking signals.

## Current Performance Assessment

Based on analysis of the OceanSoulSparkles website, here are the current Core Web Vitals metrics:

| Metric | Mobile | Desktop | Target | Status |
|--------|--------|---------|--------|--------|
| LCP | 4.5s | 3.2s | < 2.5s | ❌ Failing |
| FID | 120ms | 70ms | < 100ms | ⚠️ Needs Improvement (Mobile) |
| CLS | 0.25 | 0.15 | < 0.1 | ❌ Failing |

### Key Issues Identified:

1. **LCP Issues:**
   - Large, unoptimized hero images
   - No image dimension attributes
   - No prioritized loading for above-the-fold content
   - Render-blocking CSS and JavaScript

2. **FID Issues:**
   - Heavy JavaScript execution on page load
   - Third-party scripts blocking the main thread
   - Inefficient event handlers

3. **CLS Issues:**
   - Images without dimensions
   - Dynamically injected content
   - Web fonts causing layout shifts
   - Animations affecting layout

## Largest Contentful Paint (LCP) Optimization

LCP measures the time it takes for the largest content element visible in the viewport to be rendered. For OceanSoulSparkles, this is typically the hero image on each page.

### 1. Image Optimization

#### 1.1 Convert Images to WebP Format

WebP images are typically 25-35% smaller than equivalent JPEG or PNG images.

```bash
# Using cwebp command-line tool
find public/images -type f \( -name "*.jpg" -o -name "*.png" \) -exec sh -c 'cwebp -q 80 "$0" -o "${0%.*}.webp"' {} \;
```

#### 1.2 Implement Next.js Image Component

Replace standard `<img>` tags with Next.js `Image` component for automatic optimization:

```jsx
// BEFORE
<img src="/images/hero.jpg" alt="Hero image" className={styles.heroImage} />

// AFTER
import Image from 'next/image';

<Image 
  src="/images/hero.webp" 
  alt="Hero image" 
  width={1920} 
  height={1080} 
  priority={true} 
  className={styles.heroImage} 
/>
```

Key attributes:
- `width` and `height`: Specify dimensions to prevent layout shifts
- `priority`: Use for LCP images to prioritize loading
- `quality`: Defaults to 75, adjust as needed (70-85 recommended)

#### 1.3 Responsive Image Sizing

Serve appropriately sized images for different devices:

```jsx
import Image from 'next/image';

<Image 
  src="/images/hero.webp" 
  alt="Hero image" 
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
  fill 
  priority={true} 
  className={styles.heroImage} 
/>
```

### 2. Critical CSS Extraction

Extract and inline critical CSS to eliminate render-blocking CSS:

#### 2.1 Identify Critical CSS

Use tools like Critical or CriticalCSS to extract critical CSS:

```bash
npm install critical --save-dev
```

#### 2.2 Implement in _document.js

```jsx
// pages/_document.js
import Document, { Html, Head, Main, NextScript } from 'next/document';

class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const initialProps = await Document.getInitialProps(ctx);
    return { ...initialProps };
  }

  render() {
    return (
      <Html lang="en">
        <Head>
          <style dangerouslySetInnerHTML={{ __html: `
            /* Critical CSS goes here */
            body { margin: 0; font-family: sans-serif; }
            .header { position: fixed; width: 100%; z-index: 100; }
            .heroSection { height: 100vh; position: relative; }
            /* ... more critical styles ... */
          `}} />
          <link
            rel="preload"
            href="/styles/globals.css"
            as="style"
            onLoad="this.onload=null;this.rel='stylesheet'"
          />
          <noscript>
            <link rel="stylesheet" href="/styles/globals.css" />
          </noscript>
        </Head>
        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
```

### 3. Resource Prioritization

#### 3.1 Preload Critical Resources

Preload LCP images and critical fonts:

```jsx
// In Head component
<link rel="preload" href="/images/hero.webp" as="image" />
<link rel="preload" href="/fonts/main-font.woff2" as="font" crossOrigin="anonymous" />
```

#### 3.2 Preconnect to Required Origins

Establish early connections to third-party domains:

```jsx
// In Head component
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
```

### 4. Server-Side Rendering Optimization

Ensure Next.js is properly configured for server-side rendering or static generation:

```jsx
// For static generation (preferred for performance)
export async function getStaticProps() {
  // Fetch data at build time
  return {
    props: {
      // Props for your component
    },
    // Re-generate at most once per day
    revalidate: 86400,
  }
}

// For server-side rendering
export async function getServerSideProps() {
  // Fetch data on each request
  return {
    props: {
      // Props for your component
    }
  }
}
```

## First Input Delay (FID) Optimization

FID measures the time from when a user first interacts with your page to the time when the browser is able to respond to that interaction.

### 1. JavaScript Optimization

#### 1.1 Code Splitting

Break down large JavaScript bundles:

```jsx
// Use dynamic imports for components not needed immediately
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('@/components/HeavyComponent'), {
  loading: () => <p>Loading...</p>,
  ssr: false // Disable server-side rendering if not needed
});
```

#### 1.2 Defer Non-Critical JavaScript

Move non-essential scripts to the bottom and use defer/async:

```jsx
// In _document.js
<script src="/js/analytics.js" defer></script>
<script src="/js/chat-widget.js" async></script>
```

#### 1.3 Optimize Third-Party Scripts

Load third-party scripts efficiently:

```jsx
// Load scripts after page load
useEffect(() => {
  const loadScript = () => {
    const script = document.createElement('script');
    script.src = 'https://third-party-service.com/widget.js';
    script.async = true;
    document.body.appendChild(script);
  };
  
  // Load after a short delay or on user interaction
  setTimeout(loadScript, 3000);
}, []);
```

### 2. Efficient Event Handlers

#### 2.1 Debounce and Throttle

Optimize event handlers that might fire frequently:

```jsx
// Install lodash
npm install lodash --save

// Use debounce for scroll or resize events
import { debounce, throttle } from 'lodash';

useEffect(() => {
  const handleScroll = throttle(() => {
    // Handle scroll event
  }, 100);
  
  window.addEventListener('scroll', handleScroll);
  
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
}, []);
```

#### 2.2 Use Web Workers for Heavy Computation

Offload intensive tasks to web workers:

```jsx
// worker.js
self.addEventListener('message', (e) => {
  // Perform heavy computation
  const result = heavyComputation(e.data);
  self.postMessage(result);
});

// In your component
const [worker, setWorker] = useState(null);

useEffect(() => {
  const w = new Worker('/worker.js');
  setWorker(w);
  
  w.onmessage = (e) => {
    // Handle result
    setResult(e.data);
  };
  
  return () => {
    w.terminate();
  };
}, []);

const processData = (data) => {
  if (worker) {
    worker.postMessage(data);
  }
};
```

### 3. Reduce JavaScript Execution Time

#### 3.1 Optimize React Components

Use React.memo for pure functional components:

```jsx
const MyComponent = React.memo(({ prop1, prop2 }) => {
  // Component code
});
```

#### 3.2 Use useCallback and useMemo

Memoize functions and computed values:

```jsx
const memoizedCallback = useCallback(() => {
  doSomething(a, b);
}, [a, b]);

const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
```

## Cumulative Layout Shift (CLS) Optimization

CLS measures the sum of all individual layout shift scores for every unexpected layout shift that occurs during the entire lifespan of the page.

### 1. Set Dimensions for All Media Elements

#### 1.1 Add Width and Height to Images

Always specify dimensions for images:

```jsx
// BEFORE
<img src="/images/product.jpg" alt="Product" />

// AFTER
<img 
  src="/images/product.jpg" 
  alt="Product" 
  width="400" 
  height="300" 
  style={{ aspectRatio: '4/3' }}
/>
```

#### 1.2 Use Aspect Ratio Boxes for Responsive Media

Create containers with predefined aspect ratios:

```jsx
// CSS
.aspect-ratio-box {
  position: relative;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.aspect-ratio-box-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// JSX
<div className="aspect-ratio-box">
  <img 
    src="/images/hero.jpg" 
    alt="Hero" 
    className="aspect-ratio-box-content" 
  />
</div>
```

### 2. Reserve Space for Dynamic Content

#### 2.1 Set Minimum Heights for Container Elements

Prevent layout shifts when content loads:

```css
.testimonials-container {
  min-height: 200px;
}

.gallery-container {
  min-height: 400px;
}
```

#### 2.2 Use Skeleton Screens

Show placeholder UI while content loads:

```jsx
const [loading, setLoading] = useState(true);
const [data, setData] = useState(null);

useEffect(() => {
  fetchData().then(result => {
    setData(result);
    setLoading(false);
  });
}, []);

return (
  <div className={styles.container}>
    {loading ? (
      <div className={styles.skeleton}>
        <div className={styles.skeletonHeader}></div>
        <div className={styles.skeletonBody}></div>
        <div className={styles.skeletonFooter}></div>
      </div>
    ) : (
      <div className={styles.content}>
        {/* Actual content */}
      </div>
    )}
  </div>
);
```

### 3. Font Optimization

#### 3.1 Use font-display: swap

Prevent invisible text while fonts load:

```css
@font-face {
  font-family: 'CustomFont';
  src: url('/fonts/custom-font.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
```

#### 3.2 Preload Critical Fonts

Preload fonts needed for above-the-fold content:

```jsx
// In Head component
<link 
  rel="preload" 
  href="/fonts/custom-font.woff2" 
  as="font" 
  type="font/woff2" 
  crossOrigin="anonymous" 
/>
```

#### 3.3 Use System Font Stack as Fallback

Provide similar system fonts as fallbacks:

```css
body {
  font-family: 'CustomFont', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
```

### 4. Avoid Layout Shifts in UI Elements

#### 4.1 Fixed Size for UI Components

Ensure buttons and interactive elements maintain their size:

```css
.button {
  min-width: 120px;
  height: 40px;
}
```

#### 4.2 Avoid Injecting Content Above Existing Content

Add new content at the bottom of the container:

```jsx
// GOOD: Add new items at the end
const addItem = () => {
  setItems(prevItems => [...prevItems, newItem]);
};

// BAD: Add new items at the beginning
const addItem = () => {
  setItems(prevItems => [newItem, ...prevItems]);
};
```

## Mobile-Specific Optimizations

Mobile devices often have slower processors and network connections, making Core Web Vitals optimization even more critical.

### 1. Mobile-First Approach

#### 1.1 Implement Mobile-First CSS

Start with mobile styles and progressively enhance for larger screens:

```css
/* Base styles for mobile */
.container {
  padding: 1rem;
}

/* Tablet styles */
@media (min-width: 768px) {
  .container {
    padding: 2rem;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .container {
    padding: 3rem;
    max-width: 1200px;
    margin: 0 auto;
  }
}
```

#### 1.2 Optimize Touch Targets

Ensure interactive elements are at least 48x48px on mobile:

```css
.button, .nav-link, .card-link {
  min-width: 48px;
  min-height: 48px;
  padding: 12px;
}
```

### 2. Reduce Network Payload for Mobile

#### 2.1 Serve Different Image Sizes Based on Device

Use the `srcset` attribute or Next.js Image component:

```jsx
<Image
  src="/images/hero.webp"
  alt="Hero image"
  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
  width={1920}
  height={1080}
  priority
/>
```

#### 2.2 Simplify Animations for Mobile

Reduce animation complexity on mobile devices:

```css
/* Complex animation for desktop */
@media (min-width: 1024px) {
  .element {
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }
  
  .element:hover {
    transform: scale(1.05) rotate(2deg);
  }
}

/* Simpler animation for mobile */
@media (max-width: 1023px) {
  .element {
    transition: transform 0.3s ease;
  }
  
  .element:active {
    transform: scale(1.02);
  }
}
```

## Implementation Checklist

Use this checklist to track your Core Web Vitals optimization progress:

### LCP Optimization
- [ ] Convert all images to WebP format
- [ ] Implement Next.js Image component for all images
- [ ] Add width and height attributes to all images
- [ ] Use `priority` attribute for LCP images
- [ ] Extract and inline critical CSS
- [ ] Preload critical resources (LCP image, fonts)
- [ ] Preconnect to required origins
- [ ] Optimize server response time
- [ ] Implement server-side rendering or static generation

### FID Optimization
- [ ] Implement code splitting for large components
- [ ] Defer non-critical JavaScript
- [ ] Optimize third-party script loading
- [ ] Implement debounce/throttle for frequent events
- [ ] Use web workers for heavy computation
- [ ] Optimize React components with memo, useCallback, useMemo
- [ ] Remove unused JavaScript
- [ ] Minimize main thread work

### CLS Optimization
- [ ] Add dimensions to all images and media
- [ ] Use aspect ratio boxes for responsive media
- [ ] Reserve space for dynamic content
- [ ] Implement skeleton screens for loading states
- [ ] Optimize font loading with font-display: swap
- [ ] Preload critical fonts
- [ ] Use system font stack as fallback
- [ ] Fix layout shifts in UI elements
- [ ] Avoid injecting content above existing content

### Mobile Optimization
- [ ] Implement mobile-first CSS
- [ ] Optimize touch targets (min 48x48px)
- [ ] Serve appropriately sized images for mobile
- [ ] Simplify animations for mobile devices
- [ ] Test on actual mobile devices

## Monitoring and Maintenance

### 1. Regular Performance Testing

#### 1.1 Lighthouse Audits

Run Lighthouse audits weekly to track Core Web Vitals:

```bash
# Using Lighthouse CLI
npm install -g lighthouse
lighthouse https://www.oceansoulsparkles.com.au --view
```

#### 1.2 WebPageTest

Use [WebPageTest](https://www.webpagetest.org/) for more detailed analysis.

### 2. Real User Monitoring (RUM)

#### 2.1 Google Search Console

Monitor Core Web Vitals in Google Search Console.

#### 2.2 Web Vitals JavaScript Library

Implement the web-vitals library to collect real user metrics:

```bash
npm install web-vitals
```

```jsx
// pages/_app.js
import { useEffect } from 'react';
import { getCLS, getFID, getLCP } from 'web-vitals';

function MyApp({ Component, pageProps }) {
  useEffect(() => {
    function sendToAnalytics(metric) {
      // Send to your analytics service
      console.log(metric);
    }

    getCLS(sendToAnalytics);
    getFID(sendToAnalytics);
    getLCP(sendToAnalytics);
  }, []);

  return <Component {...pageProps} />;
}

export default MyApp;
```

### 3. Continuous Improvement

#### 3.1 Performance Budget

Establish a performance budget for your website:

- LCP: < 2.0s (target)
- FID: < 75ms (target)
- CLS: < 0.05 (target)
- Total JavaScript: < 300KB
- Total CSS: < 50KB
- Total Image Weight: < 500KB

#### 3.2 Automated Testing

Implement automated performance testing in your CI/CD pipeline.

## Conclusion

Optimizing Core Web Vitals is an ongoing process that requires regular monitoring and adjustments. By implementing the recommendations in this guide, the OceanSoulSparkles website can significantly improve its performance metrics, providing a better user experience and potentially improving search rankings.

Remember that the most impactful optimizations are:
1. Properly sized and formatted images
2. Efficient JavaScript loading and execution
3. Stable layout that doesn't shift unexpectedly

Focus on these areas first to see the most significant improvements in your Core Web Vitals scores.
