.addBookingForm {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 600px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.closeButton:hover {
  color: #343a40;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 15px 20px 0;
  font-size: 0.9rem;
}

.form {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.formSection {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.formSection:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.formSection h3 {
  font-size: 1.1rem;
  color: #495057;
  margin-top: 0;
  margin-bottom: 15px;
}

.customerToggle {
  display: flex;
  margin-bottom: 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  overflow: hidden;
}

.toggleButton {
  flex: 1;
  background-color: #f8f9fa;
  border: none;
  padding: 8px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toggleButton.active {
  background-color: #6a0dad;
  color: white;
  font-weight: 500;
}

.formGroup {
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.formRow {
  display: flex;
  gap: 15px;
}

.formRow .formGroup {
  flex: 1;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancelButton {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.cancelButton:hover {
  background-color: #e9ecef;
}

.saveButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover {
  background-color: #5a0b9d;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .formRow {
    flex-direction: column;
    gap: 0;
  }
}
