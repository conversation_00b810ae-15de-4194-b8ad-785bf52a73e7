import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for admin marketing campaigns management
 * This endpoint uses service_role key to bypass RLS policies
 * Uses the simplified authentication approach
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Check if development auth bypass is enabled
  const devBypass = process.env.NEXT_PUBLIC_DEV_AUTH_BYPASS === 'true';
  let user = null;

  if (!devBypass) {
    // Authenticate request using our simplified auth module
    const { authorized, error, user: authUser, role } = await authenticateAdminRequest(req);
    if (!authorized) {
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      });
    }
    user = authUser;
  } else {
    console.log('Development auth bypass enabled - skipping authentication for marketing campaigns');
    // Set a default user for development
    user = { id: 'dev-user' };
  }

  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Supabase admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // GET - Fetch campaigns
    if (req.method === 'GET') {
      // Get query parameters
      const {
        search,
        sort_by = 'created_at',
        sort_order = 'desc',
        limit = 10,
        offset = 0,
        status,
        campaign_type,
      } = req.query;

      // Check if campaigns table exists first
      const { data: tableCheck, error: tableError } = await adminClient
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'campaigns')
        .single();

      if (tableError || !tableCheck) {
        console.warn('campaigns table does not exist, returning empty result');
        return res.status(200).json({
          campaigns: [],
          total: 0,
          page: 1,
          limit: Number(limit),
          _note: 'campaigns table does not exist in database'
        });
      }

      // Start building the query - remove the foreign key join for now
      let query = adminClient
        .from('campaigns')
        .select('*', { count: 'exact' });

      // Apply filters if provided
      if (status) {
        query = query.eq('status', status);
      }

      if (campaign_type) {
        query = query.eq('campaign_type', campaign_type);
      }

      // Apply search filter if provided
      if (search) {
        query = query.or(`name.ilike.%${search}%, description.ilike.%${search}%`);
      }

      // Apply sorting
      query = query.order(sort_by, { ascending: sort_order === 'asc' });

      // Apply pagination
      query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

      // Execute the query
      const { data: campaigns, error, count } = await query;

      if (error) {
        throw error;
      }

      return res.status(200).json({
        campaigns,
        total: count,
        page: Math.floor(Number(offset) / Number(limit)) + 1,
        limit: Number(limit),
      });
    }

    // POST - Create new campaign
    if (req.method === 'POST') {
      const {
        name,
        description,
        segment_id,
        campaign_type,
        template_id,
        status,
        settings,
        schedule_date
      } = req.body;

      if (!name) {
        return res.status(400).json({ error: 'Campaign name is required' });
      }

      const { data, error } = await adminClient
        .from('campaigns')
        .insert({
          name,
          description,
          segment_id,
          campaign_type,
          template_id,
          status: status || 'draft',
          settings: settings || {},
          schedule_date,
          created_by: user.id,
          updated_by: user.id,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return res.status(201).json(data);
    }

    // PUT - Update a campaign
    if (req.method === 'PUT') {
      const { id } = req.query;
      const {
        name,
        description,
        segment_id,
        campaign_type,
        template_id,
        status,
        settings,
        schedule_date
      } = req.body;

      if (!id) {
        return res.status(400).json({ error: 'Campaign ID is required' });
      }

      const updateData = {
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      };

      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (segment_id !== undefined) updateData.segment_id = segment_id;
      if (campaign_type !== undefined) updateData.campaign_type = campaign_type;
      if (template_id !== undefined) updateData.template_id = template_id;
      if (status !== undefined) updateData.status = status;
      if (settings !== undefined) updateData.settings = settings;
      if (schedule_date !== undefined) updateData.schedule_date = schedule_date;

      const { data, error } = await adminClient
        .from('campaigns')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return res.status(200).json(data);
    }

    // DELETE - Delete a campaign
    if (req.method === 'DELETE') {
      const { id } = req.query;

      if (!id) {
        return res.status(400).json({ error: 'Campaign ID is required' });
      }

      const { error } = await adminClient
        .from('campaigns')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      return res.status(204).send();
    }
  } catch (error) {
    console.error('Error in campaigns API:', error);
    return res.status(500).json({ error: 'Server error', details: error.message });
  }
}
