import { useState } from 'react';
import { supabase } from '@/lib/supabase'; // Use singleton client
import styles from '@/styles/admin/DataExport.module.css';

/**
 * Component for exporting data in various formats
 *
 * @returns {JSX.Element}
 */
export default function DataExport() {
  const [exportType, setExportType] = useState('customers');
  const [exportFormat, setExportFormat] = useState('csv');
  const [dateRange, setDateRange] = useState('all');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Handle export
  const handleExport = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Determine date range
      let startDate = null;
      let endDate = new Date().toISOString();

      if (dateRange === 'last30') {
        const date = new Date();
        date.setDate(date.getDate() - 30);
        startDate = date.toISOString();
      } else if (dateRange === 'last90') {
        const date = new Date();
        date.setDate(date.getDate() - 90);
        startDate = date.toISOString();
      } else if (dateRange === 'thisYear') {
        const date = new Date();
        date.setMonth(0, 1);
        date.setHours(0, 0, 0, 0);
        startDate = date.toISOString();
      } else if (dateRange === 'custom') {
        if (!customStartDate) {
          throw new Error('Please select a start date for custom range');
        }
        startDate = new Date(customStartDate).toISOString();

        if (customEndDate) {
          endDate = new Date(customEndDate);
          endDate.setHours(23, 59, 59, 999);
          endDate = endDate.toISOString();
        }
      }

      // Fetch data based on export type
      let data;

      if (exportType === 'customers') {
        const { data: customers, error } = await supabase
          .from('customers')
          .select(`
            id,
            name,
            email,
            phone,
            address,
            city,
            state,
            postal_code,
            country,
            vip,
            customer_since,
            last_booking_date,
            booking_count,
            lifetime_value,
            notes,
            created_at
          `)
          .order('name');

        if (error) throw error;
        data = customers;
      } else if (exportType === 'bookings') {
        let query = supabase
          .from('bookings')
          .select(`
            id,
            customer_id,
            customers(name, email, phone),
            service_id,
            services(name, price),
            start_time,
            end_time,
            status,
            location,
            notes,
            created_at
          `)
          .order('start_time', { ascending: false });

        if (startDate) {
          query = query.gte('start_time', startDate);
        }

        if (endDate) {
          query = query.lte('start_time', endDate);
        }

        const { data: bookings, error } = await query;

        if (error) throw error;

        // Format booking data
        data = bookings.map(booking => ({
          id: booking.id,
          customer_id: booking.customer_id,
          customer_name: booking.customers?.name,
          customer_email: booking.customers?.email,
          customer_phone: booking.customers?.phone,
          service_id: booking.service_id,
          service_name: booking.services?.name,
          service_price: booking.services?.price,
          start_time: booking.start_time,
          end_time: booking.end_time,
          status: booking.status,
          location: booking.location,
          notes: booking.notes,
          created_at: booking.created_at
        }));
      }

      // Convert data to selected format
      let exportData;
      let fileName;
      let mimeType;

      if (exportFormat === 'csv') {
        exportData = convertToCSV(data);
        fileName = `${exportType}_export_${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else if (exportFormat === 'json') {
        exportData = JSON.stringify(data, null, 2);
        fileName = `${exportType}_export_${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      } else if (exportFormat === 'excel') {
        // For Excel, we'll use CSV format but with .xlsx extension
        exportData = convertToCSV(data);
        fileName = `${exportType}_export_${new Date().toISOString().split('T')[0]}.xlsx`;
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      }

      // Create download link
      const blob = new Blob([exportData], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setSuccess(`${exportType} data exported successfully as ${exportFormat.toUpperCase()}`);
    } catch (error) {
      console.error('Error exporting data:', error);
      setError(error.message || 'Failed to export data');
    } finally {
      setLoading(false);
    }
  };

  // Convert data to CSV format
  const convertToCSV = (data) => {
    if (!data || !data.length) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [];

    // Add headers
    csvRows.push(headers.join(','));

    // Add rows
    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];

        // Handle null values
        if (value === null || value === undefined) {
          return '';
        }

        // Handle strings with commas
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }

        return value;
      });

      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  };

  return (
    <div className={styles.dataExport}>
      <h2 className={styles.title}>Export Data</h2>

      {error && (
        <div className={styles.error}>{error}</div>
      )}

      {success && (
        <div className={styles.success}>{success}</div>
      )}

      <div className={styles.exportForm}>
        <div className={styles.formGroup}>
          <label htmlFor="exportType" className={styles.label}>Export Type</label>
          <select
            id="exportType"
            value={exportType}
            onChange={(e) => setExportType(e.target.value)}
            className={styles.select}
          >
            <option value="customers">Customers</option>
            <option value="bookings">Bookings</option>
          </select>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="exportFormat" className={styles.label}>Export Format</label>
          <select
            id="exportFormat"
            value={exportFormat}
            onChange={(e) => setExportFormat(e.target.value)}
            className={styles.select}
          >
            <option value="csv">CSV</option>
            <option value="json">JSON</option>
            <option value="excel">Excel</option>
          </select>
        </div>

        {exportType === 'bookings' && (
          <div className={styles.formGroup}>
            <label htmlFor="dateRange" className={styles.label}>Date Range</label>
            <select
              id="dateRange"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className={styles.select}
            >
              <option value="all">All Time</option>
              <option value="last30">Last 30 Days</option>
              <option value="last90">Last 90 Days</option>
              <option value="thisYear">This Year</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>
        )}

        {exportType === 'bookings' && dateRange === 'custom' && (
          <div className={styles.dateRangeInputs}>
            <div className={styles.formGroup}>
              <label htmlFor="customStartDate" className={styles.label}>Start Date</label>
              <input
                type="date"
                id="customStartDate"
                value={customStartDate}
                onChange={(e) => setCustomStartDate(e.target.value)}
                className={styles.input}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="customEndDate" className={styles.label}>End Date (Optional)</label>
              <input
                type="date"
                id="customEndDate"
                value={customEndDate}
                onChange={(e) => setCustomEndDate(e.target.value)}
                className={styles.input}
                min={customStartDate}
              />
            </div>
          </div>
        )}

        <div className={styles.formActions}>
          <button
            className={styles.exportButton}
            onClick={handleExport}
            disabled={loading}
          >
            {loading ? 'Exporting...' : 'Export Data'}
          </button>
        </div>
      </div>

      <div className={styles.securityNote}>
        <h3 className={styles.securityTitle}>Security Note</h3>
        <p className={styles.securityText}>
          Exported data may contain sensitive customer information. Please ensure you handle this data in accordance with privacy regulations and your organization's data protection policies.
        </p>
      </div>
    </div>
  );
}
