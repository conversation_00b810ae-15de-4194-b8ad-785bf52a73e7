/**
 * Test Script for Booking-Customer Relationship Fix
 * 
 * This script helps verify that the booking form correctly displays
 * customer information when editing existing bookings.
 * 
 * Usage:
 * 1. Open browser developer tools
 * 2. Navigate to admin bookings page
 * 3. Run this script in the console
 * 4. Follow the test instructions
 */

console.log('🧪 Booking-Customer Relationship Test Script');
console.log('==========================================');

// Test configuration
const TEST_CONFIG = {
  // Set this to true to enable detailed logging
  verbose: true,
  
  // Test timeout in milliseconds
  timeout: 5000,
  
  // Expected elements
  selectors: {
    bookingForm: '[class*="bookingForm"]',
    customerSelect: 'select[name="customer_id"]',
    customerOptions: 'select[name="customer_id"] option',
    editButtons: 'button[class*="editButton"], button:contains("Edit")',
    rescheduleButtons: 'button:contains("Reschedule"), button:contains("📅")'
  }
};

// Utility functions
const log = (message, data = null) => {
  if (TEST_CONFIG.verbose) {
    console.log(`🔍 ${message}`, data || '');
  }
};

const error = (message, data = null) => {
  console.error(`❌ ${message}`, data || '');
};

const success = (message, data = null) => {
  console.log(`✅ ${message}`, data || '');
};

// Test functions
function testCustomerSelectionInForm() {
  log('Testing customer selection in booking form...');
  
  const customerSelect = document.querySelector(TEST_CONFIG.selectors.customerSelect);
  
  if (!customerSelect) {
    error('Customer select element not found. Make sure booking form is open.');
    return false;
  }
  
  const selectedValue = customerSelect.value;
  const selectedOption = customerSelect.querySelector(`option[value="${selectedValue}"]`);
  
  log('Customer select state:', {
    value: selectedValue,
    hasValue: !!selectedValue,
    optionText: selectedOption?.textContent,
    totalOptions: customerSelect.options.length
  });
  
  if (!selectedValue || selectedValue === '') {
    error('No customer selected in form - this indicates the bug is still present');
    return false;
  }
  
  if (selectedOption && selectedOption.textContent.includes('Select')) {
    error('Customer select shows "Select Customer" - bug still present');
    return false;
  }
  
  if (selectedOption) {
    success(`Customer correctly selected: ${selectedOption.textContent}`);
    return true;
  }
  
  error('Selected customer option not found in dropdown');
  return false;
}

function testFormDataConsistency() {
  log('Testing form data consistency...');
  
  // Look for React component data in the DOM
  const formElement = document.querySelector(TEST_CONFIG.selectors.bookingForm);
  
  if (!formElement) {
    error('Booking form not found');
    return false;
  }
  
  // Check if form has the expected structure
  const customerSelect = formElement.querySelector('select[name="customer_id"]');
  const serviceSelect = formElement.querySelector('select[name="service_id"]');
  const startTimeInput = formElement.querySelector('input[name="start_time"]');
  
  const hasRequiredFields = !!(customerSelect && serviceSelect && startTimeInput);
  
  log('Form structure check:', {
    hasCustomerSelect: !!customerSelect,
    hasServiceSelect: !!serviceSelect,
    hasStartTimeInput: !!startTimeInput,
    allFieldsPresent: hasRequiredFields
  });
  
  if (!hasRequiredFields) {
    error('Form is missing required fields');
    return false;
  }
  
  success('Form structure is correct');
  return true;
}

function checkConsoleForErrors() {
  log('Checking for console errors...');
  
  // This is a basic check - in a real test environment,
  // you would capture console errors programmatically
  console.log('📝 Manual Check: Look for any red error messages in the console above');
  console.log('📝 Expected: You should see green "BookingForm:" log messages');
  
  return true;
}

function runAllTests() {
  console.log('\n🚀 Starting Booking-Customer Relationship Tests...\n');
  
  const tests = [
    { name: 'Form Data Consistency', fn: testFormDataConsistency },
    { name: 'Customer Selection', fn: testCustomerSelectionInForm },
    { name: 'Console Error Check', fn: checkConsoleForErrors }
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach((test, index) => {
    console.log(`\n${index + 1}. Running ${test.name}...`);
    
    try {
      const result = test.fn();
      if (result) {
        passed++;
        success(`${test.name} PASSED`);
      } else {
        failed++;
        error(`${test.name} FAILED`);
      }
    } catch (err) {
      failed++;
      error(`${test.name} ERROR:`, err.message);
    }
  });
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    success('🎉 All tests passed! The booking-customer relationship fix is working correctly.');
  } else {
    error('⚠️ Some tests failed. Please check the issues above.');
  }
  
  return { passed, failed };
}

// Instructions for manual testing
function showManualTestInstructions() {
  console.log('\n📋 Manual Testing Instructions:');
  console.log('================================');
  console.log('1. Navigate to the admin bookings page');
  console.log('2. Click on an existing booking to view details');
  console.log('3. Click "Edit Booking" or "Reschedule" button');
  console.log('4. Observe the customer field in the form');
  console.log('5. Run runAllTests() in the console');
  console.log('\n✅ Expected Result:');
  console.log('   - Customer field should show the customer name immediately');
  console.log('   - No "Select Customer" placeholder should be visible');
  console.log('   - Console should show "BookingForm:" debug messages');
  console.log('\n❌ Bug Present If:');
  console.log('   - Customer field shows "Select Customer"');
  console.log('   - Customer name is not pre-populated');
  console.log('   - Console shows errors or no debug messages');
}

// Export functions for manual use
window.testBookingCustomerRelationship = {
  runAllTests,
  testCustomerSelectionInForm,
  testFormDataConsistency,
  checkConsoleForErrors,
  showManualTestInstructions,
  config: TEST_CONFIG
};

// Auto-run instructions
showManualTestInstructions();

console.log('\n🔧 Available Commands:');
console.log('- testBookingCustomerRelationship.runAllTests()');
console.log('- testBookingCustomerRelationship.showManualTestInstructions()');
console.log('- testBookingCustomerRelationship.testCustomerSelectionInForm()');

// Auto-run if form is already open
setTimeout(() => {
  const formExists = document.querySelector(TEST_CONFIG.selectors.bookingForm);
  if (formExists) {
    console.log('\n🎯 Booking form detected! Running automatic tests...');
    runAllTests();
  } else {
    console.log('\n⏳ No booking form detected. Open a booking for editing and run tests manually.');
  }
}, 1000);

console.log('\n✨ Test script loaded successfully!');
