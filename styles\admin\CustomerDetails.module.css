.customerDetails {
  width: 100%;
}

/* Enhanced Profile Header */
.profileHeader {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.customerInfo {
  flex: 1;
}

.customerName h1 {
  font-size: 2.2rem;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.customerBadges {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.statusBadge {
  display: inline-block;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tierBadge {
  display: inline-block;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.marketingBadge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.customerMeta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  opacity: 0.9;
}

.customerEmail {
  font-size: 1.1rem;
  font-weight: 500;
}

.customerPhone {
  font-size: 1rem;
}

.customerSince {
  font-size: 0.9rem;
  opacity: 0.8;
}

.quickActions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}

.primaryButton, .secondaryButton, .dangerButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  justify-content: center;
}

.primaryButton {
  background: rgba(255, 255, 255, 0.9);
  color: #6e8efb;
}

.primaryButton:hover {
  background: white;
  transform: translateY(-1px);
}

.secondaryButton {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dangerButton {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.dangerButton:hover {
  background: rgba(244, 67, 54, 0.2);
}

/* Statistics Cards */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 24px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.statIcon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.statContent {
  flex: 1;
}

.statNumber {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.editButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.deleteButton {
  padding: 8px 16px;
  background-color: transparent;
  color: #d32f2f;
  border: 1px solid #d32f2f;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deleteButton:hover:not(:disabled) {
  background-color: rgba(211, 47, 47, 0.1);
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.leftColumn, .rightColumn {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Customer Notes */
.notesContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.existingNotes {
  min-height: 60px;
}

.noteItem {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #6e8efb;
}

.noteContent {
  font-size: 1rem;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.noteDate {
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

.noNotes {
  color: #999;
  font-style: italic;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.addNoteButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.addNoteButton:hover {
  background: #5a7cfa;
  transform: translateY(-1px);
}

/* Activity Timeline */
.activityTimeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.timelineItem {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.timelineItem:hover {
  border-color: #6e8efb;
  box-shadow: 0 2px 8px rgba(110, 142, 251, 0.1);
}

.timelineIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.timelineContent {
  flex: 1;
}

.timelineTitle {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.timelineDescription {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 6px;
}

.timelineDate {
  color: #999;
  font-size: 0.8rem;
}

.noActivity {
  color: #999;
  font-style: italic;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  border-radius: 8px;
}

.infoSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 8px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 0.9rem;
  color: #666;
}

.infoValue {
  font-size: 1rem;
  color: #333;
}

.infoValue a {
  color: #6e8efb;
  text-decoration: none;
}

.infoValue a:hover {
  text-decoration: underline;
}

.notes {
  white-space: pre-line;
  color: #333;
  line-height: 1.5;
}

.preferencesList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preferenceItem {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.preferenceKey {
  font-weight: 500;
  color: #555;
}

.preferenceValue {
  color: #333;
}

.marketingConsent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.consentStatus {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.consentGranted {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.consentDenied {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
}

.consentDescription {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.notificationButton {
  display: inline-block;
  margin-top: 12px;
  padding: 8px 16px;
  background-color: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notificationButton:hover:not(:disabled) {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.notificationButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.gdprButton {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.gdprButton:hover:not(:disabled) {
  background-color: #e5e5e5;
}

.gdprDescription {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.bookingHistory {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.modalContent {
  padding: 24px;
}

.modalContent h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.4rem;
  color: #333;
}

.modalContent p {
  margin-bottom: 24px;
  color: #555;
  line-height: 1.5;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: #6e8efb;
  outline: none;
}

.select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.select:focus {
  border-color: #6e8efb;
  outline: none;
}

.textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 100px;
}

.textarea:focus {
  border-color: #6e8efb;
  outline: none;
}

.sendButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sendButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sendButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #4caf50;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.1rem;
  color: #666;
}

@media (max-width: 992px) {
  .content {
    grid-template-columns: 1fr;
  }

  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .profileHeader {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }

  .quickActions {
    flex-direction: row;
    flex-wrap: wrap;
    min-width: auto;
  }

  .primaryButton, .secondaryButton, .dangerButton {
    flex: 1;
    min-width: 120px;
  }

  .statsContainer {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .statCard {
    padding: 20px;
  }

  .customerName h1 {
    font-size: 1.8rem;
  }

  .infoGrid {
    grid-template-columns: 1fr;
  }

  .modalActions {
    flex-direction: column-reverse;
  }

  .modalActions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .profileHeader {
    padding: 16px;
  }

  .customerName h1 {
    font-size: 1.5rem;
  }

  .quickActions {
    flex-direction: column;
  }

  .statCard {
    padding: 16px;
  }

  .statIcon {
    width: 40px;
    height: 40px;
  }

  .statNumber {
    font-size: 1.5rem;
  }
}
