import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const settings = [
  ['square_application_id', ''],
  ['square_access_token', ''],
  ['square_environment', 'sandbox'],
  ['square_location_id', ''],
  ['square_webhook_signature_key', ''],
  ['square_currency', 'AUD'],
  ['enable_square_payments', 'false'],
  ['google_analytics_measurement_id', ''],
  ['google_tag_manager_id', ''],
  ['google_search_console_verification', ''],
  ['google_business_profile_id', ''],
  ['google_my_business_api_key', ''],
  ['google_maps_api_key', ''],
  ['google_ads_customer_id', ''],
  ['google_ads_conversion_id', ''],
  ['enable_google_business_integration', 'false'],
  ['enable_google_reviews_widget', 'false'],
  ['seo_meta_title', ''],
  ['seo_meta_description', ''],
  ['seo_keywords', ''],
  ['seo_canonical_url', ''],
  ['enable_schema_markup', 'true'],
  ['smtp_host', ''],
  ['smtp_port', '587'],
  ['smtp_username', ''],
  ['smtp_password', ''],
  ['smtp_encryption', 'tls'],
  ['email_from_address', ''],
  ['email_from_name', ''],
  ['enable_email_notifications', 'true'],
  ['enable_auto_backup', 'false'],
  ['backup_frequency', 'weekly'],
  ['enable_two_factor_auth', 'false'],
  ['session_timeout', '60']
];

async function addSettings() {
  console.log('Adding enhanced settings to database...');
  
  for (const [key, value] of settings) {
    try {
      const { data, error } = await supabase
        .from('settings')
        .upsert({ key, value }, { onConflict: 'key' });
      
      if (error) {
        console.log(`❌ Error adding ${key}: ${error.message}`);
      } else {
        console.log(`✅ Added/updated setting: ${key}`);
      }
    } catch (err) {
      console.log(`❌ Exception adding ${key}: ${err.message}`);
    }
  }
  
  console.log('Enhanced settings migration complete!');
}

addSettings().catch(console.error);
