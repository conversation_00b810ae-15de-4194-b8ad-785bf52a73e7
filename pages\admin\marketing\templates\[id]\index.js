import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import Modal from '@/components/admin/Modal'
import styles from '@/styles/admin/marketing/TemplateDetail.module.css'

export default function TemplateDetail() {
  const router = useRouter()
  const { id } = router.query
  const [template, setTemplate] = useState(null)
  const [usageCount, setUsageCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteError, setDeleteError] = useState(null)
  const [showApplyModal, setShowApplyModal] = useState(false)
  const [campaigns, setCampaigns] = useState([])
  const [selectedCampaign, setSelectedCampaign] = useState('')
  const [applyLoading, setApplyLoading] = useState(false)
  const [applyError, setApplyError] = useState(null)
  const [applySuccess, setApplySuccess] = useState(null)
  const [campaignsLoading, setCampaignsLoading] = useState(false)

  // Fetch template data
  useEffect(() => {
    if (!id) return

    const fetchTemplate = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/templates/${id}`)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch template')
        }

        const data = await response.json()
        setTemplate(data.template || null)
        setUsageCount(data.usage_count || 0)
      } catch (error) {
        console.error('Error fetching template:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchTemplate()
  }, [id])

  // Fetch active campaigns when apply modal is opened
  const fetchActiveCampaigns = async () => {
    if (!template) return

    setCampaignsLoading(true)

    try {
      const response = await fetch(`/api/marketing/campaigns?status=active&campaign_type=${template.template_type}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch campaigns')
      }

      const data = await response.json()
      setCampaigns(data.campaigns || [])

      if (data.campaigns && data.campaigns.length > 0) {
        setSelectedCampaign(data.campaigns[0].id)
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error)
      setApplyError(error.message)
    } finally {
      setCampaignsLoading(false)
    }
  }

  // Handle delete template
  const handleDeleteTemplate = async () => {
    setDeleteLoading(true)
    setDeleteError(null)

    try {
      const response = await fetch(`/api/marketing/templates/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete template')
      }

      // Redirect to template list
      router.push('/admin/marketing/templates')
    } catch (error) {
      console.error('Error deleting template:', error)
      setDeleteError(error.message)
      setDeleteLoading(false)
    }
  }

  // Handle apply template to campaign
  const handleApplyTemplate = async () => {
    if (!selectedCampaign) {
      setApplyError('Please select a campaign')
      return
    }

    setApplyLoading(true)
    setApplyError(null)
    setApplySuccess(null)

    try {
      const response = await fetch(`/api/marketing/templates/${id}/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          campaign_id: selectedCampaign
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to apply template')
      }

      const data = await response.json()
      setApplySuccess('Template applied successfully')

      // Close modal after a short delay
      setTimeout(() => {
        setShowApplyModal(false)
        setApplySuccess(null)

        // Redirect to the campaign message
        if (data.message && data.message.id) {
          router.push(`/admin/marketing/campaigns/${selectedCampaign}`)
        }
      }, 1500)
    } catch (error) {
      console.error('Error applying template:', error)
      setApplyError(error.message)
    } finally {
      setApplyLoading(false)
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get category label
  const getCategoryLabel = (categoryValue) => {
    if (!categoryValue) return '-'

    const categories = {
      welcome: 'Welcome',
      promotion: 'Promotion',
      event: 'Event',
      reminder: 'Reminder',
      confirmation: 'Confirmation',
      thank_you: 'Thank You',
      abandoned_cart: 'Abandoned Cart',
      feedback: 'Feedback',
      other: 'Other'
    }

    return categories[categoryValue] || categoryValue
  }

  // Preview personalized content
  const previewPersonalizedContent = (text) => {
    if (!text) return ''

    const previewCustomer = {
      name: 'Jane Smith',
      first_name: 'Jane',
      email: '<EMAIL>',
      phone: '0412 345 678',
      city: 'Sydney',
      state: 'NSW'
    }

    return text
      .replace(/\{name\}/g, previewCustomer.name)
      .replace(/\{first_name\}/g, previewCustomer.first_name)
      .replace(/\{email\}/g, previewCustomer.email)
      .replace(/\{phone\}/g, previewCustomer.phone)
      .replace(/\{city\}/g, previewCustomer.city)
      .replace(/\{state\}/g, previewCustomer.state)
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.templateDetail}>
          <div className={styles.loading}>Loading template data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.templateDetail}>
          <div className={styles.error}>
            Error: {error}
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/templates')}
            >
              Back to Templates
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!template) {
    return (
      <AdminLayout>
        <div className={styles.templateDetail}>
          <div className={styles.notFound}>
            Template not found
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/templates')}
            >
              Back to Templates
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.templateDetail}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <h2>{template.name}</h2>
            <div className={styles.meta}>
              <span className={`${styles.status} ${template.is_active ? styles.statusActive : styles.statusInactive}`}>
                {template.is_active ? 'Active' : 'Inactive'}
              </span>
              <span className={styles.type}>
                {template.template_type === 'email' ? 'Email Template' :
                 template.template_type === 'sms' ? 'SMS Template' : 'Push Notification Template'}
              </span>
              {template.category && (
                <span className={styles.category}>
                  Category: {getCategoryLabel(template.category)}
                </span>
              )}
            </div>
          </div>
          <div className={styles.headerActions}>
            <button
              className={styles.applyButton}
              onClick={() => {
                setShowApplyModal(true)
                fetchActiveCampaigns()
              }}
              disabled={!template.is_active}
            >
              Apply to Campaign
            </button>
            <Link href={`/admin/marketing/templates/${id}/edit`} className={styles.editButton}>
              Edit Template
            </Link>
            <button
              className={styles.deleteButton}
              onClick={() => setShowDeleteModal(true)}
              disabled={usageCount > 0}
            >
              Delete
            </button>
          </div>
        </div>

        <div className={styles.templateInfo}>
          <div className={styles.infoSection}>
            <h3>Template Details</h3>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Description</span>
                <span className={styles.infoValue}>
                  {template.description || 'No description provided'}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Created</span>
                <span className={styles.infoValue}>
                  {formatDate(template.created_at)}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Last Updated</span>
                <span className={styles.infoValue}>
                  {formatDate(template.updated_at)}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Usage Count</span>
                <span className={styles.infoValue}>
                  {usageCount} {usageCount === 1 ? 'campaign' : 'campaigns'}
                </span>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Template Content</h3>

            {template.template_type === 'email' && template.subject && (
              <div className={styles.contentItem}>
                <span className={styles.contentLabel}>Subject</span>
                <span className={styles.contentValue}>
                  {template.subject}
                </span>
              </div>
            )}

            <div className={styles.contentItem}>
              <span className={styles.contentLabel}>Content</span>
              <div className={styles.contentValue}>
                <pre className={styles.contentPre}>{template.content}</pre>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Preview</h3>

            {template.template_type === 'email' && template.subject && (
              <div className={styles.previewItem}>
                <span className={styles.previewLabel}>Subject</span>
                <span className={styles.previewValue}>
                  {previewPersonalizedContent(template.subject)}
                </span>
              </div>
            )}

            <div className={styles.previewItem}>
              <span className={styles.previewLabel}>Content</span>
              <div
                className={`${styles.previewValue} ${template.template_type === 'email' ? styles.emailPreview : ''}`}
              >
                {template.template_type === 'email' ? (
                  <div dangerouslySetInnerHTML={{ __html: previewPersonalizedContent(template.content) }} />
                ) : (
                  previewPersonalizedContent(template.content).split('\n').map((line, i) => (
                    <p key={i}>{line}</p>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <Modal onClose={() => setShowDeleteModal(false)}>
            <div className={styles.deleteModal}>
              <h3>Delete Template</h3>
              <p>
                Are you sure you want to delete the template "{template.name}"?
                This action cannot be undone.
              </p>
              {deleteError && (
                <div className={styles.deleteError}>
                  Error: {deleteError}
                </div>
              )}
              <div className={styles.deleteActions}>
                <button
                  className={styles.cancelDeleteButton}
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleteLoading}
                >
                  Cancel
                </button>
                <button
                  className={styles.confirmDeleteButton}
                  onClick={handleDeleteTemplate}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? 'Deleting...' : 'Delete Template'}
                </button>
              </div>
            </div>
          </Modal>
        )}

        {/* Apply Template Modal */}
        {showApplyModal && (
          <Modal onClose={() => !applyLoading && setShowApplyModal(false)}>
            <div className={styles.applyModal}>
              <h3>Apply Template to Campaign</h3>
              <p>
                Select a campaign to apply this template to. This will create a new message in the selected campaign.
              </p>

              {applyError && (
                <div className={styles.applyError}>
                  Error: {applyError}
                </div>
              )}

              {applySuccess && (
                <div className={styles.applySuccess}>
                  {applySuccess}
                </div>
              )}

              {campaignsLoading ? (
                <div className={styles.campaignsLoading}>
                  Loading campaigns...
                </div>
              ) : campaigns.length === 0 ? (
                <div className={styles.noCampaigns}>
                  <p>No active campaigns found for {template.template_type} messages.</p>
                  <Link href="/admin/marketing/campaigns/new" className={styles.createCampaignButton}>
                    Create Campaign
                  </Link>
                </div>
              ) : (
                <>
                  <div className={styles.formGroup}>
                    <label htmlFor="campaign-select">Select Campaign</label>
                    <select
                      id="campaign-select"
                      value={selectedCampaign}
                      onChange={(e) => setSelectedCampaign(e.target.value)}
                      className={styles.select}
                      disabled={applyLoading}
                    >
                      {campaigns.map((campaign) => (
                        <option key={campaign.id} value={campaign.id}>
                          {campaign.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className={styles.applyActions}>
                    <button
                      className={styles.cancelApplyButton}
                      onClick={() => setShowApplyModal(false)}
                      disabled={applyLoading}
                    >
                      Cancel
                    </button>
                    <button
                      className={styles.confirmApplyButton}
                      onClick={handleApplyTemplate}
                      disabled={applyLoading || !selectedCampaign}
                    >
                      {applyLoading ? 'Applying...' : 'Apply Template'}
                    </button>
                  </div>
                </>
              )}
            </div>
          </Modal>
        )}
      </div>
    </AdminLayout>
  )
}
