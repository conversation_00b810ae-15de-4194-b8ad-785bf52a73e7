import { useState } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * ServiceTileGrid component for displaying services as clickable tiles
 *
 * @param {Object} props - Component props
 * @param {Array} props.services - Array of services with pricing tiers
 * @param {Function} props.onServiceSelect - Callback when service is selected
 * @returns {JSX.Element}
 */
export default function ServiceTileGrid({ services, onServiceSelect }) {
  const [selectedServiceId, setSelectedServiceId] = useState(null)
  const [serviceAvailability, setServiceAvailability] = useState({})

  // Get category icon based on service category
  const getCategoryIcon = (category) => {
    const iconMap = {
      'painting': '🎨',
      'makeup': '💄',
      'hair': '💇‍♀️',
      'nails': '💅',
      'massage': '💆‍♀️',
      'skincare': '✨',
      'photography': '📸',
      'entertainment': '🎭',
      'general': '⭐'
    }
    return iconMap[category?.toLowerCase()] || iconMap.general
  }

  // Calculate price range for a service based on its pricing tiers
  const getPriceRange = (pricingTiers, fallbackPrice) => {
    if (!pricingTiers || pricingTiers.length === 0) {
      return `$${parseFloat(fallbackPrice || 0).toFixed(2)}`
    }

    const prices = pricingTiers.map(tier => parseFloat(tier.price || 0))
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    if (minPrice === maxPrice) {
      return `$${minPrice.toFixed(2)}`
    }

    return `$${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`
  }

  const handleServiceClick = (service) => {
    setSelectedServiceId(service.id)
    // Small delay for visual feedback before proceeding
    setTimeout(() => {
      onServiceSelect(service)
    }, 150)
  }

  // Get availability status for a service based on available artists
  const getAvailabilityStatus = (service) => {
    const serviceId = service.id

    // Check if we already calculated availability for this service
    const cachedAvailability = serviceAvailability[serviceId]
    if (cachedAvailability !== undefined) return cachedAvailability

    // Determine availability based on artist count and availability
    let status = 'unavailable'

    if (service.availableArtistCount > 0) {
      // Check if any artists are available today
      const availableToday = service.availableArtists?.filter(artist => artist.isAvailableToday) || []

      if (availableToday.length >= 2) {
        status = 'available' // Multiple artists available
      } else if (availableToday.length === 1) {
        status = 'busy' // Only one artist available
      } else {
        status = 'unavailable' // No artists available today
      }
    }

    // Cache the result
    setServiceAvailability(prev => ({ ...prev, [serviceId]: status }))
    return status
  }

  if (!services || services.length === 0) {
    return (
      <div className={styles.serviceGrid}>
        <div className={styles.noServices}>
          <h3>No Services Available</h3>
          <p>Please add services in the inventory section to use the POS terminal.</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.serviceGrid}>
      {services.map((service) => {
        try {
          const isSelected = selectedServiceId === service.id
          const icon = getCategoryIcon(service.category)
          const priceRange = getPriceRange(service.pricing_tiers, service.price)
          const availabilityStatus = getAvailabilityStatus(service)

          // Get artist availability info for display
          const availableToday = service.availableArtists?.filter(artist => artist.isAvailableToday) || []
          const artistCountText = availableToday.length > 0
            ? `${availableToday.length} artist${availableToday.length > 1 ? 's' : ''} available`
            : 'No artists available'

          return (
            <div
              key={service.id}
              className={`${styles.serviceTile} ${isSelected ? styles.selected : ''}`}
              onClick={() => handleServiceClick(service)}
            >
              {/* Availability Indicator */}
              <div className={`${styles.serviceAvailability} ${styles[availabilityStatus]}`}
                   title={`${artistCountText} - ${availabilityStatus}`}
              />

              <span className={styles.serviceIcon}>
                {icon}
              </span>

              <h3 className={styles.serviceName}>
                {safeRender(service.name, 'Unnamed Service')}
              </h3>

              <p className={styles.serviceDescription}>
                {safeRender(service.description, 'No description available')}
              </p>

              <div className={styles.servicePriceRange}>
                {priceRange}
              </div>

              {/* Artist availability info */}
              <div className={styles.artistAvailability}>
                {artistCountText}
              </div>

              {service.pricing_tiers && service.pricing_tiers.length > 1 && (
                <div className={styles.tierCount}>
                  {service.pricing_tiers.length} duration options
                </div>
              )}
            </div>
          )
        } catch (error) {
          console.error('Error rendering service tile:', error, 'Service:', service)
          return (
            <div key={service.id || Math.random()} className={styles.serviceTile}>
              <div className={styles.serviceError}>
                <span className={styles.serviceIcon}>⚠️</span>
                <h3 className={styles.serviceName}>Error Loading Service</h3>
                <p className={styles.serviceDescription}>
                  Unable to display this service. Please refresh the page.
                </p>
              </div>
            </div>
          )
        }
      })}
    </div>
  )
}
