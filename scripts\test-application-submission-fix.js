#!/usr/bin/env node

/**
 * Test Application Submission Fix Script
 * Verifies that the column name mismatch is resolved
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function testApplicationSubmissionFix() {
  console.log('🧪 Testing Application Submission Fix\n')

  let allPassed = true
  const results = []

  // Test 1: Verify column name mapping
  console.log('1. Testing column name mapping...')
  try {
    const testUserId = '17464307-4beb-4156-ab10-96a29df4f367'
    
    // Test updating with professional_references (correct column name)
    const { data: updateResult, error: updateError } = await supabase
      .from('artist_braider_applications')
      .update({
        professional_references: 'Test reference update',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', testUserId)
      .select()

    if (updateError) {
      console.error('❌ Update with professional_references failed:', updateError.message)
      allPassed = false
      results.push({ test: 'column_mapping', passed: false, error: updateError.message })
    } else {
      console.log('✅ Update with professional_references successful')
      results.push({ test: 'column_mapping', passed: true })
    }
  } catch (error) {
    console.error('❌ Column mapping test failed:', error.message)
    allPassed = false
    results.push({ test: 'column_mapping', passed: false, error: error.message })
  }

  // Test 2: Verify the old column name fails (as expected)
  console.log('\n2. Confirming old column name fails...')
  try {
    const testUserId = '17464307-4beb-4156-ab10-96a29df4f367'
    
    // This should fail with the old column name
    const { data: failResult, error: failError } = await supabase
      .from('artist_braider_applications')
      .update({
        references: 'This should fail',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', testUserId)
      .select()

    if (failError && failError.message.includes('column "references" does not exist')) {
      console.log('✅ Old column name correctly fails (as expected)')
      results.push({ test: 'old_column_fails', passed: true })
    } else if (failError) {
      console.log('✅ Old column name fails with error (expected):', failError.message)
      results.push({ test: 'old_column_fails', passed: true })
    } else {
      console.log('⚠️  Old column name unexpectedly succeeded')
      results.push({ test: 'old_column_fails', passed: false, error: 'Old column should not exist' })
    }
  } catch (error) {
    console.log('✅ Old column name correctly throws error (expected):', error.message)
    results.push({ test: 'old_column_fails', passed: true })
  }

  // Test 3: Test complete application data structure
  console.log('\n3. Testing complete application data structure...')
  try {
    const testUserId = '17464307-4beb-4156-ab10-96a29df4f367'
    
    // Test with all the fields that the API uses
    const { data: completeResult, error: completeError } = await supabase
      .from('artist_braider_applications')
      .update({
        experience_years: 5,
        portfolio_url: 'https://example.com/portfolio',
        availability_preferences: { weekdays: ['monday', 'tuesday'], flexible: true },
        service_specializations: ['hair_braiding', 'face_painting'],
        previous_experience: 'Test previous experience',
        professional_references: 'Test professional references',
        status: 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', testUserId)
      .select()

    if (completeError) {
      console.error('❌ Complete application update failed:', completeError.message)
      allPassed = false
      results.push({ test: 'complete_structure', passed: false, error: completeError.message })
    } else {
      console.log('✅ Complete application structure update successful')
      results.push({ test: 'complete_structure', passed: true })
    }
  } catch (error) {
    console.error('❌ Complete structure test failed:', error.message)
    allPassed = false
    results.push({ test: 'complete_structure', passed: false, error: error.message })
  }

  // Test 4: Verify current application data
  console.log('\n4. Verifying current application data...')
  try {
    const testUserId = '17464307-4beb-4156-ab10-96a29df4f367'
    
    const { data: currentApp, error: currentError } = await supabase
      .from('artist_braider_applications')
      .select('*')
      .eq('user_id', testUserId)
      .single()

    if (currentError) {
      console.error('❌ Failed to fetch current application:', currentError.message)
      allPassed = false
      results.push({ test: 'current_data', passed: false, error: currentError.message })
    } else {
      console.log('✅ Current application data retrieved successfully')
      console.log('   Application ID:', currentApp.id)
      console.log('   Application Type:', currentApp.application_type)
      console.log('   Status:', currentApp.status)
      console.log('   Professional References:', currentApp.professional_references ? 'Present' : 'Empty')
      console.log('   Available columns:', Object.keys(currentApp))
      results.push({ test: 'current_data', passed: true })
    }
  } catch (error) {
    console.error('❌ Current data test failed:', error.message)
    allPassed = false
    results.push({ test: 'current_data', passed: false, error: error.message })
  }

  // Test 5: Test token validation still works
  console.log('\n5. Testing token validation...')
  try {
    const { data: tokens, error: tokenError } = await supabase
      .from('application_tokens')
      .select('*')
      .eq('user_id', '17464307-4beb-4156-ab10-96a29df4f367')
      .order('created_at', { ascending: false })
      .limit(1)

    if (tokenError) {
      console.error('❌ Token validation test failed:', tokenError.message)
      allPassed = false
      results.push({ test: 'token_validation', passed: false, error: tokenError.message })
    } else {
      console.log(`✅ Token validation working (found ${tokens?.length || 0} tokens)`)
      if (tokens && tokens.length > 0) {
        console.log('   Latest token:', tokens[0].token.substring(0, 8) + '...')
        console.log('   Token expires:', tokens[0].expires_at)
        console.log('   Token used:', tokens[0].used_at ? 'Yes' : 'No')
      }
      results.push({ test: 'token_validation', passed: true })
    }
  } catch (error) {
    console.error('❌ Token validation test failed:', error.message)
    allPassed = false
    results.push({ test: 'token_validation', passed: false, error: error.message })
  }

  // Test 6: Simulate API call structure
  console.log('\n6. Simulating API call structure...')
  try {
    // This simulates what the API does when it receives form data
    const formData = {
      application_type: 'artist',
      experience_years: 3,
      portfolio_url: 'https://example.com/test',
      availability_preferences: { weekdays: ['wednesday'], flexible: false },
      service_specializations: ['body_art'],
      previous_experience: 'API simulation test',
      references: 'API simulation references' // This is what comes from the form
    }

    // This is how the API should map it to the database
    const dbData = {
      experience_years: formData.experience_years,
      portfolio_url: formData.portfolio_url || null,
      availability_preferences: formData.availability_preferences || {},
      service_specializations: formData.service_specializations,
      previous_experience: formData.previous_experience,
      professional_references: formData.references || null, // Mapped correctly
      status: 'pending',
      updated_at: new Date().toISOString()
    }

    const { data: apiResult, error: apiError } = await supabase
      .from('artist_braider_applications')
      .update(dbData)
      .eq('user_id', '17464307-4beb-4156-ab10-96a29df4f367')
      .select()

    if (apiError) {
      console.error('❌ API simulation failed:', apiError.message)
      allPassed = false
      results.push({ test: 'api_simulation', passed: false, error: apiError.message })
    } else {
      console.log('✅ API simulation successful')
      results.push({ test: 'api_simulation', passed: true })
    }
  } catch (error) {
    console.error('❌ API simulation test failed:', error.message)
    allPassed = false
    results.push({ test: 'api_simulation', passed: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Test Results Summary:')
  const passedCount = results.filter(r => r.passed).length
  const totalCount = results.length
  
  console.log(`   Passed tests: ${passedCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.test}${error}`)
  })

  console.log(`\n   Overall status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

  if (allPassed) {
    console.log('\n🎉 Application submission fix is working!')
    console.log('\n📋 Next Steps:')
    console.log('   1. Deploy the API fixes to production')
    console.log('   2. Test the complete application submission workflow')
    console.log('   3. Verify that PGRST204 errors are resolved')
    console.log('   4. Monitor application submissions for any remaining issues')
  } else {
    console.log('\n🔧 Issues to Address:')
    results.filter(r => !r.passed).forEach(result => {
      console.log(`   - ${result.test}: ${result.error}`)
    })
  }

  return allPassed
}

// Run the test script
testApplicationSubmissionFix()
  .then(success => {
    if (success) {
      console.log('\n✅ Application submission fix test completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Application submission fix test completed with failures!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Test script error:', error)
    process.exit(1)
  })
