.shopShowcase {
  position: relative;
  height: 100vh;
  min-height: 700px;
  width: 100%;
  background-size: cover;
  background-position: center;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
  color: white;
}

.overlayGradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 115, 232, 0.8) 0%, rgba(255, 107, 107, 0.8) 100%);
  z-index: -1;
}

.floatingProducts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  will-change: transform;
  transition: transform 0.2s ease-out;
}

.floatingProduct {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: floatAnimation 8s infinite ease-in-out;
}

.floatingProduct:nth-child(1) {
  top: 15%;
  left: 20%;
}

.floatingProduct:nth-child(2) {
  top: 65%;
  left: 15%;
  animation-duration: 9s;
}

.floatingProduct:nth-child(3) {
  top: 25%;
  right: 20%;
  animation-duration: 10s;
}

.floatingProduct:nth-child(4) {
  top: 60%;
  right: 15%;
  animation-duration: 11s;
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(-15px, 15px) rotate(3deg);
  }
  50% {
    transform: translate(10px, -10px) rotate(-3deg);
  }
  75% {
    transform: translate(-10px, -15px) rotate(2deg);
  }
}

.productImageContainer {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid;
  position: relative;
  transform: rotate(-5deg);
  transition: all 0.3s ease;
  z-index: 2;
}

.floatingProduct:hover .productImageContainer {
  transform: rotate(0) scale(1.05);
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.floatingProduct:hover .productImage {
  transform: scale(1.1);
}

.productGlow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  filter: blur(30px);
  opacity: 0.4;
  z-index: 1;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.floatingProduct:hover .productGlow {
  opacity: 0.6;
  transform: scale(1);
}

.showcaseContent {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
}

.showcaseTitle {
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  background: linear-gradient(to right, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.showcaseTitle::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: white;
  border-radius: 2px;
}

.showcaseSubtitle {
  font-size: 1.8rem;
  max-width: 800px;
  margin: 2rem auto 4rem;
  line-height: 1.6;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

.showcaseCta {
  margin-top: 3rem;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: white;
  color: #1A73E8;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.ctaButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transform: translateX(-100%);
}

.ctaButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
  background-color: #f8f8f8;
}

.ctaButton:hover::before {
  animation: shineButton 1.5s infinite;
}

@keyframes shineButton {
  100% {
    transform: translateX(100%);
  }
}

.btnIcon {
  display: inline-flex;
  transition: transform 0.3s ease;
}

.ctaButton:hover .btnIcon {
  transform: translateX(5px);
}

.scrollIndicator {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 0.9rem;
  opacity: 0.8;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 3;
}

.scrollIndicator:hover {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.mouseIcon {
  width: 30px;
  height: 50px;
  border: 2px solid white;
  border-radius: 20px;
  margin-bottom: 10px;
  position: relative;
}

.mouseIcon::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  animation: mouseScroll 2s infinite;
}

@keyframes mouseScroll {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  75% {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  76% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .showcaseTitle {
    font-size: 3.5rem;
  }
  
  .showcaseSubtitle {
    font-size: 1.6rem;
  }
  
  .productImageContainer {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 992px) {
  .showcaseTitle {
    font-size: 3rem;
  }
  
  .showcaseSubtitle {
    font-size: 1.4rem;
    margin-bottom: 3rem;
  }
  
  .productImageContainer {
    width: 120px;
    height: 120px;
  }
  
  .floatingProduct:nth-child(1),
  .floatingProduct:nth-child(4) {
    display: none;
  }
}

@media (max-width: 768px) {
  .shopShowcase {
    min-height: 800px;
    height: auto;
    padding: 6rem 1rem;
  }
  
  .showcaseTitle {
    font-size: 2.5rem;
  }
  
  .showcaseTitle::after {
    width: 80px;
    height: 3px;
  }
  
  .showcaseSubtitle {
    font-size: 1.2rem;
    max-width: 90%;
    margin-bottom: 2.5rem;
  }
  
  .ctaButton {
    padding: 0.9rem 2rem;
    font-size: 1.1rem;
  }
  
  .scrollIndicator {
    display: none;
  }
}

@media (max-width: 480px) {
  .shopShowcase {
    padding: 5rem 1rem;
  }
  
  .showcaseTitle {
    font-size: 2.2rem;
  }
  
  .showcaseSubtitle {
    font-size: 1.1rem;
  }
  
  .productImageContainer {
    width: 100px;
    height: 100px;
    border-width: 3px;
  }
  
  .ctaButton {
    padding: 0.8rem 1.8rem;
    font-size: 1rem;
    width: 80%;
  }
} 