/**
 * Gallery Management API
 * Handles CRUD operations for gallery images and categories
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';
import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
    }

    const supabaseAdmin = getAdminClient();

    switch (req.method) {
      case 'GET':
        return await handleGetGalleryItems(req, res, supabaseAdmin);
      case 'POST':
        return await handleCreateGalleryItem(req, res, supabaseAdmin);
      case 'PUT':
        return await handleUpdateGalleryItem(req, res, supabaseAdmin);
      case 'DELETE':
        return await handleDeleteGalleryItem(req, res, supabaseAdmin);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Gallery API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get gallery items with filtering and pagination
 */
async function handleGetGalleryItems(req, res, supabaseAdmin) {
  try {
    const {
      category,
      featured,
      status = 'active',
      search,
      sort_by = 'created_at',
      sort_order = 'desc',
      limit = 50,
      offset = 0
    } = req.query;

    let query = supabaseAdmin
      .from('gallery_items')
      .select(`
        id,
        title,
        description,
        category,
        main_image_url,
        gallery_images,
        status,
        featured,
        display_order,
        meta_title,
        meta_description,
        created_at,
        updated_at,
        gallery_categories!inner(name, slug, color)
      `);

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured === 'true');
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply sorting
    const validSortFields = ['title', 'category', 'created_at', 'updated_at', 'display_order'];
    const sortField = validSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order === 'asc' ? 'asc' : 'desc';

    query = query.order(sortField, { ascending: sortDirection === 'asc' });

    // Apply pagination
    query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    const { data: galleryItems, error } = await query;

    if (error) {
      console.error('Error fetching gallery items:', error);
      return res.status(500).json({ error: 'Failed to fetch gallery items' });
    }

    // Get total count for pagination
    const { count, error: countError } = await supabaseAdmin
      .from('gallery_items')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error getting gallery count:', countError);
    }

    return res.status(200).json({
      success: true,
      data: galleryItems || [],
      pagination: {
        total: count || 0,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < (count || 0)
      }
    });
  } catch (error) {
    console.error('Error in handleGetGalleryItems:', error);
    return res.status(500).json({ error: 'Failed to fetch gallery items' });
  }
}

/**
 * Create new gallery item
 */
async function handleCreateGalleryItem(req, res, supabaseAdmin) {
  try {
    const {
      title,
      description,
      category,
      main_image_url,
      gallery_images = [],
      status = 'active',
      featured = false,
      display_order = 0,
      meta_title,
      meta_description
    } = req.body;

    // Validation
    if (!title || !category || !main_image_url) {
      return res.status(400).json({
        error: 'Missing required fields: title, category, and main_image_url are required'
      });
    }

    // Verify category exists
    const { data: categoryExists } = await supabaseAdmin
      .from('gallery_categories')
      .select('id')
      .eq('slug', category)
      .single();

    if (!categoryExists) {
      return res.status(400).json({ error: 'Invalid category' });
    }

    const galleryItem = {
      title: title.trim(),
      description: description?.trim() || '',
      category,
      main_image_url,
      gallery_images: Array.isArray(gallery_images) ? gallery_images : [],
      status,
      featured: Boolean(featured),
      display_order: parseInt(display_order) || 0,
      meta_title: meta_title?.trim() || title.trim(),
      meta_description: meta_description?.trim() || description?.trim() || ''
    };

    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .insert([galleryItem])
      .select()
      .single();

    if (error) {
      console.error('Error creating gallery item:', error);
      return res.status(500).json({ error: 'Failed to create gallery item' });
    }

    return res.status(201).json({
      success: true,
      data,
      message: 'Gallery item created successfully'
    });
  } catch (error) {
    console.error('Error in handleCreateGalleryItem:', error);
    return res.status(500).json({ error: 'Failed to create gallery item' });
  }
}

/**
 * Update existing gallery item
 */
async function handleUpdateGalleryItem(req, res, supabaseAdmin) {
  try {
    const { id } = req.query;
    const {
      title,
      description,
      category,
      main_image_url,
      gallery_images,
      status,
      featured,
      display_order,
      meta_title,
      meta_description
    } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Gallery item ID is required' });
    }

    // Build update object with only provided fields
    const updateData = {};

    if (title !== undefined) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description.trim();
    if (category !== undefined) updateData.category = category;
    if (main_image_url !== undefined) updateData.main_image_url = main_image_url;
    if (gallery_images !== undefined) updateData.gallery_images = Array.isArray(gallery_images) ? gallery_images : [];
    if (status !== undefined) updateData.status = status;
    if (featured !== undefined) updateData.featured = Boolean(featured);
    if (display_order !== undefined) updateData.display_order = parseInt(display_order) || 0;
    if (meta_title !== undefined) updateData.meta_title = meta_title.trim();
    if (meta_description !== undefined) updateData.meta_description = meta_description.trim();

    updateData.updated_at = new Date().toISOString();

    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating gallery item:', error);
      return res.status(500).json({ error: 'Failed to update gallery item' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Gallery item not found' });
    }

    return res.status(200).json({
      success: true,
      data,
      message: 'Gallery item updated successfully'
    });
  } catch (error) {
    console.error('Error in handleUpdateGalleryItem:', error);
    return res.status(500).json({ error: 'Failed to update gallery item' });
  }
}

/**
 * Delete gallery item
 */
async function handleDeleteGalleryItem(req, res, supabaseAdmin) {
  try {
    const { id } = req.query;

    if (!id) {
      return res.status(400).json({ error: 'Gallery item ID is required' });
    }

    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .delete()
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error deleting gallery item:', error);
      return res.status(500).json({ error: 'Failed to delete gallery item' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Gallery item not found' });
    }

    return res.status(200).json({
      success: true,
      message: 'Gallery item deleted successfully'
    });
  } catch (error) {
    console.error('Error in handleDeleteGalleryItem:', error);
    return res.status(500).json({ error: 'Failed to delete gallery item' });
  }
}
