/**
 * Comprehensive Booking System Integration Tests
 * 
 * This test suite verifies all aspects of the booking system including:
 * - Database connectivity and operations
 * - API endpoint functionality
 * - Customer management integration
 * - Error handling and validation
 * - Data integrity
 */

import { createClient } from '@supabase/supabase-js';

// Mock environment variables for testing
const mockEnv = {
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY
};

// Test data
const testCustomer = {
  name: 'Test Customer',
  email: '<EMAIL>',
  phone: '+***********',
  marketing_consent: true
};

const testBooking = {
  date: '2024-12-25',
  time: '10:00',
  location: 'Test Location',
  message: 'Test booking message',
  service: {
    id: null, // Will be set from actual service
    name: 'Test Service',
    bookingType: 'Instant Book'
  },
  option: {
    hours: 2,
    price: 100
  }
};

describe('Booking System Integration Tests', () => {
  let supabaseClient;
  let adminClient;
  let testServiceId;
  let testCustomerId;
  let testBookingId;

  beforeAll(async () => {
    // Verify environment variables
    expect(mockEnv.NEXT_PUBLIC_SUPABASE_URL).toBeDefined();
    expect(mockEnv.NEXT_PUBLIC_SUPABASE_ANON_KEY).toBeDefined();
    expect(mockEnv.SUPABASE_SERVICE_ROLE_KEY).toBeDefined();

    // Initialize Supabase clients
    supabaseClient = createClient(
      mockEnv.NEXT_PUBLIC_SUPABASE_URL,
      mockEnv.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );

    adminClient = createClient(
      mockEnv.NEXT_PUBLIC_SUPABASE_URL,
      mockEnv.SUPABASE_SERVICE_ROLE_KEY
    );

    // Get a test service ID
    const { data: services, error: servicesError } = await adminClient
      .from('services')
      .select('id, name')
      .limit(1);

    expect(servicesError).toBeNull();
    expect(services).toHaveLength(1);
    testServiceId = services[0].id;
    testBooking.service.id = testServiceId;
  });

  afterAll(async () => {
    // Cleanup test data
    if (testBookingId) {
      await adminClient.from('bookings').delete().eq('id', testBookingId);
    }
    if (testCustomerId) {
      await adminClient.from('customers').delete().eq('id', testCustomerId);
    }
  });

  describe('Database Connectivity', () => {
    test('should connect to Supabase successfully', async () => {
      const { data, error } = await supabaseClient
        .from('services')
        .select('count')
        .limit(1);

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });

    test('should have required tables with correct structure', async () => {
      // Test bookings table
      const { data: bookingsData, error: bookingsError } = await adminClient
        .from('bookings')
        .select('*')
        .limit(1);

      expect(bookingsError).toBeNull();

      // Test customers table
      const { data: customersData, error: customersError } = await adminClient
        .from('customers')
        .select('*')
        .limit(1);

      expect(customersError).toBeNull();

      // Test services table
      const { data: servicesData, error: servicesError } = await adminClient
        .from('services')
        .select('*')
        .limit(1);

      expect(servicesError).toBeNull();
    });
  });

  describe('Customer Management', () => {
    test('should create a new customer', async () => {
      const { data, error } = await adminClient
        .from('customers')
        .insert([testCustomer])
        .select();

      expect(error).toBeNull();
      expect(data).toHaveLength(1);
      expect(data[0].name).toBe(testCustomer.name);
      expect(data[0].email).toBe(testCustomer.email);

      testCustomerId = data[0].id;
    });

    test('should retrieve customer by email', async () => {
      const { data, error } = await adminClient
        .from('customers')
        .select('*')
        .eq('email', testCustomer.email)
        .single();

      expect(error).toBeNull();
      expect(data.name).toBe(testCustomer.name);
    });

    test('should update customer information', async () => {
      const updatedData = { phone: '+61400000001' };
      
      const { data, error } = await adminClient
        .from('customers')
        .update(updatedData)
        .eq('id', testCustomerId)
        .select();

      expect(error).toBeNull();
      expect(data[0].phone).toBe(updatedData.phone);
    });
  });

  describe('Booking Operations', () => {
    test('should create a new booking', async () => {
      const startTime = new Date(`${testBooking.date}T${testBooking.time}:00`);
      const endTime = new Date(startTime);
      endTime.setHours(endTime.getHours() + testBooking.option.hours);

      const bookingData = {
        customer_id: testCustomerId,
        service_id: testServiceId,
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
        status: 'confirmed',
        location: testBooking.location,
        notes: testBooking.message
      };

      const { data, error } = await adminClient
        .from('bookings')
        .insert([bookingData])
        .select();

      expect(error).toBeNull();
      expect(data).toHaveLength(1);
      expect(data[0].customer_id).toBe(testCustomerId);
      expect(data[0].service_id).toBe(testServiceId);

      testBookingId = data[0].id;
    });

    test('should retrieve booking with customer and service details', async () => {
      const { data, error } = await adminClient
        .from('bookings')
        .select(`
          *,
          customers (name, email, phone),
          services (name, duration, price)
        `)
        .eq('id', testBookingId)
        .single();

      expect(error).toBeNull();
      expect(data.customers.name).toBe(testCustomer.name);
      expect(data.services).toBeDefined();
    });

    test('should update booking status', async () => {
      const { data, error } = await adminClient
        .from('bookings')
        .update({ status: 'pending' })
        .eq('id', testBookingId)
        .select();

      expect(error).toBeNull();
      expect(data[0].status).toBe('pending');
    });

    test('should handle booking time conflicts', async () => {
      // Try to create overlapping booking
      const conflictBooking = {
        customer_id: testCustomerId,
        service_id: testServiceId,
        start_time: new Date(`${testBooking.date}T${testBooking.time}:00`).toISOString(),
        end_time: new Date(`${testBooking.date}T12:00:00`).toISOString(),
        status: 'confirmed',
        location: testBooking.location,
        notes: 'Conflict test'
      };

      // This should either fail or be handled gracefully
      const { data, error } = await adminClient
        .from('bookings')
        .insert([conflictBooking])
        .select();

      // The system should handle this appropriately
      // Either by preventing the conflict or allowing it with proper business logic
      if (data) {
        // If allowed, clean up
        await adminClient.from('bookings').delete().eq('id', data[0].id);
      }
    });
  });

  describe('Data Validation', () => {
    test('should reject booking with missing required fields', async () => {
      const invalidBooking = {
        customer_id: testCustomerId,
        // Missing service_id, start_time, end_time, status
        location: 'Test Location'
      };

      const { data, error } = await adminClient
        .from('bookings')
        .insert([invalidBooking])
        .select();

      expect(error).toBeDefined();
    });

    test('should reject customer with invalid email format', async () => {
      const invalidCustomer = {
        name: 'Invalid Customer',
        email: 'invalid-email',
        phone: '+***********'
      };

      const { data, error } = await adminClient
        .from('customers')
        .insert([invalidCustomer])
        .select();

      // Should either fail validation or be handled by database constraints
      if (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Error Handling', () => {
    test('should handle database connection errors gracefully', async () => {
      // Create a client with invalid credentials
      const invalidClient = createClient(
        mockEnv.NEXT_PUBLIC_SUPABASE_URL,
        'invalid-key'
      );

      const { data, error } = await invalidClient
        .from('bookings')
        .select('*')
        .limit(1);

      expect(error).toBeDefined();
    });

    test('should handle non-existent record queries', async () => {
      const { data, error } = await adminClient
        .from('bookings')
        .select('*')
        .eq('id', '00000000-0000-0000-0000-000000000000')
        .single();

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });
  });
});
