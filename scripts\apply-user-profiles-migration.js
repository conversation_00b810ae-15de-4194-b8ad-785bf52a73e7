/**
 * <PERSON><PERSON><PERSON> to apply the user_profiles view migration to Supabase
 *
 * This script reads the SQL migration file and executes it against the Supabase database
 * using the admin client with service role permissions.
 *
 * Usage:
 * node scripts/apply-user-profiles-migration.js
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase admin client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase URL or service role key in environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyMigration() {
  try {
    console.log('Reading migration file...');
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', 'create_user_profiles_view.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    console.log('Applying migration...');
    // Execute the SQL directly instead of using pgmigrate
    const { error } = await supabaseAdmin.rpc('exec_sql', { sql: migrationSql });

    if (error) {
      console.error('Error applying migration:', error);
      // If exec_sql RPC is not available, we'll try another approach
      console.log('Attempting to apply migration using Supabase SQL API...');

      // Split the SQL into separate statements
      const statements = migrationSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      // Execute each statement separately
      for (const stmt of statements) {
        console.log(`Executing: ${stmt.substring(0, 50)}...`);
        const { error: stmtError } = await supabaseAdmin.rpc('exec_sql', { sql: stmt + ';' });

        if (stmtError) {
          console.error('Error executing statement:', stmtError);
          console.log('This error might be expected if the statement is creating objects that already exist.');
          console.log('Continuing with next statement...');
        }
      }
    }

    console.log('Migration applied successfully!');

    // Verify the view was created
    console.log('Verifying user_profiles view...');
    const { data, error: verifyError } = await supabaseAdmin
      .from('user_profiles')
      .select('id, email, display_name, role')
      .limit(1);

    if (verifyError) {
      console.error('Error verifying user_profiles view:', verifyError);
      process.exit(1);
    }

    console.log('User profiles view is working correctly!');
    if (data && data.length > 0) {
      console.log('Sample user profile:', data[0]);
    } else {
      console.log('No user profiles found, but view is created successfully.');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

applyMigration();
