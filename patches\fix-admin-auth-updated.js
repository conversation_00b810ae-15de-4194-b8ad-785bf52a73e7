/**
 * Updated Admin Authentication Fix Patch
 * 
 * This patch fixes authentication issues in the admin panel by:
 * 1. Adding more detailed logging
 * 2. Improving token extraction from current sources (no legacy tokens)
 * 3. Adding a fallback authentication method for development
 * 4. Including auto-recovery mechanism
 * 
 * To apply this patch:
 * 1. Stop the server
 * 2. Run: node patches/fix-admin-auth-updated.js
 * 3. Restart the server
 * 
 * For auto-recovery, set ENABLE_AUTH_AUTO_RECOVERY=true in your environment
 */

const fs = require('fs');
const path = require('path');

// Path to the admin-auth.js file
const authFilePath = path.join(__dirname, '..', 'lib', 'admin-auth.js');

// Read the current file
console.log(`Reading ${authFilePath}...`);
let authFileContent;
try {
  authFileContent = fs.readFileSync(authFilePath, 'utf8');
  console.log('File read successfully.');
} catch (error) {
  console.error(`Error reading file: ${error.message}`);
  process.exit(1);
}

// Create a backup
const backupPath = `${authFilePath}.backup`;
console.log(`Creating backup at ${backupPath}...`);
try {
  fs.writeFileSync(backupPath, authFileContent);
  console.log('Backup created successfully.');
} catch (error) {
  console.error(`Error creating backup: ${error.message}`);
  process.exit(1);
}

// Apply the patches

// Patch 1: Add more detailed logging
console.log('Applying Patch 1: Adding detailed logging...');
const loggingPatch = `  // Generate a unique auth ID for tracking
  const authId = Math.random().toString(36).substring(2, 8);

  // Add detailed logging for debugging
  console.log(\`[\${authId}] Starting authentication process\`);
  console.log(\`[\${authId}] Request URL: \${req.url}\`);
  console.log(\`[\${authId}] Headers present: \${Object.keys(req.headers).join(', ')}\`);
  console.log(\`[\${authId}] Cookies present: \${req.cookies ? Object.keys(req.cookies).join(', ') : 'No cookies'}\`);

  // Check if this is a diagnostics request`;

const originalLoggingCode = `  // Generate a unique auth ID for tracking
  const authId = Math.random().toString(36).substring(2, 8);

  // Check if this is a diagnostics request`;

authFileContent = authFileContent.replace(originalLoggingCode, loggingPatch);

// Patch 2: Improve token extraction (remove legacy MCP tokens)
console.log('Applying Patch 2: Improving token extraction...');
const tokenExtractionPatch = `      // Method 2: Check cookies (for browser-based requests)
      if (!token) {
        try {
          // Try to get token from cookies object
          if (req.cookies) {
            if (req.cookies.oss_auth_token) {
              token = req.cookies.oss_auth_token;
              console.log(\`[\${authId}] Found OSS token in cookies\`);
            } else if (req.cookies.sb_auth_token) {
              token = req.cookies.sb_auth_token;
              console.log(\`[\${authId}] Found Supabase token in cookies\`);
            } else if (req.cookies['sb-access-token']) {
              token = req.cookies['sb-access-token'];
              console.log(\`[\${authId}] Found Supabase access token in cookies\`);
            } else {
              // Try to find any current token with 'token' in the name
              const tokenCookieKey = Object.keys(req.cookies).find(key => 
                (key.includes('token') || key.includes('auth')) && 
                !key.includes('mcp') // Exclude legacy tokens
              );
              if (tokenCookieKey) {
                token = req.cookies[tokenCookieKey];
                console.log(\`[\${authId}] Found token in cookie: \${tokenCookieKey}\`);
              }
            }
          }
          // Try to parse cookies from cookie header
          else if (req.headers.cookie) {
            const cookieHeader = req.headers.cookie;
            const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
              const [key, value] = cookie.trim().split('=');
              acc[key] = value;
              return acc;
            }, {});

            if (cookies.oss_auth_token) {
              token = cookies.oss_auth_token;
              console.log(\`[\${authId}] Found OSS token in cookie header\`);
            } else if (cookies.sb_auth_token) {
              token = cookies.sb_auth_token;
              console.log(\`[\${authId}] Found Supabase token in cookie header\`);
            } else if (cookies['sb-access-token']) {
              token = cookies['sb-access-token'];
              console.log(\`[\${authId}] Found Supabase access token in cookie header\`);
            } else {
              // Try to find any current token with 'token' in the name
              const tokenCookieKey = Object.keys(cookies).find(key => 
                (key.includes('token') || key.includes('auth')) &&
                !key.includes('mcp') // Exclude legacy tokens
              );
              if (tokenCookieKey) {
                token = cookies[tokenCookieKey];
                console.log(\`[\${authId}] Found token in cookie header: \${tokenCookieKey}\`);
              }
            }
          }`;

// Find and replace the token extraction section
const tokenStartMarker = `      // Method 2: Check cookies (for browser-based requests)
      if (!token) {`;

const tokenEndMarker = `          }`;

// Find the start and end of the token extraction section
const startIndex = authFileContent.indexOf(tokenStartMarker);
if (startIndex === -1) {
  console.error('Could not find token extraction section to patch');
  process.exit(1);
}

// Find the end of the current token extraction logic
let endIndex = startIndex;
let braceCount = 0;
let inTokenSection = false;

for (let i = startIndex; i < authFileContent.length; i++) {
  const char = authFileContent[i];
  if (char === '{') {
    braceCount++;
    inTokenSection = true;
  } else if (char === '}') {
    braceCount--;
    if (inTokenSection && braceCount === 0) {
      // Find the end of the cookies parsing section
      const remainingText = authFileContent.substring(i);
      const nextSectionIndex = remainingText.indexOf('          }');
      if (nextSectionIndex !== -1) {
        endIndex = i + nextSectionIndex + '          }'.length;
        break;
      }
    }
  }
}

if (endIndex === startIndex) {
  console.error('Could not find end of token extraction section');
  process.exit(1);
}

// Replace the token extraction section
const beforeToken = authFileContent.substring(0, startIndex);
const afterToken = authFileContent.substring(endIndex);
authFileContent = beforeToken + tokenExtractionPatch + afterToken;

// Patch 3: Add development mode fallback with auto-recovery
console.log('Applying Patch 3: Adding development mode fallback with auto-recovery...');
const devModePatch = `  try {
    // Auto-recovery mechanism for stuck authentication
    if (process.env.ENABLE_AUTH_AUTO_RECOVERY === 'true') {
      const authFailureFile = path.join(process.cwd(), '.auth-failures');
      const currentTime = Date.now();
      
      // Check for recent auth failures
      let failureCount = 0;
      if (fs.existsSync(authFailureFile)) {
        try {
          const failures = JSON.parse(fs.readFileSync(authFailureFile, 'utf8'));
          failureCount = failures.filter(f => currentTime - f < 300000).length; // 5 minutes
        } catch (e) {
          // Reset failures file if corrupted
          fs.writeFileSync(authFailureFile, '[]');
        }
      }
      
      // If too many failures, clear session storage
      if (failureCount > 3) {
        console.log(\`[\${authId}] Auto-recovery triggered: clearing session data\`);
        // This would trigger client-side session clearing
        if (res && res.setHeader) {
          res.setHeader('X-Auth-Recovery', 'clear-session');
        }
      }
    }

    // Development mode fallback for testing
    // Only active when NODE_ENV is development and ENABLE_AUTH_BYPASS is true
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(\`[\${authId}] DEVELOPMENT MODE: Using auth bypass\`);
      return {
        user: { id: 'dev-admin', email: '<EMAIL>' },
        role: 'admin',
        authorized: true,
        error: null
      };
    }

    // First try with Supabase client (robust with retry logic)`;

const originalTryBlock = `  try {
    // First try with MCP client (more robust with retry logic)`;

authFileContent = authFileContent.replace(originalTryBlock, devModePatch);

// Patch 4: Add error tracking for auto-recovery
console.log('Applying Patch 4: Adding error tracking...');
const errorTrackingPatch = `    // Track authentication failures for auto-recovery
    if (process.env.ENABLE_AUTH_AUTO_RECOVERY === 'true') {
      const authFailureFile = path.join(process.cwd(), '.auth-failures');
      let failures = [];
      if (fs.existsSync(authFailureFile)) {
        try {
          failures = JSON.parse(fs.readFileSync(authFailureFile, 'utf8'));
        } catch (e) {
          failures = [];
        }
      }
      failures.push(Date.now());
      // Keep only last 10 failures
      failures = failures.slice(-10);
      try {
        fs.writeFileSync(authFailureFile, JSON.stringify(failures));
      } catch (e) {
        console.warn('Could not write auth failure log');
      }
    }

    console.log(\`[\${authId}] Authentication failed: \${error.message}\`);`;

const originalErrorLog = `    console.log(\`[\${authId}] Authentication failed: \${error.message}\`);`;

authFileContent = authFileContent.replace(originalErrorLog, errorTrackingPatch);

// Add required imports at the top
console.log('Adding required imports...');
const importPatch = `/**
 * Admin Authentication Middleware
 *
 * This module provides a simplified, consistent approach to server-side authentication
 * for admin API endpoints using the unified Supabase client.
 */

import { getAdminClient } from './supabase';
import fs from 'fs';
import path from 'path';`;

const originalImports = `/**
 * Admin Authentication Middleware
 *
 * This module provides a simplified, consistent approach to server-side authentication
 * for admin API endpoints using the unified Supabase client.
 */

import { getAdminClient } from './supabase';`;

authFileContent = authFileContent.replace(originalImports, importPatch);

// Write the modified file
console.log('Writing modified file...');
try {
  fs.writeFileSync(authFilePath, authFileContent);
  console.log('File updated successfully.');
} catch (error) {
  console.error(`Error writing file: ${error.message}`);
  console.log('Restoring from backup...');
  try {
    fs.copyFileSync(backupPath, authFilePath);
    console.log('Restored from backup.');
  } catch (restoreError) {
    console.error(`Error restoring from backup: ${restoreError.message}`);
  }
  process.exit(1);
}

console.log('\n✅ Patch applied successfully!');
console.log('\n🔧 Configuration options:');
console.log('1. For development auth bypass: Add ENABLE_AUTH_BYPASS=true to your .env.local file');
console.log('2. For auto-recovery: Add ENABLE_AUTH_AUTO_RECOVERY=true to your .env.local file');
console.log('\n⚠️  Remember to remove the auth bypass before deploying to production!');
console.log('\n🔄 Auto-recovery will:');
console.log('   - Track authentication failures');
console.log('   - Automatically clear sessions after 3+ failures in 5 minutes');
console.log('   - Help prevent authentication getting permanently stuck');
