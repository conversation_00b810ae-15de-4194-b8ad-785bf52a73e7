/**
 * Development Authentication Utilities
 * 
 * This module provides utilities for testing authentication in development mode.
 * These functions should NEVER be used in production code!
 */

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Get a development bypass token for testing
 * This should ONLY be used in development mode
 * 
 * @returns {string} A development bypass token
 */
export function getDevBypassToken() {
  if (!isDevelopment) {
    console.warn('Attempted to use dev bypass token in non-development environment');
    return null;
  }
  
  return 'dev-bypass-token';
}

/**
 * Add development authentication headers to a fetch request
 * This should ONLY be used in development mode
 * 
 * @param {Object} options - Fetch options
 * @returns {Object} Updated fetch options with development auth headers
 */
export function addDevAuthHeaders(options = {}) {
  if (!isDevelopment) {
    console.warn('Attempted to use dev auth headers in non-development environment');
    return options;
  }
  
  const newOptions = { ...options };
  newOptions.headers = { 
    ...newOptions.headers,
    'Authorization': `Bear<PERSON> ${getDevBypassToken()}`
  };
  
  return newOptions;
}

/**
 * Make an authenticated API request in development mode
 * This should ONLY be used in development mode
 * 
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - The response data
 */
export async function devAuthFetch(url, options = {}) {
  if (!isDevelopment) {
    console.warn('Attempted to use devAuthFetch in non-development environment');
    throw new Error('devAuthFetch should only be used in development mode');
  }
  
  const newOptions = addDevAuthHeaders(options);
  
  try {
    const response = await fetch(url, newOptions);
    
    // Parse the response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    // Handle error responses
    if (!response.ok) {
      const errorMessage = data.error || data.message || 'Unknown error';
      throw new Error(errorMessage);
    }
    
    return data;
  } catch (error) {
    console.error('Error in devAuthFetch:', error);
    throw error;
  }
}

/**
 * Test the authentication system in development mode
 * This should ONLY be used in development mode
 * 
 * @returns {Promise<Object>} - The test result
 */
export async function testDevAuth() {
  if (!isDevelopment) {
    console.warn('Attempted to use testDevAuth in non-development environment');
    throw new Error('testDevAuth should only be used in development mode');
  }
  
  try {
    const result = await devAuthFetch('/api/admin/diagnostics/dev-auth');
    console.log('Development authentication test result:', result);
    return result;
  } catch (error) {
    console.error('Development authentication test failed:', error);
    throw error;
  }
}

export default {
  getDevBypassToken,
  addDevAuthHeaders,
  devAuthFetch,
  testDevAuth
};
