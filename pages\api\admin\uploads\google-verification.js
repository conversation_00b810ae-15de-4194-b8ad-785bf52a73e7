import { authenticateAdminRequest } from '@/lib/admin-auth';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * API endpoint for uploading Google Search Console verification HTML files
 * 
 * This endpoint:
 * 1. Accepts HTML file uploads from admin users
 * 2. Validates the file is a Google verification file
 * 3. Stores the file in the public directory for direct access
 * 4. Maintains security by requiring admin authentication
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate admin request
  const authResult = await authenticateAdminRequest(req);
  if (!authResult.success) {
    return res.status(401).json({ 
      error: 'Unauthorized', 
      message: authResult.message 
    });
  }

  try {
    // Parse form data
    const form = new formidable.IncomingForm({
      keepExtensions: true,
      maxFileSize: 1 * 1024 * 1024, // 1MB limit (Google verification files are tiny)
    });

    const { fields, files } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve({ fields, files });
      });
    });

    // Check if verification file exists
    if (!files.verificationFile) {
      return res.status(400).json({ error: 'No verification file provided' });
    }

    const file = files.verificationFile;
    
    // Validate file type - must be HTML
    if (file.mimetype !== 'text/html' && !file.originalFilename.endsWith('.html')) {
      return res.status(400).json({ 
        error: 'Invalid file type. Only HTML files are allowed for Google verification.' 
      });
    }

    // Validate filename pattern - must be a Google verification file
    const googleVerificationPattern = /^google[a-f0-9]+\.html$/i;
    if (!googleVerificationPattern.test(file.originalFilename)) {
      return res.status(400).json({ 
        error: 'Invalid filename. File must be a Google verification file (e.g., google7703f3860eb21a44.html).' 
      });
    }

    // Read and validate file content
    const fileContent = fs.readFileSync(file.filepath, 'utf8');
    
    // Basic validation - Google verification files should contain specific text
    if (!fileContent.includes('google-site-verification') || fileContent.length > 1000) {
      return res.status(400).json({ 
        error: 'Invalid file content. This does not appear to be a valid Google verification file.' 
      });
    }

    // Define the target path in public directory
    const publicDir = path.join(process.cwd(), 'public');
    const targetPath = path.join(publicDir, file.originalFilename);

    // Remove any existing Google verification files
    const existingFiles = fs.readdirSync(publicDir).filter(filename => 
      googleVerificationPattern.test(filename)
    );
    
    for (const existingFile of existingFiles) {
      const existingPath = path.join(publicDir, existingFile);
      try {
        fs.unlinkSync(existingPath);
        console.log(`Removed existing verification file: ${existingFile}`);
      } catch (error) {
        console.warn(`Failed to remove existing file ${existingFile}:`, error.message);
      }
    }

    // Write the new verification file
    fs.writeFileSync(targetPath, fileContent, 'utf8');

    // Verify the file was written correctly
    if (!fs.existsSync(targetPath)) {
      return res.status(500).json({ error: 'Failed to save verification file' });
    }

    console.log(`Google verification file uploaded: ${file.originalFilename}`);

    return res.status(200).json({
      success: true,
      message: 'Google verification file uploaded successfully',
      filename: file.originalFilename,
      url: `/${file.originalFilename}`,
      size: fileContent.length
    });

  } catch (error) {
    console.error('Error uploading Google verification file:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      message: 'Failed to process verification file upload' 
    });
  }
}
