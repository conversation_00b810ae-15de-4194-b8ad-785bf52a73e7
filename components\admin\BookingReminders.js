import { useState } from 'react';
import styles from '@/styles/admin/BookingReminders.module.css';

/**
 * Component for sending booking reminders from the admin panel
 * 
 * @returns {JSX.Element}
 */
export default function BookingReminders() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  // Handle sending booking reminders
  const handleSendReminders = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      // Call the API to send booking reminders
      const response = await fetch('/api/admin/send-booking-reminders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send booking reminders');
      }

      setResult(data);
    } catch (error) {
      console.error('Error sending booking reminders:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>Booking Reminders</h2>
        <p className={styles.description}>
          Send reminder notifications to customers with bookings scheduled for tomorrow.
        </p>
      </div>

      {error && (
        <div className={styles.error}>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className={styles.result}>
          <h3>Reminder Results</h3>
          <p>{result.message}</p>
          
          <div className={styles.stats}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Total Bookings:</span>
              <span className={styles.statValue}>{result.count}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Successful:</span>
              <span className={styles.statValue}>{result.successCount}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Failed:</span>
              <span className={styles.statValue}>{result.failedCount}</span>
            </div>
          </div>

          {result.results && result.results.length > 0 && (
            <div className={styles.resultDetails}>
              <h4>Details</h4>
              <ul className={styles.resultList}>
                {result.results.map((item, index) => (
                  <li key={index} className={item.success ? styles.success : styles.failure}>
                    Booking #{item.bookingId}: {item.success ? 'Reminder sent' : `Failed - ${item.error}`}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <div className={styles.actions}>
        <button
          className={styles.sendButton}
          onClick={handleSendReminders}
          disabled={loading}
        >
          {loading ? 'Sending...' : 'Send Reminders for Tomorrow'}
        </button>
      </div>

      <div className={styles.info}>
        <h3>About Booking Reminders</h3>
        <p>
          Booking reminders are automatically sent to customers 24 hours before their scheduled appointment.
          This tool allows you to manually trigger the reminder process for all bookings scheduled for tomorrow.
        </p>
        <p>
          Only confirmed bookings will receive reminders. Canceled or pending bookings are excluded.
        </p>
      </div>
    </div>
  );
}
