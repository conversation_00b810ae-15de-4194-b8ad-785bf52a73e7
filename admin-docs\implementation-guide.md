# OceanSoulSparkles Admin Panel Implementation Guide

This document provides a step-by-step guide for implementing the OceanSoulSparkles admin panel.

## Prerequisites

Before beginning implementation, ensure you have the following:

1. Access to the OceanSoulSparkles website codebase
2. A Supabase account and project
3. A Vercel account for deployment
4. Node.js and npm installed on your development machine
5. Access to payment gateway accounts (PayPal and Square)
6. A OneSignal account for notifications

## Implementation Phases

The admin panel will be implemented in phases to allow for incremental development and testing:

### Phase 1: Setup and Authentication (Week 1-2)

1. Set up Supabase project and database schema
2. Configure authentication with Supabase Auth
3. Create protected admin routes
4. Implement the admin dashboard UI
5. Set up role-based access control

### Phase 2: Booking Management System (Week 3-4)

1. Create booking database tables
2. Implement booking API endpoints
3. Create booking calendar interface
4. Implement booking creation and editing
5. Set up OneSignal notifications for bookings

### Phase 3: Customer Database (Week 5-6)

1. Create customer database tables
2. Implement customer API endpoints
3. Create customer management interface
4. Implement GDPR compliance features
5. Set up data export functionality

### Phase 4: Payment Processing (Week 7-8)

1. Set up payment gateway integrations
2. Create payment database tables
3. Implement payment API endpoints
4. Create financial dashboard
5. Implement invoicing system

### Phase 5: Inventory Management (Week 9-10)

1. Create inventory database tables
2. Implement inventory API endpoints
3. Create inventory management interface
4. Set up low stock alerts
5. Implement purchase order system

### Phase 6: Analytics Dashboard (Week 11-12)

1. Create analytics database functions
2. Implement analytics API endpoints
3. Create analytics dashboard interface
4. Set up report export functionality
5. Implement data visualization

### Phase 7: Marketing Tools (Week 13-14)

1. Create marketing database tables
2. Implement marketing API endpoints
3. Create campaign management interface
4. Set up OneSignal marketing notifications
5. Implement discount code system

## Step-by-Step Implementation

### Step 1: Set Up Supabase Project

1. Create a new Supabase project at [https://app.supabase.com](https://app.supabase.com)
2. Note your Supabase URL and anon key for configuration
3. Create database tables as specified in each component's documentation
4. Set up Row Level Security (RLS) policies

### Step 2: Configure Next.js Project

1. Install required dependencies:

```bash
npm install @supabase/supabase-js uuid react-big-calendar moment chart.js react-chartjs-2 react-onesignal json2csv
```

2. Create Supabase client configuration:

```javascript
// lib/supabase.js
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

3. Set up environment variables in `.env.local`:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_ONESIGNAL_APP_ID=your-onesignal-app-id
NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID=your-safari-web-id
ONESIGNAL_REST_API_KEY=your-onesignal-rest-api-key
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
SQUARE_APPLICATION_ID=your-square-application-id
SQUARE_ACCESS_TOKEN=your-square-access-token
```

### Step 3: Implement Authentication System

1. Create authentication utilities as specified in `authentication.md`
2. Create authentication context provider
3. Create login page and protected route component
4. Set up user roles in Supabase

### Step 4: Create Admin Layout

1. Create admin layout component:

```javascript
// components/admin/AdminLayout.js
import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import styles from '@/styles/admin/AdminLayout.module.css'

export default function AdminLayout({ children, title }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, role, signOut } = useAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push('/admin/login')
  }

  return (
    <div className={styles.adminLayout}>
      <aside className={`${styles.sidebar} ${sidebarOpen ? styles.open : ''}`}>
        <div className={styles.sidebarHeader}>
          <h2>OceanSoulSparkles</h2>
          <button
            className={styles.closeSidebar}
            onClick={() => setSidebarOpen(false)}
          >
            ×
          </button>
        </div>

        <nav className={styles.sidebarNav}>
          <Link href="/admin">
            <a className={router.pathname === '/admin' ? styles.active : ''}>
              Dashboard
            </a>
          </Link>
          <Link href="/admin/bookings">
            <a className={router.pathname.startsWith('/admin/bookings') ? styles.active : ''}>
              Bookings
            </a>
          </Link>
          <Link href="/admin/customers">
            <a className={router.pathname.startsWith('/admin/customers') ? styles.active : ''}>
              Customers
            </a>
          </Link>
          <Link href="/admin/payments">
            <a className={router.pathname.startsWith('/admin/payments') ? styles.active : ''}>
              Payments
            </a>
          </Link>
          <Link href="/admin/inventory">
            <a className={router.pathname.startsWith('/admin/inventory') ? styles.active : ''}>
              Inventory
            </a>
          </Link>
          <Link href="/admin/analytics">
            <a className={router.pathname.startsWith('/admin/analytics') ? styles.active : ''}>
              Analytics
            </a>
          </Link>
          <Link href="/admin/marketing">
            <a className={router.pathname.startsWith('/admin/marketing') ? styles.active : ''}>
              Marketing
            </a>
          </Link>
          <Link href="/admin/settings">
            <a className={router.pathname.startsWith('/admin/settings') ? styles.active : ''}>
              Settings
            </a>
          </Link>
        </nav>

        <div className={styles.sidebarFooter}>
          <div className={styles.userInfo}>
            <span>{user?.email}</span>
            <span className={styles.userRole}>{role}</span>
          </div>
          <button
            className={styles.signOutButton}
            onClick={handleSignOut}
          >
            Sign Out
          </button>
        </div>
      </aside>

      <main className={styles.mainContent}>
        <header className={styles.header}>
          <button
            className={styles.menuButton}
            onClick={() => setSidebarOpen(true)}
          >
            ☰
          </button>
          <h1>{title}</h1>
        </header>

        <div className={styles.content}>
          {children}
        </div>
      </main>
    </div>
  )
}
```

### Step 5: Create Admin Dashboard

1. Create admin dashboard page:

```javascript
// pages/admin/index.js
import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import styles from '@/styles/admin/Dashboard.module.css'

export default function AdminDashboard() {
  const [summary, setSummary] = useState({
    totalBookings: 0,
    pendingBookings: 0,
    totalCustomers: 0,
    totalRevenue: 0,
    lowStockItems: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch dashboard summary data
  useEffect(() => {
    const fetchSummary = async () => {
      try {
        const response = await fetch('/api/admin/summary')

        if (!response.ok) {
          throw new Error('Failed to fetch summary data')
        }

        const data = await response.json()
        setSummary(data)
      } catch (error) {
        console.error('Error fetching summary:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchSummary()
  }, [])

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout title="Dashboard">
          <div className={styles.loading}>Loading dashboard data...</div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout title="Dashboard">
          <div className={styles.error}>{error}</div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <AdminLayout title="Dashboard">
        <div className={styles.dashboard}>
          <div className={styles.summaryCards}>
            <div className={styles.summaryCard}>
              <h3>Total Bookings</h3>
              <p className={styles.summaryValue}>{summary.totalBookings}</p>
            </div>
            <div className={styles.summaryCard}>
              <h3>Pending Bookings</h3>
              <p className={styles.summaryValue}>{summary.pendingBookings}</p>
            </div>
            <div className={styles.summaryCard}>
              <h3>Total Customers</h3>
              <p className={styles.summaryValue}>{summary.totalCustomers}</p>
            </div>
            <div className={styles.summaryCard}>
              <h3>Total Revenue</h3>
              <p className={styles.summaryValue}>${summary.totalRevenue.toFixed(2)}</p>
            </div>
            <div className={styles.summaryCard}>
              <h3>Low Stock Items</h3>
              <p className={styles.summaryValue}>{summary.lowStockItems}</p>
            </div>
          </div>

          {/* Add more dashboard widgets here */}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
```

### Step 6: Implement Component Features

For each component (Booking Management, Customer Database, etc.):

1. Create database tables as specified in the component documentation
2. Implement API endpoints
3. Create UI components
4. Test functionality

### Step 7: Set Up Deployment

1. Configure Vercel deployment:

```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/$1"
    }
  ],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key",
    "NEXT_PUBLIC_ONESIGNAL_APP_ID": "@next_public_onesignal_app_id",
    "NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID": "@next_public_onesignal_safari_web_id",
    "ONESIGNAL_REST_API_KEY": "@onesignal_rest_api_key",
    "PAYPAL_CLIENT_ID": "@paypal_client_id",
    "PAYPAL_CLIENT_SECRET": "@paypal_client_secret",
    "SQUARE_APPLICATION_ID": "@square_application_id",
    "SQUARE_ACCESS_TOKEN": "@square_access_token"
  }
}
```

2. Set up environment variables in Vercel project settings
3. Deploy to Vercel

## Testing

For each component, perform the following tests:

1. Unit tests for API endpoints
2. Integration tests for database operations
3. UI tests for component functionality
4. End-to-end tests for user workflows

## Security Considerations

1. Implement proper authentication and authorization
2. Validate all input data
3. Protect against SQL injection
4. Implement rate limiting for API endpoints
5. Use HTTPS for all communications
6. Follow PCI DSS compliance for payment processing
7. Ensure GDPR compliance for customer data
8. Log all administrative actions for audit purposes

## Maintenance and Updates

1. Set up monitoring for API endpoints
2. Implement error logging and reporting
3. Create a backup strategy for database
4. Document update procedures
5. Plan for future feature enhancements

## Resources

- [Supabase Documentation](https://supabase.io/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Vercel Documentation](https://vercel.com/docs)
- [OneSignal Documentation](https://documentation.onesignal.com)
- [PayPal Developer Documentation](https://developer.paypal.com/docs)
- [Square Developer Documentation](https://developer.squareup.com/docs)
