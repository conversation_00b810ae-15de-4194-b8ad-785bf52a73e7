/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Dancing+Script:wght@400;700&display=swap');

:root {
  /* Primary Colors */
  --primary-color: #4ECDC4;
  --primary-light: #7FDBDA;
  --primary-dark: #3A9D96;
  --primary-gradient: linear-gradient(135deg, #4ECDC4 0%, #2E9BBA 100%);

  /* Secondary Colors */
  --secondary-color: #FF6B6B;
  --secondary-light: #FF9E9E;
  --secondary-dark: #E54B4B;
  --secondary-gradient: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);

  /* Accent Colors */
  --accent-color: #FFE66D;
  --accent-blue: #1A73E8;
  --accent-purple: #9C27B0;
  --accent-teal: #009688;
  --accent-gold: #FFD700;

  /* Background Colors */
  --background-color: #ffffff;
  --background-off-white: #F8F9FA;
  --background-gradient: linear-gradient(180deg, #F8F9FA 0%, #E9F7F6 100%);
  --background-overlay: rgba(78, 205, 196, 0.8);

  /* Text Colors */
  --text-color: #333333;
  --light-text-color: #666666;
  --lighter-text-color: #999999;
  --text-inverse: #FFFFFF;

  /* Utility Colors */
  --success-color: #4CAF50;
  --error-color: #F44336;
  --warning-color: #FF9800;
  --info-color: #2196F3;

  /* Border Colors */
  --border-color: #EEEEEE;
  --border-medium: #DDDDDD;
  --border-dark: #CCCCCC;

  /* Typography */
  --font-primary: 'Montserrat', sans-serif;
  --font-secondary: 'Lato', sans-serif;
  --font-accent: 'Dancing Script', cursive;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 2.5rem;
  --spacing-3xl: 3rem;
  --spacing-4xl: 4rem;
  --spacing-5xl: 6rem;

  /* Layout */
  --max-width: 1200px;
  --header-height: 80px;

  /* Border Radius */
  --border-radius-sm: 0.125rem;
  --border-radius: 0.25rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;

  /* Box Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-secondary);
  font-weight: 400;
  font-size: 16px;
  color: var(--text-color);
  background-color: var(--background-color);
  scroll-behavior: smooth;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-normal);
  position: relative;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: none;
}

/* Fancy link underline animation */
a.animated-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition-normal);
}

a.animated-link:hover::after {
  width: 100%;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  margin-bottom: 1rem;
  line-height: 1.2;
  font-weight: 600;
}

h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.25rem;
  margin-bottom: 1.25rem;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.125rem;
}

h6 {
  font-size: 1rem;
}

.accent-text {
  font-family: var(--font-accent);
  font-weight: 700;
}

p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

button {
  cursor: pointer;
}

img {
  max-width: 100%;
  height: auto;
}

/* Utility classes */
.container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

.button {
  display: inline-block;
  background: var(--primary-gradient);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-lg);
  border: none;
  font-family: var(--font-primary);
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  transition: var(--transition-bounce);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: var(--shadow-md);
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--secondary-gradient);
  z-index: -1;
  transition: var(--transition-normal);
  opacity: 0;
}

.button:hover {
  color: white;
  text-decoration: none;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.button:hover::before {
  opacity: 1;
}

.button:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.button:disabled {
  background: var(--light-text-color);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.button:disabled::before {
  display: none;
}

.button--secondary {
  background: var(--secondary-gradient);
}

.button--secondary::before {
  background: var(--primary-gradient);
}

.button--outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  box-shadow: none;
}

.button--outline::before {
  opacity: 0.1;
}

.button--outline:hover {
  color: white;
}

.button--outline:hover::before {
  opacity: 1;
}

.button--glow {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.button--glow::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  width: calc(100% + 10px);
  height: calc(100% + 10px);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--primary-light), var(--secondary-color), var(--primary-color));
  background-size: 400% 400%;
  z-index: -1;
  border-radius: var(--border-radius-lg);
  animation: glowAnimation 5s ease infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.button--glow:hover::before {
  opacity: 1;
}

.button--glow:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

@keyframes glowAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Form styles */
input, textarea, select {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 1rem;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
}

textarea {
  min-height: 150px;
  resize: vertical;
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.stagger-item.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Delay classes for staggered animations */
.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }
.delay-500 { transition-delay: 0.5s; }
.delay-600 { transition-delay: 0.6s; }
.delay-700 { transition-delay: 0.7s; }
.delay-800 { transition-delay: 0.8s; }

/* Scale animation */
.scale-in {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Slide animations */
.slide-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.slide-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.slide-left.visible,
.slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-2xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-2xl); }

.section-padding {
  padding: var(--spacing-4xl) 0;
}

.section-padding-sm {
  padding: var(--spacing-2xl) 0;
}

/* Background utilities */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-light { background-color: var(--background-off-white); }
.bg-gradient { background: var(--background-gradient); }
.bg-primary-gradient { background: var(--primary-gradient); }

/* Text color utilities */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-light { color: var(--light-text-color); }
.text-white { color: var(--text-inverse); }

/* Responsive design */
@media (max-width: 1024px) {
  html, body {
    font-size: 15px;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .section-padding {
    padding: var(--spacing-3xl) 0;
  }
}

@media (max-width: 768px) {
  html, body {
    font-size: 14px;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.35rem;
  }

  .section-padding {
    padding: var(--spacing-2xl) 0;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  .button {
    width: 100%;
  }

  .section-padding {
    padding: var(--spacing-xl) 0;
  }
}
