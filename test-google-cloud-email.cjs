#!/usr/bin/env node

/**
 * Google Cloud Email Configuration Test Script
 * Tests the email system integration and configuration
 */

const path = require('path');
const fs = require('fs');

// Load environment variables from .env.local
const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, ...values] = line.split('=');
    if (key && values.length > 0 && !key.startsWith('#')) {
      process.env[key.trim()] = values.join('=').trim();
    }
  });
  console.log('✅ Environment variables loaded from .env.local\n');
} else {
  console.log('❌ .env.local file not found\n');
}

async function testEmailConfiguration() {
  console.log('🧪 Testing Google Cloud Email Configuration...\n');

  // Test 1: Check environment variables
  console.log('1️⃣ Checking Environment Variables:');
  const requiredVars = {
    'GMAIL_SMTP_HOST': process.env.GMAIL_SMTP_HOST,
    'GMAIL_SMTP_PORT': process.env.GMAIL_SMTP_PORT,
    'GMAIL_SMTP_USER': process.env.GMAIL_SMTP_USER,
    'GMAIL_SMTP_APP_PASSWORD': process.env.GMAIL_SMTP_APP_PASSWORD,
    'WORKSPACE_SMTP_USER': process.env.WORKSPACE_SMTP_USER,
    'WORKSPACE_SMTP_APP_PASSWORD': process.env.WORKSPACE_SMTP_APP_PASSWORD
  };

  let configStatus = { gmail: false, workspace: false };

  Object.entries(requiredVars).forEach(([key, value]) => {
    const status = value ? '✅' : '❌';
    const displayValue = key.includes('PASSWORD') ? (value ? '****(configured)' : 'not set') : (value || 'not set');
    console.log(`   ${status} ${key}: ${displayValue}`);

    if (key.includes('GMAIL') && value) configStatus.gmail = true;
    if (key.includes('WORKSPACE') && value) configStatus.workspace = true;
  });

  console.log('\n📊 Configuration Status:');
  console.log(`   Gmail SMTP: ${configStatus.gmail ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Workspace SMTP: ${configStatus.workspace ? '✅ Configured' : '❌ Not configured'}`);

  // Test 2: Check Google Cloud Email library
  console.log('\n2️⃣ Testing Google Cloud Email Library:');
  try {
    const googleCloudEmail = require('./lib/google-cloud-email.js');
    console.log('   ✅ Google Cloud Email library loaded successfully');

    // Test connection functions
    console.log('\n3️⃣ Testing Connection Functions:');
    
    if (configStatus.gmail) {
      console.log('   🔄 Testing Gmail SMTP connection...');
      try {
        const gmailResult = await googleCloudEmail.testGmailConnection();
        console.log(`   ${gmailResult.success ? '✅' : '❌'} Gmail SMTP: ${gmailResult.message}`);
        if (gmailResult.details) {
          console.log(`      Details: ${JSON.stringify(gmailResult.details, null, 2)}`);
        }
      } catch (error) {
        console.log(`   ❌ Gmail SMTP test failed: ${error.message}`);
      }
    } else {
      console.log('   ⏭️  Gmail SMTP: Skipped (not configured)');
    }

    if (configStatus.workspace) {
      console.log('   🔄 Testing Workspace SMTP connection...');
      try {
        const workspaceResult = await googleCloudEmail.testWorkspaceConnection();
        console.log(`   ${workspaceResult.success ? '✅' : '❌'} Workspace SMTP: ${workspaceResult.message}`);
        if (workspaceResult.details) {
          console.log(`      Details: ${JSON.stringify(workspaceResult.details, null, 2)}`);
        }
      } catch (error) {
        console.log(`   ❌ Workspace SMTP test failed: ${error.message}`);
      }
    } else {
      console.log('   ⏭️  Workspace SMTP: Skipped (not configured)');
    }

    // Test 4: Check email service status
    console.log('\n4️⃣ Checking Email Service Status:');
    try {
      const status = await googleCloudEmail.getEmailServiceStatus();
      console.log(`   Primary Service: ${status.primary || 'None'}`);
      console.log(`   Available Services: ${status.available.join(', ') || 'None'}`);
      console.log('   Service Details:');
      Object.entries(status.services).forEach(([service, details]) => {
        console.log(`     ${details.success ? '✅' : '❌'} ${service}: ${details.message}`);
      });
    } catch (error) {
      console.log(`   ❌ Email service status check failed: ${error.message}`);
    }

  } catch (error) {
    console.log(`   ❌ Failed to load Google Cloud Email library: ${error.message}`);
  }

  // Test 5: Check notifications integration
  console.log('\n5️⃣ Testing Notifications Integration:');
  try {
    const notifications = require('./lib/notifications.js');
    console.log('   ✅ Notifications library loaded successfully');

    // Check if sendOneSignalEmail function exists and has Google Cloud integration
    if (typeof notifications.sendOneSignalEmail === 'function') {
      console.log('   ✅ sendOneSignalEmail function available');
    } else {
      console.log('   ❌ sendOneSignalEmail function not found');
    }

    if (typeof notifications.getEmailStatus === 'function') {
      console.log('   ✅ getEmailStatus function available');
    } else {
      console.log('   ❌ getEmailStatus function not found');
    }

  } catch (error) {
    console.log(`   ❌ Failed to load notifications library: ${error.message}`);
  }

  // Test 6: Check admin API endpoints
  console.log('\n6️⃣ Checking Admin API Endpoints:');
  const apiFiles = [
    './pages/api/admin/test-connections.js',
    './pages/api/admin/google-cloud-email.js'
  ];

  apiFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file} exists`);
    } else {
      console.log(`   ❌ ${file} missing`);
    }
  });

  console.log('\n🎯 Summary and Next Steps:');
  console.log('='.repeat(50));

  if (!configStatus.gmail && !configStatus.workspace) {
    console.log('❌ No email services configured.');
    console.log('\n📋 Action Required:');
    console.log('1. Choose Gmail or Google Workspace option');
    console.log('2. Follow the setup guide in GOOGLE_CLOUD_EMAIL_SETUP_GUIDE.md');
    console.log('3. Update your .env.local file with real credentials');
    console.log('4. Re-run this test script');
  } else {
    console.log('✅ Email configuration detected.');
    console.log('\n📋 Next Steps:');
    console.log('1. Test email sending in the admin panel');
    console.log('2. Send a test email to verify delivery');
    console.log('3. Configure production environment variables');
    console.log('4. Test user registration and booking emails');
  }

  console.log('\n🔗 Resources:');
  console.log('- Setup Guide: GOOGLE_CLOUD_EMAIL_SETUP_GUIDE.md');
  console.log('- Admin Panel: http://localhost:3000/admin/settings');
  console.log('- Email Settings Tab: Click "Email Settings" in admin panel');
}

// Run the test
testEmailConfiguration().catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
