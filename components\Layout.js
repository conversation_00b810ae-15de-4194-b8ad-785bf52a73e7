import Head from 'next/head'
import Link from 'next/link'
import { useState, useEffect } from 'react'
import styles from '@/styles/Layout.module.css'
import ModernNavbar from './ModernNavbar'
import { useRouter } from 'next/router'
import SparkleButton from './SparkleButton'
import Image from 'next/image'
import SchemaManager from './StructuredData/SchemaManager'
import NotificationPrompt from './NotificationPrompt'
import { OneSignalProvider } from './OneSignalProvider'
import { safeRender } from '@/lib/safe-render-utils'
import { initializeProductionSecurity } from '@/lib/production-security'
import GoogleVerification from './GoogleVerification'

export default function Layout({ children }) {
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const [navOpen, setNavOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const router = useRouter();

  // Handle scroll to top button visibility
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Toggle mobile navigation
  const toggleNav = () => {
    setNavOpen(!navOpen);
  };

  // Close nav when route changes
  useEffect(() => {
    setNavOpen(false);
  }, [router.pathname]);

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const menuItems = [
    { label: 'Home', href: '/' },
    { label: 'About Us', href: '/about' },
    { label: 'Services', href: '/services' },
    { label: 'Gallery', href: '/gallery' }, // Added new menu item for gallery page
    { label: 'Shop', href: '/shop' },
    { label: 'Gift Card', href: '/gift-card' },
    { label: 'Contact', href: '/contact' },
  ];

  // Get the current URL for canonical tag
  const { asPath } = router;
  const canonicalUrl = `https://www.oceansoulsparkles.com.au${asPath}`;

  // Initialize production security
  useEffect(() => {
    initializeProductionSecurity();
  }, []);

  // Determine if we should use OneSignalProvider
  const useOneSignal = process.env.NODE_ENV === 'production';

  // Render the layout
  const content = (
    <div className={styles.container}>
        <Head>
          <link rel="canonical" href={canonicalUrl} />
        </Head>
        <GoogleVerification />
        <SchemaManager />
        <header className={`${styles.header} ${scrolled ? styles.scrolled : ''}`}>
          <div className={styles.headerContainer}>          <Link href="/" className={styles.logo}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles"
              />
            </Link>

            <button
              className={`${styles.mobileToggle} ${navOpen ? styles.active : ''}`}
              onClick={toggleNav}
              aria-label="Toggle Navigation"
            >
              <span></span>
              <span></span>
              <span></span>
            </button>

            <nav className={`${styles.nav} ${navOpen ? styles.open : ''}`}>
              <ul>
                <li className={router.pathname === '/' ? styles.active : ''}>
                  <Link href="/">Home</Link>
                </li>
                <li className={router.pathname === '/about' ? styles.active : ''}>
                  <Link href="/about">About</Link>
                </li>
                <li className={router.pathname === '/services' ? styles.active : ''}>
                  <Link href="/services">Services</Link>
                </li>
                <li className={router.pathname === '/gallery' ? styles.active : ''}>
                  <Link href="/gallery">Gallery</Link>
                </li>
                <li className={router.pathname === '/shop' ? styles.active : ''}>
                  <Link href="/shop">Shop</Link>
                </li>
                <li className={router.pathname === '/gift-card' ? styles.active : ''}>
                  <Link href="/gift-card">Gift Card</Link>
                </li>
                <li className={router.pathname === '/contact' ? styles.active : ''}>
                  <Link href="/contact">Contact</Link>
                </li>
              </ul>
            </nav>

            <div className={styles.bookNowContainer}>
              <SparkleButton href="/book-online" className={styles.bookNowButton}>
                Book Now
              </SparkleButton>
            </div>
          </div>
        </header>

        {children}

        <NotificationPrompt />

      <footer className={styles.footer}>
        <div className={styles.footerWave}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" preserveAspectRatio="none">
            <path
              fill="#4ECDC4"
              fillOpacity="0.2"
              d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
            ></path>
          </svg>
        </div>

        <div className={styles.footerContent}>
          <div className={styles.footerGrid}>
            <div className={styles.footerBranding}>
              <Link href="/">
                <div className={styles.rainbowLogoText}>OceanSoulSparkles</div>
              </Link>
              <p className={styles.footerTagline}>
                <span className="accent-text">"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!"</span>
              </p>
              <div className={styles.footerSocial}>
                <a href="https://www.instagram.com/oceansoulsparkles" target="_blank" rel="noopener noreferrer" className={styles.footerSocialLink}>
                  <img src="/images/social/instagram-icon.png" alt="Instagram" />
                </a>
                <a href="https://www.facebook.com/OceanSoulSparkles/" target="_blank" rel="noopener noreferrer" className={styles.footerSocialLink}>
                  <img src="/images/social/facebook-icon.png" alt="Facebook" />
                </a>
              </div>
            </div>

            <div className={styles.footerLinks}>
              <h3>Quick Links</h3>
              <ul>
                {menuItems.map((item, index) => (
                  <li key={index}>
                    <Link href={safeRender(item.href, '/')} className={styles.footerLink}>
                      {safeRender(item.label)}
                    </Link>
                  </li>
                ))}
                <li>
                  <Link href="/book-online" className={styles.footerLink}>
                    Book Online
                  </Link>
                </li>
                <li>
                  <Link href="/policies#return-policy" className={styles.footerLink}>
                    Return & Refund Policy
                  </Link>
                </li>
                <li>
                  <Link href="/policies#shipping-info" className={styles.footerLink}>
                    Shipping Information
                  </Link>
                </li>
              </ul>
            </div>

            <div className={styles.footerContact}>
              <h3>Contact Us</h3>
              <p>
                <a href="mailto:<EMAIL>" className={styles.footerContactLink}>
                  <EMAIL>
                </a>
              </p>
              <p>Melbourne, Victoria</p>
              <Link href="/contact" className={`${styles.footerButton} button button--outline`}>
                Get in Touch
              </Link>
            </div>
          </div>
        </div>

        <div className={styles.footerPayments}>
          <h3>Payment Methods</h3>
          <div className={styles.paymentLogos}>
            <img src="/images/logos/paypal-logo.svg" alt="PayPal" width={100} height={25} />
            <img src="/images/logos/square-logo.svg" alt="Square" width={100} height={25} />
          </div>
          <p className={styles.securePaymentNote}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
            </svg>
            <span>Secure payment processing</span>
          </p>
        </div>

        <div className={styles.footerBottom}>
          <p>&copy; {new Date().getFullYear()} by OceanSoulSparkles. All rights reserved.</p>
          <p>Proudly created with Next.js</p>
        </div>

        {scrollToTopVisible && (
          <button
            className={styles.scrollToTop}
            onClick={scrollToTop}
            aria-label="Scroll to top"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="18 15 12 9 6 15"></polyline>
            </svg>
          </button>
        )}
      </footer>
    </div>
  );

  // Wrap with OneSignalProvider in production, otherwise return content directly
  return useOneSignal ? <OneSignalProvider>{content}</OneSignalProvider> : content;
}
