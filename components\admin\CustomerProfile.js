import { useState } from 'react';
import Link from 'next/link';
import { createClient } from '@supabase/supabase-js';
import CustomerTagManager from './CustomerTagManager';
import styles from '@/styles/admin/CustomerProfile.module.css';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Enhanced customer profile component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.customer - Customer data
 * @param {Array} props.bookings - Customer bookings
 * @param {Array} props.preferences - Customer preferences
 * @returns {JSX.Element}
 */
export default function CustomerProfile({ customer, bookings, preferences }) {
  const [activeTab, setActiveTab] = useState('info');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  
  if (!customer) {
    return <div className={styles.loading}>Loading customer profile...</div>;
  }
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };
  
  // Calculate customer statistics
  const totalSpent = bookings
    .filter(booking => booking.status !== 'canceled')
    .reduce((sum, booking) => sum + (booking.services?.price || 0), 0);
    
  const upcomingBookings = bookings
    .filter(booking => 
      booking.status !== 'canceled' && 
      new Date(booking.start_time) > new Date()
    ).length;
    
  const lastBookingDate = bookings.length > 0 
    ? new Date(Math.max(...bookings.map(b => new Date(b.start_time))))
    : null;
    
  const firstBookingDate = bookings.length > 0
    ? new Date(Math.min(...bookings.map(b => new Date(b.start_time))))
    : null;
    
  // Toggle VIP status
  const toggleVipStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccessMessage(null);
      
      // Update customer VIP status
      const { data, error } = await supabase
        .from('customers')
        .update({ vip: !customer.vip })
        .eq('id', customer.id)
        .select();
        
      if (error) throw error;
      
      // Update local state
      customer.vip = !customer.vip;
      
      setSuccessMessage(`Customer ${customer.vip ? 'marked as VIP' : 'removed from VIP'}`);
    } catch (error) {
      console.error('Error updating VIP status:', error);
      setError('Failed to update VIP status');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className={styles.customerProfile}>
      <div className={styles.profileHeader}>
        <div className={styles.customerInfo}>
          <h2 className={styles.customerName}>{customer.name}</h2>
          <div className={styles.customerMeta}>
            <span className={styles.customerEmail}>{customer.email}</span>
            {customer.phone && (
              <span className={styles.customerPhone}>{customer.phone}</span>
            )}
          </div>
        </div>
        
        <div className={styles.customerActions}>
          <Link
            href={`/admin/customers/${customer.id}/edit`}
            className={styles.editButton}
          >
            Edit Profile
          </Link>
          <Link
            href={`/admin/bookings/new?customer_id=${customer.id}`}
            className={styles.newBookingButton}
          >
            New Booking
          </Link>
        </div>
      </div>
      
      {error && (
        <div className={styles.error}>{error}</div>
      )}
      
      {successMessage && (
        <div className={styles.success}>{successMessage}</div>
      )}
      
      <div className={styles.customerStats}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>${totalSpent.toFixed(2)}</div>
          <div className={styles.statLabel}>Total Spent</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{bookings.length}</div>
          <div className={styles.statLabel}>Total Bookings</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{upcomingBookings}</div>
          <div className={styles.statLabel}>Upcoming</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{formatDate(customer.customer_since || firstBookingDate)}</div>
          <div className={styles.statLabel}>Customer Since</div>
        </div>
      </div>
      
      <div className={styles.tabsContainer}>
        <div className={styles.tabs}>
          <button
            className={`${styles.tabButton} ${activeTab === 'info' ? styles.active : ''}`}
            onClick={() => setActiveTab('info')}
          >
            Information
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'tags' ? styles.active : ''}`}
            onClick={() => setActiveTab('tags')}
          >
            Tags
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'preferences' ? styles.active : ''}`}
            onClick={() => setActiveTab('preferences')}
          >
            Preferences
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'notes' ? styles.active : ''}`}
            onClick={() => setActiveTab('notes')}
          >
            Notes
          </button>
        </div>
        
        <div className={styles.tabContent}>
          {activeTab === 'info' && (
            <div className={styles.infoTab}>
              <div className={styles.infoSection}>
                <h3 className={styles.sectionTitle}>Contact Information</h3>
                <div className={styles.infoGrid}>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Email</div>
                    <div className={styles.infoValue}>{customer.email}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Phone</div>
                    <div className={styles.infoValue}>{customer.phone || 'N/A'}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Address</div>
                    <div className={styles.infoValue}>
                      {customer.address ? (
                        <>
                          {customer.address}<br />
                          {customer.city && `${customer.city}, `}
                          {customer.state && `${customer.state} `}
                          {customer.postal_code && customer.postal_code}
                        </>
                      ) : (
                        'N/A'
                      )}
                    </div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Country</div>
                    <div className={styles.infoValue}>{customer.country || 'Australia'}</div>
                  </div>
                </div>
              </div>
              
              <div className={styles.infoSection}>
                <h3 className={styles.sectionTitle}>Additional Information</h3>
                <div className={styles.infoGrid}>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Customer Since</div>
                    <div className={styles.infoValue}>{formatDate(customer.customer_since || firstBookingDate)}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Last Booking</div>
                    <div className={styles.infoValue}>{formatDate(customer.last_booking_date || lastBookingDate)}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Referral Source</div>
                    <div className={styles.infoValue}>{customer.referral_source || 'N/A'}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>VIP Status</div>
                    <div className={styles.infoValue}>
                      <button
                        className={`${styles.vipToggle} ${customer.vip ? styles.vipActive : ''}`}
                        onClick={toggleVipStatus}
                        disabled={loading}
                      >
                        {customer.vip ? 'VIP Customer' : 'Regular Customer'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className={styles.infoSection}>
                <h3 className={styles.sectionTitle}>Marketing Preferences</h3>
                <div className={styles.infoGrid}>
                  <div className={styles.infoItem}>
                    <div className={styles.infoLabel}>Marketing Consent</div>
                    <div className={styles.infoValue}>
                      {customer.marketing_consent ? 'Yes' : 'No'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'tags' && (
            <div className={styles.tagsTab}>
              <CustomerTagManager customerId={customer.id} />
            </div>
          )}
          
          {activeTab === 'preferences' && (
            <div className={styles.preferencesTab}>
              <div className={styles.preferencesSection}>
                <h3 className={styles.sectionTitle}>Customer Preferences</h3>
                {preferences && preferences.length > 0 ? (
                  <div className={styles.preferencesList}>
                    {preferences.map(pref => (
                      <div key={pref.id} className={styles.preferenceItem}>
                        <div className={styles.preferenceKey}>{pref.preference_key}</div>
                        <div className={styles.preferenceValue}>{pref.preference_value}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={styles.emptyState}>No preferences recorded</div>
                )}
              </div>
            </div>
          )}
          
          {activeTab === 'notes' && (
            <div className={styles.notesTab}>
              <div className={styles.notesSection}>
                <h3 className={styles.sectionTitle}>Customer Notes</h3>
                <div className={styles.notesContent}>
                  {customer.notes ? (
                    <div className={styles.notesText}>{customer.notes}</div>
                  ) : (
                    <div className={styles.emptyState}>No notes recorded</div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
