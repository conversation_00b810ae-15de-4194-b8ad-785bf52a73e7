/* Mobile Booking Page - Mobile-First Design */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo img {
  height: 40px;
  width: auto;
}

.stepIndicator {
  background: #4ECDC4;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

/* Content */
.content {
  padding: 1rem;
  max-width: 480px;
  margin: 0 auto;
}

.stepContainer {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.stepHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.stepTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.stepSubtitle {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

/* Service Selection */
.servicesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.serviceCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.serviceCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(78, 205, 196, 0.2);
}

.serviceCard.selected {
  border-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.05);
}

.serviceIcon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
  flex-shrink: 0;
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.3rem 0;
}

.serviceDescription {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.servicePricing {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.servicePrice {
  font-size: 1.1rem;
  font-weight: 700;
  color: #4ECDC4;
}

.serviceDuration {
  font-size: 0.9rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
}

/* Time Selection */
.timeSlots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.timeSlot {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.timeSlot:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
}

.timeSlot.selected {
  border-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.1);
}

.timeSlot.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.slotTime {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.slotDate {
  font-size: 0.8rem;
  color: #666;
}

/* Customer Form */
.customerForm {
  margin-bottom: 2rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input,
.textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #4ECDC4;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

/* Booking Summary */
.bookingSummary {
  margin-bottom: 2rem;
}

.summarySection {
  padding: 1rem 0;
  border-bottom: 1px solid #e1e8ed;
}

.summarySection:last-child {
  border-bottom: none;
}

.summaryTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.summaryText {
  color: #666;
  margin: 0 0 0.2rem 0;
  font-size: 0.9rem;
}

.totalSection {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.totalRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.totalLabel {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.totalAmount {
  font-size: 1.3rem;
  font-weight: 700;
  color: #4ECDC4;
}

/* Action Buttons */
.stepActions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.primaryButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 1.2rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(78, 205, 196, 0.3);
  min-height: 56px;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(78, 205, 196, 0.4);
}

.primaryButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.secondaryButton {
  background: white;
  color: #4ECDC4;
  border: 2px solid #4ECDC4;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
}

.secondaryButton:hover {
  background: #4ECDC4;
  color: white;
}

/* Error State */
.errorContainer {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  margin: 2rem 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e74c3c;
  margin: 0 0 1rem 0;
}

.errorMessage {
  color: #666;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.errorActions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .content {
    padding: 0.5rem;
  }
  
  .stepContainer {
    padding: 1rem;
  }
  
  .timeSlots {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stepActions {
    gap: 0.8rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 480px;
    margin: 0 auto;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
  }
  
  .timeSlots {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Touch device optimizations */
@media (hover: none) {
  .serviceCard:hover,
  .timeSlot:hover,
  .primaryButton:hover,
  .secondaryButton:hover {
    transform: none;
  }
}
