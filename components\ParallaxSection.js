import { useEffect, useRef, useState } from 'react';
import styles from '@/styles/ParallaxSection.module.css';

/**
 * ParallaxSection component that creates a parallax scrolling effect
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child elements
 * @param {string} props.backgroundImage - URL of the background image
 * @param {string} props.overlayColor - CSS color value for the overlay
 * @param {number} props.overlayOpacity - Opacity of the overlay (0-1)
 * @param {number} props.speed - Parallax speed (0-1)
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.height - Height of the section (CSS value)
 * @returns {JSX.Element}
 */
const ParallaxSection = ({ 
  children, 
  backgroundImage,
  overlayColor = 'rgba(0, 0, 0, 0.4)',
  overlayOpacity = 0.4,
  speed = 0.5,
  className = '',
  height = '500px',
  ...props 
}) => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const sectionRef = useRef(null);
  
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;
      
      const element = sectionRef.current;
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Check if element is in viewport
      if (rect.top < windowHeight && rect.bottom > 0) {
        // Calculate how far the element is from the top of the viewport
        const distanceFromTop = rect.top;
        // Calculate the parallax offset
        const parallaxOffset = distanceFromTop * speed;
        
        setScrollPosition(parallaxOffset);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    // Initial calculation
    handleScroll();
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [speed]);
  
  const overlayStyle = {
    backgroundColor: overlayColor,
    opacity: overlayOpacity,
  };
  
  const backgroundStyle = {
    backgroundImage: `url(${backgroundImage})`,
    transform: `translateY(${scrollPosition}px)`,
  };
  
  const sectionStyle = {
    height,
  };
  
  return (
    <div 
      ref={sectionRef}
      className={`${styles.parallaxSection} ${className}`}
      style={sectionStyle}
      {...props}
    >
      <div 
        className={styles.parallaxBackground} 
        style={backgroundStyle}
      />
      <div 
        className={styles.parallaxOverlay} 
        style={overlayStyle}
      />
      <div className={styles.parallaxContent}>
        {children}
      </div>
    </div>
  );
};

export default ParallaxSection;
