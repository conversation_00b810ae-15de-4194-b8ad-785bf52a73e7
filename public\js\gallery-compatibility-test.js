/**
 * Gallery Image Compatibility Test Script
 * Tests image loading across different browsers and modes
 */

(function() {
  'use strict';

  // Test configuration
  const TEST_CONFIG = {
    maxRetries: 3,
    timeout: 10000,
    testImages: [
      '/images/gallery/fav/Butterfly.JPG',
      '/images/gallery/fav/Gold Leopard.jpg',
      '/images/gallery/fav/UV Alex.JPG'
    ]
  };

  // Test results storage
  let testResults = {
    browser: getBrowserInfo(),
    timestamp: new Date().toISOString(),
    imageTests: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    }
  };

  /**
   * Get browser information
   */
  function getBrowserInfo() {
    const ua = navigator.userAgent;
    const isPrivate = detectPrivateMode();
    
    return {
      userAgent: ua,
      isPrivateMode: isPrivate,
      browser: detectBrowser(ua),
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };
  }

  /**
   * Detect browser type
   */
  function detectBrowser(ua) {
    if (ua.includes('Edge')) return 'Microsoft Edge';
    if (ua.includes('Chrome')) return 'Google Chrome';
    if (ua.includes('Firefox')) return 'Mozilla Firefox';
    if (ua.includes('Safari')) return 'Safari';
    return 'Unknown';
  }

  /**
   * Detect private/incognito mode
   */
  function detectPrivateMode() {
    try {
      // Test localStorage availability
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      
      // Test sessionStorage availability
      sessionStorage.setItem('test', 'test');
      sessionStorage.removeItem('test');
      
      return false; // Not private mode
    } catch (e) {
      return true; // Likely private mode
    }
  }

  /**
   * Test image loading
   */
  function testImageLoading(imageSrc) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const img = new Image();
      
      const timeout = setTimeout(() => {
        resolve({
          src: imageSrc,
          status: 'timeout',
          loadTime: Date.now() - startTime,
          error: 'Image loading timed out'
        });
      }, TEST_CONFIG.timeout);

      img.onload = function() {
        clearTimeout(timeout);
        resolve({
          src: imageSrc,
          status: 'success',
          loadTime: Date.now() - startTime,
          dimensions: {
            width: img.naturalWidth,
            height: img.naturalHeight
          }
        });
      };

      img.onerror = function() {
        clearTimeout(timeout);
        resolve({
          src: imageSrc,
          status: 'error',
          loadTime: Date.now() - startTime,
          error: 'Failed to load image'
        });
      };

      // Add cache-busting parameter for private mode compatibility
      const cacheBuster = `?t=${Date.now()}&r=${Math.random()}`;
      img.src = imageSrc + cacheBuster;
    });
  }

  /**
   * Test gallery functionality
   */
  function testGalleryFunctionality() {
    const galleryItems = document.querySelectorAll('[class*="galleryItem"]');
    const galleryImages = document.querySelectorAll('[class*="galleryImage"]');
    
    return {
      galleryItemsFound: galleryItems.length,
      galleryImagesFound: galleryImages.length,
      hasLoadingStates: Array.from(galleryImages).some(img => 
        img.classList.contains('loading') || img.classList.contains('loaded')
      )
    };
  }

  /**
   * Run all tests
   */
  async function runTests() {
    console.log('🖼️ Starting Gallery Compatibility Tests...\n');
    console.log('Browser Info:', testResults.browser);

    // Test gallery functionality
    const galleryTest = testGalleryFunctionality();
    console.log('Gallery Elements:', galleryTest);

    // Test individual images
    for (const imageSrc of TEST_CONFIG.testImages) {
      console.log(`Testing image: ${imageSrc}`);
      const result = await testImageLoading(imageSrc);
      testResults.imageTests.push(result);
      testResults.summary.total++;

      if (result.status === 'success') {
        testResults.summary.passed++;
        console.log(`✅ ${imageSrc} - Loaded in ${result.loadTime}ms`);
      } else {
        testResults.summary.failed++;
        testResults.summary.errors.push(result.error);
        console.log(`❌ ${imageSrc} - ${result.error}`);
      }
    }

    // Generate summary
    const passRate = (testResults.summary.passed / testResults.summary.total * 100).toFixed(1);
    console.log('\n📊 Test Summary:');
    console.log(`Total Tests: ${testResults.summary.total}`);
    console.log(`Passed: ${testResults.summary.passed}`);
    console.log(`Failed: ${testResults.summary.failed}`);
    console.log(`Pass Rate: ${passRate}%`);

    if (testResults.summary.failed > 0) {
      console.log('\n❌ Errors:');
      testResults.summary.errors.forEach(error => console.log(`  - ${error}`));
    }

    // Store results globally for debugging
    window.galleryTestResults = testResults;
    
    return testResults;
  }

  /**
   * Test specific browser compatibility issues
   */
  function testBrowserSpecificIssues() {
    const issues = [];

    // Test for Edge private mode issues
    if (testResults.browser.browser.includes('Edge') && testResults.browser.isPrivateMode) {
      issues.push('Edge private mode detected - may have image loading restrictions');
    }

    // Test for CORS issues
    if (!testResults.browser.onLine) {
      issues.push('Browser appears to be offline');
    }

    // Test for storage restrictions
    if (testResults.browser.isPrivateMode) {
      issues.push('Private browsing mode detected - storage restrictions may apply');
    }

    return issues;
  }

  // Auto-run tests when script loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runTests);
  } else {
    runTests();
  }

  // Export test function for manual use
  window.testGalleryCompatibility = runTests;
  window.getBrowserInfo = getBrowserInfo;

})();
