// Theme configuration for Ocean Soul Sparkles website
// This file contains color schemes, typography settings, and other theme variables

const theme = {
  // Color palette
  colors: {
    // Primary colors
    primary: {
      main: '#4ECDC4', // Teal - current primary color
      light: '#7FDBDA', // Lighter teal
      dark: '#3A9D96', // Darker teal
      gradient: 'linear-gradient(135deg, #4ECDC4 0%, #2E9BBA 100%)', // Teal to blue gradient
    },
    // Secondary colors
    secondary: {
      main: '#FF6B6B', // Coral accent
      light: '#FF9E9E', // Light coral
      dark: '#E54B4B', // Dark coral
      gradient: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)', // Coral gradient
    },
    // Ocean-inspired accent colors
    accent: {
      blue: '#1A73E8', // Deep ocean blue
      purple: '#9C27B0', // Purple for contrast
      teal: '#009688', // Alternative teal
      gold: '#FFD700', // Gold for sparkle elements
    },
    // Background colors
    background: {
      light: '#FFFFFF', // White
      offWhite: '#F8F9FA', // Off-white for sections
      gradient: 'linear-gradient(180deg, #F8F9FA 0%, #E9F7F6 100%)', // Subtle gradient
      overlay: 'rgba(78, 205, 196, 0.8)', // Teal overlay with transparency
    },
    // Text colors
    text: {
      primary: '#333333', // Dark gray for main text
      secondary: '#666666', // Medium gray for secondary text
      light: '#999999', // Light gray for tertiary text
      inverse: '#FFFFFF', // White text for dark backgrounds
    },
    // Utility colors
    utility: {
      success: '#4CAF50', // Green for success messages
      error: '#F44336', // Red for error messages
      warning: '#FF9800', // Orange for warnings
      info: '#2196F3', // Blue for information
    },
    // Border colors
    border: {
      light: '#EEEEEE', // Light gray for subtle borders
      medium: '#DDDDDD', // Medium gray for more visible borders
      dark: '#CCCCCC', // Darker gray for emphasized borders
    }
  },

  // Typography
  typography: {
    // Font families
    fontFamily: {
      primary: "'Montserrat', sans-serif", // Main font for headings
      secondary: "'Lato', sans-serif", // Secondary font for body text
      accent: "'Dancing Script', cursive", // Decorative font for special elements
    },
    // Font weights
    fontWeight: {
      light: 300,
      regular: 400,
      medium: 500,
      semiBold: 600,
      bold: 700,
    },
    // Font sizes
    fontSize: {
      xs: '0.75rem', // 12px
      sm: '0.875rem', // 14px
      base: '1rem', // 16px
      lg: '1.125rem', // 18px
      xl: '1.25rem', // 20px
      '2xl': '1.5rem', // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem', // 48px
      '6xl': '4rem', // 64px
    },
    // Line heights
    lineHeight: {
      none: 1,
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2,
    },
    // Letter spacing
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em',
    },
  },

  // Spacing
  spacing: {
    xs: '0.25rem', // 4px
    sm: '0.5rem', // 8px
    md: '1rem', // 16px
    lg: '1.5rem', // 24px
    xl: '2rem', // 32px
    '2xl': '2.5rem', // 40px
    '3xl': '3rem', // 48px
    '4xl': '4rem', // 64px
    '5xl': '6rem', // 96px
  },

  // Breakpoints for responsive design
  breakpoints: {
    xs: '320px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Border radius
  borderRadius: {
    none: '0',
    sm: '0.125rem', // 2px
    md: '0.25rem', // 4px
    lg: '0.5rem', // 8px
    xl: '1rem', // 16px
    full: '9999px', // Fully rounded (circles)
  },

  // Box shadows
  boxShadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none',
  },

  // Transitions
  transition: {
    fast: 'all 0.2s ease',
    normal: 'all 0.3s ease',
    slow: 'all 0.5s ease',
    bounce: 'all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55)', // Bouncy effect
  },

  // Z-index values
  zIndex: {
    behind: -1,
    auto: 'auto',
    base: 0,
    raised: 1,
    dropdown: 1000,
    sticky: 1100,
    fixed: 1200,
    modal: 1300,
    popover: 1400,
    tooltip: 1500,
  },
};

export default theme;
