import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for managing artist/braider applications
 * GET /api/admin/users/applications - Fetch applications with filtering
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Applications API called with method: ${req.method}`)

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed: ${authResult.error?.message}`)
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.error?.message || 'Authentication required'
      })
    }

    const { user: adminUser, role: adminRole } = authResult
    console.log(`[${requestId}] Authenticated user: ${adminUser.email} (${adminRole})`)

    // Ensure user has appropriate privileges
    if (!['dev', 'admin', 'artist', 'braider'].includes(adminRole)) {
      return res.status(403).json({ error: 'Insufficient permissions' })
    }

    if (req.method === 'GET') {
      return await handleGetApplications(req, res, requestId)
    } else {
      return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

/**
 * Handle GET request - fetch applications with optional filtering
 */
async function handleGetApplications(req, res, requestId) {
  try {
    const { 
      status = 'all',
      type = 'all',
      search = '',
      limit = 50,
      offset = 0 
    } = req.query

    console.log(`[${requestId}] Fetching applications with filters:`, { status, type, search, limit, offset })

    const adminClient = getAdminClient()
    if (!adminClient) {
      throw new Error('Failed to initialize admin client')
    }

    // Build query for applications (without user details for now)
    let query = adminClient
      .from('artist_braider_applications')
      .select(`
        id,
        user_id,
        application_type,
        experience_years,
        portfolio_url,
        availability_preferences,
        service_specializations,
        previous_experience,
        professional_references,
        status,
        reviewed_by,
        reviewed_at,
        review_notes,
        welcome_email_sent,
        welcome_email_sent_at,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status)
    }

    if (type !== 'all') {
      query = query.eq('application_type', type)
    }

    if (search) {
      // Search in application details (we'll filter by user name later)
      query = query.or(`previous_experience.ilike.%${search}%,professional_references.ilike.%${search}%`)
    }

    // Apply pagination
    query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)

    const { data: applications, error } = await query

    if (error) {
      console.error(`[${requestId}] Error fetching applications:`, error)
      throw error
    }

    console.log(`[${requestId}] Found ${applications?.length || 0} applications`)

    // Get user details for each application
    const applicationsWithUserData = []
    if (applications && applications.length > 0) {
      for (const app of applications) {
        try {
          // Get user profile data
          const { data: userProfile, error: profileError } = await adminClient
            .from('user_profiles')
            .select('name, phone')
            .eq('id', app.user_id)
            .single()

          // Get user email from auth.users
          const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(app.user_id)

          applicationsWithUserData.push({
            ...app,
            userName: userProfile?.name || userData?.user?.email || 'Unknown',
            userPhone: userProfile?.phone || null,
            userEmail: userData?.user?.email || null
          })
        } catch (error) {
          console.warn(`[${requestId}] Error fetching user data for ${app.user_id}:`, error)
          applicationsWithUserData.push({
            ...app,
            userName: 'Unknown',
            userPhone: null,
            userEmail: null
          })
        }
      }
    }

    // Get statistics
    const { data: statsData, error: statsError } = await adminClient
      .from('artist_braider_applications')
      .select('status')

    if (statsError) {
      console.warn(`[${requestId}] Error fetching stats:`, statsError)
    }

    // Calculate statistics
    const stats = {
      pending: 0,
      under_review: 0,
      approved: 0,
      rejected: 0,
      total: 0
    }

    if (statsData) {
      statsData.forEach(app => {
        stats[app.status] = (stats[app.status] || 0) + 1
        stats.total++
      })
    }

    console.log(`[${requestId}] Application statistics:`, stats)

    // Filter by search term if provided (now that we have user names)
    let filteredApplications = applicationsWithUserData
    if (search) {
      const searchLower = search.toLowerCase()
      filteredApplications = applicationsWithUserData.filter(app =>
        app.userName?.toLowerCase().includes(searchLower) ||
        app.userEmail?.toLowerCase().includes(searchLower) ||
        app.previous_experience?.toLowerCase().includes(searchLower) ||
        app.professional_references?.toLowerCase().includes(searchLower)
      )
    }

    // Transform applications data for frontend
    const transformedApplications = filteredApplications.map(app => ({
      id: app.id,
      userId: app.user_id,
      applicationType: app.application_type,
      experienceYears: app.experience_years,
      portfolioUrl: app.portfolio_url,
      availabilityPreferences: app.availability_preferences,
      serviceSpecializations: app.service_specializations,
      previousExperience: app.previous_experience,
      professionalReferences: app.professional_references,
      status: app.status,
      reviewedBy: app.reviewed_by,
      reviewedAt: app.reviewed_at,
      reviewNotes: app.review_notes,
      welcomeEmailSent: app.welcome_email_sent,
      welcomeEmailSentAt: app.welcome_email_sent_at,
      createdAt: app.created_at,
      updatedAt: app.updated_at,
      // User profile data
      userName: app.userName,
      userPhone: app.userPhone,
      userEmail: app.userEmail
    }))

    return res.status(200).json({
      applications: transformedApplications,
      stats,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: stats.total
      }
    })
  } catch (error) {
    console.error(`[${requestId}] Error in handleGetApplications:`, error)
    return res.status(500).json({ 
      error: 'Failed to fetch applications',
      message: error.message 
    })
  }
}
