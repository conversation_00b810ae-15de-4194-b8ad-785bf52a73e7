import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for managing events
 * Handles CRUD operations for events and festivals
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Events API called: ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetEvents(req, res, requestId);
    } else if (req.method === 'POST') {
      return await handleCreateEvent(req, res, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in events API:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

/**
 * Handle GET request - fetch events with optional filtering
 */
async function handleGetEvents(req, res, requestId) {
  try {
    const { 
      status = 'all',
      upcoming = false,
      limit = 50,
      offset = 0 
    } = req.query;

    console.log(`[${requestId}] Fetching events with filters:`, { status, upcoming, limit, offset });

    // Build query
    let query = supabase
      .from('events')
      .select(`
        id,
        name,
        location,
        description,
        start_date,
        end_date,
        status,
        max_capacity,
        current_bookings,
        created_by,
        created_at,
        updated_at,
        total_expenses,
        total_revenue,
        net_profit,
        artist_ticket_cost,
        artists_pay_tickets,
        expense_budget,
        revenue_target
      `)
      .order('start_date', { ascending: false });

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (upcoming === 'true') {
      const now = new Date().toISOString();
      query = query.gte('end_date', now);
    }

    // Apply pagination
    if (limit && offset) {
      query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);
    }

    const { data: events, error } = await query;

    if (error) {
      console.error(`[${requestId}] Error fetching events:`, error);
      throw error;
    }

    // Get QR code counts for each event
    const eventIds = events.map(event => event.id);
    const { data: qrCounts, error: qrError } = await supabase
      .from('event_qr_codes')
      .select('event_id, id')
      .in('event_id', eventIds);

    if (qrError) {
      console.warn(`[${requestId}] Error fetching QR code counts:`, qrError);
    }

    // Add QR code counts to events
    const eventsWithQRCounts = events.map(event => ({
      ...event,
      qr_code_count: qrCounts?.filter(qr => qr.event_id === event.id).length || 0
    }));

    console.log(`[${requestId}] Successfully fetched ${events.length} events`);
    return res.status(200).json({ 
      events: eventsWithQRCounts,
      total: events.length 
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetEvents:`, error);
    throw error;
  }
}

/**
 * Handle POST request - create new event
 */
async function handleCreateEvent(req, res, user, requestId) {
  try {
    const {
      name,
      location,
      description,
      start_date,
      end_date,
      max_capacity,
      status = 'active',
      // New financial tracking fields
      artist_ticket_cost,
      artists_pay_tickets,
      expense_budget,
      revenue_target
    } = req.body;

    console.log(`[${requestId}] Creating event:`, {
      name,
      location,
      start_date,
      end_date,
      artist_ticket_cost,
      artists_pay_tickets,
      expense_budget,
      revenue_target
    });

    // Validate required fields
    if (!name || !location || !start_date || !end_date) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['name', 'location', 'start_date', 'end_date']
      });
    }

    // Validate dates
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    const now = new Date();

    if (startDate >= endDate) {
      return res.status(400).json({
        error: 'Event start date must be before end date'
      });
    }

    if (endDate < now) {
      return res.status(400).json({
        error: 'Event end date cannot be in the past'
      });
    }

    // Validate financial fields
    if (artist_ticket_cost && (isNaN(artist_ticket_cost) || parseFloat(artist_ticket_cost) < 0)) {
      return res.status(400).json({
        error: 'Artist ticket cost must be a valid positive number'
      });
    }

    if (expense_budget && (isNaN(expense_budget) || parseFloat(expense_budget) < 0)) {
      return res.status(400).json({
        error: 'Expense budget must be a valid positive number'
      });
    }

    if (revenue_target && (isNaN(revenue_target) || parseFloat(revenue_target) < 0)) {
      return res.status(400).json({
        error: 'Revenue target must be a valid positive number'
      });
    }

    // Prepare event data
    const eventData = {
      name,
      location,
      description: description || '',
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      status,
      max_capacity: max_capacity ? parseInt(max_capacity) : null,
      current_bookings: 0,
      created_by: user.id,
      // Financial tracking fields with defaults
      total_expenses: 0.00,
      total_revenue: 0.00,
      net_profit: 0.00,
      artist_ticket_cost: artist_ticket_cost ? parseFloat(artist_ticket_cost) : 0.00,
      artists_pay_tickets: Boolean(artists_pay_tickets),
      expense_budget: expense_budget ? parseFloat(expense_budget) : null,
      revenue_target: revenue_target ? parseFloat(revenue_target) : null
    };

    // Create event
    const { data: event, error } = await supabase
      .from('events')
      .insert([eventData])
      .select()
      .single();

    if (error) {
      console.error(`[${requestId}] Error creating event:`, error);
      throw error;
    }

    console.log(`[${requestId}] Event created successfully:`, event.id);
    return res.status(201).json({ 
      event: {
        ...event,
        qr_code_count: 0
      }
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleCreateEvent:`, error);
    throw error;
  }
}
