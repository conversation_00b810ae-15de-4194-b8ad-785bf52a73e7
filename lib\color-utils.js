/**
 * Utility functions for color manipulation
 */

/**
 * Adjusts a hex color by the given percentage
 * @param {string} color - Hex color code (e.g., '#6a0dad')
 * @param {number} percent - Percentage to adjust (-100 to 100)
 * @returns {string} - Adjusted hex color
 */
export const adjustColor = (color, percent) => {
  // Remove # if present
  let hex = color.replace('#', '');
  
  // Convert to RGB
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);

  // Adjust color
  r = Math.max(0, Math.min(255, r + Math.floor(r * percent / 100)));
  g = Math.max(0, Math.min(255, g + Math.floor(g * percent / 100)));
  b = Math.max(0, Math.min(255, b + Math.floor(b * percent / 100)));

  // Convert back to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

/**
 * Determines if a color is light or dark
 * @param {string} color - Hex color code
 * @returns {boolean} - True if color is light, false if dark
 */
export const isLightColor = (color) => {
  // Remove # if present
  let hex = color.replace('#', '');
  
  // Convert to RGB
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);
  
  // Calculate luminance
  // Formula: 0.299*R + 0.587*G + 0.114*B
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return true if light, false if dark
  return luminance > 0.5;
};

/**
 * Gets appropriate text color (black or white) based on background color
 * @param {string} backgroundColor - Hex color code
 * @returns {string} - '#000000' for dark text or '#ffffff' for light text
 */
export const getTextColor = (backgroundColor) => {
  return isLightColor(backgroundColor) ? '#000000' : '#ffffff';
};

/**
 * Adds transparency to a hex color
 * @param {string} color - Hex color code
 * @param {number} opacity - Opacity value (0 to 1)
 * @returns {string} - RGBA color string
 */
export const addTransparency = (color, opacity) => {
  // Remove # if present
  let hex = color.replace('#', '');
  
  // Convert to RGB
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);
  
  // Return rgba string
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
