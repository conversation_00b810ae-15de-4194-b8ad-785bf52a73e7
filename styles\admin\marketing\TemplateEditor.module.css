.templateEditor {
  padding: 0;
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.formSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.formSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.formGroup {
  margin-bottom: 20px;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #6e8efb;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.helpText {
  margin-top: 4px;
  font-size: 0.8rem;
  color: #666;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
  margin: 0;
  width: 16px;
  height: 16px;
}

.contentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.inputWithButton {
  display: flex;
  gap: 10px;
}

.inputWithButton .input {
  flex: 1;
}

.personalizationButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.personalizationButton:hover:not(:disabled) {
  background-color: rgba(110, 142, 251, 0.1);
}

.personalizationButton:disabled {
  color: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.personalizationMenu {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.personalizationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #eee;
}

.personalizationHeader h4 {
  margin: 0;
  font-size: 1rem;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.personalizationTokens {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
}

.personalizationTokens button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.personalizationTokens button:hover {
  background-color: #e0e0e0;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancelButton,
.saveButton {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.saveButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cancelButton:disabled,
.saveButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.preview {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.preview h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.previewSubject {
  margin-bottom: 16px;
}

.previewContentText {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
}

.previewContentText p {
  margin: 0 0 8px 0;
}

.previewContentText p:last-child {
  margin-bottom: 0;
}

.emailPreview {
  font-family: Arial, sans-serif;
}

@media (max-width: 768px) {
  .formRow {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .inputWithButton {
    flex-direction: column;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .cancelButton,
  .saveButton {
    width: 100%;
  }
}
