import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query
  const { campaign_id } = req.body

  try {
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Get template
    const { data: template, error: templateError } = await client
      .from('marketing_templates')
      .select('*')
      .eq('id', id)
      .single()

    if (templateError) {
      throw new Error(`Error fetching template: ${templateError.message}`)
    }

    if (!template) {
      return res.status(404).json({ error: 'Template not found' })
    }

    // Check if template is active
    if (!template.is_active) {
      return res.status(400).json({ error: 'Cannot apply inactive template' })
    }

    // If campaign_id is provided, check if campaign exists
    if (campaign_id) {
      const { data: campaign, error: campaignError } = await client
        .from('marketing_campaigns')
        .select('id, status, campaign_type')
        .eq('id', campaign_id)
        .single()

      if (campaignError) {
        throw new Error(`Error fetching campaign: ${campaignError.message}`)
      }

      if (!campaign) {
        return res.status(404).json({ error: 'Campaign not found' })
      }

      // Check if campaign is in a state where messages can be added
      if (campaign.status === 'completed' || campaign.status === 'canceled') {
        return res.status(400).json({ error: `Cannot add messages to a ${campaign.status} campaign` })
      }

      // Check if template type matches campaign type
      if (
        (campaign.campaign_type === 'email' && template.template_type !== 'email') ||
        (campaign.campaign_type === 'sms' && template.template_type !== 'sms') ||
        (campaign.campaign_type === 'push' && template.template_type !== 'push')
      ) {
        return res.status(400).json({ 
          error: `Template type (${template.template_type}) does not match campaign type (${campaign.campaign_type})` 
        })
      }

      // Create a new message from the template
      const { data: message, error: messageError } = await supabase
        .from('campaign_messages')
        .insert([
          {
            campaign_id,
            subject: template.subject,
            content: `${template.content}\n\n<!-- Template ID: ${template.id} -->`,
            message_type: template.template_type,
            status: 'draft'
          }
        ])
        .select()

      if (messageError) {
        throw new Error(`Error creating message: ${messageError.message}`)
      }

      return res.status(201).json({
        message: message[0],
        template,
        applied_to_campaign: true
      })
    }

    // If no campaign_id, just return the template content
    return res.status(200).json({
      template_content: {
        subject: template.subject,
        content: template.content,
        template_type: template.template_type
      },
      template,
      applied_to_campaign: false
    })
  } catch (error) {
    console.error('Error applying template:', error)
    return res.status(500).json({ error: error.message })
  }
}
