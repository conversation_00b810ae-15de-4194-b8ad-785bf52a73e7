/* PaymentsPage.module.css */
.paymentsPage {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: #333;
}

.refreshButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.refreshButton:hover {
  background-color: #5a0c8f;
}

.refreshButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.errorBox {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border-left: 4px solid #c62828;
}

.errorBox button {
  background-color: #c62828;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-top: 8px;
  cursor: pointer;
}

.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statsCard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.2s;
}

.statsCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.statsCard h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #666;
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.statValue.completed {
  color: #2e7d32;
}

.statValue.pending {
  color: #f57c00;
}

.statValue.failed {
  color: #c62828;
}

@media (max-width: 768px) {
  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .statsCard {
    padding: 1rem;
  }
  
  .statValue {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .statsContainer {
    grid-template-columns: 1fr;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
