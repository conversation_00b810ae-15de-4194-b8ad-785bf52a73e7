// Server-side notification functions - DO NOT IMPORT IN CLIENT COMPONENTS
// This file contains Node.js-specific code and should only be imported by API routes

import { supabase } from './supabase';

/**
 * Send OneSignal email notification
 * @param {Object} params - Email parameters
 * @param {string} params.email - Recipient email
 * @param {string} params.subject - Email subject
 * @param {string} params.message - Email message (plain text)
 * @param {string} params.htmlBody - HTML body (optional)
 * @param {Object} params.data - Additional data to include in the notification
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendOneSignalEmail = async ({ email, subject, message, htmlBody, data = {} }) => {
  try {
    // Check if we should force email sending in development
    const forceEmailInDev = process.env.FORCE_EMAIL_IN_DEV === 'true';

    // Skip actual sending in development unless forced
    if (process.env.NODE_ENV === 'development' && !forceEmailInDev) {
      console.log('Development mode: Email would be sent', {
        email, subject, message, htmlBody, data
      });
      console.log('To enable actual email sending in development, set FORCE_EMAIL_IN_DEV=true in your .env.local file');
      return { success: true, development: true };
    }

    // Try Google Cloud email services first (Gmail SMTP or Workspace SMTP)
    let googleResult;
    try {
      const { sendEmail } = await import('./google-cloud-email');
      googleResult = await sendEmail({
        to: email,
        subject,
        text: message,
        html: htmlBody || message,
        fromName: 'Ocean Soul Sparkles'
      });

      if (googleResult.success) {
        console.log(`Email sent successfully via ${googleResult.service}`);
        return { 
          success: true, 
          service: googleResult.service,
          messageId: googleResult.messageId 
        };
      }
    } catch (error) {
      console.log('Google Cloud email not available, trying OneSignal...');
    }

    // If Google Cloud fails, fall back to OneSignal (original implementation)
    console.log('Google Cloud email failed, falling back to OneSignal...');
    
    if (!process.env.ONESIGNAL_REST_API_KEY) {
      throw new Error('No email service available. Please configure Google Cloud email or OneSignal.');
    }

    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify({
        app_id: process.env.ONESIGNAL_APP_ID,
        include_email_tokens: [email],
        email_subject: subject,
        email_body: htmlBody || message,
        data: {
          ...data,
          type: 'email'
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OneSignal API error: ${errorData.errors?.[0] || 'Unknown error'}`);
    }

    const result = await response.json();
    console.log('OneSignal email sent successfully');
    
    return { 
      success: true, 
      service: 'OneSignal',
      messageId: result.id 
    };

  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

/**
 * Send OneSignal push notification
 * @param {Object} params - Push notification parameters
 * @param {Array} params.userIds - Array of user IDs to send to
 * @param {string} params.title - Notification title
 * @param {string} params.message - Notification message
 * @param {Object} params.data - Additional data to include in the notification
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendOneSignalPush = async ({ userIds, title, message, data = {} }) => {
  try {
    // Skip actual sending in development and just log
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode: Push notification would be sent', {
        userIds, title, message, data
      });
      return { success: true, development: true };
    }

    if (!process.env.ONESIGNAL_REST_API_KEY || !process.env.ONESIGNAL_APP_ID) {
      throw new Error('OneSignal configuration missing. Please check ONESIGNAL_REST_API_KEY and ONESIGNAL_APP_ID environment variables.');
    }

    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify({
        app_id: process.env.ONESIGNAL_APP_ID,
        include_external_user_ids: userIds,
        headings: { en: title },
        contents: { en: message },
        data: {
          ...data,
          type: 'push'
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OneSignal API error: ${errorData.errors?.[0] || 'Unknown error'}`);
    }

    const result = await response.json();
    console.log('OneSignal push notification sent successfully');
    
    return { 
      success: true, 
      service: 'OneSignal',
      messageId: result.id 
    };

  } catch (error) {
    console.error('Error sending push notification:', error);
    throw error;
  }
};

/**
 * Send booking notification via database record and external services
 * @param {Object} params - Notification parameters
 * @param {number} params.bookingId - Booking ID
 * @param {string} params.customerId - Customer ID
 * @param {string} params.customerEmail - Customer email (optional)
 * @param {string} params.customerName - Customer name (optional)
 * @param {string} params.customerPhone - Customer phone (used in message templates)
 * @param {string} params.status - Booking status
 * @param {string} params.startTime - Booking start time
 * @param {string} params.serviceName - Service name
 * @param {string} params.location - Booking location
 * @param {boolean} params.marketingConsent - Whether customer has consented to marketing (for future use)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendBookingNotification = async ({
  bookingId,
  customerId,
  customerEmail,
  customerName,
  status,
  startTime,
  serviceName,
  location,
  customerPhone = '', // Optional parameter, not used directly in this function
  marketingConsent = false // For future marketing integrations
}) => {
  try {
    // Format date for display
    const bookingDate = new Date(startTime);
    const formattedDate = bookingDate.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const formattedTime = bookingDate.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    // Create notification title and content based on status
    let title, message;

    switch(status) {
      case 'confirmed':
        title = 'Booking Confirmed';
        message = `Your booking for ${serviceName} has been confirmed for ${formattedDate} at ${formattedTime}. Location: ${location}.`;
        break;
      case 'pending':
        title = 'Booking Received';
        message = `We've received your booking request for ${serviceName} on ${formattedDate} at ${formattedTime}. We'll confirm shortly.`;
        break;
      case 'cancelled':
        title = 'Booking Cancelled';
        message = `Your booking for ${serviceName} on ${formattedDate} at ${formattedTime} has been cancelled.`;
        break;
      case 'completed':
        title = 'Booking Completed';
        message = `Thank you for visiting OceanSoulSparkles. Your ${serviceName} booking has been marked as completed.`;
        break;
      default:
        title = 'Booking Update';
        message = `Your booking for ${serviceName} on ${formattedDate} at ${formattedTime} has been updated.`;
    }

    // Record notification in database with duplicate prevention
    try {
      // Check for existing notification to prevent duplicates
      const { data: existingNotifications, error: checkError } = await supabase
        .from('notifications')
        .select('id')
        .eq('related_id', bookingId)
        .eq('user_id', customerId)
        .eq('notification_type', 'booking')
        .eq('title', title)
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Within last 5 minutes
        .limit(1);

      if (checkError) {
        console.warn('Error checking for duplicate notifications:', checkError);
        // Continue with insertion despite check error
      }

      // Only insert if no recent duplicate found
      if (!existingNotifications || existingNotifications.length === 0) {
        const { error: insertError } = await supabase
          .from('notifications')
          .insert([
            {
              title,
              message,
              notification_type: 'booking',
              related_id: bookingId,
              user_id: customerId
            }
          ]);

        if (insertError) {
          // Handle specific duplicate key errors
          if (insertError.code === '23505' || insertError.message?.includes('duplicate')) {
            console.log('Notification already exists, skipping duplicate insertion');
          } else {
            throw insertError;
          }
        } else {
          console.log('Booking notification recorded successfully');
        }
      } else {
        console.log('Duplicate notification prevented - recent notification already exists');
      }
    } catch (error) {
      console.error('Error recording booking notification:', error);
      // Continue with sending notification even if recording fails
    }

    // Send email notification
    if (customerEmail) {
      await sendOneSignalEmail({
        email: customerEmail,
        subject: title,
        message,
        htmlBody: `<div>
          <h2>${title}</h2>
          <p>Hello ${customerName},</p>
          <p>${message}</p>
          <p>Booking details:</p>
          <ul>
            <li>Service: ${serviceName}</li>
            <li>Date: ${formattedDate}</li>
            <li>Time: ${formattedTime}</li>
            <li>Location: ${location}</li>
            <li>Status: ${status}</li>
          </ul>
          <p>For any questions, please contact us.</p>
          <p>Thank you for choosing OceanSoulSparkles.</p>
        </div>`,
        data: {
          type: 'booking_notification',
          booking_id: bookingId,
          status
        }
      });
    }

    // Send push notification if customer has a user ID
    if (customerId) {
      await sendOneSignalPush({
        userIds: [customerId],
        title,
        message,
        data: {
          type: 'booking_notification',
          booking_id: bookingId,
          status
        }
      });
    }

    return {
      success: true,
      title,
      message,
      notificationRecorded: true,
      emailSent: !!customerEmail,
      pushSent: !!customerId
    };

  } catch (error) {
    console.error('Error sending booking notification:', error);
    throw error;
  }
};

/**
 * Send email notification - Generic function that tries multiple email services
 * @param {Object} params - Email parameters
 * @param {string} params.to - Recipient email address
 * @param {string} params.subject - Email subject
 * @param {string} params.message - Email message content
 * @param {string} params.htmlBody - HTML version of the email (optional)
 * @param {Object} params.data - Additional data for tracking (optional)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendNotificationEmail = async ({ to, subject, message, htmlBody, data = {} }) => {
  try {
    return await sendOneSignalEmail({
      email: to,
      subject,
      message,
      htmlBody,
      data
    });
  } catch (error) {
    console.error('Error sending notification email:', error);
    throw error;
  }
};

/**
 * Test SMTP connection with nodemailer
 * @param {Object} smtpConfig - SMTP configuration
 * @returns {Promise<Object>} - Test result
 */
export const testSMTPConnection = async (smtpConfig) => {
  try {
    // Dynamic import of nodemailer to avoid client-side bundle issues
    const nodemailer = (await import('nodemailer')).default;
    
    const transporter = nodemailer.createTransporter({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      auth: {
        user: smtpConfig.user,
        pass: smtpConfig.pass
      },
      connectionTimeout: 5000,
      greetingTimeout: 5000,
      socketTimeout: 5000
    });

    // Verify connection
    await transporter.verify();
    
    // Send test email if testEmail is provided
    if (smtpConfig.testEmail) {
      const info = await transporter.sendMail({
        from: smtpConfig.user,
        to: smtpConfig.testEmail,
        subject: 'SMTP Connection Test - Ocean Soul Sparkles',
        text: 'This is a test email to verify SMTP configuration.',
        html: '<p>This is a test email to verify SMTP configuration.</p><p>If you received this, your SMTP settings are working correctly!</p>'
      });
      
      return {
        success: true,
        message: 'SMTP connection verified and test email sent successfully',
        messageId: info.messageId
      };
    } else {
      return {
        success: true,
        message: 'SMTP connection verified successfully'
      };
    }
  } catch (error) {
    console.error('SMTP test error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Send booking reminder notification
 * @param {Object} params - Reminder parameters
 * @param {string} params.bookingId - Booking ID
 * @param {string} params.customerId - Customer ID
 * @param {string} params.startTime - Booking start time
 * @param {string} params.serviceName - Service name
 * @param {string} params.location - Booking location (optional)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendBookingReminderNotification = async ({
  bookingId,
  customerId,
  startTime,
  serviceName,
  location
}) => {
  try {
    // Get customer details from database
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('name, email, phone')
      .eq('id', customerId)
      .single();

    if (customerError || !customer) {
      throw new Error('Customer not found');
    }

    if (!customer.email) {
      throw new Error('Customer email not available');
    }

    const startDate = new Date(startTime);
    const formattedDate = startDate.toLocaleDateString();
    const formattedTime = startDate.toLocaleTimeString();

    const subject = `Reminder: Your ${serviceName} appointment tomorrow`;
    const message = `Hi ${customer.name},\n\nThis is a friendly reminder about your upcoming appointment:\n\nService: ${serviceName}\nDate: ${formattedDate}\nTime: ${formattedTime}${location ? `\nLocation: ${location}` : ''}\n\nWe look forward to seeing you!\n\nBest regards,\nOcean Soul Sparkles`;
    
    const htmlBody = `
      <h2>Appointment Reminder</h2>
      <p>Hi ${customer.name},</p>
      <p>This is a friendly reminder about your upcoming appointment:</p>
      <ul>
        <li><strong>Service:</strong> ${serviceName}</li>
        <li><strong>Date:</strong> ${formattedDate}</li>
        <li><strong>Time:</strong> ${formattedTime}</li>
        ${location ? `<li><strong>Location:</strong> ${location}</li>` : ''}
      </ul>
      <p>We look forward to seeing you!</p>
      <p>Best regards,<br>Ocean Soul Sparkles</p>
    `;

    return await sendOneSignalEmail({
      email: customer.email,
      subject,
      message,
      htmlBody,
      data: {
        type: 'booking_reminder',
        bookingId,
        customerId
      }
    });

  } catch (error) {
    console.error('Error sending booking reminder notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send customer notification
 * @param {Object} params - Notification parameters
 * @param {string} params.customerId - Customer ID
 * @param {string} params.type - Notification type
 * @param {string} params.subject - Email subject
 * @param {string} params.message - Email message
 * @param {string} params.htmlBody - HTML body (optional)
 * @param {Object} params.data - Additional data (optional)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendCustomerNotification = async ({
  customerId,
  type,
  subject,
  message,
  htmlBody,
  data = {}
}) => {
  try {
    // Get customer details from database
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('name, email, phone, marketing_consent')
      .eq('id', customerId)
      .single();

    if (customerError || !customer) {
      throw new Error('Customer not found');
    }

    if (!customer.email) {
      throw new Error('Customer email not available');
    }

    // Check marketing consent for promotional messages
    if (type === 'marketing' && !customer.marketing_consent) {
      return { success: false, error: 'Customer has not consented to marketing communications' };
    }

    return await sendOneSignalEmail({
      email: customer.email,
      subject,
      message,
      htmlBody,
      data: {
        type,
        customerId,
        ...data
      }
    });

  } catch (error) {
    console.error('Error sending customer notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send welcome notification for new users
 * @param {Object} params - Welcome notification parameters
 * @param {string} params.userId - User ID
 * @param {string} params.email - User email
 * @param {string} params.name - User name
 * @param {string} params.role - User role
 * @param {string} params.applicationToken - Optional secure application token
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendWelcomeNotification = async ({ userId, email, name, role, applicationToken }) => {
  try {
    console.log(`📧 Sending welcome notification to ${email} for role: ${role}`);

    // Log token status for debugging
    if (applicationToken && ['artist', 'braider'].includes(role)) {
      console.log(`🔗 Including application token in email: ${applicationToken.substring(0, 8)}...`);
    } else if (['artist', 'braider'].includes(role)) {
      console.warn(`⚠️ No application token provided for ${role} role - email will not include application link`);
    }

    // Import email templates
    const { generateWelcomeEmail } = await import('./email-templates');

    // Generate role-specific welcome email
    const emailTemplate = generateWelcomeEmail({
      name,
      email,
      role,
      applicationToken
    });

    // Send the welcome email
    const emailResult = await sendOneSignalEmail({
      email,
      subject: emailTemplate.subject,
      message: emailTemplate.textBody,
      htmlBody: emailTemplate.htmlBody,
      data: {
        type: 'welcome_email',
        user_id: userId,
        role
      }
    });

    // Track email sending status in database
    let emailTrackingResult = { success: false };
    try {
      // For artist/braider roles, update the application entry
      if (role === 'artist' || role === 'braider') {
        const { error: updateError } = await supabase
          .from('artist_braider_applications')
          .update({
            welcome_email_sent: emailResult.success,
            welcome_email_sent_at: emailResult.success ? new Date().toISOString() : null
          })
          .eq('user_id', userId)
          .eq('application_type', role);

        if (updateError) {
          console.error('Error updating application email status:', updateError);
        } else {
          emailTrackingResult.success = true;
          console.log(`Updated application email status for ${role} user ${userId}`);
        }
      } else {
        // For non-artist/braider roles, we can track in user activity log
        try {
          const { error: activityError } = await supabase
            .from('user_activity_log')
            .insert([
              {
                user_id: userId,
                activity_type: 'welcome_email_sent',
                activity_description: `Welcome email sent for role: ${role}. Status: ${emailResult.success ? 'success' : 'failed'}`,
                ip_address: null,
                user_agent: 'system'
              }
            ]);

          if (!activityError) {
            emailTrackingResult.success = true;
            console.log(`Logged welcome email activity for ${role} user ${userId}`);
          }
        } catch (activityLogError) {
          console.warn('Could not log welcome email activity:', activityLogError);
        }
      }

    } catch (trackingError) {
      console.error('Error tracking email status:', trackingError);
      // Continue anyway - email sending is more important than tracking
    }

    return {
      success: emailResult.success,
      emailSent: emailResult.success,
      emailService: emailResult.service,
      messageId: emailResult.messageId,
      emailTracked: emailTrackingResult.success,
      role,
      applicationFormRequired: role === 'artist' || role === 'braider'
    };

  } catch (error) {
    console.error('Error sending welcome notification:', error);
    return {
      success: false,
      error: error.message,
      emailSent: false,
      emailTracked: false
    };
  }
};

/**
 * Send order notification
 * @param {Object} params - Order parameters
 * @param {string} params.orderId - Order ID
 * @param {string} params.customerId - Customer ID
 * @param {string} params.status - Order status
 * @param {number} params.totalAmount - Total amount
 * @param {Array} params.items - Order items
 * @param {Object} params.additionalData - Additional order data (optional)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendOrderNotification = async ({
  orderId,
  customerId,
  status,
  totalAmount,
  items = [],
  additionalData = {}
}) => {
  try {
    // Get customer details from database
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('name, email, phone')
      .eq('id', customerId)
      .single();

    if (customerError || !customer) {
      throw new Error('Customer not found');
    }

    if (!customer.email) {
      throw new Error('Customer email not available');
    }

    let subject, message, htmlBody;

    switch (status) {
      case 'confirmed':
        subject = `Order Confirmation - #${orderId}`;
        message = `Hi ${customer.name},\n\nThank you for your order! Your order #${orderId} has been confirmed.\n\nTotal: $${totalAmount}\n\nWe'll process your order and send you updates.\n\nBest regards,\nOcean Soul Sparkles`;
        htmlBody = `
          <h2>Order Confirmation</h2>
          <p>Hi ${customer.name},</p>
          <p>Thank you for your order! Your order #${orderId} has been confirmed.</p>
          <p><strong>Total:</strong> $${totalAmount}</p>
          ${items.length > 0 ? `
            <h3>Order Items:</h3>
            <ul>
              ${items.map(item => `<li>${item.name} - $${item.price}</li>`).join('')}
            </ul>
          ` : ''}
          <p>We'll process your order and send you updates.</p>
          <p>Best regards,<br>Ocean Soul Sparkles</p>
        `;
        break;

      case 'processing':
        subject = `Order Processing - #${orderId}`;
        message = `Hi ${customer.name},\n\nYour order #${orderId} is now being processed.\n\nWe'll notify you when it's ready for pickup or shipped.\n\nBest regards,\nOcean Soul Sparkles`;
        htmlBody = `
          <h2>Order Processing</h2>
          <p>Hi ${customer.name},</p>
          <p>Your order #${orderId} is now being processed.</p>
          <p>We'll notify you when it's ready for pickup or shipped.</p>
          <p>Best regards,<br>Ocean Soul Sparkles</p>
        `;
        break;

      case 'ready':
        subject = `Order Ready - #${orderId}`;
        message = `Hi ${customer.name},\n\nGreat news! Your order #${orderId} is ready for pickup.\n\nPlease visit us at your convenience to collect your order.\n\nBest regards,\nOcean Soul Sparkles`;
        htmlBody = `
          <h2>Order Ready</h2>
          <p>Hi ${customer.name},</p>
          <p>Great news! Your order #${orderId} is ready for pickup.</p>
          <p>Please visit us at your convenience to collect your order.</p>
          <p>Best regards,<br>Ocean Soul Sparkles</p>
        `;
        break;

      case 'completed':
        subject = `Order Completed - #${orderId}`;
        message = `Hi ${customer.name},\n\nYour order #${orderId} has been completed.\n\nThank you for choosing Ocean Soul Sparkles!\n\nBest regards,\nOcean Soul Sparkles`;
        htmlBody = `
          <h2>Order Completed</h2>
          <p>Hi ${customer.name},</p>
          <p>Your order #${orderId} has been completed.</p>
          <p>Thank you for choosing Ocean Soul Sparkles!</p>
          <p>Best regards,<br>Ocean Soul Sparkles</p>
        `;
        break;

      default:
        subject = `Order Update - #${orderId}`;
        message = `Hi ${customer.name},\n\nThere's an update on your order #${orderId}.\n\nStatus: ${status}\n\nBest regards,\nOcean Soul Sparkles`;
        htmlBody = `
          <h2>Order Update</h2>
          <p>Hi ${customer.name},</p>
          <p>There's an update on your order #${orderId}.</p>
          <p><strong>Status:</strong> ${status}</p>
          <p>Best regards,<br>Ocean Soul Sparkles</p>
        `;
    }

    return await sendOneSignalEmail({
      email: customer.email,
      subject,
      message,
      htmlBody,
      data: {
        type: 'order_notification',
        orderId,
        customerId,
        status,
        ...additionalData
      }
    });

  } catch (error) {
    console.error('Error sending order notification:', error);
    return { success: false, error: error.message };
  }
};
