import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * API endpoint for admin payment management
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Payments API endpoint called: ${req.method} ${req.url}`);

  // Log headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));
  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }
  if (req.headers.cookie) {
    console.log(`[${requestId}] Cookie header present`);
  }
  if (req.headers['x-auth-token'] || req.headers['X-Auth-Token']) {
    console.log(`[${requestId}] Auth token header present`);
  }

  // Authenticate request using our robust auth module
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);

  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch payments
    if (req.method === 'GET') {
      console.log(`[${requestId}] Processing GET request for payments`);

      // Get query parameters
      const {
        search,
        sort_by = 'created_at',
        sort_order = 'desc',
        limit = 10,
        offset = 0,
        status,
        payment_method,
        date_from,
        date_to,
      } = req.query;

      // Start building the query using Supabase directly
      let query = supabaseAdmin
        .from('payments')
        .select(`
          id,
          order_id,
          booking_id,
          amount,
          currency,
          payment_method,
          payment_status,
          transaction_id,
          payment_date,
          notes,
          created_at,
          updated_at,
          bookings:booking_id (id, customer_id)
        `);

      // Apply search filter
      if (search) {
        const searchTerm = `%${search}%`;
        query = query.or(
          `id.ilike.${searchTerm},transaction_id.ilike.${searchTerm}`
        );
      }

      // Apply status filter
      if (status) {
        query = query.eq('payment_status', status);
      }

      // Apply payment method filter
      if (payment_method) {
        query = query.eq('payment_method', payment_method);
      }

      // Apply date filters
      if (date_from) {
        query = query.gte('created_at', `${date_from}T00:00:00`);
      }

      if (date_to) {
        query = query.lte('created_at', `${date_to}T23:59:59`);
      }      // Get total count
      let count;
      try {
        const { count: totalCount, error: countError } = await supabaseAdmin
          .from('payments')
          .select('id', { count: 'exact', head: true });

        if (countError) {
          throw countError;
        }

        count = totalCount;
      } catch (countError) {
        console.error(`[${requestId}] Count query error:`, countError);
        throw countError;
      }

      // Apply sorting and pagination
      query = query
        .order(sort_by || 'created_at', {
          ascending: sort_order === 'asc',
        })
        .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

      // Execute the query
      let data;
      try {
        const { data: queryData, error: queryError } = await query;

        if (queryError) {
          throw queryError;
        }

        data = queryData;
      } catch (queryError) {
        console.error(`[${requestId}] Query execution error:`, queryError);
        throw queryError;
      }

      // Process the data to include customer information
      // Since we don't have direct customer_id in payments table,
      // we'll need to get customer info through bookings if available
      const payments = await Promise.all(data.map(async (payment) => {
        let customerName = 'Unknown';
        let customerEmail = '';

        // If payment has a booking_id and the booking has a customer_id
        if (payment.booking_id && payment.bookings?.customer_id) {
          // Get customer information
          const { data: customerData, error: customerError } = await supabaseAdmin
            .from('customers')
            .select('name, email')
            .eq('id', payment.bookings.customer_id)
            .single();

          if (!customerError && customerData) {
            customerName = customerData.name;
            customerEmail = customerData.email;
          }
        }

        return {
          ...payment,
          customer_name: customerName,
          customer_email: customerEmail,
          status: payment.payment_status // Normalize status field name
        };
      }));

      // Set appropriate cache headers for payments data
      setCacheHeaders(res, 'payments', 'GET', true, req.query);

      return res.status(200).json({ payments, total: count });
    }

    // POST - Create a new payment (record an offline payment)
    if (req.method === 'POST') {
      const {
        booking_id,
        order_id,
        amount,
        currency = 'AUD',
        payment_method,
        payment_status = 'completed',
        transaction_id = null,
        payment_date = new Date().toISOString(),
        notes = '',
      } = req.body;

      // Validate required fields
      if (!amount || !payment_method) {
        return res.status(400).json({
          error: 'Missing required fields: amount, payment_method',
        });
      }

      // Create the payment record
      const { data, error } = await supabaseAdmin
        .from('payments')
        .insert([
          {
            order_id,
            booking_id,
            amount,
            currency,
            payment_method,
            payment_status,
            transaction_id,
            payment_date,
            notes,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
        ])
        .select();

      if (error) {
        throw error;
      }

      // Update related order or booking if provided
      if (order_id) {
        await supabaseAdmin
          .from('orders')
          .update({ payment_status })
          .eq('id', order_id);
      }

      if (booking_id) {
        await supabaseAdmin
          .from('bookings')
          .update({ payment_status })
          .eq('id', booking_id);
      }

      return res.status(201).json({ payment: data[0] });
    }

    // PUT - Update a payment record
    if (req.method === 'PUT') {
      const { id, ...updateData } = req.body;

      // Validate required fields
      if (!id) {
        return res.status(400).json({ error: 'Missing payment ID' });
      }

      // Add updated_at timestamp
      updateData.updated_at = new Date().toISOString();

      // Update the payment
      const { data, error } = await supabaseAdmin
        .from('payments')
        .update(updateData)
        .eq('id', id)
        .select();

      if (error) {
        throw error;
      }

      return res.status(200).json({ payment: data[0] });
    }
  } catch (error) {
    console.error(`[${requestId}] Payment API Error:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process payment request';

    if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out while processing';
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested payment not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    }

    return res.status(statusCode).json({
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
