import { useEffect, useState } from 'react';

// Check if we're in development mode or a local environment
const isLocalEnvironment = () => {
  if (typeof window === 'undefined') return process.env.NODE_ENV === 'development';
  
  const hostname = window.location.hostname;
  return process.env.NODE_ENV === 'development' || 
         hostname === 'localhost' || 
         hostname === '127.0.0.1';
};

// Mock implementation for development
const oneSignalMock = {
  getNotificationPermission: () => Promise.resolve('default'),
  isPushNotificationsEnabled: () => Promise.resolve(false),
  showNativePrompt: () => Promise.resolve(),
  showHttpPrompt: () => Promise.resolve(),
  showCategorySlidedown: () => Promise.resolve(),
  getUserId: () => Promise.resolve('mock-user-id'),
  setExternalUserId: () => Promise.resolve(),
  removeExternalUserId: () => Promise.resolve(),
  setEmail: () => Promise.resolve(),
  sendTag: () => Promise.resolve(),
  getTags: () => Promise.resolve({ role: 'customer', environment: 'development' }),
  on: () => {},
  once: () => {},
  off: () => {},
};

// Get OneSignal instance
export const getOneSignalInstance = () => {
  if (isLocalEnvironment()) {
    // Return a mock instance in development
    return oneSignalMock;
  }

  if (typeof window !== 'undefined' && window.OneSignal) {
    return window.OneSignal;
  }
  return null;
};

// Check if OneSignal is initialized
export const isOneSignalInitialized = () => {
  if (isLocalEnvironment()) {
    // Return true so we use the mock in development
    return true;
  }

  return typeof window !== 'undefined' && !!window.OneSignal;
};

// Wait for OneSignal to initialize
export const waitForOneSignal = () => {
  if (isLocalEnvironment()) {
    // In development, immediately resolve with the mock
    return Promise.resolve(oneSignalMock);
  }

  return new Promise((resolve) => {
    if (isOneSignalInitialized()) {
      resolve(getOneSignalInstance());
      return;
    }

    const checkInterval = setInterval(() => {
      if (isOneSignalInitialized()) {
        clearInterval(checkInterval);
        resolve(getOneSignalInstance());
      }
    }, 200);

    // Add a timeout to prevent hanging indefinitely
    setTimeout(() => {
      clearInterval(checkInterval);
      resolve(null);
    }, 10000); // 10 second timeout
  });
};

// Get notification permission status
export const getNotificationPermissionStatus = async () => {
  if (isLocalEnvironment()) {
    // In development, return a mock permission status
    return 'default';
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      return await OneSignal.getNotificationPermission();
    }
    return null;
  } catch (error) {
    console.error('Error getting notification permission status:', error);
    return null;
  }
};

// Show native notification prompt
export const showNativePrompt = async () => {
  if (isLocalEnvironment()) {
    // In development, log a message and return success
    console.log('OneSignal showNativePrompt called in development mode (mocked)');
    return true;
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      return await OneSignal.showNativePrompt();
    }
    return false;
  } catch (error) {
    console.error('Error showing native prompt:', error);
    return false;
  }
};

// Show category slidedown prompt
export const showCategorySlidedown = async () => {
  if (isLocalEnvironment()) {
    // In development, log a message and return success
    console.log('OneSignal showCategorySlidedown called in development mode (mocked)');
    return true;
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      return await OneSignal.showCategorySlidedown();
    }
    return false;
  } catch (error) {
    console.error('Error showing category slidedown:', error);
    return false;
  }
};

// React hook for OneSignal
export const useOneSignal = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // In development or local environment, use mock
    if (isLocalEnvironment()) {
      console.log('Using OneSignal mock implementation in development/local environment');
      setIsInitialized(true);
      return;
    }

    const checkOneSignal = async () => {
      const oneSignal = await waitForOneSignal();
      if (oneSignal) {
        setIsInitialized(true);
      }
    };

    checkOneSignal();
  }, []);

  return { isInitialized };
};

// Set external user ID for OneSignal
export const setOneSignalExternalUserId = async (userId) => {
  if (isLocalEnvironment()) {
    // In development, log a message and return success
    console.log(`OneSignal setExternalUserId called in development mode with ID: ${userId} (mocked)`);
    return { success: true };
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      await OneSignal.setExternalUserId(userId);
      return { success: true };
    }
    return { success: false, error: 'OneSignal not initialized' };
  } catch (error) {
    console.error('Error setting OneSignal external user ID:', error);
    return { success: false, error };
  }
};

// Set OneSignal email
export const setOneSignalEmail = async (email) => {
  if (isLocalEnvironment()) {
    // In development, log a message and return success
    console.log(`OneSignal setEmail called in development mode with email: ${email} (mocked)`);
    return { success: true };
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      await OneSignal.setEmail(email);
      return { success: true };
    }
    return { success: false, error: 'OneSignal not initialized' };
  } catch (error) {
    console.error('Error setting OneSignal email:', error);
    return { success: false, error };
  }
};

// Add OneSignal tag
export const addOneSignalTag = async (key, value) => {
  if (isLocalEnvironment()) {
    // In development, log a message and return success
    console.log(`OneSignal addTag called in development mode with key: ${key}, value: ${value} (mocked)`);
    return { success: true };
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      await OneSignal.sendTag(key, value);
      return { success: true };
    }
    return { success: false, error: 'OneSignal not initialized' };
  } catch (error) {
    console.error('Error adding OneSignal tag:', error);
    return { success: false, error };
  }
};

// Get OneSignal tags
export const getOneSignalTags = async () => {
  if (isLocalEnvironment()) {
    // In development, return mock tags
    console.log('OneSignal getTags called in development mode (mocked)');
    return {
      success: true,
      tags: {
        role: 'customer',
        environment: 'development'
      }
    };
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      const tags = await OneSignal.getTags();
      return { success: true, tags };
    }
    return { success: false, error: 'OneSignal not initialized' };
  } catch (error) {
    console.error('Error getting OneSignal tags:', error);
    return { success: false, error };
  }
};

// Check if push notifications are enabled
export const isPushNotificationsEnabled = async () => {
  if (isLocalEnvironment()) {
    // In development, return false to simulate not enabled
    console.log('OneSignal isPushNotificationsEnabled called in development mode (mocked)');
    return false;
  }

  try {
    const OneSignal = await waitForOneSignal();
    if (OneSignal) {
      return await OneSignal.isPushNotificationsEnabled();
    }
    return false;
  } catch (error) {
    console.error('Error checking if push notifications are enabled:', error);
    return false;
  }
};
