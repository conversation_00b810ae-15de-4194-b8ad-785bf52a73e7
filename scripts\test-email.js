/**
 * Email Testing Script
 * Run this script to test email functionality in development
 * Usage: node scripts/test-email.js
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function testEmailConfiguration() {
  console.log('🧪 Testing Email Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log('NODE_ENV:', process.env.NODE_ENV || 'development');
  console.log('FORCE_EMAIL_IN_DEV:', process.env.FORCE_EMAIL_IN_DEV);
  console.log('GMAIL_SMTP_USER:', process.env.GMAIL_SMTP_USER ? '✅ Set' : '❌ Missing');
  console.log('GMAIL_SMTP_APP_PASSWORD:', process.env.GMAIL_SMTP_APP_PASSWORD ? '✅ Set' : '❌ Missing');
  console.log('GMAIL_FROM_EMAIL:', process.env.GMAIL_FROM_EMAIL || 'Not set');
  console.log('GMAIL_FROM_NAME:', process.env.GMAIL_FROM_NAME || 'Not set');
  console.log('');

  // Test Gmail SMTP configuration
  console.log('📧 Testing Gmail SMTP Configuration...');
  try {
    const { sendEmail } = await import('../lib/google-cloud-email.js');
    
    const testResult = await sendEmail({
      to: process.env.GMAIL_SMTP_USER, // Send to yourself for testing
      subject: 'Test Email from Ocean Soul Sparkles',
      text: 'This is a test email to verify Gmail SMTP configuration is working correctly.',
      html: `
        <h2>Test Email</h2>
        <p>This is a test email to verify Gmail SMTP configuration is working correctly.</p>
        <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
        <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
      `
    });

    if (testResult.success) {
      console.log('✅ Gmail SMTP test successful!');
      console.log('Message ID:', testResult.messageId);
      console.log('Service:', testResult.service);
    } else {
      console.log('❌ Gmail SMTP test failed:');
      console.log('Error:', testResult.error);
    }
  } catch (error) {
    console.log('❌ Gmail SMTP test error:');
    console.log(error.message);
  }
  console.log('');

  // Test welcome email generation
  console.log('📝 Testing Welcome Email Generation...');
  try {
    const { generateWelcomeEmail } = await import('../lib/email-templates.js');
    
    const emailTemplate = generateWelcomeEmail({
      name: 'Test Artist',
      email: '<EMAIL>',
      role: 'artist',
      applicationToken: 'test-token-123'
    });

    console.log('✅ Welcome email template generated successfully');
    console.log('Subject:', emailTemplate.subject);
    console.log('Contains application link:', emailTemplate.html.includes('/apply/artist?token='));
    console.log('Contains staff portal link:', emailTemplate.html.includes('/staff-login'));
  } catch (error) {
    console.log('❌ Welcome email generation failed:');
    console.log(error.message);
  }
  console.log('');

  // Test notification system
  console.log('🔔 Testing Notification System...');
  try {
    const { sendWelcomeNotification } = await import('../lib/notifications-server.js');
    
    const notificationResult = await sendWelcomeNotification({
      userId: 'test-user-id',
      email: process.env.GMAIL_SMTP_USER, // Send to yourself
      name: 'Test Artist',
      role: 'artist',
      applicationToken: 'test-token-123'
    });

    if (notificationResult.success) {
      console.log('✅ Welcome notification test successful!');
      console.log('Email sent:', notificationResult.emailSent);
      console.log('Service:', notificationResult.emailService);
      console.log('Message ID:', notificationResult.messageId);
    } else {
      console.log('❌ Welcome notification test failed:');
      console.log('Error:', notificationResult.error);
    }
  } catch (error) {
    console.log('❌ Welcome notification test error:');
    console.log(error.message);
  }
  console.log('');

  console.log('🏁 Email testing complete!');
  console.log('');
  console.log('📋 Next Steps:');
  console.log('1. Check your email inbox for test messages');
  console.log('2. If emails are not received, check Gmail app password settings');
  console.log('3. Verify Gmail SMTP settings in .env.local');
  console.log('4. Try creating a new Artist user through the admin interface');
}

// Run the test
testEmailConfiguration().catch(console.error);
