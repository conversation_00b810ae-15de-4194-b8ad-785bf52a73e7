# Event-Based QR Code System Documentation

## Overview

The Event-Based QR Code System enables Ocean Soul Sparkles to generate unique QR codes for specific events and festivals, allowing customers to scan codes and instantly book services through a mobile-optimized interface. The system includes comprehensive revenue tracking, analytics, and automatic expiration management.

## Features

### 🎯 Core Functionality
- **Event-Specific QR Codes**: Generate unique QR codes tied to specific events
- **Mobile-First Booking**: Optimized mobile interface for quick bookings
- **Automatic Expiration**: QR codes automatically expire after events end
- **Real-Time Availability**: Live availability checking and booking conflicts prevention
- **Square Payment Integration**: Secure payment processing through existing Square setup

### 📊 Analytics & Revenue Tracking
- **Comprehensive Analytics**: Track scans, conversions, and revenue per QR code
- **Artist Performance Metrics**: Individual artist earnings and booking statistics
- **Event Revenue Reports**: Detailed financial reporting for each event
- **Customer Acquisition Tracking**: Monitor customer sources and conversion rates
- **Usage Analytics**: Device types, peak usage times, and scan patterns

### 🛡️ Security & Validation
- **QR Code Validation**: Automatic validation of QR codes and event status
- **Usage Limits**: Optional scan limits per QR code
- **Event Status Checking**: Automatic handling of upcoming, active, and expired events
- **Secure Payment Processing**: PCI-compliant payment handling

## System Architecture

### Database Schema

#### Events Table
```sql
CREATE TABLE public.events (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  description TEXT,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  status TEXT DEFAULT 'active',
  max_capacity INTEGER,
  current_bookings INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Event QR Codes Table
```sql
CREATE TABLE public.event_qr_codes (
  id UUID PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  event_id UUID REFERENCES public.events(id),
  event_name TEXT NOT NULL,
  event_location TEXT NOT NULL,
  event_start_date TIMESTAMPTZ NOT NULL,
  event_end_date TIMESTAMPTZ NOT NULL,
  assigned_artists UUID[],
  available_services UUID[],
  is_active BOOLEAN DEFAULT TRUE,
  usage_count INTEGER DEFAULT 0,
  max_usage INTEGER,
  revenue_tracking JSONB DEFAULT '{}',
  analytics_data JSONB DEFAULT '{}'
);
```

#### Event Bookings Table
```sql
CREATE TABLE public.event_bookings (
  id UUID PRIMARY KEY,
  booking_id UUID REFERENCES public.bookings(id),
  event_id UUID REFERENCES public.events(id),
  qr_code_id UUID REFERENCES public.event_qr_codes(id),
  artist_id UUID REFERENCES auth.users(id),
  service_id UUID REFERENCES public.services(id),
  customer_id UUID REFERENCES public.customers(id),
  revenue_amount DECIMAL(10,2) NOT NULL,
  commission_rate DECIMAL(5,2) DEFAULT 0.00,
  artist_earnings DECIMAL(10,2),
  platform_earnings DECIMAL(10,2),
  payment_method TEXT,
  payment_status TEXT DEFAULT 'pending'
);
```

### API Endpoints

#### Admin Event Management
- `GET /api/admin/events` - List all events
- `POST /api/admin/events` - Create new event
- `GET /api/admin/events/[eventId]` - Get event details
- `PUT /api/admin/events/[eventId]` - Update event
- `DELETE /api/admin/events/[eventId]` - Delete event

#### QR Code Management
- `GET /api/admin/events/[eventId]/qr-codes` - List QR codes for event
- `POST /api/admin/events/[eventId]/qr-codes` - Generate new QR code
- `GET /api/admin/qr-codes/[qrCodeId]/analytics` - Get QR code analytics

#### Mobile Booking
- `GET /qr/[code]` - QR code landing page
- `GET /api/mobile/availability` - Check real-time availability
- `POST /api/mobile/create-booking` - Create booking from QR scan
- `POST /api/mobile/process-payment` - Process payment

## Usage Guide

### 1. Setting Up Events

#### Creating an Event
1. Navigate to `/admin/events` in the admin panel
2. Click "Create Event"
3. Fill in event details:
   - Event name and location
   - Start and end dates/times
   - Description (optional)
   - Maximum capacity (optional)
4. Save the event

#### Generating QR Codes
1. Open the event details page
2. Click "Generate QR Code"
3. Configure options:
   - Assigned artists (optional)
   - Available services (optional)
   - Usage limits (optional)
4. Generate and download the QR code

### 2. Customer Flow

#### QR Code Scanning
1. Customer scans QR code with phone camera
2. Redirected to mobile-optimized landing page
3. Event information and available services displayed
4. Click "Book Your Appointment Now"

#### Mobile Booking Process
1. Service selection with pricing
2. Time slot selection with real-time availability
3. Customer information collection
4. Payment processing through Square
5. Booking confirmation and digital receipt

### 3. Analytics & Reporting

#### Event Analytics
- Total QR code scans and conversions
- Revenue breakdown by artist and service
- Customer acquisition metrics
- Peak usage times and patterns

#### QR Code Performance
- Individual QR code statistics
- Conversion rates and revenue tracking
- Device type and referrer analysis
- Usage patterns and trends

#### Artist Performance
- Bookings and revenue per artist
- Commission calculations
- Event-specific performance metrics

## Technical Implementation

### QR Code Generation
```javascript
import { generateEventQRCode } from '@/lib/qr-code-manager';

const qrResult = await generateEventQRCode({
  eventId: 'event-uuid',
  eventName: 'Summer Festival 2024',
  eventLocation: 'Bondi Beach, Sydney',
  eventStartDate: '2024-12-20T09:00:00+10:00',
  eventEndDate: '2024-12-22T18:00:00+10:00',
  assignedArtists: ['artist-uuid-1', 'artist-uuid-2'],
  availableServices: ['service-uuid-1', 'service-uuid-2']
}, {
  size: 256,
  maxUsage: 100
});
```

### QR Code Validation
```javascript
import { validateQRCode } from '@/lib/qr-code-manager';

const validation = await validateQRCode('OSS-EVENT-12345678-87654321');

if (validation.isValid) {
  // Proceed with booking flow
  const eventData = validation.qrData;
} else {
  // Handle validation error
  console.log(validation.error, validation.code);
}
```

### Analytics Tracking
```javascript
import { trackQRCodeScan, getQRCodeAnalytics } from '@/lib/qr-code-manager';

// Track scan
await trackQRCodeScan(qrCodeId, {
  userAgent: navigator.userAgent,
  deviceType: 'mobile',
  referrer: document.referrer
});

// Get analytics
const analytics = await getQRCodeAnalytics(qrCodeId);
```

## Configuration

### Environment Variables
```env
# Required for QR code URL generation
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au

# Square payment integration
NEXT_PUBLIC_SQUARE_APPLICATION_ID=your_square_app_id
SQUARE_ACCESS_TOKEN=your_square_access_token

# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key
```

### Admin Panel Access
- Ensure `ENABLE_AUTH_BYPASS=true` in `.env.local` for development
- Admin users need `admin` or `staff` role in the database
- Access admin panel at `/admin/events`

## Testing

### Running Tests
```bash
# Install dependencies
npm install qrcode qr-scanner uuid

# Run test suite
node scripts/test-qr-system.js
```

### Manual Testing
1. Create a test event in admin panel
2. Generate QR code for the event
3. Scan QR code with mobile device
4. Complete booking flow
5. Verify analytics and revenue tracking

## Deployment

### Database Migration
1. Run the migration script: `db/migrations/event_qr_system.sql`
2. Verify all tables and functions are created
3. Check RLS policies are properly configured

### Production Checklist
- [ ] Database migration completed
- [ ] Environment variables configured
- [ ] Square payment integration tested
- [ ] QR code generation working
- [ ] Mobile booking flow tested
- [ ] Analytics tracking verified
- [ ] Admin panel accessible

## Troubleshooting

### Common Issues

#### QR Code Not Working
- Check event dates (not expired or too far in future)
- Verify QR code is active in database
- Ensure proper URL configuration

#### Payment Issues
- Verify Square credentials in environment
- Check payment form integration
- Test with Square sandbox first

#### Analytics Not Tracking
- Verify database triggers are working
- Check QR code scan tracking function
- Ensure proper error handling

### Support
For technical support or questions about the QR code system, refer to the main project documentation or contact the development team.

## Future Enhancements

### Planned Features
- Bulk QR code generation for large events
- Advanced analytics dashboard with charts
- Email notifications for event organizers
- Integration with social media platforms
- Multi-language support for international events
- Offline QR code validation capability

### Performance Optimizations
- QR code image caching
- Database query optimization
- Mobile interface performance improvements
- Real-time analytics updates via WebSocket
