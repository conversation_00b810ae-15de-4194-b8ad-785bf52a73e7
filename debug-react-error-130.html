<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Error #130 Debugger</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            padding: 20px;
            background-color: #1a1a1a;
            color: #00ff00;
            line-height: 1.4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #000;
            padding: 20px;
            border: 2px solid #00ff00;
            border-radius: 8px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 4px;
            background: #111;
        }
        .error {
            color: #ff4444;
            font-weight: bold;
        }
        .success {
            color: #44ff44;
            font-weight: bold;
        }
        .warning {
            color: #ffaa00;
            font-weight: bold;
        }
        .info {
            color: #4488ff;
        }
        .button {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background: #00ff00;
            color: #000;
        }
        .log {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            font-size: 12px;
        }
        .json {
            background: #001122;
            border: 1px solid #0066cc;
            padding: 10px;
            margin: 5px 0;
            white-space: pre-wrap;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
        }
        h1, h2, h3 {
            color: #00ffff;
            text-shadow: 0 0 5px #00ffff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 React Error #130 Systematic Debugger</h1>
        <p class="info">This tool will help identify the exact cause of React Error #130 in the ServiceForm component.</p>
        
        <div class="section">
            <h2>🎯 Step 1: Initialize Debugging</h2>
            <button class="button" onclick="initializeDebugging()">Start Debugging Session</button>
            <button class="button" onclick="openAdminWindow()">Open Admin Inventory</button>
            <button class="button" onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="section">
            <h2>🔍 Step 2: Capture Service Data</h2>
            <button class="button" onclick="captureServiceData()">Analyze Service Data Structure</button>
            <button class="button" onclick="testEditButtonClick()">Test Edit Button Click</button>
            <button class="button" onclick="monitorReactErrors()">Monitor React Errors</button>
        </div>
        
        <div class="section">
            <h2>📊 Step 3: Data Analysis</h2>
            <div id="serviceDataAnalysis" class="json">Service data analysis will appear here...</div>
        </div>
        
        <div class="section">
            <h2>🚨 Error Capture</h2>
            <div id="errorCapture" class="log">Error details will appear here...</div>
        </div>
        
        <div class="section">
            <h2>📝 Debug Log</h2>
            <div id="debugLog" class="log">Debug log initialized...\n</div>
        </div>
    </div>

    <script>
        let adminWindow = null;
        let originalConsoleError = null;
        let capturedErrors = [];
        let serviceData = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLog');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLogs() {
            document.getElementById('debugLog').innerHTML = 'Debug log cleared...\n';
            document.getElementById('errorCapture').innerHTML = 'Error capture cleared...\n';
            document.getElementById('serviceDataAnalysis').innerHTML = 'Service data analysis cleared...\n';
            capturedErrors = [];
        }
        
        function initializeDebugging() {
            log('🚀 Initializing React Error #130 debugging session...', 'info');
            log('📋 Debugging checklist:', 'info');
            log('  1. ✅ Development mode active (unminified errors)', 'success');
            log('  2. 🔄 Error monitoring ready', 'info');
            log('  3. 📊 Data analysis tools loaded', 'info');
            log('  4. 🎯 Ready to capture service edit errors', 'info');
        }
        
        function openAdminWindow() {
            log('🌐 Opening admin inventory window...', 'info');
            adminWindow = window.open('http://localhost:3001/admin/inventory?tab=services', 'adminDebug', 'width=1200,height=800');
            
            if (adminWindow) {
                log('✅ Admin window opened successfully', 'success');
                
                // Wait for page to load
                setTimeout(() => {
                    try {
                        if (adminWindow.document && adminWindow.document.readyState === 'complete') {
                            log('✅ Admin page loaded and ready for testing', 'success');
                            setupErrorMonitoring();
                        } else {
                            log('⏳ Admin page still loading...', 'warning');
                            setTimeout(() => setupErrorMonitoring(), 2000);
                        }
                    } catch (e) {
                        log('⚠️ Cannot access admin window directly (cross-origin)', 'warning');
                        log('💡 Please manually click Edit button and check browser console', 'info');
                    }
                }, 3000);
            } else {
                log('❌ Failed to open admin window (popup blocked?)', 'error');
            }
        }
        
        function setupErrorMonitoring() {
            if (!adminWindow) {
                log('❌ Admin window not available for error monitoring', 'error');
                return;
            }
            
            try {
                // Override console.error in admin window
                originalConsoleError = adminWindow.console.error;
                adminWindow.console.error = function(...args) {
                    const message = args.join(' ');
                    capturedErrors.push({
                        timestamp: new Date().toISOString(),
                        message: message,
                        args: args
                    });
                    
                    // Check for React Error #130
                    if (message.includes('Element type is invalid') || 
                        message.includes('Objects are not valid as a React child') ||
                        message.includes('React Error #130') ||
                        message.includes('Minified React error #130')) {
                        
                        log('🚨 REACT ERROR #130 DETECTED!', 'error');
                        log(`Error: ${message}`, 'error');
                        
                        // Try to get stack trace
                        if (args[0] && args[0].stack) {
                            log(`Stack trace: ${args[0].stack}`, 'error');
                        }
                        
                        document.getElementById('errorCapture').innerHTML = 
                            `<span class="error">REACT ERROR #130 CAPTURED:</span>\n` +
                            `Message: ${message}\n` +
                            `Timestamp: ${new Date().toISOString()}\n` +
                            `Args: ${JSON.stringify(args, null, 2)}`;
                    }
                    
                    // Call original console.error
                    originalConsoleError.apply(adminWindow.console, args);
                };
                
                log('✅ Error monitoring setup complete', 'success');
            } catch (e) {
                log(`❌ Error setting up monitoring: ${e.message}`, 'error');
            }
        }
        
        async function captureServiceData() {
            log('📊 Analyzing service data structure...', 'info');
            
            try {
                // Fetch service data from admin API
                const response = await fetch('http://localhost:3001/api/admin/services?sort_by=name&sort_order=asc');
                const services = await response.json();
                
                if (services && services.length > 0) {
                    serviceData = services[0]; // Get first service for analysis
                    
                    log(`✅ Captured service data (${services.length} services total)`, 'success');
                    
                    // Analyze data structure
                    const analysis = analyzeServiceStructure(serviceData);
                    document.getElementById('serviceDataAnalysis').innerHTML = analysis;
                    
                    log('🔍 Service data structure analysis complete', 'success');
                } else {
                    log('❌ No service data found', 'error');
                }
            } catch (error) {
                log(`❌ Error fetching service data: ${error.message}`, 'error');
            }
        }
        
        function analyzeServiceStructure(service) {
            let analysis = `<span class="success">SERVICE DATA STRUCTURE ANALYSIS:</span>\n\n`;
            analysis += `<span class="info">Service ID:</span> ${service.id} (${typeof service.id})\n`;
            analysis += `<span class="info">Service Name:</span> ${service.name} (${typeof service.name})\n\n`;
            
            analysis += `<span class="warning">POTENTIAL PROBLEM AREAS:</span>\n`;
            
            // Check for objects that might cause React Error #130
            for (const [key, value] of Object.entries(service)) {
                const type = typeof value;
                const isArray = Array.isArray(value);
                const isObject = type === 'object' && value !== null && !isArray;
                const isDate = value instanceof Date;
                
                if (isObject && !isDate) {
                    analysis += `<span class="error">❌ ${key}:</span> OBJECT (${JSON.stringify(value).substring(0, 100)}...)\n`;
                } else if (isArray && value.length > 0 && typeof value[0] === 'object') {
                    analysis += `<span class="error">❌ ${key}:</span> ARRAY OF OBJECTS (${value.length} items)\n`;
                    analysis += `   First item: ${JSON.stringify(value[0]).substring(0, 100)}...\n`;
                } else if (isDate) {
                    analysis += `<span class="warning">⚠️ ${key}:</span> DATE OBJECT (${value.toISOString()})\n`;
                } else {
                    analysis += `<span class="success">✅ ${key}:</span> ${type.toUpperCase()} (${String(value).substring(0, 50)}${String(value).length > 50 ? '...' : ''})\n`;
                }
            }
            
            analysis += `\n<span class="info">FULL SERVICE OBJECT:</span>\n`;
            analysis += JSON.stringify(service, null, 2);
            
            return analysis;
        }
        
        function testEditButtonClick() {
            if (!adminWindow || adminWindow.closed) {
                log('❌ Admin window not open. Please open it first.', 'error');
                return;
            }
            
            log('🖱️ Testing edit button click...', 'info');
            
            try {
                // Clear previous errors
                capturedErrors = [];
                
                // Find edit buttons
                const editButtons = adminWindow.document.querySelectorAll('button');
                let editButton = null;
                
                for (let button of editButtons) {
                    if (button.textContent.includes('Edit') || button.textContent.includes('✏️')) {
                        editButton = button;
                        break;
                    }
                }
                
                if (editButton) {
                    log(`✅ Found edit button: "${editButton.textContent.trim()}"`, 'success');
                    
                    // Click the button and monitor for errors
                    log('🎯 Clicking edit button...', 'info');
                    editButton.click();
                    
                    // Check for errors after a delay
                    setTimeout(() => {
                        if (capturedErrors.length > 0) {
                            log(`🚨 ${capturedErrors.length} error(s) captured after click`, 'error');
                            capturedErrors.forEach((error, index) => {
                                log(`Error ${index + 1}: ${error.message}`, 'error');
                            });
                        } else {
                            log('✅ No errors detected after edit button click', 'success');
                        }
                        
                        // Check if modal opened
                        const modals = adminWindow.document.querySelectorAll('[class*="modal"]');
                        if (modals.length > 0) {
                            log(`✅ Modal opened successfully (${modals.length} modal(s) found)`, 'success');
                        } else {
                            log('⚠️ No modal detected after click', 'warning');
                        }
                    }, 2000);
                    
                } else {
                    log('❌ No edit button found on the page', 'error');
                    log('💡 Available buttons:', 'info');
                    editButtons.forEach((btn, index) => {
                        log(`  ${index + 1}. "${btn.textContent.trim()}"`, 'info');
                    });
                }
                
            } catch (error) {
                log(`❌ Error during edit button test: ${error.message}`, 'error');
            }
        }
        
        function monitorReactErrors() {
            log('👁️ Starting continuous React error monitoring...', 'info');
            log('💡 Click an Edit button in the admin window to test', 'info');
            
            // Monitor for 30 seconds
            let monitorCount = 0;
            const monitorInterval = setInterval(() => {
                monitorCount++;
                
                if (capturedErrors.length > 0) {
                    log(`🚨 React errors detected during monitoring!`, 'error');
                    clearInterval(monitorInterval);
                    return;
                }
                
                if (monitorCount >= 30) {
                    log('✅ 30-second monitoring complete - no React errors detected', 'success');
                    clearInterval(monitorInterval);
                }
            }, 1000);
        }
        
        // Initialize
        log('🔧 React Error #130 Debugger loaded', 'success');
        log('📋 Instructions:', 'info');
        log('1. Click "Start Debugging Session" to initialize', 'info');
        log('2. Click "Open Admin Inventory" to open the admin page', 'info');
        log('3. Click "Analyze Service Data Structure" to examine data', 'info');
        log('4. Click "Test Edit Button Click" to trigger the error', 'info');
        log('5. Review captured errors and data analysis', 'info');
    </script>
</body>
</html>
