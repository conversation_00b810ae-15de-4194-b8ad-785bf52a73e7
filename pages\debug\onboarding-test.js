import { useState } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'

export default function OnboardingTest() {
  const router = useRouter()
  const [testResults, setTestResults] = useState(null)
  const [loading, setLoading] = useState(false)

  const runOnboardingTest = async () => {
    setLoading(true)
    setTestResults(null)

    try {
      // Test 1: Email generation
      const emailResponse = await fetch('/api/debug/test-email-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: 'artist',
          includeToken: true
        })
      })

      const emailResult = await emailResponse.json()

      // Test 2: Token validation
      let tokenValidationResult = null
      if (emailResult.success && emailResult.applicationLink?.token) {
        const tokenResponse = await fetch('/api/applications/validate-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token: emailResult.applicationLink.token,
            role: 'artist'
          })
        })

        tokenValidationResult = await tokenResponse.json()
      }

      setTestResults({
        emailGeneration: emailResult,
        tokenValidation: tokenValidationResult,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      setTestResults({
        error: error.message,
        timestamp: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const testApplicationPage = (role, token) => {
    const url = `/apply/${role}?token=${token}`
    window.open(url, '_blank')
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Head>
        <title>Onboarding Flow Test - Ocean Soul Sparkles</title>
      </Head>

      <h1>🧪 Artist/Braider Onboarding Flow Test</h1>
      <p>This page helps debug and verify the onboarding flow for Artist and Braider users.</p>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runOnboardingTest}
          disabled={loading}
          style={{
            padding: '12px 24px',
            backgroundColor: '#6a0dad',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px'
          }}
        >
          {loading ? 'Running Tests...' : 'Run Onboarding Flow Test'}
        </button>
      </div>

      {testResults && (
        <div style={{ marginTop: '20px' }}>
          <h2>Test Results</h2>
          
          {testResults.error ? (
            <div style={{ 
              padding: '15px', 
              backgroundColor: '#ffebee', 
              border: '1px solid #f44336',
              borderRadius: '6px',
              color: '#c62828'
            }}>
              <strong>Error:</strong> {testResults.error}
            </div>
          ) : (
            <>
              {/* Email Generation Test */}
              <div style={{ 
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: testResults.emailGeneration?.success ? '#e8f5e8' : '#ffebee',
                border: `1px solid ${testResults.emailGeneration?.success ? '#4caf50' : '#f44336'}`,
                borderRadius: '6px'
              }}>
                <h3>📧 Email Generation Test</h3>
                <p><strong>Status:</strong> {testResults.emailGeneration?.success ? '✅ Success' : '❌ Failed'}</p>
                
                {testResults.emailGeneration?.success && (
                  <>
                    <p><strong>Token Generated:</strong> {testResults.emailGeneration.testData?.tokenGenerated ? '✅ Yes' : '❌ No'}</p>
                    <p><strong>Application Link:</strong></p>
                    <div style={{ 
                      backgroundColor: '#f5f5f5', 
                      padding: '10px', 
                      borderRadius: '4px',
                      fontFamily: 'monospace',
                      fontSize: '14px',
                      wordBreak: 'break-all'
                    }}>
                      {testResults.emailGeneration.rawLink || 'No link found'}
                    </div>
                    
                    {testResults.emailGeneration.applicationLink?.token && (
                      <div style={{ marginTop: '10px' }}>
                        <button
                          onClick={() => testApplicationPage('artist', testResults.emailGeneration.applicationLink.token)}
                          style={{
                            padding: '8px 16px',
                            backgroundColor: '#2196f3',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                          }}
                        >
                          Test Application Page
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>

              {/* Token Validation Test */}
              {testResults.tokenValidation && (
                <div style={{ 
                  marginBottom: '20px',
                  padding: '15px',
                  backgroundColor: testResults.tokenValidation?.valid ? '#e8f5e8' : '#ffebee',
                  border: `1px solid ${testResults.tokenValidation?.valid ? '#4caf50' : '#f44336'}`,
                  borderRadius: '6px'
                }}>
                  <h3>🔐 Token Validation Test</h3>
                  <p><strong>Status:</strong> {testResults.tokenValidation?.valid ? '✅ Valid' : '❌ Invalid'}</p>
                  
                  {testResults.tokenValidation?.valid ? (
                    <>
                      <p><strong>User Email:</strong> {testResults.tokenValidation.user?.email}</p>
                      <p><strong>Application Type:</strong> {testResults.tokenValidation.application?.type}</p>
                    </>
                  ) : (
                    <p><strong>Error:</strong> {testResults.tokenValidation?.error}</p>
                  )}
                </div>
              )}

              {/* Quick Links */}
              <div style={{ 
                padding: '15px',
                backgroundColor: '#f0f8ff',
                border: '1px solid #2196f3',
                borderRadius: '6px'
              }}>
                <h3>🔗 Quick Test Links</h3>
                <p>Test the application pages directly:</p>
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                  <button
                    onClick={() => window.open('/apply/artist', '_blank')}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#ff9800',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    Artist Page (No Token)
                  </button>
                  <button
                    onClick={() => window.open('/apply/braider', '_blank')}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#ff9800',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    Braider Page (No Token)
                  </button>
                  <button
                    onClick={() => router.push('/admin/users/new')}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#6a0dad',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    Create New User
                  </button>
                </div>
              </div>
            </>
          )}

          <div style={{ 
            marginTop: '20px',
            padding: '10px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
            fontSize: '12px',
            color: '#666'
          }}>
            <strong>Test completed at:</strong> {testResults.timestamp}
          </div>
        </div>
      )}
    </div>
  )
}
