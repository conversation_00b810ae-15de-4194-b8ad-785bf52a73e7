.recurringBookingForm {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.title {
  font-size: 1.25rem;
  color: #333;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.select,
.numberInput,
.dateInput,
.countInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
}

.intervalGroup {
  display: flex;
  align-items: center;
  gap: 10px;
}

.intervalGroup .numberInput {
  width: 80px;
}

.daysOfWeek {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.dayCheckbox {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
  transition: background-color 0.2s;
}

.dayCheckbox:hover {
  background-color: #e9ecef;
}

.dayCheckbox input {
  margin: 0;
}

.endOptions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.radioOption {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.countInput {
  width: 60px;
}

.dateInput {
  width: auto;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.cancelButton {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancelButton:hover {
  background-color: #e9ecef;
}

.saveButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover {
  background-color: #5a0b9d;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}
