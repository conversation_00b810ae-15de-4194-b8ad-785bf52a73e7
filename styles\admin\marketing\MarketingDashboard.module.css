.marketingDashboard {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.actions {
  display: flex;
  gap: 10px;
}

.analyticsButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.analyticsButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statValue {
  font-size: 2rem;
  font-weight: 600;
  color: #6e8efb;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 12px;
}

.statLink {
  display: inline-block;
  font-size: 0.85rem;
  color: #6e8efb;
  text-decoration: none;
  transition: color 0.2s ease;
}

.statLink:hover {
  color: #5a7df9;
  text-decoration: underline;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.dashboardSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sectionHeader h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.viewAllLink {
  font-size: 0.85rem;
  color: #6e8efb;
  text-decoration: none;
  transition: color 0.2s ease;
}

.viewAllLink:hover {
  color: #5a7df9;
  text-decoration: underline;
}

.emptyState {
  text-align: center;
  padding: 30px 0;
}

.emptyState p {
  color: #666;
  margin-bottom: 16px;
}

.createButton {
  display: inline-block;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.createButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.segmentList,
.campaignList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segmentCard,
.campaignCard {
  display: block;
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 16px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.segmentCard:hover,
.campaignCard:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.segmentName,
.campaignName {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.segmentMeta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #666;
}

.campaignHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.campaignStatus {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: capitalize;
}

.statusActive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.statusScheduled {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.statusCompleted {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.statusDraft {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.campaignType {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 8px;
}

.campaignDates {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 12px;
}

.campaignMetrics {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.metricItem {
  display: flex;
  align-items: center;
  gap: 4px;
}

.metricLabel {
  font-size: 0.8rem;
  color: #666;
}

.metricValue {
  font-size: 0.85rem;
  font-weight: 500;
  color: #333;
}

.actionButtons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.actionButton {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
  text-align: center;
}

.actionButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1024px) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboardGrid {
    grid-template-columns: 1fr;
  }

  .actionButtons {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }

  .actionButtons {
    grid-template-columns: 1fr;
  }
}
