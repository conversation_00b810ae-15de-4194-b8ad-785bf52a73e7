#!/usr/bin/env node

/**
 * Production Security Check Script
 * Validates security configuration before deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🔒 Ocean Soul Sparkles - Production Security Check\n');

let hasErrors = false;
let hasWarnings = false;

function logError(message) {
  console.log(`❌ ERROR: ${message}`);
  hasErrors = true;
}

function logWarning(message) {
  console.log(`⚠️  WARNING: ${message}`);
  hasWarnings = true;
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

// Check environment variables
function checkEnvironmentVariables() {
  console.log('📋 Checking Environment Variables...\n');

  const envFile = path.join(rootDir, '.env.production');

  if (!fs.existsSync(envFile)) {
    logError('.env.production file not found');
    logInfo('Create .env.production with production-safe settings');
    return;
  }

  const envContent = fs.readFileSync(envFile, 'utf8');

  // Check for development flags
  if (envContent.includes('NEXT_PUBLIC_DEV_MODE=true')) {
    logError('NEXT_PUBLIC_DEV_MODE is set to true in production');
  } else {
    logSuccess('NEXT_PUBLIC_DEV_MODE is properly set to false');
  }

  if (envContent.includes('NEXT_PUBLIC_DEBUG_AUTH=true')) {
    logError('NEXT_PUBLIC_DEBUG_AUTH is set to true in production');
  } else {
    logSuccess('NEXT_PUBLIC_DEBUG_AUTH is properly set to false');
  }

  if (envContent.includes('ENABLE_AUTH_BYPASS=true')) {
    logError('ENABLE_AUTH_BYPASS is set to true in production');
  } else {
    logSuccess('ENABLE_AUTH_BYPASS is properly set to false');
  }

  // Check for localhost URLs
  if (envContent.includes('localhost')) {
    logWarning('localhost URLs found in production environment file');
  } else {
    logSuccess('No localhost URLs found in production environment');
  }

  // Check for HTTPS URLs
  if (envContent.includes('NEXT_PUBLIC_SITE_URL=https://')) {
    logSuccess('NEXT_PUBLIC_SITE_URL uses HTTPS');
  } else {
    logError('NEXT_PUBLIC_SITE_URL does not use HTTPS');
  }

  if (envContent.includes('NEXT_PUBLIC_ADMIN_URL=https://')) {
    logSuccess('NEXT_PUBLIC_ADMIN_URL uses HTTPS');
  } else {
    logError('NEXT_PUBLIC_ADMIN_URL does not use HTTPS');
  }

  // Check for required variables
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_SITE_URL'
  ];

  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`)) {
      logSuccess(`${varName} is configured`);
    } else {
      logError(`${varName} is missing from production environment`);
    }
  });
}

// Check for debug files that should be removed
function checkDebugFiles() {
  console.log('\n🗂️  Checking for Debug Files...\n');

  const debugFiles = [
    'browser-debug.js',
    'quick-error-check.js',
    'node-error-check.js',
    'node-error-check.cjs',
    'public/console-monitor.js'
  ];

  debugFiles.forEach(file => {
    const filePath = path.join(rootDir, file);
    if (fs.existsSync(filePath)) {
      logWarning(`Debug file found: ${file} - Consider removing for production`);
    } else {
      logSuccess(`Debug file not present: ${file}`);
    }
  });

  // Check public/scripts directory
  const scriptsDir = path.join(rootDir, 'public/scripts');
  if (fs.existsSync(scriptsDir)) {
    const scriptFiles = fs.readdirSync(scriptsDir);
    if (scriptFiles.length > 0) {
      logWarning(`${scriptFiles.length} debug scripts found in public/scripts/ - Consider removing for production`);
      scriptFiles.forEach(file => {
        logInfo(`  - ${file}`);
      });
    } else {
      logSuccess('No debug scripts in public/scripts/');
    }
  }
}

// Check security implementations
function checkSecurityImplementations() {
  console.log('\n🛡️  Checking Security Implementations...\n');

  // Check if security utils exist
  const securityUtilsPath = path.join(rootDir, 'lib/security-utils.js');
  if (fs.existsSync(securityUtilsPath)) {
    logSuccess('Security utilities implemented');
  } else {
    logError('Security utilities not found');
  }

  // Check if production security exists
  const prodSecurityPath = path.join(rootDir, 'lib/production-security.js');
  if (fs.existsSync(prodSecurityPath)) {
    logSuccess('Production security module implemented');
  } else {
    logError('Production security module not found');
  }

  // Check if Layout includes security initialization
  const layoutPath = path.join(rootDir, 'components/Layout.js');
  if (fs.existsSync(layoutPath)) {
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    if (layoutContent.includes('initializeProductionSecurity')) {
      logSuccess('Layout includes security initialization');
    } else {
      logWarning('Layout does not include security initialization');
    }
  }
}

// Check robots.txt configuration
function checkRobotsTxt() {
  console.log('\n🤖 Checking robots.txt...\n');

  const robotsPath = path.join(rootDir, 'public/robots.txt');
  if (fs.existsSync(robotsPath)) {
    const robotsContent = fs.readFileSync(robotsPath, 'utf8');

    if (robotsContent.includes('Disallow: /admin/')) {
      logSuccess('Admin panel is disallowed in robots.txt');
    } else {
      logWarning('Admin panel is not disallowed in robots.txt');
    }

    if (robotsContent.includes('Disallow: /api/')) {
      logSuccess('API routes are disallowed in robots.txt');
    } else {
      logWarning('API routes are not disallowed in robots.txt');
    }
  } else {
    logWarning('robots.txt file not found');
  }
}

// Check Next.js configuration
function checkNextConfig() {
  console.log('\n⚙️  Checking Next.js Configuration...\n');

  const nextConfigPath = path.join(rootDir, 'next.config.js');
  if (fs.existsSync(nextConfigPath)) {
    const configContent = fs.readFileSync(nextConfigPath, 'utf8');

    if (configContent.includes('reactStrictMode: true')) {
      logSuccess('React Strict Mode is enabled');
    } else {
      logWarning('React Strict Mode is not enabled');
    }

    if (configContent.includes('swcMinify: true')) {
      logSuccess('SWC minification is enabled');
    } else {
      logWarning('SWC minification is not enabled');
    }

    if (configContent.includes('Access-Control-Allow-Origin')) {
      logSuccess('CORS headers are configured');
    } else {
      logWarning('CORS headers are not configured');
    }
  } else {
    logError('next.config.js file not found');
  }
}

// Check package.json for security
function checkPackageJson() {
  console.log('\n📦 Checking Package Configuration...\n');

  const packagePath = path.join(rootDir, 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

    if (packageContent.scripts && packageContent.scripts['production-check']) {
      logSuccess('Production check script is available');
    } else {
      logWarning('Production check script is not available');
    }

    if (packageContent.scripts && packageContent.scripts['build:production']) {
      logSuccess('Production build script is available');
    } else {
      logWarning('Production build script is not available');
    }
  } else {
    logError('package.json file not found');
  }
}

// Main execution
async function runSecurityCheck() {
  checkEnvironmentVariables();
  checkDebugFiles();
  checkSecurityImplementations();
  checkRobotsTxt();
  checkNextConfig();
  checkPackageJson();

  console.log('\n📊 Security Check Summary\n');

  if (hasErrors) {
    console.log('❌ SECURITY CHECK FAILED');
    console.log('Please fix the errors above before deploying to production.\n');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('⚠️  SECURITY CHECK PASSED WITH WARNINGS');
    console.log('Consider addressing the warnings above for better security.\n');
    process.exit(0);
  } else {
    console.log('✅ SECURITY CHECK PASSED');
    console.log('Your application is ready for secure production deployment.\n');
    process.exit(0);
  }
}

// Run the check
runSecurityCheck().catch(error => {
  console.error('❌ Security check failed with error:', error);
  process.exit(1);
});
