import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { authenticatedFetch } from '@/lib/auth-utils'
import { safeRender } from '@/lib/safe-render-utils'
import LoadingButton from './LoadingButton'
import styles from '@/styles/admin/CustomerList.module.css'

export default function CustomerList() {
  const router = useRouter()
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(10)
  const [totalCustomers, setTotalCustomers] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [refreshInterval, setRefreshInterval] = useState(null)
  const [lastRefresh, setLastRefresh] = useState(Date.now())
  const [filters, setFilters] = useState({
    customer_status: '',
    location: '',
    date_range: ''
  })
  const [selectedCustomers, setSelectedCustomers] = useState([])
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    newThisMonth: 0,
    withBookings: 0
  })

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
    }, 500)

    return () => clearTimeout(timer)
  }, [search])

  // Reset page when search or filters change
  useEffect(() => {
    setPage(1)
  }, [debouncedSearch, filters])

  // Fetch customers when search, sort, filters, or page changes
  // Enhanced with better error handling, timeout mechanisms, and resource cleanup
  const fetchCustomers = useCallback(async () => {
    // Create an AbortController to cancel requests if component unmounts
    const controller = new AbortController();
    const signal = controller.signal;

    // Create a timeout ID for cleanup
    let timeoutId = null;

    // Track if the component is still mounted
    let isMounted = true;

    // Safe state update functions that check if component is mounted
    const safeSetLoading = (value) => {
      if (isMounted && !signal.aborted) {
        setLoading(value);
      }
    };

    const safeSetError = (value) => {
      if (isMounted && !signal.aborted) {
        setError(value);
      }
    };

    const safeSetCustomers = (value) => {
      if (isMounted && !signal.aborted) {
        setCustomers(value);
      }
    };

    const safeSetTotalCustomers = (value) => {
      if (isMounted && !signal.aborted) {
        setTotalCustomers(value);
      }
    };

    const safeSetTotalPages = (value) => {
      if (isMounted && !signal.aborted) {
        setTotalPages(value);
      }
    };

    safeSetLoading(true);
    safeSetError(null);

    try {
      const offset = (page - 1) * limit;
      const queryParams = new URLSearchParams({
        limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      // Create an AbortController for the authenticatedFetch
      const abortController = new AbortController();

      // Set up a timeout to abort the request if it takes too long
      timeoutId = setTimeout(() => {
        abortController.abort();
        if (isMounted && !signal.aborted) {
          safeSetError('Request timed out. Please try again.');
          safeSetLoading(false);
        }
      }, 15000); // 15 second timeout

      // Use authenticatedFetch with the signal from both controllers
      const data = await authenticatedFetch(
        `/api/admin/customers?${queryParams.toString()}`,
        {
          signal: abortController.signal,
          // Don't redirect on auth failure during list view to prevent disruption
          // Just show the error message instead
        },
        {
          redirect: false,
          notify: true
        }
      );

      // Clear the timeout since the request completed
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (signal.aborted || !isMounted) {
        // Request was aborted or component unmounted, don't update state
        return;
      }

      // The API returns an object with customers property for compatibility
      if (!data) {
        console.error('Invalid response format: no data received');
        throw new Error('Invalid response from server');
      }

      // Handle both array format (legacy) and object format (current)
      let customers, totalCount;

      if (Array.isArray(data)) {
        // Legacy format: direct array
        customers = data;
        totalCount = data.length;
      } else if (data.customers && Array.isArray(data.customers)) {
        // Current format: object with customers property
        customers = data.customers;
        totalCount = data.total || data.customers.length;
      } else {
        console.error('Invalid response format:', typeof data, data);
        throw new Error('Invalid response from server');
      }

      // Set the customers from the processed data
      safeSetCustomers(customers);
      safeSetTotalCustomers(totalCount);
      safeSetTotalPages(Math.ceil(totalCount / limit));

      // Update stats
      updateStats(customers);
    } catch (error) {
      // Clear the timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (signal.aborted || !isMounted) {
        // Request was aborted or component unmounted, don't update state
        return;
      }

      console.error('Error fetching customers:', error);
      safeSetError(error.message || 'Failed to load customers');

      // Set empty data to prevent UI from hanging
      safeSetCustomers([]);
      safeSetTotalCustomers(0);
      safeSetTotalPages(0);
    } finally {
      if (!signal.aborted && isMounted) {
        safeSetLoading(false);
      }
    }

    // Return cleanup function
    return () => {
      isMounted = false;

      // Abort the fetch if component unmounts
      if (!signal.aborted) {
        controller.abort();
      }

      // Clear any remaining timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [page, limit, sortBy, sortOrder, debouncedSearch, filters]);

  useEffect(() => {
    fetchCustomers()
  }, [fetchCustomers])

  // Set up auto-refresh for customer data (every 60 seconds)
  // Enhanced with better error handling and resource management
  useEffect(() => {
    // Track if component is mounted
    let isMounted = true;

    // Create a function to safely refresh data
    const refreshData = async () => {
      if (!isMounted) return;

      // Don't auto-refresh if there's an error or if we're already loading
      if (loading || error) return;

      try {
        // Update last refresh timestamp
        setLastRefresh(Date.now());

        // Create a timeout for the refresh operation
        const refreshPromise = fetchCustomers();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Auto-refresh timeout')), 10000)
        );

        // Race the refresh against the timeout
        await Promise.race([refreshPromise, timeoutPromise]);
      } catch (refreshError) {
        console.error('Auto-refresh failed:', refreshError);
        // Don't set error state to prevent UI disruption during auto-refresh
      }
    };

    // Start auto-refresh with a longer interval (60 seconds instead of 30)
    // to reduce server load and prevent potential race conditions
    const interval = setInterval(refreshData, 60000); // 60 seconds

    // Clean up on unmount
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, [fetchCustomers, loading, error]);

  // Handle sort column click
  const handleSort = (column) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      // Default to ascending order for new column
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null

    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    )
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Check if a date is within the last 7 days
  const isRecent = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const sevenDaysAgo = new Date(now.setDate(now.getDate() - 7))
    return date >= sevenDaysAgo
  }

  // Update customer statistics
  const updateStats = (customerList) => {
    const now = new Date()
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    const newStats = {
      total: customerList.length,
      active: customerList.filter(c => c.customer_status === 'active' || !c.customer_status).length,
      newThisMonth: customerList.filter(c => {
        const createdDate = new Date(c.created_at)
        return createdDate >= thisMonth
      }).length,
      withBookings: customerList.filter(c => c.booking_count > 0 || c.total_bookings > 0).length
    }

    setStats(newStats)
  }

  // Handle customer selection
  const handleCustomerSelect = (customerId, selected) => {
    if (selected) {
      setSelectedCustomers(prev => [...prev, customerId])
    } else {
      setSelectedCustomers(prev => prev.filter(id => id !== customerId))
    }
  }

  // Handle select all
  const handleSelectAll = (selected) => {
    if (selected) {
      setSelectedCustomers(customers.map(c => c.id))
    } else {
      setSelectedCustomers([])
    }
  }

  // Render customer status badge
  const renderStatusBadge = (status) => {
    const statusConfig = {
      active: { color: '#4caf50', label: 'Active' },
      inactive: { color: '#ff9800', label: 'Inactive' },
      suspended: { color: '#f44336', label: 'Suspended' },
      churned: { color: '#9e9e9e', label: 'Churned' }
    }

    const config = statusConfig[status] || { color: '#9e9e9e', label: 'Unknown' }

    return (
      <span
        className={styles.statusBadge}
        style={{ backgroundColor: config.color }}
      >
        {config.label}
      </span>
    )
  }

  // Render customer tier badge
  const renderTierBadge = (tier) => {
    if (!tier || tier === 'bronze') return null

    const tierColors = {
      silver: '#C0C0C0',
      gold: '#FFD700',
      platinum: '#E5E4E2'
    }

    return (
      <span
        className={styles.tierBadge}
        style={{ backgroundColor: tierColors[tier] }}
      >
        {tier.toUpperCase()}
      </span>
    )
  }

  // Handle export
  const handleExport = (format = 'csv', options = {}) => {
    const queryParams = new URLSearchParams({
      format,
      ...options
    })

    // Add current filters to export
    if (debouncedSearch) {
      queryParams.append('search', debouncedSearch)
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        queryParams.append(key, value)
      }
    })

    window.open(`/api/customers/export?${queryParams.toString()}`, '_blank')
  }

  return (
    <div className={styles.customerList}>
      <div className={styles.header}>
        <h2>Customers</h2>
        <div className={styles.actions}>
          <button
            className={styles.addButton}
            onClick={() => router.push('/admin/customers/new')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add Customer
          </button>
          <div className={styles.exportDropdown}>
            <button className={styles.exportButton}>
              Export
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div className={styles.exportOptions}>
              <button onClick={() => handleExport('csv')}>Current View (CSV)</button>
              <button onClick={() => handleExport('csv', { marketing_only: true })}>Marketing Opt-in Only (CSV)</button>
              <button onClick={() => handleExport('csv', { include_bookings: true })}>Include Booking History (CSV)</button>
              <button onClick={() => handleExport('excel')}>Current View (Excel)</button>
              <button onClick={() => handleExport('json')}>Current View (JSON)</button>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.filters}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Search by name, email, or phone..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={styles.searchIcon}>
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </div>

        <div className={styles.filterControls}>
          <select
            name="customer_status"
            value={filters.customer_status}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>

          <select
            name="location"
            value={filters.location}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Locations</option>
            <option value="Sydney, NSW">Sydney, NSW</option>
            <option value="Melbourne, VIC">Melbourne, VIC</option>
            <option value="Brisbane, QLD">Brisbane, QLD</option>
            <option value="Perth, WA">Perth, WA</option>
            <option value="Adelaide, SA">Adelaide, SA</option>
            <option value="Other">Other</option>
          </select>

          <select
            name="date_range"
            value={filters.date_range}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Time</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
          </select>
        </div>
      </div>

      {/* Customer Statistics */}
      <div className={styles.statsContainer}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>{stats.total}</div>
            <div className={styles.statLabel}>Total Customers</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={`${styles.statIcon} ${styles.active}`}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>{stats.active}</div>
            <div className={styles.statLabel}>Active Customers</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={`${styles.statIcon} ${styles.new}`}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 2v20m8-10H4"></path>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>{stats.newThisMonth}</div>
            <div className={styles.statLabel}>New This Month</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={`${styles.statIcon} ${styles.bookings}`}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </div>
          <div className={styles.statContent}>
            <div className={styles.statNumber}>{stats.withBookings}</div>
            <div className={styles.statLabel}>With Bookings</div>
          </div>
        </div>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      {/* Bulk Actions */}
      {selectedCustomers.length > 0 && (
        <div className={styles.bulkActions}>
          <span className={styles.selectedCount}>
            {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected
          </span>
          <div className={styles.bulkActionButtons}>
            <button
              className={styles.bulkButton}
              onClick={() => setShowBulkActions(!showBulkActions)}
            >
              Bulk Actions ▼
            </button>
            {showBulkActions && (
              <div className={styles.bulkActionMenu}>
                <button onClick={() => alert('Export selected customers')}>
                  Export Selected
                </button>
                <button onClick={() => alert('Send bulk email')}>
                  Send Email
                </button>
                <button onClick={() => alert('Deactivate customers')}>
                  Deactivate Selected
                </button>
                <button onClick={() => setSelectedCustomers([])}>
                  Clear Selection
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {loading ? (
        <div className={styles.loading}>Loading customers...</div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.customerTable}>
              <thead>
                <tr>
                  <th className={styles.checkboxColumn}>
                    <input
                      type="checkbox"
                      checked={selectedCustomers.length === customers.length && customers.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  </th>
                  <th onClick={() => handleSort('name')}>
                    Name {renderSortIndicator('name')}
                  </th>
                  <th onClick={() => handleSort('email')}>
                    Email {renderSortIndicator('email')}
                  </th>
                  <th onClick={() => handleSort('customer_status')}>
                    Status {renderSortIndicator('customer_status')}
                  </th>
                  <th onClick={() => handleSort('phone')}>
                    Phone {renderSortIndicator('phone')}
                  </th>
                  <th onClick={() => handleSort('city')}>
                    Location {renderSortIndicator('city')}
                  </th>
                  <th onClick={() => handleSort('created_at')}>
                    Created {renderSortIndicator('created_at')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {customers.map((customer) => {
                  try {
                    return (
                      <tr key={customer.id} className={customer.recent_booking ? styles.recentBooking : ''}>
                        <td>
                          <input
                            type="checkbox"
                            checked={selectedCustomers.includes(customer.id)}
                            onChange={(e) => handleCustomerSelect(customer.id, e.target.checked)}
                          />
                        </td>
                        <td>
                          <div className={styles.customerName}>
                            {safeRender(customer.name)}
                            {renderTierBadge(customer.customer_tier)}
                            {customer.marketing_consent && (
                              <span className={styles.marketingBadge} title="Marketing Opt-in">📧</span>
                            )}
                          </div>
                        </td>
                        <td>{safeRender(customer.email)}</td>
                        <td>
                          {renderStatusBadge(customer.customer_status || 'active')}
                        </td>
                        <td>{safeRender(customer.phone, '-')}</td>
                        <td>
                          {customer.city ? `${safeRender(customer.city)}, ${safeRender(customer.state, '')}` : '-'}
                        </td>
                        <td>
                          {customer.created_at ? new Date(customer.created_at).toLocaleDateString() : 'N/A'}
                          {customer.created_at && isRecent(customer.created_at) && (
                            <span className={styles.newBadge} title="New Customer">New</span>
                          )}
                        </td>
                        <td className={styles.actions}>
                          <Link
                            href={`/admin/customers/${customer.id}`}
                            className={styles.viewButton}
                          >
                            View
                          </Link>
                          <Link
                            href={`/admin/customers/${customer.id}/edit`}
                            className={styles.editButton}
                          >
                            Edit
                          </Link>
                        </td>
                      </tr>
                    );
                  } catch (error) {
                    console.error('Error rendering customer row:', error, 'Customer:', customer);
                    return (
                      <tr key={customer.id || Math.random()}>
                        <td colSpan="8" style={{ color: 'red', padding: '10px' }}>
                          Error displaying customer data. Please refresh the page.
                        </td>
                      </tr>
                    );
                  }
                })}
              </tbody>
            </table>
          </div>

          {customers.length === 0 && (
            <div className={styles.noResults}>No customers found</div>
          )}

          <div className={styles.paginationContainer}>
            <div className={styles.pageSize}>
              <label>Show:</label>
              <select
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value))}
                className={styles.pageSizeSelect}
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span>per page</span>
            </div>

            <div className={styles.pagination}>
              <button
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
                className={styles.paginationButton}
              >
                Previous
              </button>
              <span className={styles.pageInfo}>
                Page {page} of {totalPages} ({totalCustomers} customers)
              </span>
              <button
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages || totalPages === 0}
                className={styles.paginationButton}
              >
                Next
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
