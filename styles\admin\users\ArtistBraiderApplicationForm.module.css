.formContainer {
  max-width: 800px;
  margin: 0 auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(78, 205, 196, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.formContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4ECDC4 0%, #FF6B6B 50%, #FFE66D 100%);
  z-index: 1;
}

.form {
  padding: 40px;
  position: relative;
  z-index: 2;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid rgba(78, 205, 196, 0.1);
  position: relative;
}

.headerIcon {
  font-size: 3rem;
  margin-bottom: 15px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(5deg); }
}

.header h2 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4ECDC4, #2E9BBA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  color: #666;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.formGroup {
  margin-bottom: 30px;
}

.formGroup label,
.requiredLabel {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  font-size: 1rem;
}

.required {
  color: #FF6B6B;
  font-weight: 700;
  margin-left: 4px;
}

.optional {
  color: #999;
  font-weight: 400;
  font-size: 0.9rem;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input,
.textarea {
  width: 100%;
  padding: 16px 20px;
  padding-right: 50px;
  border: 2px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.15);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

.inputIcon {
  position: absolute;
  right: 15px;
  font-size: 1.2rem;
  pointer-events: none;
  opacity: 0.6;
}

.textarea {
  resize: vertical;
  min-height: 120px;
  padding-right: 20px;
}

.radioGroup {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 12px;
}

.radioLabel {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  font-weight: 500;
  cursor: pointer;
  padding: 20px;
  border: 2px solid rgba(78, 205, 196, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
}

.radioLabel:hover {
  border-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.15);
}

.radioLabel input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: #4ECDC4;
  margin-top: 2px;
}

.radioLabel input[type="radio"]:checked {
  transform: scale(1.1);
}

.radioText {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.radioTitle {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.radioDescription {
  font-size: 0.9rem;
  color: #666;
  font-weight: 400;
}

.radioLabel input[type="radio"]:checked ~ .radioText .radioTitle {
  color: #4ECDC4;
}

.checkboxGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 15px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(78, 205, 196, 0.2);
  background: rgba(255, 255, 255, 0.6);
}

.checkboxLabel:hover {
  background: rgba(78, 205, 196, 0.05);
  border-color: #4ECDC4;
  transform: translateY(-1px);
}

.checkboxLabel input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #4ECDC4;
}

.checkboxLabel input[type="checkbox"]:checked {
  transform: scale(1.1);
}

.availabilitySection {
  margin: 20px 0;
  padding: 25px;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.05) 0%, rgba(255, 107, 107, 0.05) 100%);
  border-radius: 15px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.availabilitySection h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.availabilitySection h4::before {
  content: '📅';
  font-size: 1.2rem;
}

.availabilityOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.charCount {
  font-size: 0.85rem;
  color: #666;
  margin-top: 8px;
  text-align: right;
  font-style: italic;
}

.error {
  color: #FF6B6B;
  font-size: 0.9rem;
  margin-top: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

.error::before {
  content: '⚠️';
  font-size: 1rem;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 2px solid rgba(78, 205, 196, 0.1);
}

.cancelButton,
.submitButton {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.cancelButton {
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  border: 2px solid rgba(78, 205, 196, 0.3);
  backdrop-filter: blur(5px);
}

.cancelButton:hover {
  background: rgba(78, 205, 196, 0.1);
  border-color: #4ECDC4;
  color: #333;
  transform: translateY(-2px);
}

.submitButton {
  background: linear-gradient(135deg, #4ECDC4 0%, #2E9BBA 100%);
  color: white;
  border: 2px solid transparent;
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
  position: relative;
}

.submitButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6B6B 0%, #FFE66D 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.submitButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(78, 205, 196, 0.4);
}

.submitButton:hover::before {
  opacity: 1;
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submitButton:disabled::before {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .formContainer {
    margin: 15px;
    border-radius: 15px;
  }

  .form {
    padding: 25px;
  }

  .header h2 {
    font-size: 1.8rem;
  }

  .headerIcon {
    font-size: 2.5rem;
  }

  .radioGroup {
    gap: 12px;
  }

  .radioLabel {
    padding: 15px;
  }

  .checkboxGrid {
    grid-template-columns: 1fr;
  }

  .formActions {
    flex-direction: column;
    gap: 15px;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
    padding: 18px 24px;
  }

  .availabilitySection {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .formContainer {
    margin: 10px;
    border-radius: 12px;
  }

  .form {
    padding: 20px;
  }

  .header {
    margin-bottom: 25px;
    padding-bottom: 20px;
  }

  .header h2 {
    font-size: 1.6rem;
  }

  .headerIcon {
    font-size: 2rem;
  }

  .formGroup {
    margin-bottom: 25px;
  }

  .input,
  .textarea {
    padding: 14px 18px;
    padding-right: 45px;
  }

  .radioLabel {
    padding: 12px;
    gap: 12px;
  }

  .radioTitle {
    font-size: 1rem;
  }

  .checkboxLabel {
    padding: 10px 12px;
  }

  .availabilitySection {
    padding: 15px;
  }
}
