import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import AuthTest from '@/components/admin/AuthTest';
import styles from '@/styles/admin/Dashboard.module.css';

export default function TestAuthPage() {
  return (
    <ProtectedRoute>
      <AdminLayout title="Authentication Testing">
        <div className={styles.dashboard}>
          <AuthTest />
          
          <div className={styles.dashboardSection}>
            <h2 className={styles.sectionTitle}>Authentication System Information</h2>
            <div className={styles.infoCard}>
              <p>
                This page allows you to test the Supabase authentication system implementation.
                The tests verify:
              </p>
              <ul>
                <li>Login functionality with test credentials</li>
                <li>Session persistence and management</li>
                <li>Protected route access control</li>
                <li>Role-based permissions for admin users</li>
              </ul>
              <p>
                Click the "Run Authentication Tests" button above to execute all tests and view the results.
              </p>
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
