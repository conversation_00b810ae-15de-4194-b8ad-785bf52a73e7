-- Fix token validation function for <PERSON><PERSON><PERSON>raider onboarding
-- This resolves token validation API failures

-- Create or replace the validate_application_token function
CREATE OR REPLACE FUNCTION public.validate_application_token(token_value text)
RETURNS TABLE(
  is_valid boolean,
  user_id uuid,
  application_id uuid,
  error_message text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_record RECORD;
  app_record RECORD;
BEGIN
  -- Initialize return values
  is_valid := false;
  user_id := NULL;
  application_id := NULL;
  error_message := NULL;

  -- Check if token exists and is not expired
  SELECT 
    at.user_id,
    at.application_id,
    at.expires_at,
    at.used_at,
    at.token_type
  INTO token_record
  FROM public.application_tokens at
  WHERE at.token = token_value;

  -- If token not found
  IF NOT FOUND THEN
    error_message := 'Invalid or expired token';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if token has expired
  IF token_record.expires_at < NOW() THEN
    error_message := 'Token has expired';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if token has already been used (for one-time tokens)
  IF token_record.used_at IS NOT NULL AND token_record.token_type = 'application_access' THEN
    error_message := 'Token has already been used';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if associated application exists
  SELECT 
    aba.id,
    aba.application_type,
    aba.status
  INTO app_record
  FROM public.artist_braider_applications aba
  WHERE aba.id = token_record.application_id;

  -- If application not found
  IF NOT FOUND THEN
    error_message := 'Associated application not found';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if application is in a valid state for token usage
  IF app_record.status NOT IN ('pending', 'under_review') THEN
    error_message := 'Application is not in a valid state for token access';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Token is valid
  is_valid := true;
  user_id := token_record.user_id;
  application_id := token_record.application_id;
  error_message := NULL;

  RETURN NEXT;
  RETURN;
END;
$$;

-- Create or replace the mark_token_as_used function
CREATE OR REPLACE FUNCTION public.mark_token_as_used(token_value text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  rows_updated integer;
BEGIN
  -- Mark the token as used
  UPDATE public.application_tokens
  SET 
    used_at = NOW(),
    updated_at = NOW()
  WHERE token = token_value
    AND used_at IS NULL;

  GET DIAGNOSTICS rows_updated = ROW_COUNT;
  
  RETURN rows_updated > 0;
END;
$$;

-- Create or replace the generate_application_token function if it doesn't exist
CREATE OR REPLACE FUNCTION public.generate_application_token()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_value text;
  token_exists boolean;
BEGIN
  -- Generate a secure random token
  LOOP
    -- Generate a 32-character random string
    token_value := encode(gen_random_bytes(24), 'base64');
    -- Remove URL-unsafe characters
    token_value := replace(replace(replace(token_value, '+', '-'), '/', '_'), '=', '');
    
    -- Check if token already exists
    SELECT EXISTS(
      SELECT 1 FROM public.application_tokens 
      WHERE token = token_value
    ) INTO token_exists;
    
    -- Exit loop if token is unique
    EXIT WHEN NOT token_exists;
  END LOOP;
  
  RETURN token_value;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.validate_application_token(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_token_as_used(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.generate_application_token() TO authenticated;

-- Add comments
COMMENT ON FUNCTION public.validate_application_token(text) IS 'Validate an application token and return validation details';
COMMENT ON FUNCTION public.mark_token_as_used(text) IS 'Mark an application token as used';
COMMENT ON FUNCTION public.generate_application_token() IS 'Generate a secure unique application token';
