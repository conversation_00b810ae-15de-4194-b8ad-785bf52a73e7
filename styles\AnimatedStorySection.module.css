.storySection {
  position: relative;
  padding: 6rem 2rem;
  overflow: hidden;
  background-color: rgba(249, 249, 249, 0.7); /* Changed to translucent */
  z-index: 1;
}

.backgroundPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234ecdc4' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  z-index: -1;
  opacity: 0.7;
}

.storyContainer {
  max-width: var(--max-width);
  margin: 0 auto;
}

.storyContent {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 3rem;
}

.storyContent.imageRight {
  flex-direction: row-reverse;
}

.storyText {
  flex: 1;
  min-width: 300px;
  padding: 2rem;
  z-index: 2;
}

.storyTitle {
  font-size: 2.5rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.storyDivider {
  width: 70px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin-bottom: 2rem;
  border-radius: 3px;
}

.storyParagraph {
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.7;
  font-size: 1.1rem;
}

.storyParagraph:last-child {
  margin-bottom: 0;
}

.boldParagraph {
  font-weight: 700;
}

.storyImageContainer {
  flex: 0 0 45%;
  min-width: 300px;
  position: relative;
  transform: translateY(30px);
  opacity: 0;
  transition: all 1s ease;
}

.imageVisible {
  transform: translateY(0);
  opacity: 1;
}

.imageLeft {
  margin-right: auto;
}

.imageRight {
  margin-left: auto;
}

.imageFrame {
  width: 100%;
  height: 450px;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.storyImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s ease;
}

.imageFrame:hover .storyImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3) 0%, transparent 50%);
}

.imageDeco1, .imageDeco2 {
  position: absolute;
  border-radius: 8px;
  opacity: 0.6;
  z-index: 1;
}

.imageDeco1 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  top: -30px;
  left: -30px;
  animation: float1 8s ease-in-out infinite;
}

.imageRight .imageDeco1 {
  left: auto;
  right: -30px;
}

.imageDeco2 {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
  bottom: -20px;
  right: -20px;
  animation: float2 6s ease-in-out infinite;
}

.imageRight .imageDeco2 {
  right: auto;
  left: -20px;
}

@keyframes float1 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(5deg);
  }
}

@keyframes float2 {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(15px) rotate(-5deg);
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .storyTitle {
    font-size: 2.2rem;
  }

  .storyParagraph {
    font-size: 1.05rem;
  }

  .imageFrame {
    height: 400px;
  }
}

@media (max-width: 992px) {
  .storySection {
    padding: 5rem 1.5rem;
  }

  .storyContent {
    gap: 2rem;
  }

  .storyText {
    padding: 1.5rem;
  }

  .storyTitle {
    font-size: 2rem;
  }

  .storyParagraph {
    font-size: 1rem;
  }

  .imageFrame {
    height: 350px;
  }

  .imageDeco1 {
    width: 120px;
    height: 120px;
  }

  .imageDeco2 {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 768px) {
  .storySection {
    padding: 4rem 1rem;
  }

  .storyContent {
    flex-direction: column;
  }

  .storyContent.imageRight {
    flex-direction: column;
  }

  .storyText {
    padding: 1rem;
  }

  .storyTitle {
    font-size: 1.8rem;
  }

  .storyImageContainer {
    margin: 0 auto;
    max-width: 90%;
  }

  .imageFrame {
    height: 300px;
  }

  .imageDeco1, .imageDeco2 {
    display: none;
  }
}

@media (max-width: 480px) {
  .storySection {
    padding: 3rem 1rem;
  }

  .storyTitle {
    font-size: 1.6rem;
  }

  .storyDivider {
    width: 50px;
    margin-bottom: 1.5rem;
  }

  .storyParagraph {
    font-size: 0.95rem;
  }

  .imageFrame {
    height: 250px;
  }
}