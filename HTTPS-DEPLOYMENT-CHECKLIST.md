# HTTPS Deployment Checklist for Ocean Soul Sparkles

## 🚀 **IMMEDIATE ACTION STEPS**

### **Step 1: Pre-Deployment Checks (5 minutes)**

Run these commands to check your current setup:

```bash
# Check for HTTP URLs in your code
npm run https-scan

# Check security configuration
npm run security-check
```

### **Step 2: Vercel Dashboard Configuration (10 minutes)**

1. **Go to Vercel Dashboard**
   - Visit: https://vercel.com/dashboard
   - Select your Ocean Soul Sparkles project

2. **Configure Domain Settings**
   - Go to Settings → Domains
   - Ensure `www.oceansoulsparkles.com.au` is set as primary
   - Enable "Redirect to HTTPS" (should be automatic)
   - Add redirect from `oceansoulsparkles.com.au` → `www.oceansoulsparkles.com.au`

3. **Update Environment Variables**
   - Go to Settings → Environment Variables
   - Update these variables:
     ```
     NEXT_PUBLIC_SITE_URL = https://www.oceansoulsparkles.com.au
     NEXT_PUBLIC_ADMIN_URL = https://www.oceansoulsparkles.com.au/admin
     NEXT_PUBLIC_DEV_MODE = false
     NEXT_PUBLIC_DEBUG_AUTH = false
     ENABLE_AUTH_BYPASS = false
     ```

### **Step 3: Deploy Updated Code (5 minutes)**

```bash
# Fix any HTTP URLs automatically
npm run https-fix

# Build and deploy with security checks
npm run deploy:secure
```

### **Step 4: Verify HTTPS is Working (5 minutes)**

```bash
# Test HTTPS configuration
npm run https-verify
```

**Manual Verification:**
1. Visit: https://www.oceansoulsparkles.com.au
2. Check for lock icon in browser
3. Try: http://www.oceansoulsparkles.com.au (should redirect to HTTPS)
4. Test admin panel: https://www.oceansoulsparkles.com.au/admin

---

## 📋 **DETAILED CHECKLIST**

### **A. Pre-Deployment Security**
- [ ] Run `npm run security-check` - all tests pass
- [ ] Run `npm run https-scan` - no HTTP URLs found
- [ ] Environment variables updated for production
- [ ] Debug modes disabled

### **B. Vercel Configuration**
- [ ] Domain configured: `www.oceansoulsparkles.com.au`
- [ ] HTTPS redirect enabled
- [ ] SSL certificate active (automatic with Vercel)
- [ ] Environment variables updated
- [ ] Production build successful

### **C. HTTPS Functionality**
- [ ] Website loads via HTTPS: `https://www.oceansoulsparkles.com.au`
- [ ] HTTP redirects to HTTPS automatically
- [ ] Lock icon appears in browser
- [ ] No mixed content warnings
- [ ] Admin panel accessible via HTTPS

### **D. Security Headers**
- [ ] HSTS header present
- [ ] X-Frame-Options: DENY
- [ ] X-Content-Type-Options: nosniff
- [ ] Content-Security-Policy configured
- [ ] Referrer-Policy set

### **E. External Services**
- [ ] Square payment webhooks updated to HTTPS
- [ ] OneSignal configuration updated
- [ ] Supabase callbacks use HTTPS
- [ ] Any other webhooks updated

### **F. SEO and Marketing**
- [ ] Google Search Console updated with HTTPS URLs
- [ ] Sitemap uses HTTPS URLs
- [ ] Social media profiles updated
- [ ] Email signatures updated

---

## 🔧 **TROUBLESHOOTING GUIDE**

### **Issue: HTTPS not working**
**Symptoms:** Website doesn't load via HTTPS
**Solutions:**
1. Check Vercel domain configuration
2. Verify DNS settings
3. Wait for SSL certificate propagation (up to 24 hours)
4. Contact Vercel support

### **Issue: Mixed content warnings**
**Symptoms:** Browser shows "not secure" despite HTTPS
**Solutions:**
1. Run `npm run https-scan` to find HTTP URLs
2. Check browser console for specific errors
3. Update any hardcoded HTTP resources

### **Issue: Redirect loop**
**Symptoms:** Page keeps redirecting
**Solutions:**
1. Check Vercel redirect configuration
2. Verify Next.js redirect rules in `next.config.js`
3. Clear browser cache

### **Issue: Admin panel not accessible**
**Symptoms:** Admin login fails or redirects incorrectly
**Solutions:**
1. Check environment variables are updated
2. Verify authentication URLs use HTTPS
3. Clear browser cookies and try again

---

## 🎯 **VERIFICATION COMMANDS**

### **Quick Health Check**
```bash
# Run all verification tests
npm run verify:production
```

### **Individual Tests**
```bash
# Check for HTTP URLs
npm run https-scan

# Security configuration check
npm run security-check

# HTTPS functionality test
npm run https-verify
```

### **Manual Browser Tests**
1. **HTTPS Access:** https://www.oceansoulsparkles.com.au
2. **HTTP Redirect:** http://www.oceansoulsparkles.com.au
3. **Admin Panel:** https://www.oceansoulsparkles.com.au/admin
4. **API Health:** https://www.oceansoulsparkles.com.au/api/health

---

## 📊 **SUCCESS CRITERIA**

### **✅ HTTPS is Working When:**
- Website loads with lock icon
- HTTP automatically redirects to HTTPS
- No mixed content warnings
- All functionality works correctly
- Security headers are present

### **✅ Ready for Production When:**
- All checklist items completed
- `npm run verify:production` passes
- Manual tests successful
- External services updated

---

## 🆘 **EMERGENCY ROLLBACK**

### **If Something Goes Wrong:**

1. **Immediate Rollback:**
   ```bash
   # Revert environment variables in Vercel dashboard
   # Set NEXT_PUBLIC_SITE_URL back to HTTP temporarily
   ```

2. **Quick Fix:**
   ```bash
   # Deploy previous working version
   git revert HEAD
   git push origin main
   ```

3. **Contact Support:**
   - Vercel Support: Through dashboard
   - Technical Support: <EMAIL>

---

## 📞 **SUPPORT CONTACTS**

- **Vercel Support:** Available 24/7 through dashboard
- **Domain Issues:** Contact your domain registrar
- **Technical Support:** <EMAIL>
- **Emergency:** Revert changes and contact support

---

## 🎉 **POST-DEPLOYMENT TASKS**

### **Immediate (Within 24 hours):**
- [ ] Monitor website for any issues
- [ ] Check Google Search Console for errors
- [ ] Test all major functionality
- [ ] Verify payment processing works

### **Within 1 Week:**
- [ ] Submit HTTPS sitemap to Google
- [ ] Update all marketing materials
- [ ] Monitor SSL Labs rating
- [ ] Check for any broken external links

### **Ongoing:**
- [ ] Monitor HTTPS certificate expiry (Vercel handles automatically)
- [ ] Regular security header checks
- [ ] Keep security configurations updated

---

## 🏆 **COMPLETION CONFIRMATION**

**Your HTTPS implementation is complete when:**
- ✅ All checklist items are checked
- ✅ `npm run verify:production` passes
- ✅ Website shows lock icon in browser
- ✅ All functionality works correctly
- ✅ No security warnings in browser console

**Congratulations! Your Ocean Soul Sparkles website is now secure with HTTPS! 🔒✨**
