import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for sales analytics data
 * 
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get date range from query params
    const { period, start_date, end_date } = req.query;
    
    // Calculate date range based on period or use provided dates
    const { startDate, endDate } = calculateDateRange(period, start_date, end_date);
    
    // Format dates for Supabase queries
    const startDateIso = startDate.toISOString();
    const endDateIso = endDate.toISOString();

    // Get orders within date range
    const { data: orders, error: ordersError } = await supabaseAdmin
      .from('orders')
      .select('*')
      .gte('created_at', startDateIso)
      .lte('created_at', endDateIso);
    
    if (ordersError) {
      throw ordersError;
    }

    // Calculate summary metrics
    const totalSales = orders?.reduce((sum, order) => sum + (parseFloat(order.total_amount) || 0), 0) || 0;
    const totalOrders = orders?.length || 0;
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;
    
    // Get pageviews and sessions for conversion rate calculation
    const { data: analytics, error: analyticsError } = await supabaseAdmin
      .from('analytics')
      .select('sessions, page_views')
      .gte('date', startDateIso)
      .lte('date', endDateIso)
      .single();
      
    if (analyticsError && analyticsError.code !== 'PGRST116') { // Ignore not found error
      throw analyticsError;
    }
    
    const sessions = analytics?.sessions || 100; // Default value if no analytics
    const conversionRate = totalOrders / sessions;

    // Get revenue by date
    const revenueByDate = await getRevenueByDate(startDate, endDate);

    // Get sales by product
    const { data: orderItems, error: itemsError } = await supabaseAdmin
      .from('order_items')
      .select(`
        product_id,
        products:product_id (name, category),
        quantity,
        price
      `)
      .join('orders', { foreignTable: 'orders', localColumn: 'order_id', foreignColumn: 'id' })
      .gte('orders.created_at', startDateIso)
      .lte('orders.created_at', endDateIso);
    
    if (itemsError) {
      throw itemsError;
    }

    // Process product sales data
    const productMap = {};
    const categoryMap = {};
    
    orderItems?.forEach(item => {
      const productId = item.product_id;
      const productName = item.products?.name || 'Unknown Product';
      const category = item.products?.category || 'Uncategorized';
      const revenue = (parseFloat(item.price) || 0) * (item.quantity || 1);
      
      // Aggregate by product
      if (!productMap[productId]) {
        productMap[productId] = {
          id: productId,
          name: productName,
          quantity: 0,
          revenue: 0
        };
      }
      
      productMap[productId].quantity += (item.quantity || 1);
      productMap[productId].revenue += revenue;
      
      // Aggregate by category
      if (!categoryMap[category]) {
        categoryMap[category] = {
          name: category,
          quantity: 0,
          revenue: 0
        };
      }
      
      categoryMap[category].quantity += (item.quantity || 1);
      categoryMap[category].revenue += revenue;
    });
    
    // Convert to arrays and sort by revenue
    const salesByProduct = Object.values(productMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10); // Top 10 products
    
    const salesByCategory = Object.values(categoryMap)
      .sort((a, b) => b.revenue - a.revenue);

    // Return sales analytics data
    return res.status(200).json({
      summary: {
        totalSales,
        totalOrders,
        averageOrderValue,
        conversionRate
      },
      revenueByDate,
      salesByProduct,
      salesByCategory
    });
  } catch (err) {
    console.error('Error fetching sales analytics:', err);
    return res.status(500).json({ error: 'Failed to fetch sales analytics' });
  }
}

/**
 * Calculate date range based on period or use provided dates
 * 
 * @param {string} period - Time period (day, week, month, year)
 * @param {string} startDateStr - Start date string
 * @param {string} endDateStr - End date string
 * @returns {Object} - Start and end dates
 */
function calculateDateRange(period, startDateStr, endDateStr) {
  // Use provided dates if available
  if (startDateStr && endDateStr) {
    return {
      startDate: new Date(startDateStr),
      endDate: new Date(endDateStr)
    };
  }
  
  // Calculate date range based on period
  const now = new Date();
  let startDate = new Date(now);
  
  switch (period) {
    case 'day':
      startDate.setHours(0, 0, 0, 0);
      break;
    case 'week':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'year':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    case 'month':
    default:
      startDate.setMonth(startDate.getMonth() - 1);
  }
  
  return {
    startDate,
    endDate: now
  };
}

/**
 * Get revenue by date within date range
 * 
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @returns {Array} - Revenue by date
 */
async function getRevenueByDate(startDate, endDate) {
  // Determine appropriate date grouping based on date range
  const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
  let dateFormat, dateGroup;
  
  if (daysDiff <= 7) {
    // For 7 days or less, group by hour
    dateFormat = 'YYYY-MM-DD HH24';
    dateGroup = 'hour';
  } else if (daysDiff <= 31) {
    // For a month or less, group by day
    dateFormat = 'YYYY-MM-DD';
    dateGroup = 'day';
  } else if (daysDiff <= 365) {
    // For a year or less, group by week
    dateFormat = 'YYYY-WW';
    dateGroup = 'week';
  } else {
    // For more than a year, group by month
    dateFormat = 'YYYY-MM';
    dateGroup = 'month';
  }
  
  // Query revenue by date
  const { data, error } = await supabaseAdmin
    .from('orders')
    .select('created_at, total_amount')
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString());
  
  if (error) {
    throw error;
  }
  
  // Group by date format
  const revenueMap = {};
  
  data?.forEach(order => {
    let dateKey;
    const orderDate = new Date(order.created_at);
    
    switch (dateGroup) {
      case 'hour':
        dateKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}-${String(orderDate.getDate()).padStart(2, '0')} ${String(orderDate.getHours()).padStart(2, '0')}`;
        break;
      case 'day':
        dateKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}-${String(orderDate.getDate()).padStart(2, '0')}`;
        break;
      case 'week':
        // Calculate week number
        const firstDayOfYear = new Date(orderDate.getFullYear(), 0, 1);
        const pastDaysOfYear = (orderDate - firstDayOfYear) / 86400000;
        const weekNum = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
        dateKey = `${orderDate.getFullYear()}-${String(weekNum).padStart(2, '0')}`;
        break;
      case 'month':
        dateKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
        break;
    }
    
    if (!revenueMap[dateKey]) {
      revenueMap[dateKey] = {
        date: dateKey,
        revenue: 0
      };
    }
    
    revenueMap[dateKey].revenue += parseFloat(order.total_amount) || 0;
  });
  
  // Convert to array and sort by date
  return Object.values(revenueMap).sort((a, b) => a.date.localeCompare(b.date));
}
