-- Fix Application Tokens Database Relationships
-- Resolves PGRST200 foreign key relationship errors

-- First, let's check and fix the application_tokens table structure
-- Drop existing foreign key constraints that might be causing issues
ALTER TABLE IF EXISTS public.application_tokens 
DROP CONSTRAINT IF EXISTS application_tokens_created_by_fkey;

ALTER TABLE IF EXISTS public.application_tokens 
DROP CONSTRAINT IF EXISTS application_tokens_user_id_fkey;

ALTER TABLE IF EXISTS public.application_tokens 
DROP CONSTRAINT IF EXISTS application_tokens_application_id_fkey;

-- Ensure the application_tokens table exists with correct structure
CREATE TABLE IF NOT EXISTS public.application_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  token TEXT UNIQUE NOT NULL,
  user_id UUID NOT NULL,
  application_id UUID,
  token_type TEXT DEFAULT 'application_access',
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Ensure the artist_braider_applications table exists
CREATE TABLE IF NOT EXISTS public.artist_braider_applications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  application_type TEXT NOT NULL CHECK (application_type IN ('artist', 'braider')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected', 'cancelled')),
  personal_info JSONB DEFAULT '{}',
  experience_info JSONB DEFAULT '{}',
  availability_info JSONB DEFAULT '{}',
  portfolio_info JSONB DEFAULT '{}',
  additional_info JSONB DEFAULT '{}',
  admin_notes TEXT,
  reviewed_by UUID,
  reviewed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add proper foreign key constraints
-- Reference to auth.users (Supabase auth table)
ALTER TABLE public.application_tokens 
ADD CONSTRAINT application_tokens_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Reference to artist_braider_applications
ALTER TABLE public.application_tokens 
ADD CONSTRAINT application_tokens_application_id_fkey 
FOREIGN KEY (application_id) REFERENCES public.artist_braider_applications(id) ON DELETE CASCADE;

-- Add foreign key for artist_braider_applications
ALTER TABLE public.artist_braider_applications 
ADD CONSTRAINT artist_braider_applications_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add foreign key for reviewed_by
ALTER TABLE public.artist_braider_applications 
ADD CONSTRAINT artist_braider_applications_reviewed_by_fkey 
FOREIGN KEY (reviewed_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_application_tokens_user_id ON public.application_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_application_tokens_token ON public.application_tokens(token);
CREATE INDEX IF NOT EXISTS idx_application_tokens_expires_at ON public.application_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_artist_braider_applications_user_id ON public.artist_braider_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_artist_braider_applications_status ON public.artist_braider_applications(status);

-- Enable Row Level Security
ALTER TABLE public.application_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_braider_applications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for application_tokens
DROP POLICY IF EXISTS "Users can view their own tokens" ON public.application_tokens;
CREATE POLICY "Users can view their own tokens" 
  ON public.application_tokens FOR SELECT 
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can manage all tokens" ON public.application_tokens;
CREATE POLICY "Admins can manage all tokens" 
  ON public.application_tokens FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    )
  );

-- Create RLS policies for artist_braider_applications
DROP POLICY IF EXISTS "Users can view their own applications" ON public.artist_braider_applications;
CREATE POLICY "Users can view their own applications" 
  ON public.artist_braider_applications FOR SELECT 
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own applications" ON public.artist_braider_applications;
CREATE POLICY "Users can update their own applications" 
  ON public.artist_braider_applications FOR UPDATE 
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can manage all applications" ON public.artist_braider_applications;
CREATE POLICY "Admins can manage all applications" 
  ON public.artist_braider_applications FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    )
  );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.application_tokens TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.artist_braider_applications TO authenticated;

-- Update the token validation function to handle the correct relationships
CREATE OR REPLACE FUNCTION public.validate_application_token(token_value text)
RETURNS TABLE(
  is_valid boolean,
  user_id uuid,
  application_id uuid,
  error_message text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  token_record RECORD;
  app_record RECORD;
BEGIN
  -- Initialize return values
  is_valid := false;
  user_id := NULL;
  application_id := NULL;
  error_message := NULL;

  -- Check if token exists and is not expired
  SELECT 
    at.user_id,
    at.application_id,
    at.expires_at,
    at.used_at,
    at.token_type
  INTO token_record
  FROM public.application_tokens at
  WHERE at.token = token_value;

  -- If token not found
  IF NOT FOUND THEN
    error_message := 'Invalid or expired token';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if token has expired
  IF token_record.expires_at < NOW() THEN
    error_message := 'Token has expired';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if token has already been used (for one-time tokens)
  IF token_record.used_at IS NOT NULL AND token_record.token_type = 'application_access' THEN
    error_message := 'Token has already been used';
    RETURN NEXT;
    RETURN;
  END IF;

  -- Check if associated application exists (if application_id is not null)
  IF token_record.application_id IS NOT NULL THEN
    SELECT 
      aba.id,
      aba.application_type,
      aba.status
    INTO app_record
    FROM public.artist_braider_applications aba
    WHERE aba.id = token_record.application_id;

    -- If application not found
    IF NOT FOUND THEN
      error_message := 'Associated application not found';
      RETURN NEXT;
      RETURN;
    END IF;

    -- Check if application is in a valid state for token usage
    IF app_record.status NOT IN ('pending', 'under_review') THEN
      error_message := 'Application is not in a valid state for token access';
      RETURN NEXT;
      RETURN;
    END IF;
  END IF;

  -- Token is valid
  is_valid := true;
  user_id := token_record.user_id;
  application_id := token_record.application_id;
  error_message := NULL;

  RETURN NEXT;
  RETURN;
END;
$$;

-- Create a function to safely get application tokens for a user
CREATE OR REPLACE FUNCTION public.get_user_application_tokens(target_user_id uuid)
RETURNS TABLE(
  id uuid,
  token text,
  token_type text,
  expires_at timestamptz,
  used_at timestamptz,
  created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the requesting user is admin or the token owner
  IF NOT (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() 
      AND role IN ('dev', 'admin')
    ) OR auth.uid() = target_user_id
  ) THEN
    RAISE EXCEPTION 'Insufficient permissions to view tokens';
  END IF;

  RETURN QUERY
  SELECT 
    at.id,
    at.token,
    at.token_type,
    at.expires_at,
    at.used_at,
    at.created_at
  FROM public.application_tokens at
  WHERE at.user_id = target_user_id
  ORDER BY at.created_at DESC;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.validate_application_token(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_application_tokens(uuid) TO authenticated;

-- Add comments
COMMENT ON TABLE public.application_tokens IS 'Secure tokens for Artist/Braider application access';
COMMENT ON TABLE public.artist_braider_applications IS 'Artist and Braider application submissions';
COMMENT ON FUNCTION public.validate_application_token(text) IS 'Validate an application token with proper relationship handling';
COMMENT ON FUNCTION public.get_user_application_tokens(uuid) IS 'Safely retrieve application tokens for a user';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Application tokens database relationships fixed!';
  RAISE NOTICE '🔗 Foreign key constraints properly configured';
  RAISE NOTICE '🔒 Row Level Security policies updated';
  RAISE NOTICE '⚡ Performance indexes created';
END $$;
