import { useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import ArtistBraiderDashboard from '@/components/admin/ArtistBraiderDashboard'
import styles from '@/styles/admin/ArtistDashboard.module.css'

export default function ArtistDashboardPage() {
  const router = useRouter()
  const { user, role, loading } = useAuth()

  // Redirect non-artist/braider users to main admin dashboard
  useEffect(() => {
    if (!loading && user && role && !['artist', 'braider'].includes(role)) {
      router.replace('/admin/dashboard')
    }
  }, [user, role, loading, router])

  // Don't render anything while checking auth or if user is not artist/braider
  if (loading || !user || !['artist', 'braider'].includes(role)) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <ProtectedRoute staffOnly>
      <Head>
        <title>
          {role === 'artist' ? 'Artist' : 'Braider'} Dashboard | Ocean Soul Sparkles
        </title>
        <meta name="description" content={`${role === 'artist' ? 'Artist' : 'Braider'} dashboard for Ocean Soul Sparkles team members`} />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.breadcrumb}>
            <span className={styles.breadcrumbItem}>Ocean Soul Sparkles</span>
            <span className={styles.breadcrumbSeparator}>›</span>
            <span className={styles.breadcrumbItem}>
              {role === 'artist' ? 'Artist' : 'Braider'} Dashboard
            </span>
          </div>

          <div className={styles.headerActions}>
            <button 
              onClick={() => router.push('/admin/profile')}
              className={styles.profileButton}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
              Profile
            </button>

            <button 
              onClick={() => router.push('/admin/schedule')}
              className={styles.scheduleButton}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                <line x1="16" y1="2" x2="16" y2="6"/>
                <line x1="8" y1="2" x2="8" y2="6"/>
                <line x1="3" y1="10" x2="21" y2="10"/>
              </svg>
              Schedule
            </button>

            <button 
              onClick={() => router.push('/admin/payments')}
              className={styles.paymentsButton}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"/>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              </svg>
              Payments
            </button>

            <button 
              onClick={() => {
                // Sign out functionality
                const { signOut } = require('@/contexts/AuthContext')
                signOut()
                router.push('/')
              }}
              className={styles.signOutButton}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
              </svg>
              Sign Out
            </button>
          </div>
        </div>

        <main className={styles.main}>
          <ArtistBraiderDashboard />
        </main>

        <footer className={styles.footer}>
          <div className={styles.footerContent}>
            <p>
              <strong>Ocean Soul Sparkles</strong> - {role === 'artist' ? 'Artist' : 'Braider'} Portal
            </p>
            <div className={styles.footerLinks}>
              <button onClick={() => router.push('/admin/help')}>
                Help & Support
              </button>
              <span className={styles.separator}>|</span>
              <button onClick={() => router.push('/')}>
                Main Website
              </button>
            </div>
          </div>
        </footer>
      </div>
    </ProtectedRoute>
  )
}
