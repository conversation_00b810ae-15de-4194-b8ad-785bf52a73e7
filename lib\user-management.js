/**
 * User Management API Client
 *
 * This module provides a client-side interface for the user management API.
 * It handles authentication, error handling, and data formatting.
 */
import apiFetch from './api-fetch';
import { getApiUrl } from './cross-origin';

/**
 * Fetch users with optional filtering, sorting, and pagination
 *
 * @param {Object} options - Query options
 * @param {number} options.page - Page number (default: 1)
 * @param {number} options.limit - Items per page (default: 10)
 * @param {string} options.sortBy - Sort field (default: 'created_at')
 * @param {string} options.sortOrder - Sort order ('asc' or 'desc', default: 'desc')
 * @param {string} options.search - Search query (optional)
 * @param {string} options.role - Role filter (optional)
 * @returns {Promise<Object>} - { users, total, page, limit, pages }
 */
export async function fetchUsers({
  page = 1,
  limit = 10,
  sortBy = 'created_at',
  sortOrder = 'desc',
  search = '',
  role = 'all'
} = {}) {
  try {
    console.log('User Management: Fetching users with options:', { page, limit, sortBy, sortOrder, search, role });

    // Build query string
    const queryParams = new URLSearchParams({
      page,
      limit,
      sortBy,
      sortOrder
    });

    if (search) {
      queryParams.append('search', search);
    }

    if (role && role !== 'all') {
      queryParams.append('role', role);
    }

    // Make API request with proper URL
    const apiUrl = getApiUrl(`/api/admin/users/list?${queryParams.toString()}`);
    console.log('User Management: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl);

    console.log('User Management: Fetched users successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error fetching users:', error);
    throw error;
  }
}

/**
 * Update a user's role
 *
 * @param {string} userId - User ID
 * @param {string} role - New role
 * @returns {Promise<Object>} - Updated user
 */
export async function updateUserRole(userId, role) {  try {
    console.log(`User Management: Updating role for user ${userId} to ${role}`);
    console.log(`User Management: Role type: ${typeof role}, value: "${role}"`);

    // Validate inputs before sending
    if (!userId || !role) {
      throw new Error('User ID and role are required');
    }

    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user'];
    if (!validRoles.includes(role)) {
      throw new Error(`Invalid role: ${role}. Must be one of: ${validRoles.join(', ')}`);
    }

    // Try the main endpoint first
    try {
      console.log('User Management: Trying main set-role endpoint...');
      const apiUrl = getApiUrl('/api/admin/users/set-role');
      console.log('User Management: Using API URL:', apiUrl);

      const response = await apiFetch(apiUrl, {
        method: 'POST',
        body: JSON.stringify({ userId, role })
      });

      console.log('User Management: Main endpoint succeeded');
      return response;
    } catch (mainError) {
      console.warn('User Management: Main endpoint failed, trying simplified endpoint...', mainError.message);

      // Fallback to simplified endpoint
      try {
        const fallbackUrl = getApiUrl('/api/admin/users/simple-set-role');
        console.log('User Management: Using fallback URL:', fallbackUrl);

        const response = await apiFetch(fallbackUrl, {
          method: 'POST',
          body: JSON.stringify({ userId, role })
        });

        console.log('User Management: Fallback endpoint succeeded');
        return response;
      } catch (fallbackError) {
        console.error('User Management: Both endpoints failed');
        console.error('Main error:', mainError.message);
        console.error('Fallback error:', fallbackError.message);
        throw new Error(`Role update failed: ${fallbackError.message}`);
      }
    }
  } catch (error) {
    console.error('User Management: Error updating role:', error);
    throw error;
  }
}

/**
 * Create a new user
 *
 * @param {Object} userData - User data
 * @param {string} userData.email - Email address
 * @param {string} userData.password - Password
 * @param {string} userData.role - Role
 * @param {string} userData.name - Name
 * @param {string} userData.phone - Phone (optional)
 * @param {string} userData.notes - Notes (optional)
 * @param {boolean} userData.sendWelcomeEmail - Send welcome email
 * @returns {Promise<Object>} - Created user
 */
export async function createUser({ email, password, role, name, phone, notes, sendWelcomeEmail = true }) {
  try {
    console.log(`User Management: Creating new user with email ${email}`);

    // Prepare API URL
    const apiUrl = getApiUrl('/api/admin/users/create');
    console.log('User Management: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        role,
        name,
        phone,
        notes,
        sendWelcomeEmail
      })
    });

    console.log('User Management: User created successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error creating user:', error);
    throw error;
  }
}

/**
 * Get user statistics for dashboard
 *
 * @returns {Promise<Object>} - User statistics
 */
export async function getUserStats() {
  try {
    console.log('User Management: Fetching user statistics');

    const apiUrl = getApiUrl('/api/admin/users/stats');
    const response = await apiFetch(apiUrl);

    console.log('User Management: User statistics fetched successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error fetching user statistics:', error);
    throw error;
  }
}

/**
 * Get recent user activity
 *
 * @param {number} limit - Number of activities to fetch
 * @returns {Promise<Array>} - Recent activities
 */
export async function getRecentActivity(limit = 10) {
  try {
    console.log('User Management: Fetching recent activity');

    const apiUrl = getApiUrl(`/api/admin/users/activity?limit=${limit}`);
    const response = await apiFetch(apiUrl);

    console.log('User Management: Recent activity fetched successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error fetching recent activity:', error);
    throw error;
  }
}

/**
 * Submit artist/braider application
 *
 * @param {Object} applicationData - Application data
 * @returns {Promise<Object>} - Application result
 */
export async function submitApplication(applicationData) {
  try {
    console.log('User Management: Submitting artist/braider application');

    const apiUrl = getApiUrl('/api/admin/users/applications');
    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify(applicationData)
    });

    console.log('User Management: Application submitted successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error submitting application:', error);
    throw error;
  }
}

/**
 * Get artist/braider applications
 *
 * @param {string} status - Filter by status (optional)
 * @returns {Promise<Array>} - Applications
 */
export async function getApplications(status = null) {
  try {
    console.log('User Management: Fetching applications');

    const queryParams = status ? `?status=${status}` : '';
    const apiUrl = getApiUrl(`/api/admin/users/applications${queryParams}`);
    const response = await apiFetch(apiUrl);

    console.log('User Management: Applications fetched successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error fetching applications:', error);
    throw error;
  }
}

/**
 * Review artist/braider application
 *
 * @param {string} applicationId - Application ID
 * @param {string} status - New status (approved/rejected)
 * @param {string} notes - Review notes
 * @returns {Promise<Object>} - Review result
 */
export async function reviewApplication(applicationId, status, notes) {
  try {
    console.log('User Management: Reviewing application');

    const apiUrl = getApiUrl(`/api/admin/users/applications/${applicationId}/review`);
    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify({ status, notes })
    });

    console.log('User Management: Application reviewed successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error reviewing application:', error);
    throw error;
  }
}

/**
 * Check user status to determine if they can be deleted or should be deactivated
 *
 * @param {string} userId - User ID to check
 * @returns {Promise<Object>} - User status information
 */
export async function checkUserStatus(userId) {
  try {
    console.log(`User Management: Checking status for user ${userId}`);

    const apiUrl = getApiUrl(`/api/admin/users/status?userId=${encodeURIComponent(userId)}`);
    const response = await apiFetch(apiUrl, {
      method: 'GET'
    });

    console.log('User Management: User status retrieved successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error checking user status:', error);
    throw error;
  }
}

/**
 * Deactivate or reactivate a user account
 *
 * @param {string} userId - User ID to deactivate/reactivate
 * @param {boolean} isActive - Whether to activate (true) or deactivate (false)
 * @param {string} reason - Optional reason for the action
 * @returns {Promise<Object>} - Deactivation result
 */
export async function deactivateUser(userId, isActive = false, reason = '') {
  try {
    console.log(`User Management: ${isActive ? 'Reactivating' : 'Deactivating'} user ${userId}`);

    const apiUrl = getApiUrl('/api/admin/users/deactivate');
    const response = await apiFetch(apiUrl, {
      method: 'PATCH',
      body: JSON.stringify({ userId, isActive, reason })
    });

    console.log(`User Management: User ${isActive ? 'reactivated' : 'deactivated'} successfully`);
    return response;
  } catch (error) {
    console.error(`User Management: Error ${isActive ? 'reactivating' : 'deactivating'} user:`, error);
    throw error;
  }
}

/**
 * Delete a user (with restrictions for active users)
 *
 * @param {string} userId - User ID to delete
 * @returns {Promise<Object>} - Deletion result
 */
export async function deleteUser(userId) {
  try {
    console.log(`User Management: Deleting user ${userId}`);

    const apiUrl = getApiUrl('/api/admin/users/delete');
    const response = await apiFetch(apiUrl, {
      method: 'DELETE',
      body: JSON.stringify({ userId })
    });

    console.log('User Management: User deleted successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error deleting user:', error);
    throw error;
  }
}

export default {
  fetchUsers,
  updateUserRole,
  createUser,
  getUserStats,
  getRecentActivity,
  submitApplication,
  getApplications,
  reviewApplication,
  checkUserStatus,
  deactivateUser,
  deleteUser
};
