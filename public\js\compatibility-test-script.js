/**
 * OceanSoulSparkles Website Compatibility Test Script
 * 
 * This script helps automate compatibility testing across different viewport sizes
 * and can be run in the browser console to check for issues.
 */

// Configuration
const config = {
  // Pages to test
  pages: [
    '/',
    '/about',
    '/services',
    '/gallery',
    '/shop',
    '/book-online',
    '/contact',
    '/checkout',
    '/gift-card'
  ],
  
  // Viewport sizes to test (width x height)
  viewports: [
    { name: 'Mobile Small', width: 320, height: 568 },
    { name: 'Mobile Medium', width: 375, height: 667 },
    { name: 'Mobile Large', width: 428, height: 926 },
    { name: 'Tablet Small', width: 768, height: 1024 },
    { name: 'Tablet Large', width: 1024, height: 1366 },
    { name: 'Desktop Small', width: 1280, height: 800 },
    { name: 'Desktop Medium', width: 1440, height: 900 },
    { name: 'Desktop Large', width: 1920, height: 1080 }
  ],
  
  // Elements to test on each page
  elements: [
    { name: 'Navigation', selector: 'header' },
    { name: '<PERSON> <PERSON>', selector: '[class*="hero"], [class*="Hero"]' },
    { name: 'Main Content', selector: 'main' },
    { name: 'Footer', selector: 'footer' },
    { name: 'Forms', selector: 'form' },
    { name: 'Buttons', selector: 'button, a.button, [class*="button"], [class*="Button"]' },
    { name: 'Images', selector: 'img' },
    { name: 'Service Cards', selector: '[class*="serviceCard"], [class*="ServiceCard"]' },
    { name: 'Gallery Items', selector: '[class*="galleryItem"], [class*="GalleryItem"]' }
  ]
};

// Results storage
const testResults = {
  viewport: {},
  elements: {},
  performance: {},
  errors: []
};

/**
 * Run viewport size tests
 */
async function testViewports() {
  console.log('🔍 Starting viewport size tests...');
  
  for (const viewport of config.viewports) {
    console.log(`Testing viewport: ${viewport.name} (${viewport.width}x${viewport.height})`);
    
    // Resize window (for testing in browser)
    window.resizeTo(viewport.width, viewport.height);
    
    // Wait for resize to take effect
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check for horizontal scrolling
    const hasHorizontalScroll = document.documentElement.scrollWidth > viewport.width;
    
    testResults.viewport[viewport.name] = {
      width: viewport.width,
      height: viewport.height,
      hasHorizontalScroll,
      actualWidth: window.innerWidth,
      actualHeight: window.innerHeight
    };
    
    console.log(`✓ ${viewport.name} tested. Horizontal scroll: ${hasHorizontalScroll ? '❌' : '✅'}`);
  }
  
  console.log('✅ Viewport tests completed');
}

/**
 * Test element visibility and rendering
 */
async function testElements() {
  console.log('🔍 Starting element tests...');
  
  for (const element of config.elements) {
    console.log(`Testing element: ${element.name} (${element.selector})`);
    
    const elements = document.querySelectorAll(element.selector);
    const isVisible = Array.from(elements).some(el => {
      const rect = el.getBoundingClientRect();
      return (
        rect.width > 0 &&
        rect.height > 0 &&
        rect.top < window.innerHeight &&
        rect.left < window.innerWidth &&
        getComputedStyle(el).display !== 'none' &&
        getComputedStyle(el).visibility !== 'hidden'
      );
    });
    
    const overflowIssues = Array.from(elements).some(el => {
      const rect = el.getBoundingClientRect();
      return rect.right > window.innerWidth;
    });
    
    testResults.elements[element.name] = {
      selector: element.selector,
      count: elements.length,
      isVisible,
      overflowIssues
    };
    
    console.log(`✓ ${element.name} tested. Found: ${elements.length}, Visible: ${isVisible ? '✅' : '❌'}, Overflow: ${overflowIssues ? '❌' : '✅'}`);
  }
  
  console.log('✅ Element tests completed');
}

/**
 * Test performance metrics
 */
async function testPerformance() {
  console.log('🔍 Starting performance tests...');
  
  // Basic performance metrics
  const perfEntries = performance.getEntriesByType('navigation');
  
  if (perfEntries.length > 0) {
    const navEntry = perfEntries[0];
    
    testResults.performance = {
      loadTime: navEntry.loadEventEnd - navEntry.startTime,
      domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.startTime,
      firstPaint: 0, // Will be populated if available
      firstContentfulPaint: 0 // Will be populated if available
    };
    
    // Try to get paint metrics if available
    const paintEntries = performance.getEntriesByType('paint');
    for (const entry of paintEntries) {
      if (entry.name === 'first-paint') {
        testResults.performance.firstPaint = entry.startTime;
      }
      if (entry.name === 'first-contentful-paint') {
        testResults.performance.firstContentfulPaint = entry.startTime;
      }
    }
    
    console.log(`✓ Performance tested. Load time: ${testResults.performance.loadTime.toFixed(2)}ms`);
  } else {
    console.log('❌ Performance entries not available');
  }
  
  console.log('✅ Performance tests completed');
}

/**
 * Test for JavaScript errors
 */
function setupErrorListener() {
  console.log('🔍 Setting up error listener...');
  
  window.addEventListener('error', (event) => {
    testResults.errors.push({
      message: event.message,
      source: event.filename,
      line: event.lineno,
      column: event.colno,
      timestamp: new Date().toISOString()
    });
    
    console.error(`❌ JavaScript error detected: ${event.message}`);
  });
  
  console.log('✅ Error listener set up');
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting compatibility tests...');
  
  setupErrorListener();
  
  await testViewports();
  await testElements();
  await testPerformance();
  
  console.log('🏁 All tests completed!');
  console.log('📊 Test results:', testResults);
  
  // Format results as markdown for easy copying
  const markdown = formatResultsAsMarkdown();
  console.log('📋 Markdown results:\n', markdown);
  
  return testResults;
}

/**
 * Format results as markdown
 */
function formatResultsAsMarkdown() {
  let markdown = '## Compatibility Test Results\n\n';
  
  // Viewport results
  markdown += '### Viewport Tests\n\n';
  markdown += '| Viewport | Size | Horizontal Scroll | Status |\n';
  markdown += '|----------|------|-------------------|--------|\n';
  
  for (const [name, data] of Object.entries(testResults.viewport)) {
    const status = data.hasHorizontalScroll ? '❌ Issue' : '✅ Pass';
    markdown += `| ${name} | ${data.width}x${data.height} | ${data.hasHorizontalScroll ? 'Yes' : 'No'} | ${status} |\n`;
  }
  
  // Element results
  markdown += '\n### Element Tests\n\n';
  markdown += '| Element | Count | Visible | Overflow | Status |\n';
  markdown += '|---------|-------|---------|----------|--------|\n';
  
  for (const [name, data] of Object.entries(testResults.elements)) {
    const status = (!data.isVisible || data.overflowIssues) ? '❌ Issue' : '✅ Pass';
    markdown += `| ${name} | ${data.count} | ${data.isVisible ? 'Yes' : 'No'} | ${data.overflowIssues ? 'Yes' : 'No'} | ${status} |\n`;
  }
  
  // Performance results
  markdown += '\n### Performance Tests\n\n';
  markdown += '| Metric | Time (ms) |\n';
  markdown += '|--------|----------|\n';
  markdown += `| Load Time | ${testResults.performance.loadTime?.toFixed(2) || 'N/A'} |\n`;
  markdown += `| DOM Content Loaded | ${testResults.performance.domContentLoaded?.toFixed(2) || 'N/A'} |\n`;
  markdown += `| First Paint | ${testResults.performance.firstPaint?.toFixed(2) || 'N/A'} |\n`;
  markdown += `| First Contentful Paint | ${testResults.performance.firstContentfulPaint?.toFixed(2) || 'N/A'} |\n`;
  
  // Error results
  markdown += '\n### JavaScript Errors\n\n';
  
  if (testResults.errors.length === 0) {
    markdown += 'No JavaScript errors detected.\n';
  } else {
    markdown += '| Error | Source | Line:Column |\n';
    markdown += '|-------|--------|-------------|\n';
    
    for (const error of testResults.errors) {
      markdown += `| ${error.message} | ${error.source} | ${error.line}:${error.column} |\n`;
    }
  }
  
  return markdown;
}

// Export functions for use in browser console
window.compatibilityTests = {
  runAllTests,
  testViewports,
  testElements,
  testPerformance,
  getResults: () => testResults
};

console.log('💻 Compatibility test script loaded. Run tests with: compatibilityTests.runAllTests()');
