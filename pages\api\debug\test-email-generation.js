/**
 * Debug API endpoint to test email generation for Artist/Braider onboarding
 * This helps verify that emails are being generated with correct tokens and URLs
 */

import { getAdminClient } from '@/lib/supabase'
import { generateWelcomeEmail } from '@/lib/email-templates'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { role = 'artist', includeToken = true } = req.body

    // Validate role
    if (!['artist', 'braider'].includes(role)) {
      return res.status(400).json({ error: 'Invalid role. Must be artist or braider.' })
    }

    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    let applicationToken = null

    // Generate token if requested
    if (includeToken) {
      const { data: generatedToken, error: tokenError } = await adminClient
        .rpc('generate_application_token')

      if (tokenError) {
        console.error('Token generation error:', tokenError)
        return res.status(500).json({ 
          error: 'Failed to generate token',
          details: tokenError.message 
        })
      }

      applicationToken = generatedToken
    }

    // Create test user data
    const testUser = {
      name: `Test ${role === 'artist' ? 'Artist' : 'Braider'}`,
      email: '<EMAIL>',
      role: role,
      applicationToken: applicationToken
    }

    // Generate email template
    const emailTemplate = generateWelcomeEmail(testUser)

    // Extract the application link from the email (improved regex)
    const linkRegex = /href="([^"]*apply\/[^"]*)"[^>]*>\s*Complete Application Form\s*<\/a>/i
    const linkMatch = emailTemplate.htmlBody.match(linkRegex)
    const applicationLink = linkMatch ? linkMatch[1] : null

    // Also try a broader search for any apply links
    const broadLinkRegex = /https:\/\/[^"]*apply\/[^"?]*/g
    const allApplyLinks = emailTemplate.htmlBody.match(broadLinkRegex) || []

    // Parse the link to check components
    let linkAnalysis = null
    if (applicationLink) {
      try {
        const url = new URL(applicationLink)
        linkAnalysis = {
          fullUrl: applicationLink,
          domain: url.hostname,
          path: url.pathname,
          token: url.searchParams.get('token'),
          hasToken: !!url.searchParams.get('token')
        }
      } catch (urlError) {
        linkAnalysis = { error: 'Invalid URL format', url: applicationLink }
      }
    }

    return res.status(200).json({
      success: true,
      testData: {
        role: role,
        tokenRequested: includeToken,
        tokenGenerated: !!applicationToken,
        token: applicationToken ? `${applicationToken.substring(0, 8)}...` : null
      },
      emailTemplate: {
        subject: emailTemplate.subject,
        hasHtmlBody: !!emailTemplate.htmlBody,
        hasTextBody: !!emailTemplate.textBody,
        htmlBodyLength: emailTemplate.htmlBody?.length || 0
      },
      applicationLink: linkAnalysis,
      rawLink: applicationLink,
      allApplyLinks: allApplyLinks,
      emailPreview: {
        // Include first 500 chars of HTML for preview
        htmlPreview: emailTemplate.htmlBody?.substring(0, 500) + '...',
        textPreview: emailTemplate.textBody?.substring(0, 300) + '...'
      }
    })

  } catch (error) {
    console.error('Email generation test error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    })
  }
}
