import { useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from '@/styles/ShopHeroShowcase.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * ShopHeroShowcase component with floating product animations and interactive elements
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Hero section title
 * @param {string} props.subtitle - Hero section subtitle
 * @param {string} props.backgroundImage - URL of the background image
 * @param {Array} props.products - Array of featured product objects to display
 * @param {string} props.ctaText - Call to action button text
 * @param {string} props.ctaLink - Call to action button link
 * @returns {JSX.Element}
 */
const ShopHeroShowcase = ({ 
  title = 'Our Shop',
  subtitle = 'Eco-friendly products to help you sparkle',
  backgroundImage = '/images/shop-hero.jpg',
  products = [],
  ctaText = 'Shop Now',
  ctaLink = '#products',
  ...props 
}) => {
  const showcaseRef = useRef(null);
  const floatingProductsRef = useRef(null);
  
  useEffect(() => {
    // Create floating animation effect for product images
    const handleMouseMove = (e) => {
      if (!floatingProductsRef.current || !showcaseRef.current) return;
      
      const { clientX, clientY } = e;
      const { left, top, width, height } = showcaseRef.current.getBoundingClientRect();
      
      // Calculate mouse position relative to the container
      const x = (clientX - left) / width - 0.5;
      const y = (clientY - top) / height - 0.5;
      
      // Apply parallax effect to floating products
      floatingProductsRef.current.style.transform = `translate(${x * 35}px, ${y * 35}px)`;
    };
    
    // Parallax effect on scroll
    const handleScroll = () => {
      if (!showcaseRef.current) return;
      
      const scrollPosition = window.scrollY;
      
      // Parallax effect for background
      showcaseRef.current.style.backgroundPositionY = `${scrollPosition * 0.4}px`;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  
  // Default products if none provided
  const defaultProducts = [
    {
      id: 1,
      name: 'Biodegradable Chunky Glitter',
      image: '/images/products/biodegradable-glitter.jpg',
      color: '#4ECDC4'
    },
    {
      id: 2,
      name: 'Face Painting Kit',
      image: '/images/services/face-paint.jpg',
      color: '#FF6B6B'
    },
    {
      id: 3,
      name: 'Eco-Friendly Glitter Gel',
      image: '/images/gallery/gallery-5.jpg',
      color: '#FFE66D'
    },
    {
      id: 4,
      name: 'Glitter Application Brushes',
      image: '/images/products/product-4.jpg',
      color: '#1A73E8'
    }
  ];
  
  const showcaseProducts = products.length > 0 ? products : defaultProducts;
  
  return (
    <section 
      ref={showcaseRef}
      className={styles.shopShowcase}
      style={{ backgroundImage: `url(${backgroundImage})` }}
      {...props}
    >
      <div className={styles.overlayGradient}></div>
      <div ref={floatingProductsRef} className={styles.floatingProducts}>
        {showcaseProducts.map((product, index) => (
          <div 
            key={index} 
            className={styles.floatingProduct}
            style={{ 
              animationDelay: `${index * 0.3}s`
            }}
          >
            <div 
              className={styles.productImageContainer}
              style={{ 
                borderColor: product.color,
                boxShadow: `0 10px 25px ${product.color}40`
              }}
            >
              <img 
                src={product.image} 
                alt={product.name} 
                className={styles.productImage} 
              />
            </div>
            <div 
              className={styles.productGlow}
              style={{ background: product.color }}
            ></div>
          </div>
        ))}
      </div>
      
      <div className={styles.showcaseContent}>
        <AnimatedSection animation="fade-in" delay={100}>
          <h1 className={styles.showcaseTitle}>{title}</h1>
          <p className={styles.showcaseSubtitle}>{subtitle}</p>
          
          <div className={styles.showcaseCta}>
            <Link href={ctaLink} className={styles.ctaButton}>
              <span className={styles.btnText}>{ctaText}</span>
              <span className={styles.btnIcon}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </span>
            </Link>
          </div>
        </AnimatedSection>
      </div>
      
      <div className={styles.scrollIndicator}>
        <div className={styles.mouseIcon}></div>
        <span>Explore our products</span>
      </div>
    </section>
  );
};

export default ShopHeroShowcase; 