/**
 * Manual test script for Customer API endpoints
 * 
 * This script provides a simple way to test the Customer API endpoints
 * without relying on complex test frameworks.
 * 
 * Usage:
 *   node scripts/test-customer-api.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

// Test data
let testCustomerId = null;
let session = null;

/**
 * Make an authenticated API request
 */
async function makeRequest(url, method = 'GET', body = null) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  if (session?.access_token) {
    headers['Authorization'] = `Bearer ${session.access_token}`;
  }
  
  const options = {
    method,
    headers,
    credentials: 'include'
  };
  
  if (body && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(body);
  }
  
  try {
    const response = await fetch(url, options);
    
    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    return {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      data
    };
  } catch (error) {
    console.error('Request error:', error);
    return { error: error.message };
  }
}

/**
 * Sign in as admin
 */
async function signIn() {
  console.log('\n--- Signing in ---');
  
  // Get credentials from environment or prompt
  const email = process.env.TEST_ADMIN_EMAIL;
  const password = process.env.TEST_ADMIN_PASSWORD;
  
  if (!email || !password) {
    console.error('Error: TEST_ADMIN_EMAIL and TEST_ADMIN_PASSWORD must be set in .env');
    process.exit(1);
  }
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) throw error;
    
    session = data.session;
    console.log('Signed in successfully as:', data.user.email);
    return data;
  } catch (error) {
    console.error('Sign in error:', error);
    process.exit(1);
  }
}

/**
 * Test creating a customer
 */
async function testCreateCustomer() {
  console.log('\n--- Testing Create Customer ---');
  
  const timestamp = new Date().getTime();
  const customerData = {
    name: `Test Customer ${timestamp}`,
    email: `test-${timestamp}@example.com`,
    phone: '0412345678',
    city: 'Sydney',
    state: 'NSW',
    marketing_consent: true
  };
  
  console.log('Creating customer:', customerData);
  
  const response = await makeRequest(
    `${API_BASE_URL}/customers`,
    'POST',
    customerData
  );
  
  console.log('Status:', response.status);
  console.log('Response:', response.data);
  
  if (response.status === 201 && response.data.id) {
    testCustomerId = response.data.id;
    console.log('✅ Create customer test passed');
    return response.data;
  } else {
    console.log('❌ Create customer test failed');
    return null;
  }
}

/**
 * Test getting customers list
 */
async function testGetCustomers() {
  console.log('\n--- Testing Get Customers List ---');
  
  const response = await makeRequest(`${API_BASE_URL}/customers`);
  
  console.log('Status:', response.status);
  console.log('Total customers:', response.data.total);
  console.log('First few customers:', response.data.customers.slice(0, 2));
  
  if (response.status === 200 && Array.isArray(response.data.customers)) {
    console.log('✅ Get customers test passed');
    return response.data;
  } else {
    console.log('❌ Get customers test failed');
    return null;
  }
}

/**
 * Test getting a single customer
 */
async function testGetCustomer(customerId) {
  console.log('\n--- Testing Get Customer Details ---');
  console.log('Customer ID:', customerId);
  
  const response = await makeRequest(`${API_BASE_URL}/customers/${customerId}`);
  
  console.log('Status:', response.status);
  console.log('Customer data:', response.data.customer);
  
  if (response.status === 200 && response.data.customer) {
    console.log('✅ Get customer test passed');
    return response.data;
  } else {
    console.log('❌ Get customer test failed');
    return null;
  }
}

/**
 * Test updating a customer
 */
async function testUpdateCustomer(customerId) {
  console.log('\n--- Testing Update Customer ---');
  console.log('Customer ID:', customerId);
  
  const updateData = {
    name: 'Updated Test Customer',
    notes: 'This customer was updated by the test script',
    marketing_consent: false
  };
  
  console.log('Update data:', updateData);
  
  const response = await makeRequest(
    `${API_BASE_URL}/customers/${customerId}`,
    'PUT',
    updateData
  );
  
  console.log('Status:', response.status);
  console.log('Updated customer:', response.data);
  
  if (response.status === 200 && response.data.name === updateData.name) {
    console.log('✅ Update customer test passed');
    return response.data;
  } else {
    console.log('❌ Update customer test failed');
    return null;
  }
}

/**
 * Test customer export
 */
async function testExportCustomers() {
  console.log('\n--- Testing Export Customers ---');
  
  const response = await makeRequest(`${API_BASE_URL}/customers/export?format=json`);
  
  console.log('Status:', response.status);
  console.log('Export count:', Array.isArray(response.data) ? response.data.length : 'N/A');
  
  if (response.status === 200 && Array.isArray(response.data)) {
    console.log('✅ Export customers test passed');
    return response.data;
  } else {
    console.log('❌ Export customers test failed');
    return null;
  }
}

/**
 * Test deleting a customer
 */
async function testDeleteCustomer(customerId) {
  console.log('\n--- Testing Delete Customer ---');
  console.log('Customer ID:', customerId);
  
  const response = await makeRequest(
    `${API_BASE_URL}/customers/${customerId}`,
    'DELETE'
  );
  
  console.log('Status:', response.status);
  console.log('Response:', response.data);
  
  if (response.status === 200) {
    console.log('✅ Delete customer test passed');
    return true;
  } else {
    console.log('❌ Delete customer test failed');
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  try {
    console.log('=== CUSTOMER API TESTS ===');
    
    // Sign in
    await signIn();
    
    // Test get customers
    await testGetCustomers();
    
    // Test create customer
    const customer = await testCreateCustomer();
    if (!customer) return;
    
    // Test get customer
    await testGetCustomer(customer.id);
    
    // Test update customer
    await testUpdateCustomer(customer.id);
    
    // Test export customers
    await testExportCustomers();
    
    // Test delete customer
    await testDeleteCustomer(customer.id);
    
    console.log('\n=== ALL TESTS COMPLETED ===');
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the tests
runTests();
