/**
 * Interactive Email Configuration Helper
 * Guides through the email setup process step by step
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function configureEmail() {
  console.log('🎯 Ocean Soul Sparkles Email Configuration Helper\n');
  console.log('This helper will guide you through configuring Google Cloud email.\n');

  // Read current .env.local
  const envPath = path.join(__dirname, '.env.local');
  let envContent = '';
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
    console.log('✅ Found existing .env.local file\n');
  } else {
    console.log('❌ No .env.local file found. Please create one first.\n');
    rl.close();
    return;
  }

  console.log('📧 Email Service Options:');
  console.log('1. Gmail SMTP (Personal Gmail account)');
  console.log('2. Google Workspace (Business email with custom domain)');
  console.log('3. Both (Gmail as backup, Workspace as primary)\n');

  const choice = await question('Choose an option (1, 2, or 3): ');

  let updateEnv = false;

  if (choice === '1' || choice === '3') {
    console.log('\n📮 Gmail SMTP Configuration:');
    console.log('Prerequisites:');
    console.log('1. Gmail account with 2-Factor Authentication enabled');
    console.log('2. App Password generated for "Mail" application\n');

    const hasPrereqs = await question('Do you have these prerequisites? (y/n): ');
    
    if (hasPrereqs.toLowerCase() === 'y') {
      const gmailUser = await question('Enter your Gmail address: ');
      const gmailPass = await question('Enter your 16-character app password: ');

      // Update Gmail configuration
      envContent = envContent.replace(
        /GMAIL_SMTP_USER=.*/,
        `GMAIL_SMTP_USER=${gmailUser}`
      );
      envContent = envContent.replace(
        /GMAIL_SMTP_APP_PASSWORD=.*/,
        `GMAIL_SMTP_APP_PASSWORD=${gmailPass}`
      );
      envContent = envContent.replace(
        /GMAIL_FROM_EMAIL=.*/,
        `GMAIL_FROM_EMAIL=${gmailUser}`
      );

      updateEnv = true;
      console.log('✅ Gmail configuration updated\n');
    } else {
      console.log('\n📋 Gmail Setup Instructions:');
      console.log('1. Go to https://myaccount.google.com/security');
      console.log('2. Enable 2-Step Verification if not already enabled');
      console.log('3. Go to "App passwords" and generate one for "Mail"');
      console.log('4. Run this helper again with your credentials\n');
    }
  }

  if (choice === '2' || choice === '3') {
    console.log('\n🏢 Google Workspace Configuration:');
    console.log('Prerequisites:');
    console.log('1. Google Workspace account (G Suite)');
    console.log('2. Business email address (e.g., <EMAIL>)');
    console.log('3. App Password generated for the business email\n');

    const hasWorkspacePrereqs = await question('Do you have these prerequisites? (y/n): ');
    
    if (hasWorkspacePrereqs.toLowerCase() === 'y') {
      const workspaceUser = await question('Enter your business email address: ');
      const workspacePass = await question('Enter the app password for business email: ');

      // Update Workspace configuration
      envContent = envContent.replace(
        /WORKSPACE_SMTP_USER=.*/,
        `WORKSPACE_SMTP_USER=${workspaceUser}`
      );
      envContent = envContent.replace(
        /WORKSPACE_SMTP_APP_PASSWORD=.*/,
        `WORKSPACE_SMTP_APP_PASSWORD=${workspacePass}`
      );
      envContent = envContent.replace(
        /WORKSPACE_FROM_EMAIL=.*/,
        `WORKSPACE_FROM_EMAIL=${workspaceUser}`
      );

      updateEnv = true;
      console.log('✅ Google Workspace configuration updated\n');
    } else {
      console.log('\n📋 Google Workspace Setup Instructions:');
      console.log('1. Access Google Workspace Admin Console');
      console.log('2. Create a business email (e.g., <EMAIL>)');
      console.log('3. Enable 2FA for the business email account');
      console.log('4. Generate an app password for "Mail"');
      console.log('5. Run this helper again with your credentials\n');
    }
  }

  if (updateEnv) {
    // Write updated .env.local
    fs.writeFileSync(envPath, envContent);
    console.log('💾 Environment variables updated in .env.local\n');

    console.log('🧪 Testing configuration...');
    console.log('Run the following commands to test:');
    console.log('1. node test-email-api.cjs');
    console.log('2. Visit http://localhost:3000/admin/settings');
    console.log('3. Go to "Email Settings" tab and click test buttons\n');

    const testNow = await question('Test the configuration now? (y/n): ');
    if (testNow.toLowerCase() === 'y') {
      console.log('\n🔄 Running email test...\n');
      rl.close();
      
      // Run the test script
      const { spawn } = require('child_process');
      const testProcess = spawn('node', ['test-email-api.cjs'], {
        stdio: 'inherit',
        cwd: __dirname
      });
      
      testProcess.on('close', (code) => {
        console.log(`\n✅ Test completed with code ${code}`);
        console.log('\n🎉 Next Steps:');
        console.log('1. If tests passed, your email is ready!');
        console.log('2. Test user registration and booking emails');
        console.log('3. Configure production environment variables');
        console.log('4. Visit admin panel for interactive testing');
      });
      
      return;
    }
  }

  console.log('\n🎉 Configuration Complete!');
  console.log('\n📋 Next Steps:');
  console.log('1. Test your configuration: node test-email-api.cjs');
  console.log('2. Visit admin panel: http://localhost:3000/admin/settings');
  console.log('3. Go to "Email Settings" tab for interactive testing');
  console.log('4. Test user registration and booking emails');
  console.log('5. Read GOOGLE_CLOUD_EMAIL_SETUP_GUIDE.md for detailed info');

  rl.close();
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Configuration cancelled. Run this script again when ready.');
  rl.close();
  process.exit(0);
});

// Run the configuration helper
configureEmail().catch(error => {
  console.error('❌ Configuration failed:', error);
  rl.close();
  process.exit(1);
});
