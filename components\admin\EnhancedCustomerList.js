import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import AdvancedCustomerSearch from './AdvancedCustomerSearch';
import CustomerTagManager from './CustomerTagManager';
import { authenticatedFetch } from '@/lib/auth-utils';
import { safeRender } from '@/lib/safe-render-utils';
import styles from '@/styles/admin/EnhancedCustomerList.module.css';

export default function EnhancedCustomerList() {
  const router = useRouter();
  const [customers, setCustomers] = useState([]);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);

  // Handle search filters change
  const handleFiltersChange = (filters) => {
    // Reset pagination when filters change
    setCurrentPage(1);
    setSelectedCustomers([]);
  };

  // Handle customers update from search
  const handleCustomersUpdate = (newCustomers, total) => {
    setCustomers(newCustomers);
    setTotalCustomers(total);
    setSelectedCustomers([]);
  };

  // Handle customer selection
  const handleCustomerSelect = (customerId, selected) => {
    if (selected) {
      setSelectedCustomers(prev => [...prev, customerId]);
    } else {
      setSelectedCustomers(prev => prev.filter(id => id !== customerId));
    }
  };

  // Handle select all
  const handleSelectAll = (selected) => {
    if (selected) {
      setSelectedCustomers(customers.map(c => c.id));
    } else {
      setSelectedCustomers([]);
    }
  };

  // Handle bulk tag assignment
  const handleBulkTagAssignment = async (tagId) => {
    try {
      setLoading(true);

      // Assign tag to all selected customers
      const promises = selectedCustomers.map(customerId =>
        authenticatedFetch(`/api/admin/customers/${customerId}/tags`, {
          method: 'POST',
          body: JSON.stringify({ tagId })
        })
      );

      await Promise.all(promises);

      // Refresh the customer list
      // This would trigger a re-search with current filters
      setSelectedCustomers([]);

      alert(`Tag assigned to ${selectedCustomers.length} customers successfully`);
    } catch (error) {
      console.error('Error in bulk tag assignment:', error);
      alert('Failed to assign tags to some customers');
    } finally {
      setLoading(false);
    }
  };

  // Handle sort
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null;
    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    );
  };

  // Format customer tier badge
  const renderTierBadge = (tier) => {
    if (!tier || tier === 'bronze') return null;

    const tierColors = {
      silver: '#C0C0C0',
      gold: '#FFD700',
      platinum: '#E5E4E2'
    };

    return (
      <span
        className={styles.tierBadge}
        style={{ backgroundColor: tierColors[tier] }}
      >
        {tier.toUpperCase()}
      </span>
    );
  };

  // Format health score
  const renderHealthScore = (score) => {
    if (!score) return '-';

    let className = styles.healthScore;
    if (score >= 70) className += ` ${styles.healthGood}`;
    else if (score >= 40) className += ` ${styles.healthMedium}`;
    else className += ` ${styles.healthPoor}`;

    return <span className={className}>{score}%</span>;
  };

  // Format customer status
  const renderStatus = (status) => {
    const statusColors = {
      active: '#4caf50',
      inactive: '#ff9800',
      suspended: '#f44336',
      churned: '#9e9e9e'
    };

    return (
      <span
        className={styles.statusBadge}
        style={{ backgroundColor: statusColors[status] || '#9e9e9e' }}
      >
        {status?.toUpperCase() || 'UNKNOWN'}
      </span>
    );
  };

  return (
    <div className={styles.enhancedCustomerList}>
      <div className={styles.header}>
        <h2>Customer Management</h2>
        <div className={styles.actions}>
          <button
            className={styles.addButton}
            onClick={() => router.push('/admin/customers/new')}
          >
            <span className={styles.addIcon}>+</span>
            Add Customer
          </button>
        </div>
      </div>

      {/* Advanced Search Component */}
      <AdvancedCustomerSearch
        onFiltersChange={handleFiltersChange}
        onCustomersUpdate={handleCustomersUpdate}
      />

      {error && <div className={styles.error}>{error}</div>}

      {/* Bulk Actions */}
      {selectedCustomers.length > 0 && (
        <div className={styles.bulkActions}>
          <span className={styles.selectedCount}>
            {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''} selected
          </span>
          <div className={styles.bulkActionButtons}>
            <button
              className={styles.bulkButton}
              onClick={() => setShowBulkActions(!showBulkActions)}
            >
              Bulk Actions ▼
            </button>
            {showBulkActions && (
              <div className={styles.bulkActionMenu}>
                <button onClick={() => alert('Export selected customers')}>
                  Export Selected
                </button>
                <button onClick={() => alert('Send bulk email')}>
                  Send Email
                </button>
                <button onClick={() => alert('Bulk tag assignment')}>
                  Assign Tags
                </button>
                <button onClick={() => setSelectedCustomers([])}>
                  Clear Selection
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Customer Table */}
      {loading ? (
        <div className={styles.loading}>Loading customers...</div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.customerTable}>
              <thead>
                <tr>
                  <th className={styles.checkboxColumn}>
                    <input
                      type="checkbox"
                      checked={selectedCustomers.length === customers.length && customers.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  </th>
                  <th onClick={() => handleSort('name')}>
                    Name {renderSortIndicator('name')}
                  </th>
                  <th onClick={() => handleSort('email')}>
                    Email {renderSortIndicator('email')}
                  </th>
                  <th onClick={() => handleSort('customer_status')}>
                    Status {renderSortIndicator('customer_status')}
                  </th>
                  <th onClick={() => handleSort('customer_tier')}>
                    Tier {renderSortIndicator('customer_tier')}
                  </th>
                  <th onClick={() => handleSort('lifetime_value')}>
                    Value {renderSortIndicator('lifetime_value')}
                  </th>
                  <th onClick={() => handleSort('total_bookings')}>
                    Bookings {renderSortIndicator('total_bookings')}
                  </th>
                  <th onClick={() => handleSort('customer_health_score')}>
                    Health {renderSortIndicator('customer_health_score')}
                  </th>
                  <th onClick={() => handleSort('registration_date')}>
                    Registered {renderSortIndicator('registration_date')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {customers.map((customer) => (
                  <tr key={customer.id} className={styles.customerRow}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedCustomers.includes(customer.id)}
                        onChange={(e) => handleCustomerSelect(customer.id, e.target.checked)}
                      />
                    </td>
                    <td>
                      <div className={styles.customerName}>
                        {safeRender(customer.name)}
                        {renderTierBadge(customer.customer_tier)}
                        {customer.tags && customer.tags.length > 0 && (
                          <div className={styles.customerTags}>
                            {customer.tags.slice(0, 2).map(tag => (
                              <span key={tag} className={styles.tag}>
                                {safeRender(tag)}
                              </span>
                            ))}
                            {customer.tags.length > 2 && (
                              <span className={styles.tagMore}>
                                +{customer.tags.length - 2}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </td>
                    <td>{safeRender(customer.email)}</td>
                    <td>{renderStatus(customer.customer_status)}</td>
                    <td>{safeRender(customer.customer_tier, 'Bronze')}</td>
                    <td>${safeRender(customer.lifetime_value, '0')}</td>
                    <td>{safeRender(customer.total_bookings, '0')}</td>
                    <td>{renderHealthScore(customer.customer_health_score)}</td>
                    <td>
                      {customer.registration_date ? new Date(customer.registration_date).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className={styles.actions}>
                      <Link
                        href={`/admin/customers/${customer.id}`}
                        className={styles.viewButton}
                      >
                        View
                      </Link>
                      <Link
                        href={`/admin/customers/${customer.id}/edit`}
                        className={styles.editButton}
                      >
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {customers.length === 0 && (
            <div className={styles.noResults}>
              No customers found matching your search criteria
            </div>
          )}

          {/* Results Summary */}
          <div className={styles.resultsSummary}>
            Showing {customers.length} of {totalCustomers} customers
          </div>
        </>
      )}
    </div>
  );
}
