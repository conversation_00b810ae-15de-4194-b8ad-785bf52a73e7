/**
 * Debug script to identify React Error #130 issues in services page
 */

// Test the API response structure
console.log('🔍 Debugging Services Page React Error #130\n');

// Test 1: Check API response
console.log('1. Testing API response structure:');
fetch('http://localhost:3000/api/public/services')
  .then(response => response.json())
  .then(data => {
    console.log('   API Response received');
    console.log('   Data structure:', typeof data);
    console.log('   Has services property:', 'services' in data);
    console.log('   Services is array:', Array.isArray(data.services));
    console.log('   Number of services:', data.services?.length || 0);
    
    if (data.services && data.services.length > 0) {
      const firstService = data.services[0];
      console.log('\n   First service structure:');
      console.log('   - id:', typeof firstService.id, firstService.id);
      console.log('   - title:', typeof firstService.title, firstService.title);
      console.log('   - image:', typeof firstService.image, firstService.image);
      console.log('   - accentColor:', typeof firstService.accentColor, firstService.accentColor);
      console.log('   - icon:', typeof firstService.icon, firstService.icon);
      console.log('   - pricing:', typeof firstService.pricing, Array.isArray(firstService.pricing));
      
      if (firstService.pricing && firstService.pricing.length > 0) {
        console.log('   - pricing[0]:', typeof firstService.pricing[0], firstService.pricing[0]);
      }
    }
  })
  .catch(error => {
    console.error('   API Error:', error);
  });

// Test 2: Test safe rendering functions
console.log('\n2. Testing safe rendering functions:');

// Import safe render function (simulate)
const safeRender = (value, fallback = 'N/A') => {
  try {
    if (value === null || value === undefined) return fallback;
    if (typeof value === 'object') {
      if (value.name) return String(value.name);
      if (value.value) return String(value.value);
      if (value.title) return String(value.title);
      if (value.label) return String(value.label);
      if (Array.isArray(value)) {
        return value.map(item => safeRender(item, '')).join(', ') || fallback;
      }
      return fallback;
    }
    return String(value);
  } catch (error) {
    console.error('Error rendering value:', error, 'Value:', value);
    return fallback;
  }
};

// Test various data types
const testValues = [
  null,
  undefined,
  'normal string',
  123,
  { title: 'Object with title' },
  { name: 'Object with name' },
  { complex: { nested: 'value' } },
  ['array', 'of', 'strings'],
  [{ title: 'Object in array' }],
  true,
  false,
  ''
];

testValues.forEach((value, index) => {
  const result = safeRender(value);
  console.log(`   Test ${index + 1}: ${typeof value} -> "${result}"`);
});

// Test 3: Check for potential React issues
console.log('\n3. Checking for potential React rendering issues:');

// Simulate the heroServices mapping
const mockServices = [
  {
    id: 'test-1',
    title: 'Test Service',
    icon: '🎨',
    accentColor: '#4ECDC4',
    image: '/test.jpg'
  },
  {
    id: 'test-2',
    title: null,
    icon: undefined,
    accentColor: { color: '#FF6B6B' },
    image: { url: '/test2.jpg' }
  }
];

console.log('   Testing heroServices mapping:');
const heroServices = (mockServices && Array.isArray(mockServices) ? mockServices : []).map(service => ({
  title: safeRender(service?.title),
  icon: safeRender(service?.icon, '🎨'),
  color: safeRender(service?.accentColor, '#4ECDC4'),
  image: safeRender(service?.image, '/images/services/face-paint.jpg')
}));

heroServices.forEach((service, index) => {
  console.log(`   Hero Service ${index + 1}:`, service);
});

console.log('\n✅ Debug script completed. Check console for any errors.');
