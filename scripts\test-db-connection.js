import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testConnection() {
  console.log('Testing Supabase connection...');
  console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
  console.log('Service Key:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Present' : 'Missing');
  
  try {
    // Test connection by checking existing settings
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .limit(5);
    
    if (error) {
      console.log('❌ Connection test failed:', error.message);
      console.log('Error details:', error);
    } else {
      console.log('✅ Connection successful!');
      console.log('Existing settings count:', data.length);
      console.log('Sample settings:', data);
    }
  } catch (err) {
    console.log('❌ Exception during connection test:', err.message);
  }
}

testConnection().catch(console.error);
