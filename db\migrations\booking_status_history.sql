-- Create booking_status_history table
CREATE TABLE IF NOT EXISTS public.booking_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  previous_status TEXT,
  new_status TEXT NOT NULL,
  changed_by UUID REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS booking_status_history_booking_id_idx ON public.booking_status_history(booking_id);

-- Update bookings table to support new statuses
DO $$ 
BEGIN
  -- Check if the constraint exists
  IF EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'bookings_status_check' AND conrelid = 'public.bookings'::regclass
  ) THEN
    -- Drop the existing constraint
    ALTER TABLE public.bookings DROP CONSTRAINT bookings_status_check;
    
    -- Add the new constraint with expanded status options
    ALTER TABLE public.bookings ADD CONSTRAINT bookings_status_check 
    CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'canceled', 'no_show', 'rescheduled'));
  END IF;
END $$;

-- Set up RLS policies
ALTER TABLE public.booking_status_history ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all booking history" ON public.booking_status_history
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can insert booking history" ON public.booking_status_history
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Create function to automatically record status changes
CREATE OR REPLACE FUNCTION public.record_booking_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only record if status has changed
  IF OLD.status <> NEW.status THEN
    INSERT INTO public.booking_status_history (
      booking_id,
      previous_status,
      new_status,
      changed_by,
      notes
    ) VALUES (
      NEW.id,
      OLD.status,
      NEW.status,
      auth.uid(),
      'Status changed via booking update'
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to call the function
DROP TRIGGER IF EXISTS booking_status_change_trigger ON public.bookings;
CREATE TRIGGER booking_status_change_trigger
AFTER UPDATE OF status ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION public.record_booking_status_change();
