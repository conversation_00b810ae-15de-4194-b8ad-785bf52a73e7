/* Main layout */
.main {
  width: 100%;
  overflow-x: hidden;
}

.sectionContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  position: relative;
  height: 80vh;
  min-height: 600px;
  width: 100%;
  background-image: url('/images/gift-card-hero.jpg');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.85) 0%, rgba(26, 115, 232, 0.85) 100%);
  z-index: 1;
}

.heroContent {
  position: relative;
  z-index: 2;
  max-width: 900px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.heroTitle::after {
  content: '';
  position: absolute;
  width: 100px;
  height: 4px;
  background: white;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

.heroSubtitle {
  font-size: 1.8rem;
  max-width: 800px;
  margin: 2rem auto 3rem;
  line-height: 1.6;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.heroActions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.heroCta {
  background-color: white;
  color: #1A73E8;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.heroCta:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.heroSecondary {
  border: 2px solid white;
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.heroSecondary:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
}

.scrollIndicator {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 0.9rem;
  opacity: 0.8;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 3;
}

.scrollIndicator:hover {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.mouseIcon {
  width: 30px;
  height: 50px;
  border: 2px solid white;
  border-radius: 20px;
  margin-bottom: 10px;
  position: relative;
}

.mouseIcon::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  animation: mouseScroll 2s infinite;
}

@keyframes mouseScroll {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  75% {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  76% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Shape dividers */
.shapeDividerTop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  transform: rotate(180deg);
}

.shapeDividerTop svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 80px;
}

.shapeDividerBottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.shapeDividerBottom svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 80px;
}

.shapeFill {
  fill: #FFFFFF;
}

/* Perfect Gift Section */
.perfectGiftSection {
  position: relative;
  padding: 8rem 0;
  background-color: #f8f9fa;
}

.perfectGiftContent {
  max-width: 800px;
  margin: 0 auto 4rem;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2.5rem;
  color: #333;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: #1A73E8;
  border-radius: 2px;
}

.sectionText {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: #555;
}

.valueProposition {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.valueItem {
  flex: 1;
  min-width: 200px;
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.valueItem:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.valueIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.valueItem h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.valueItem p {
  color: #666;
}

/* Gift Card Options */
.giftCardOptions {
  margin-top: 3rem;
}

.giftCardOptions h3 {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 2.5rem;
  color: #333;
}

.optionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.optionCard {
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: all 0.5s ease;
}

.optionCardInner {
  padding: 2.5rem 1.5rem;
  text-align: center;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.optionCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.optionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, #4ECDC4, #1A73E8);
}

.optionCard h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
  position: relative;
}

.optionCard p {
  margin-bottom: 2rem;
  color: #666;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: #1A73E8;
  display: block;
  margin-top: auto;
}

/* Customize Section */
.customizeSection {
  padding: 6rem 0;
  background-color: white;
}

/* How It Works Section */
.howItWorksSection {
  padding: 8rem 0;
  background-color: #f8f9fa;
  text-align: center;
}

.stepsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  max-width: 1200px;
  margin: 3rem auto 0;
}

.stepCard {
  flex: 1;
  min-width: 250px;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2.5rem 1.5rem;
  border-radius: 16px;
  background-color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stepCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stepNumber {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4ECDC4 0%, #1A73E8 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  box-shadow: 0 10px 20px rgba(26, 115, 232, 0.3);
}

.stepContent {
  text-align: center;
}

.stepCard h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.stepCard p {
  color: #666;
  line-height: 1.6;
}

/* Purchase Section */
.purchaseSection {
  padding: 8rem 0;
  background-color: white;
}

.purchaseFormContainer {
  max-width: 900px;
  margin: 0 auto;
  padding: 2.5rem;
  border-radius: 20px;
  background-color: #f8f9fa;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
}

.purchaseForm {
  width: 100%;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formLabel {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.required {
  color: #e53935;
}

.formInput, 
.formSelect, 
.formTextarea {
  width: 100%;
  padding: 0.9rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.formInput:focus, 
.formSelect:focus, 
.formTextarea:focus {
  border-color: #1A73E8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
  outline: none;
}

.formTextarea {
  min-height: 120px;
  resize: vertical;
}

.inputWithIcon {
  position: relative;
}

.currencySymbol {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-weight: 600;
}

.inputWithIcon .formInput {
  padding-left: 2rem;
}

.formActions {
  margin-top: 2rem;
  text-align: center;
}

.submitButton {
  background: linear-gradient(135deg, #4ECDC4 0%, #1A73E8 100%);
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(26, 115, 232, 0.3);
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
}

.submitButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(26, 115, 232, 0.4);
}

.btnIcon {
  font-size: 1.4rem;
}

/* FAQ Section */
.faqSection {
  padding: 8rem 0;
  background-color: #f8f9fa;
}

.faqGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 3rem auto 0;
}

.faqItem {
  padding: 2rem;
  border-radius: 16px;
  background-color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.faqItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.faqItem h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
  position: relative;
  padding-left: 1.5rem;
}

.faqItem h3::before {
  content: '•';
  color: #1A73E8;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1.5rem;
  line-height: 1;
}

.faqItem p {
  color: #666;
  line-height: 1.6;
}

/* CTA Section */
.ctaSection {
  position: relative;
  padding: 8rem 2rem;
  text-align: center;
  color: white;
  background-image: url('/images/gift-card-hero.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.ctaOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.9) 0%, rgba(26, 115, 232, 0.9) 100%);
  z-index: 1;
}

.ctaContent {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.ctaContent h2 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.ctaContent p {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  line-height: 1.6;
}

.ctaButton {
  background-color: white;
  color: #1A73E8;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  display: inline-block;
}

.ctaButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

/* Media Queries */
@media (max-width: 1200px) {
  .heroTitle {
    font-size: 3.8rem;
  }
  
  .heroSubtitle {
    font-size: 1.6rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .faqGrid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
  
  .ctaContent h2 {
    font-size: 3rem;
  }
}

@media (max-width: 992px) {
  .heroSection {
    min-height: 500px;
  }
  
  .heroTitle {
    font-size: 3.2rem;
  }
  
  .heroSubtitle {
    font-size: 1.4rem;
    margin: 1.5rem auto 2.5rem;
  }
  
  .heroActions {
    margin-top: 2rem;
  }
  
  .heroCta, .heroSecondary {
    padding: 0.9rem 2rem;
    font-size: 1.1rem;
  }
  
  .perfectGiftSection, 
  .howItWorksSection, 
  .purchaseSection, 
  .faqSection, 
  .ctaSection {
    padding: 6rem 0;
  }
  
  .sectionTitle {
    font-size: 2.3rem;
  }
  
  .valueProposition {
    gap: 1.5rem;
  }
  
  .valueItem {
    min-width: 180px;
    padding: 1.2rem;
  }
  
  .stepCard {
    min-width: 220px;
  }
  
  .faqGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  .ctaContent h2 {
    font-size: 2.5rem;
  }
  
  .ctaContent p {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .heroSection {
    min-height: 450px;
  }
  
  .heroTitle {
    font-size: 2.8rem;
  }
  
  .heroSubtitle {
    font-size: 1.3rem;
  }
  
  .heroActions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 300px;
    margin: 2rem auto 0;
  }
  
  .scrollIndicator {
    display: none;
  }
  
  .perfectGiftSection, 
  .howItWorksSection, 
  .purchaseSection, 
  .faqSection, 
  .ctaSection {
    padding: 5rem 0;
  }
  
  .shapeDividerTop svg, 
  .shapeDividerBottom svg {
    height: 50px;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .sectionText {
    font-size: 1rem;
  }
  
  .valueProposition {
    flex-direction: column;
    align-items: center;
  }
  
  .valueItem {
    width: 100%;
    max-width: 300px;
  }
  
  .optionsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
  
  .stepCard {
    padding: 2rem 1.2rem;
  }
  
  .stepNumber {
    width: 50px;
    height: 50px;
    font-size: 1.6rem;
  }
  
  .faqGrid {
    grid-template-columns: 1fr;
  }
  
  .ctaContent h2 {
    font-size: 2.2rem;
  }
}

@media (max-width: 576px) {
  .heroSection {
    min-height: 400px;
  }
  
  .heroTitle {
    font-size: 2.4rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .perfectGiftSection, 
  .howItWorksSection, 
  .purchaseSection, 
  .faqSection {
    padding: 4rem 0;
  }
  
  .sectionTitle {
    font-size: 1.8rem;
  }
  
  .sectionTitle::after {
    bottom: -10px;
    width: 60px;
    height: 2px;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
  }
  
  .ctaSection {
    padding: 5rem 1.5rem;
  }
  
  .ctaContent h2 {
    font-size: 2rem;
  }
  
  .ctaContent p {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }
  
  .ctaButton {
    padding: 0.9rem 2rem;
    font-size: 1.1rem;
  }
}
