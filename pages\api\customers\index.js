import supabase, { getCurrentUserFromRequest } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    // Allow dev, admin, artist, and braider roles (5-tier role system)
    const allowedRoles = ['dev', 'admin', 'artist', 'braider'];
    if (!user || !allowedRoles.includes(role)) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCustomers(req, res)
    case 'POST':
      return createCustomer(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get customers with optional filters
async function getCustomers(req, res) {
  const {
    search,
    sort_by,
    sort_order,
    limit = 10,
    offset = 0,
    city,
    state,
    has_bookings,
    date_added,
    marketing_consent
  } = req.query

  try {
    // Use Supabase client directly
    // Note: We'll use the regular client since it already has admin privileges through its token
    let query = supabase
      .from('customers')
      .select('*', { count: 'exact' })

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,phone.ilike.%${search}%`)
    }

    // Apply location filters
    if (city) {
      query = query.ilike('city', `%${city}%`)
    }
    if (state) {
      query = query.ilike('state', `%${state}%`)
    }

    // Apply booking history filter
    if (has_bookings === 'true') {
      const { data: customersWithBookings } = await supabaseAdmin
        .from('bookings')
        .select('customer_id')
        .not('customer_id', 'is', null)
        .order('customer_id')

      if (customersWithBookings && customersWithBookings.length > 0) {
        const customerIds = [...new Set(customersWithBookings.map(b => b.customer_id))]
        query = query.in('id', customerIds)
      } else {
        // No customers with bookings found
        return res.status(200).json({ customers: [], total: 0 })
      }
    } else if (has_bookings === 'false') {
      const { data: customersWithBookings } = await supabase
        .from('bookings')
        .select('customer_id')
        .not('customer_id', 'is', null)
        .order('customer_id')

      if (customersWithBookings && customersWithBookings.length > 0) {
        const customerIds = [...new Set(customersWithBookings.map(b => b.customer_id))]
        query = query.not('id', 'in', customerIds)
      }
    }

    // Apply date added filter
    if (date_added) {
      const days = parseInt(date_added)
      if (!isNaN(days)) {
        const cutoffDate = new Date()
        cutoffDate.setDate(cutoffDate.getDate() - days)
        query = query.gte('created_at', cutoffDate.toISOString())
      }
    }

    // Apply marketing consent filter
    if (marketing_consent === 'true') {
      query = query.eq('marketing_consent', true)
    } else if (marketing_consent === 'false') {
      query = query.eq('marketing_consent', false)
    }

    // Apply sorting
    if (sort_by) {
      const order = sort_order === 'desc' ? false : true
      query = query.order(sort_by, { ascending: order })
    } else {
      query = query.order('created_at', { ascending: false })
    }

    // Get total count before applying pagination
    const { count } = await query

    // Apply pagination
    query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1)

    // Execute query
    const { data: customers, error } = await query

    if (error) {
      throw error
    }

    // Check for recent bookings (within last 30 days)
    if (customers && customers.length > 0) {
      const customerIds = customers.map(c => c.id)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { data: recentBookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('customer_id')
        .in('customer_id', customerIds)
        .gte('start_time', thirtyDaysAgo.toISOString())

      if (!bookingsError && recentBookings) {
        const recentBookingCustomerIds = new Set(recentBookings.map(b => b.customer_id))

        // Add recent_booking flag to customers
        customers.forEach(customer => {
          customer.recent_booking = recentBookingCustomerIds.has(customer.id)
        })
      }
    }

    return res.status(200).json({ customers, total: count || 0 })
  } catch (error) {
    console.error('Error fetching customers:', error)
    return res.status(500).json({ error: 'Failed to fetch customers' })
  }
}

// Create a new customer
async function createCustomer(req, res) {
  const { name, email, phone, address, city, state, postal_code, country, notes, marketing_consent } = req.body

  try {
    // Use Supabase client directly

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({ error: 'Name and email are required' })
    }

    // Check if email already exists
    const { data: existingCustomer, error: checkError } = await supabase
      .from('customers')
      .select('id')
      .eq('email', email)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError
    }

    if (existingCustomer) {
      return res.status(409).json({ error: 'A customer with this email already exists' })
    }

    // Create customer
    const { data, error } = await supabase
      .from('customers')
      .insert([
        {
          name,
          email,
          phone,
          address,
          city,
          state,
          postal_code,
          country: country || 'Australia',
          notes,
          marketing_consent: marketing_consent || false
        }
      ])
      .select()

    if (error) {
      throw error
    }

    return res.status(201).json(data[0])
  } catch (error) {
    console.error('Error creating customer:', error)
    return res.status(500).json({ error: 'Failed to create customer' })
  }
}
