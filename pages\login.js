// User login page
import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import Layout from '@/components/Layout';
import CustomerForm from '@/components/CustomerForm';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomer } from '@/contexts/CustomerContext';
import styles from '@/styles/Auth.module.css';

export default function Login() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { customer } = useCustomer();
  const [isLoading, setIsLoading] = useState(true);

  // Check if user is already authenticated
  useEffect(() => {
    setIsLoading(false);
    if (isAuthenticated && customer) {
      // Redirect to account page if already logged in
      router.push('/account');
    }
  }, [isAuthenticated, customer, router]);

  // Handle successful login
  const handleLoginComplete = (customerData) => {
    // Redirect to account page or return URL
    const returnUrl = router.query.returnUrl || '/account';
    router.push(returnUrl);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className={styles.loading}>Loading...</div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Sign In | OceanSoulSparkles</title>
        <meta name="description" content="Sign in to your OceanSoulSparkles account to access your profile and order history." />
      </Head>

      <main className={styles.main}>
        <div className={styles.container}>
          <div className={styles.header}>
            <h1>Welcome Back</h1>
            <p>Sign in to your OceanSoulSparkles account</p>
          </div>          <div className={styles.formWrapper}>
            <CustomerForm 
              initialMode="login" 
              onComplete={handleLoginComplete} 
            />
          </div>

          <div className={styles.authLinks}>
            <p>
              Don't have an account?{' '}
              <Link href="/signup" className={styles.link}>
                Create one here
              </Link>
            </p>
            <p>
              <Link href="/account" className={styles.link}>
                Continue as guest
              </Link>
            </p>
          </div>
        </div>
      </main>
    </Layout>
  );
}
