# Supabase Migrations

This directory contains SQL migrations for the Ocean Soul Sparkles Supabase database.

## Running Migrations

To run the migrations, you can use the Supabase CLI or execute the SQL directly in the Supabase SQL Editor.

### Using Supabase CLI

1. Install the Supabase CLI if you haven't already:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref ndlgbcsbidyhxbpqzgqp
   ```

4. Run migrations:
   ```bash
   supabase db push
   ```

### Using Supabase SQL Editor

1. Log in to the [Supabase Dashboard](https://app.supabase.io)
2. Select your project
3. Go to the SQL Editor
4. Open the migration file you want to run (e.g., `migrations/create_settings_table.sql`)
5. Click "Run" to execute the SQL

## Available Migrations

- `create_settings_table.sql`: Creates the settings table and populates it with default values

## Troubleshooting

If you encounter any issues with the migrations:

1. Check the Supabase logs for error messages
2. Verify that you have the necessary permissions to create tables and functions
3. If a migration fails, you may need to manually clean up any partially created objects before retrying

## Manual Database Setup

If you prefer to set up the database manually, you can use the SQL scripts in the `migrations` directory as a reference. The key tables and functions needed are:

1. `settings` table: Stores application settings as key-value pairs
2. `create_settings_table` function: Creates the settings table with default values
3. `execute_sql` function: Utility function for executing SQL statements

For more information on database schema and design, see the `admin-docs/database-schema.sql` file.
