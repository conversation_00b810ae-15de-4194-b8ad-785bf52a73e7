/* Artist/Braider Dashboard Styles */

.dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
  text-align: center;
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  color: #4a5568;
  font-size: 1.1rem;
  margin: 0;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.statIcon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.statContent h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.statContent p {
  color: #4a5568;
  font-size: 0.875rem;
  margin: 0;
  font-weight: 500;
}

/* Content Grid */
.contentGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

/* Bookings List */
.bookingsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bookingCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.bookingDate {
  font-size: 0.75rem;
  font-weight: 600;
  color: #667eea;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 60px;
}

.bookingDetails {
  flex: 1;
}

.bookingDetails h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.bookingDetails p {
  font-size: 0.75rem;
  color: #4a5568;
  margin: 0;
}

.bookingTime {
  font-size: 0.75rem;
  color: #667eea;
  font-weight: 500;
}

.bookingStatus {
  min-width: 80px;
  text-align: right;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.statusBadge.confirmed {
  background: #c6f6d5;
  color: #22543d;
}

.statusBadge.pending {
  background: #fef5e7;
  color: #c05621;
}

.statusBadge.completed {
  background: #bee3f8;
  color: #2a4365;
}

/* Payments List */
.paymentsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.paymentCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.paymentDate {
  font-size: 0.75rem;
  font-weight: 600;
  color: #667eea;
  min-width: 80px;
}

.paymentDetails {
  flex: 1;
}

.paymentDetails h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.paymentDetails p {
  font-size: 0.75rem;
  color: #4a5568;
  margin: 0;
}

.paymentAmount {
  text-align: right;
  min-width: 100px;
}

.amount {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #22543d;
}

.commission {
  display: block;
  font-size: 0.75rem;
  color: #4a5568;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 2rem;
  color: #4a5568;
}

.emptyState p {
  margin: 0;
  font-style: italic;
}

/* Quick Actions */
.quickActions {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.quickActions h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

.actionButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #1a202c;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.actionIcon {
  font-size: 1.25rem;
}

/* Loading and Error States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading p {
  color: #4a5568;
  font-size: 1rem;
}

.error {
  text-align: center;
  padding: 2rem;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  color: #c53030;
}

.error h2 {
  margin: 0 0 0.5rem 0;
  color: #c53030;
}

.error p {
  margin: 0 0 1rem 0;
}

.retryButton {
  padding: 0.5rem 1rem;
  background: #c53030;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.retryButton:hover {
  background: #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }

  .contentGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .actionButtons {
    grid-template-columns: 1fr;
  }

  .bookingCard,
  .paymentCard {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .bookingStatus,
  .paymentAmount {
    text-align: left;
    min-width: auto;
  }
}
