import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createSettingsTable() {
  console.log('Creating settings table...');
  
  // Create the settings table with a direct SQL query
  const createTableSQL = `
    -- Create the settings table if it doesn't exist
    CREATE TABLE IF NOT EXISTS public.settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Enable Row Level Security
    ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
    
    -- Create policy for admin access (allow all operations)
    DO $$
    BEGIN
      IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'settings' 
        AND policyname = 'Admin users can manage settings'
      ) THEN
        CREATE POLICY "Admin users can manage settings" 
          ON public.settings 
          USING (true)
          WITH CHECK (true);
      END IF;
    END
    $$;
  `;
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.log('❌ Error creating table with exec_sql:', error.message);
      
      // Try using direct SQL execution
      console.log('Trying alternative approach...');
      
      const { data: data2, error: error2 } = await supabase
        .from('_schema')
        .select('*')
        .limit(1);
        
      if (error2) {
        console.log('❌ Cannot access database schema:', error2.message);
      }
      
    } else {
      console.log('✅ Settings table created successfully!');
    }
  } catch (err) {
    console.log('❌ Exception creating table:', err.message);
  }
}

createSettingsTable().catch(console.error);
