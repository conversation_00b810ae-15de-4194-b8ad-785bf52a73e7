# Admin Customer Management Implementation Examples

## 1. Advanced Customer Search Component

### Enhanced Search Interface
```jsx
// components/admin/AdvancedCustomerSearch.js
import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import styles from '@/styles/admin/AdvancedCustomerSearch.module.css';

export default function AdvancedCustomerSearch({ onFiltersChange, onCustomersUpdate }) {
  const [filters, setFilters] = useState({
    search: '',
    tags: [],
    segment: 'all',
    lifetimeValue: { min: '', max: '' },
    bookingCount: { min: '', max: '' },
    lastBooking: '',
    registrationDate: { start: '', end: '' },
    location: { city: '', state: '', country: 'Australia' },
    vipStatus: 'all',
    communicationPreference: 'all',
    customerStatus: 'all'
  });

  const [savedFilters, setSavedFilters] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const customerSegments = [
    { value: 'all', label: 'All Customers' },
    { value: 'new', label: 'New Customers (< 30 days)' },
    { value: 'regular', label: 'Regular Customers' },
    { value: 'vip', label: 'VIP Customers' },
    { value: 'inactive', label: 'Inactive (> 90 days)' },
    { value: 'high_value', label: 'High Value (> $1000)' },
    { value: 'at_risk', label: 'At Risk of Churning' }
  ];

  const lastBookingOptions = [
    { value: '', label: 'Any time' },
    { value: '7', label: 'Last 7 days' },
    { value: '30', label: 'Last 30 days' },
    { value: '90', label: 'Last 90 days' },
    { value: '365', label: 'Last year' },
    { value: 'never', label: 'Never booked' }
  ];

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchFilters) => {
      onFiltersChange(searchFilters);
    }, 300),
    [onFiltersChange]
  );

  useEffect(() => {
    debouncedSearch(filters);
  }, [filters, debouncedSearch]);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleNestedFilterChange = (parentKey, childKey, value) => {
    setFilters(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [childKey]: value
      }
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      tags: [],
      segment: 'all',
      lifetimeValue: { min: '', max: '' },
      bookingCount: { min: '', max: '' },
      lastBooking: '',
      registrationDate: { start: '', end: '' },
      location: { city: '', state: '', country: 'Australia' },
      vipStatus: 'all',
      communicationPreference: 'all',
      customerStatus: 'all'
    });
  };

  const saveCurrentFilters = () => {
    const filterName = prompt('Enter a name for this filter set:');
    if (filterName) {
      const newSavedFilter = {
        id: Date.now(),
        name: filterName,
        filters: { ...filters }
      };
      setSavedFilters(prev => [...prev, newSavedFilter]);
      localStorage.setItem('customerFilters', JSON.stringify([...savedFilters, newSavedFilter]));
    }
  };

  const loadSavedFilter = (savedFilter) => {
    setFilters(savedFilter.filters);
  };

  return (
    <div className={styles.searchContainer}>
      {/* Quick Search Bar */}
      <div className={styles.quickSearch}>
        <div className={styles.searchInputGroup}>
          <input
            type="text"
            placeholder="Search customers by name, email, phone..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className={styles.searchInput}
          />
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={styles.advancedToggle}
          >
            Advanced {showAdvanced ? '▲' : '▼'}
          </button>
        </div>

        <div className={styles.quickFilters}>
          <select
            value={filters.segment}
            onChange={(e) => handleFilterChange('segment', e.target.value)}
            className={styles.quickSelect}
          >
            {customerSegments.map(segment => (
              <option key={segment.value} value={segment.value}>
                {segment.label}
              </option>
            ))}
          </select>

          <select
            value={filters.lastBooking}
            onChange={(e) => handleFilterChange('lastBooking', e.target.value)}
            className={styles.quickSelect}
          >
            {lastBookingOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className={styles.advancedFilters}>
          <div className={styles.filterSection}>
            <h4>Customer Value</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min lifetime value"
                value={filters.lifetimeValue.min}
                onChange={(e) => handleNestedFilterChange('lifetimeValue', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max lifetime value"
                value={filters.lifetimeValue.max}
                onChange={(e) => handleNestedFilterChange('lifetimeValue', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          <div className={styles.filterSection}>
            <h4>Booking History</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min bookings"
                value={filters.bookingCount.min}
                onChange={(e) => handleNestedFilterChange('bookingCount', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max bookings"
                value={filters.bookingCount.max}
                onChange={(e) => handleNestedFilterChange('bookingCount', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          <div className={styles.filterSection}>
            <h4>Location</h4>
            <div className={styles.locationInputs}>
              <input
                type="text"
                placeholder="City"
                value={filters.location.city}
                onChange={(e) => handleNestedFilterChange('location', 'city', e.target.value)}
                className={styles.locationInput}
              />
              <input
                type="text"
                placeholder="State"
                value={filters.location.state}
                onChange={(e) => handleNestedFilterChange('location', 'state', e.target.value)}
                className={styles.locationInput}
              />
            </div>
          </div>

          <div className={styles.filterSection}>
            <h4>Registration Date</h4>
            <div className={styles.dateInputs}>
              <input
                type="date"
                value={filters.registrationDate.start}
                onChange={(e) => handleNestedFilterChange('registrationDate', 'start', e.target.value)}
                className={styles.dateInput}
              />
              <span>to</span>
              <input
                type="date"
                value={filters.registrationDate.end}
                onChange={(e) => handleNestedFilterChange('registrationDate', 'end', e.target.value)}
                className={styles.dateInput}
              />
            </div>
          </div>

          <div className={styles.filterActions}>
            <button onClick={clearFilters} className={styles.clearButton}>
              Clear All Filters
            </button>
            <button onClick={saveCurrentFilters} className={styles.saveButton}>
              Save Filter Set
            </button>
          </div>
        </div>
      )}

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div className={styles.savedFilters}>
          <h4>Saved Filters:</h4>
          <div className={styles.savedFiltersList}>
            {savedFilters.map(savedFilter => (
              <button
                key={savedFilter.id}
                onClick={() => loadSavedFilter(savedFilter)}
                className={styles.savedFilterButton}
              >
                {savedFilter.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className={styles.activeFilters}>
          <span className={styles.activeFiltersLabel}>Active Filters:</span>
          {renderActiveFilters()}
        </div>
      )}
    </div>
  );
}
```

## 2. Customer Segmentation and Tagging System

### Customer Tag Manager Component
```jsx
// components/admin/CustomerTagManager.js
import { useState, useEffect } from 'react';
import styles from '@/styles/admin/CustomerTagManager.module.css';

export default function CustomerTagManager({ customerId, onTagsUpdate }) {
  const [availableTags, setAvailableTags] = useState([]);
  const [customerTags, setCustomerTags] = useState([]);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#6e8efb');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const predefinedColors = [
    '#6e8efb', '#a777e3', '#4ecdc4', '#ff6b6b', '#ffe66d',
    '#4caf50', '#ff9800', '#f44336', '#2196f3', '#9c27b0'
  ];

  useEffect(() => {
    fetchAvailableTags();
    if (customerId) {
      fetchCustomerTags();
    }
  }, [customerId]);

  const fetchAvailableTags = async () => {
    try {
      const response = await fetch('/api/admin/customer-tags');
      const data = await response.json();
      setAvailableTags(data.tags || []);
    } catch (error) {
      console.error('Error fetching tags:', error);
      setError('Failed to load available tags');
    }
  };

  const fetchCustomerTags = async () => {
    try {
      const response = await fetch(`/api/admin/customers/${customerId}/tags`);
      const data = await response.json();
      setCustomerTags(data.tags || []);
    } catch (error) {
      console.error('Error fetching customer tags:', error);
      setError('Failed to load customer tags');
    }
  };

  const createNewTag = async () => {
    if (!newTagName.trim()) return;

    try {
      setLoading(true);
      const response = await fetch('/api/admin/customer-tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newTagName.trim(),
          color: newTagColor,
          description: `Custom tag: ${newTagName}`
        })
      });

      if (!response.ok) throw new Error('Failed to create tag');

      const data = await response.json();
      setAvailableTags(prev => [...prev, data.tag]);
      setNewTagName('');
      setNewTagColor('#6e8efb');
    } catch (error) {
      setError('Failed to create new tag');
    } finally {
      setLoading(false);
    }
  };

  const assignTag = async (tagId) => {
    if (!customerId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/admin/customers/${customerId}/tags`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tagId })
      });

      if (!response.ok) throw new Error('Failed to assign tag');

      await fetchCustomerTags();
      onTagsUpdate && onTagsUpdate();
    } catch (error) {
      setError('Failed to assign tag');
    } finally {
      setLoading(false);
    }
  };

  const removeTag = async (tagId) => {
    if (!customerId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/admin/customers/${customerId}/tags/${tagId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to remove tag');

      await fetchCustomerTags();
      onTagsUpdate && onTagsUpdate();
    } catch (error) {
      setError('Failed to remove tag');
    } finally {
      setLoading(false);
    }
  };

  const getUnassignedTags = () => {
    const assignedTagIds = customerTags.map(tag => tag.id);
    return availableTags.filter(tag => !assignedTagIds.includes(tag.id));
  };

  return (
    <div className={styles.tagManager}>
      <h3>Customer Tags</h3>
      
      {error && <div className={styles.error}>{error}</div>}

      {/* Current Tags */}
      <div className={styles.currentTags}>
        <h4>Current Tags</h4>
        {customerTags.length > 0 ? (
          <div className={styles.tagsList}>
            {customerTags.map(tag => (
              <div
                key={tag.id}
                className={styles.tag}
                style={{ backgroundColor: tag.color }}
              >
                <span className={styles.tagName}>{tag.name}</span>
                <button
                  onClick={() => removeTag(tag.id)}
                  className={styles.removeTagButton}
                  disabled={loading}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className={styles.noTags}>No tags assigned</p>
        )}
      </div>

      {/* Available Tags */}
      <div className={styles.availableTags}>
        <h4>Available Tags</h4>
        {getUnassignedTags().length > 0 ? (
          <div className={styles.tagsList}>
            {getUnassignedTags().map(tag => (
              <div
                key={tag.id}
                className={styles.tag}
                style={{ backgroundColor: tag.color }}
              >
                <span className={styles.tagName}>{tag.name}</span>
                <button
                  onClick={() => assignTag(tag.id)}
                  className={styles.addTagButton}
                  disabled={loading}
                >
                  +
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className={styles.noTags}>All available tags assigned</p>
        )}
      </div>

      {/* Create New Tag */}
      <div className={styles.createTag}>
        <h4>Create New Tag</h4>
        <div className={styles.createTagForm}>
          <input
            type="text"
            placeholder="Tag name"
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            className={styles.tagNameInput}
          />
          <div className={styles.colorPicker}>
            {predefinedColors.map(color => (
              <button
                key={color}
                onClick={() => setNewTagColor(color)}
                className={`${styles.colorOption} ${newTagColor === color ? styles.selected : ''}`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <button
            onClick={createNewTag}
            disabled={!newTagName.trim() || loading}
            className={styles.createButton}
          >
            Create Tag
          </button>
        </div>
      </div>
    </div>
  );
}
```

## 3. Enhanced Customer Profile Dashboard

### Comprehensive Customer Profile Component
```jsx
// components/admin/EnhancedCustomerProfile.js
import { useState, useEffect } from 'react';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import CustomerTagManager from './CustomerTagManager';
import CustomerCommunicationHistory from './CustomerCommunicationHistory';
import CustomerBookingTimeline from './CustomerBookingTimeline';
import CustomerInsights from './CustomerInsights';
import styles from '@/styles/admin/EnhancedCustomerProfile.module.css';

export default function EnhancedCustomerProfile({ customerId }) {
  const [customer, setCustomer] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'bookings', label: 'Booking History', icon: '📅' },
    { id: 'communications', label: 'Communications', icon: '💬' },
    { id: 'analytics', label: 'Analytics', icon: '📈' },
    { id: 'preferences', label: 'Preferences', icon: '⚙️' },
    { id: 'tags', label: 'Tags & Segments', icon: '🏷️' }
  ];

  useEffect(() => {
    if (customerId) {
      fetchCustomerData();
      fetchCustomerAnalytics();
    }
  }, [customerId]);

  const fetchCustomerData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/customers/${customerId}/profile`);
      if (!response.ok) throw new Error('Failed to fetch customer data');
      
      const data = await response.json();
      setCustomer(data.customer);
    } catch (error) {
      setError('Failed to load customer data');
      console.error('Error fetching customer:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomerAnalytics = async () => {
    try {
      const response = await fetch(`/api/admin/customers/${customerId}/analytics`);
      if (!response.ok) throw new Error('Failed to fetch analytics');
      
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  };

  if (loading) {
    return <div className={styles.loading}>Loading customer profile...</div>;
  }

  if (error || !customer) {
    return <div className={styles.error}>{error || 'Customer not found'}</div>;
  }

  return (
    <div className={styles.customerProfile}>
      {/* Customer Header */}
      <div className={styles.customerHeader}>
        <div className={styles.customerBasicInfo}>
          <div className={styles.customerAvatar}>
            {customer.profile_image_url ? (
              <img src={customer.profile_image_url} alt={customer.name} />
            ) : (
              <div className={styles.avatarPlaceholder}>
                {customer.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div className={styles.customerDetails}>
            <h1 className={styles.customerName}>
              {customer.name}
              {customer.vip && <span className={styles.vipBadge}>VIP</span>}
            </h1>
            <div className={styles.customerMeta}>
              <span className={styles.customerEmail}>{customer.email}</span>
              {customer.phone && <span className={styles.customerPhone}>{customer.phone}</span>}
              <span className={styles.customerSince}>
                Customer since {new Date(customer.customer_since || customer.created_at).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        <div className={styles.customerActions}>
          <button className={styles.actionButton}>
            📧 Send Message
          </button>
          <button className={styles.actionButton}>
            📅 New Booking
          </button>
          <button className={styles.actionButton}>
            ✏️ Edit Profile
          </button>
        </div>
      </div>

      {/* Customer Key Metrics */}
      <div className={styles.customerMetrics}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>${customer.lifetime_value || 0}</div>
          <div className={styles.metricLabel}>Lifetime Value</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{customer.booking_count || 0}</div>
          <div className={styles.metricLabel}>Total Bookings</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{customer.customer_health_score || 50}%</div>
          <div className={styles.metricLabel}>Health Score</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>
            {customer.last_booking_date 
              ? Math.floor((new Date() - new Date(customer.last_booking_date)) / (1000 * 60 * 60 * 24))
              : 'Never'
            }
          </div>
          <div className={styles.metricLabel}>Days Since Last Booking</div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className={styles.tabNavigation}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`${styles.tab} ${activeTab === tab.id ? styles.active : ''}`}
          >
            <span className={styles.tabIcon}>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className={styles.tabContent}>
        {activeTab === 'overview' && (
          <CustomerOverviewTab customer={customer} analytics={analytics} />
        )}
        {activeTab === 'bookings' && (
          <CustomerBookingTimeline customerId={customerId} />
        )}
        {activeTab === 'communications' && (
          <CustomerCommunicationHistory customerId={customerId} />
        )}
        {activeTab === 'analytics' && (
          <CustomerAnalyticsTab analytics={analytics} />
        )}
        {activeTab === 'preferences' && (
          <CustomerPreferencesTab customer={customer} />
        )}
        {activeTab === 'tags' && (
          <CustomerTagManager 
            customerId={customerId} 
            onTagsUpdate={fetchCustomerData}
          />
        )}
      </div>
    </div>
  );
}

// Overview Tab Component
function CustomerOverviewTab({ customer, analytics }) {
  return (
    <div className={styles.overviewTab}>
      <div className={styles.overviewGrid}>
        <div className={styles.overviewSection}>
          <h3>Contact Information</h3>
          <div className={styles.contactInfo}>
            <div className={styles.contactItem}>
              <span className={styles.contactLabel}>Email:</span>
              <span className={styles.contactValue}>{customer.email}</span>
            </div>
            {customer.phone && (
              <div className={styles.contactItem}>
                <span className={styles.contactLabel}>Phone:</span>
                <span className={styles.contactValue}>{customer.phone}</span>
              </div>
            )}
            {customer.address && (
              <div className={styles.contactItem}>
                <span className={styles.contactLabel}>Address:</span>
                <span className={styles.contactValue}>
                  {customer.address}, {customer.city}, {customer.state} {customer.postal_code}
                </span>
              </div>
            )}
          </div>
        </div>

        <div className={styles.overviewSection}>
          <h3>Customer Insights</h3>
          <CustomerInsights customer={customer} analytics={analytics} />
        </div>

        <div className={styles.overviewSection}>
          <h3>Recent Activity</h3>
          <div className={styles.recentActivity}>
            {/* Recent activity timeline */}
          </div>
        </div>

        <div className={styles.overviewSection}>
          <h3>Quick Stats</h3>
          <div className={styles.quickStats}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Preferred Service:</span>
              <span className={styles.statValue}>{analytics?.preferredService || 'N/A'}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Avg Booking Value:</span>
              <span className={styles.statValue}>${analytics?.averageBookingValue || 0}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Booking Frequency:</span>
              <span className={styles.statValue}>{analytics?.bookingFrequency || 'N/A'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 4. Customer Analytics Dashboard

### Customer Analytics Component
```jsx
// components/admin/CustomerAnalytics.js
import { useState, useEffect } from 'react';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import styles from '@/styles/admin/CustomerAnalytics.module.css';

export default function CustomerAnalytics({ timeRange = '90d' }) {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('overview');

  const metrics = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'acquisition', label: 'Acquisition', icon: '📈' },
    { id: 'retention', label: 'Retention', icon: '🔄' },
    { id: 'value', label: 'Customer Value', icon: '💰' },
    { id: 'segmentation', label: 'Segmentation', icon: '🎯' },
    { id: 'satisfaction', label: 'Satisfaction', icon: '😊' }
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/analytics/customers?range=${timeRange}`);
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching customer analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className={styles.loading}>Loading analytics...</div>;
  }

  return (
    <div className={styles.customerAnalytics}>
      <div className={styles.analyticsHeader}>
        <h2>Customer Analytics</h2>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className={styles.timeRangeSelector}
        >
          <option value="30d">Last 30 Days</option>
          <option value="90d">Last 90 Days</option>
          <option value="180d">Last 6 Months</option>
          <option value="365d">Last Year</option>
        </select>
      </div>

      <div className={styles.metricsNavigation}>
        {metrics.map(metric => (
          <button
            key={metric.id}
            onClick={() => setSelectedMetric(metric.id)}
            className={`${styles.metricTab} ${selectedMetric === metric.id ? styles.active : ''}`}
          >
            <span className={styles.metricIcon}>{metric.icon}</span>
            {metric.label}
          </button>
        ))}
      </div>

      <div className={styles.analyticsContent}>
        {selectedMetric === 'overview' && (
          <CustomerOverviewAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'acquisition' && (
          <CustomerAcquisitionAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'retention' && (
          <CustomerRetentionAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'value' && (
          <CustomerValueAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'segmentation' && (
          <CustomerSegmentationAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'satisfaction' && (
          <CustomerSatisfactionAnalytics analytics={analytics} />
        )}
      </div>
    </div>
  );
}
```

This implementation provides a comprehensive foundation for the enhanced customer management system with advanced search, segmentation, analytics, and customer relationship management capabilities.
