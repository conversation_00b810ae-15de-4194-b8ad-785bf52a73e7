-- Add account_activation token type to application_tokens table
-- This allows the approval workflow to generate activation tokens

-- Drop existing constraint if it exists
ALTER TABLE public.application_tokens 
DROP CONSTRAINT IF EXISTS application_tokens_token_type_check;

-- Add new constraint that includes account_activation
ALTER TABLE public.application_tokens 
ADD CONSTRAINT application_tokens_token_type_check 
CHECK (token_type IN ('application_access', 'account_activation', 'password_reset'));

-- Add comment
COMMENT ON CONSTRAINT application_tokens_token_type_check ON public.application_tokens 
IS 'Allows application_access, account_activation, and password_reset token types';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Added account_activation token type to application_tokens table';
END $$;
