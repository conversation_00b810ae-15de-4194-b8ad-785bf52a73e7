#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Phase 2B Migration Data Verification Script
 *
 * This script verifies the Phase 2B data migration results by checking:
 * 1. Customer count and data quality (760+ expected)
 * 2. Invoice count and revenue data (73 invoices, $21,097.50+ AUD)
 * 3. Email campaigns and delivery tracking (39 campaigns)
 * 4. Product catalog and inventory (12 Split Cake products)
 * 5. Database relationships and data integrity
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyMigrationData() {
  console.log('🔍 Ocean Soul Sparkles Phase 2B Migration Verification...\n');

  const results = {
    customers: { expected: 760, actual: 0, status: '❌' },
    invoices: { expected: 73, actual: 0, status: '❌', revenue: 0 },
    emailCampaigns: { expected: 39, actual: 0, status: '❌' },
    products: { expected: 12, actual: 0, status: '❌', inventory: 0 },
    relationships: { status: '❌', details: [] },
    dataQuality: { status: '❌', issues: [] }
  };

  try {
    // 1. Verify Customer Count
    console.log('📊 Checking customer data...');
    const { count: customerCount, error: customerError } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true });

    if (customerError) {
      console.error('❌ Error fetching customers:', customerError.message);
    } else {
      results.customers.actual = customerCount;
      results.customers.status = customerCount >= 760 ? '✅' : '⚠️';
      console.log(`   Total customers: ${customerCount} (expected: 760+)`);
    }

    // 2. Verify Invoice Count and Revenue
    console.log('💰 Checking invoice data...');
    const { count: invoiceCount, error: invoiceError } = await supabase
      .from('invoices')
      .select('*', { count: 'exact', head: true });

    if (invoiceError) {
      console.error('❌ Error fetching invoices:', invoiceError.message);
    } else {
      results.invoices.actual = invoiceCount;
      results.invoices.status = invoiceCount === 73 ? '✅' : '⚠️';
      console.log(`   Total invoices: ${invoiceCount} (expected: 73)`);

      // Get revenue total
      const { data: invoiceData } = await supabase
        .from('invoices')
        .select('total_amount');

      if (invoiceData) {
        const totalRevenue = invoiceData.reduce((sum, inv) => sum + (inv.total_amount || 0), 0);
        results.invoices.revenue = totalRevenue;
        console.log(`   Total revenue: $${totalRevenue.toFixed(2)} AUD (expected: $21,097.50+)`);
      }
    }

    // 3. Verify Email Campaigns
    console.log('📧 Checking email campaigns...');
    const { count: campaignCount, error: campaignError } = await supabase
      .from('email_campaigns')
      .select('*', { count: 'exact', head: true });

    if (campaignError) {
      console.error('❌ Error fetching email campaigns:', campaignError.message);
    } else {
      results.emailCampaigns.actual = campaignCount;
      results.emailCampaigns.status = campaignCount === 39 ? '✅' : '⚠️';
      console.log(`   Total email campaigns: ${campaignCount} (expected: 39)`);
    }

    // 4. Verify Product Count and Inventory
    console.log('🛍️ Checking product data...');
    const { count: productCount, error: productError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (productError) {
      console.error('❌ Error fetching products:', productError.message);
    } else {
      results.products.actual = productCount;
      results.products.status = productCount === 12 ? '✅' : '⚠️';
      console.log(`   Total products: ${productCount} (expected: 12)`);

      // Get inventory total
      const { data: productData } = await supabase
        .from('products')
        .select('stock');

      if (productData) {
        const totalInventory = productData.reduce((sum, prod) => sum + (prod.stock || 0), 0);
        results.products.inventory = totalInventory;
        console.log(`   Total inventory: ${totalInventory} units (expected: 479)`);
      }
    }

    // 4. Verify Customer-Booking Relationships
    console.log('🔗 Checking data relationships...');
    const { data: bookingsWithCustomers, error: relationError } = await supabase
      .from('bookings')
      .select(`
        id,
        customer_id,
        customers:customer_id (id, name, email)
      `)
      .limit(10);

    if (relationError) {
      console.error('❌ Error checking relationships:', relationError.message);
      results.relationships.details.push('Failed to fetch booking-customer relationships');
    } else {
      const orphanedBookings = bookingsWithCustomers.filter(b => !b.customers);
      if (orphanedBookings.length === 0) {
        results.relationships.status = '✅';
        results.relationships.details.push('All bookings have valid customer relationships');
      } else {
        results.relationships.status = '⚠️';
        results.relationships.details.push(`${orphanedBookings.length} bookings have missing customer relationships`);
      }
      console.log(`   Checked ${bookingsWithCustomers.length} booking relationships`);
    }

    // 5. Data Quality Checks
    console.log('🔍 Checking data quality...');

    // Check for customers with valid emails
    const { data: customersWithEmails, error: emailError } = await supabase
      .from('customers')
      .select('id, email')
      .not('email', 'is', null)
      .neq('email', '');

    if (!emailError && customersWithEmails) {
      const emailValidationRate = (customersWithEmails.length / results.customers.actual) * 100;
      if (emailValidationRate >= 95) {
        results.dataQuality.status = '✅';
      } else {
        results.dataQuality.issues.push(`Email validation rate: ${emailValidationRate.toFixed(1)}% (expected: >95%)`);
      }
      console.log(`   Email validation rate: ${emailValidationRate.toFixed(1)}%`);
    }

    // Check for customers with phone numbers
    const { data: customersWithPhones, error: phoneError } = await supabase
      .from('customers')
      .select('id, phone')
      .not('phone', 'is', null)
      .neq('phone', '');

    if (!phoneError && customersWithPhones) {
      const phoneValidationRate = (customersWithPhones.length / results.customers.actual) * 100;
      console.log(`   Phone validation rate: ${phoneValidationRate.toFixed(1)}%`);
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    return false;
  }

  // Print Summary
  console.log('\n📋 PHASE 2B MIGRATION VERIFICATION SUMMARY');
  console.log('==========================================');
  console.log(`${results.customers.status} Customers: ${results.customers.actual}/${results.customers.expected}+`);
  console.log(`${results.invoices.status} Invoices: ${results.invoices.actual}/${results.invoices.expected} ($${results.invoices.revenue.toFixed(2)} AUD)`);
  console.log(`${results.emailCampaigns.status} Email Campaigns: ${results.emailCampaigns.actual}/${results.emailCampaigns.expected}`);
  console.log(`${results.products.status} Products: ${results.products.actual}/${results.products.expected} (${results.products.inventory} units)`);
  console.log(`${results.relationships.status} Relationships: ${results.relationships.details.join(', ')}`);
  console.log(`${results.dataQuality.status} Data Quality: ${results.dataQuality.issues.length === 0 ? 'Excellent' : results.dataQuality.issues.join(', ')}`);

  const totalRecords = results.customers.actual + results.invoices.actual + results.emailCampaigns.actual + results.products.actual;
  console.log(`\n📊 Total Migrated Records: ${totalRecords}`);
  console.log(`💰 Total Business Value: $${results.invoices.revenue.toFixed(2)} AUD revenue + ${results.products.inventory} inventory units`);

  const overallStatus = [
    results.customers.status,
    results.invoices.status,
    results.emailCampaigns.status,
    results.products.status,
    results.relationships.status,
    results.dataQuality.status
  ].every(status => status === '✅') ? '🎉 PHASE 2B: 100% SUCCESS!' : '⚠️ NEEDS ATTENTION';

  console.log(`\n🎯 Overall Migration Status: ${overallStatus}`);

  if (overallStatus.includes('SUCCESS')) {
    console.log('\n✅ All Ocean Soul Sparkles data successfully migrated!');
    console.log('✅ Admin panel should have full access to all business data.');
    console.log('✅ Ready for business operations and e-commerce functionality.');
  }

  return overallStatus.includes('SUCCESS');
}

// Run verification
if (require.main === module) {
  verifyMigrationData()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Verification script failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyMigrationData };
