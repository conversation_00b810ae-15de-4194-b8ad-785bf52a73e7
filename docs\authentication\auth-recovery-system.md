# Authentication Recovery System

This document explains the updated authentication recovery system that replaces the legacy MCP-based patch script.

## Overview

The authentication recovery system provides automatic detection and resolution of common authentication issues without requiring manual intervention.

## Components

### 1. Updated Patch Script (`fix-admin-auth-updated.js`)

**✅ What's New:**
- Removed all MCP/legacy token references
- Only handles current tokens: `oss_auth_token`, `sb_auth_token`
- Added automatic failure tracking
- Enhanced logging and debugging
- Added auto-recovery mechanisms

**🔧 Usage:**
```bash
# Apply the updated patch
node patches/fix-admin-auth-updated.js

# Restart your server
npm run dev
```

### 2. Client-Side Auto-Recovery (`auth-auto-recovery.js`)

**✅ Features:**
- Monitors authentication failures automatically
- Clears corrupted session data
- Automatically redirects to login when needed
- Provides manual recovery button in development
- Health check monitoring

**🔧 Integration:**
Add to your admin layout or main app component:
```javascript
import '/scripts/auth-auto-recovery.js';
```

### 3. Health Check Endpoint (`/api/admin/health`)

**✅ Purpose:**
- Provides a simple way to test authentication status
- Used by auto-recovery to detect stuck states
- Returns detailed authentication information

**🔧 Usage:**
```bash
# Test authentication health
curl http://localhost:3000/api/admin/health
```

## Configuration

### Environment Variables

Add these to your `.env.local` file:

```env
# Enable automatic recovery (recommended)
ENABLE_AUTH_AUTO_RECOVERY=true

# Enable development bypass (development only)
ENABLE_AUTH_BYPASS=true
```

### Auto-Recovery Settings

The system triggers recovery when:
- 3+ authentication failures within 5 minutes
- Server sends recovery signals
- Health check fails consistently

## How It Works

### Automatic Detection
1. **API Monitoring**: Watches for 401/403 responses to admin endpoints
2. **Health Checks**: Periodic checks every 2 minutes
3. **Server Signals**: Responds to server-side recovery triggers

### Recovery Process
1. **Clear Authentication Data**: Removes all tokens and session data
2. **Reset Browser Storage**: Clears corrupted Supabase data
3. **Reset Auth State**: Signs out from all services
4. **Redirect to Login**: Automatically redirects to fresh login

### Failure Tracking
- Tracks authentication failures with timestamps
- Resets counter after 5-minute window
- Stores failure log in `.auth-failures` file

## Development Features

### Manual Recovery Button
In development mode, a "🔧 Fix Auth" button appears in the top-right corner for manual recovery.

### Enhanced Logging
All authentication attempts are logged with unique IDs for easy debugging:
```
[a1b2c3] Starting authentication process
[a1b2c3] Request URL: /api/admin/dashboard
[a1b2c3] Headers present: authorization, cookie, user-agent
[a1b2c3] Found OSS token in cookies
```

## Migration from Legacy Script

### Old Script Issues ❌
- Referenced obsolete MCP tokens
- Required manual execution
- No automatic recovery
- Limited debugging information

### New System Benefits ✅
- Only handles current token types
- Automatic detection and recovery
- Comprehensive logging
- Health monitoring
- Development tools

## Usage Scenarios

### When Authentication Gets Stuck

**Before (Manual):**
1. Notice authentication not working
2. Remember to run patch script
3. Stop server
4. Run `node patches/fix-admin-auth.js`
5. Restart server
6. Try logging in again

**Now (Automatic):**
1. System detects stuck authentication
2. Automatically clears corrupted data
3. Redirects to fresh login
4. No manual intervention needed

### For Development Testing

**Manual Recovery:**
```javascript
// In browser console
window.authAutoRecovery.trigger();
```

**Or click the "🔧 Fix Auth" button**

### For Production Monitoring

The system logs all recovery actions:
```
🔄 Auth auto-recovery initialized
⚠️ Auth failure detected (3/3)
🔧 Starting authentication recovery...
🧹 Cleared authentication data
🗄️ Cleared browser storage
🔓 Signed out from Supabase
🔄 Redirecting to login...
✅ Authentication recovery completed
```

## Safety Features

### Development Only
- Auth bypass only works with `NODE_ENV=development`
- Recovery button only appears in development
- Enhanced logging respects production settings

### Graceful Degradation
- Falls back to manual recovery if auto-recovery fails
- Preserves backup files
- Never deletes permanent data

### Rate Limiting
- Recovery only triggers after multiple failures
- 5-minute cooldown window
- Prevents infinite recovery loops

## Troubleshooting

### Auto-Recovery Not Working

1. **Check Environment Variables:**
   ```env
   ENABLE_AUTH_AUTO_RECOVERY=true
   ```

2. **Check Console Logs:**
   ```
   🔄 Auth auto-recovery initialized
   ```

3. **Verify Health Endpoint:**
   ```bash
   curl http://localhost:3000/api/admin/health
   ```

### Manual Recovery Still Needed

If automatic recovery fails, you can still use the updated patch script:

```bash
node patches/fix-admin-auth-updated.js
```

### Checking Recovery Status

View the failure log:
```bash
cat .auth-failures
```

## Best Practices

### For Development
1. Enable auto-recovery: `ENABLE_AUTH_AUTO_RECOVERY=true`
2. Use auth bypass for testing: `ENABLE_AUTH_BYPASS=true`
3. Monitor console logs for recovery actions
4. Use manual recovery button when needed

### For Production
1. Enable auto-recovery: `ENABLE_AUTH_AUTO_RECOVERY=true`
2. **Never** enable auth bypass: Remove `ENABLE_AUTH_BYPASS`
3. Monitor logs for recovery patterns
4. Set up alerting for frequent recoveries

### For Staging
1. Test auto-recovery under load
2. Verify recovery doesn't affect legitimate users
3. Check performance impact of health checks

## Support

If you continue to experience authentication issues:

1. Check the logs for recovery actions
2. Verify environment variables are set correctly
3. Test the health endpoint manually
4. Use the updated patch script as a fallback
5. Check for any remaining MCP references in your codebase

The new system should eliminate the need for manual intervention in most authentication stuck states.
