// Standard error classes for API client

export class StandardError extends Error {
  constructor(message) {
    super(message);
    this.name = 'StandardError';
  }
}

export class NetworkError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class AuthenticationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NotFoundError';
  }
}

// Add the remaining error classes
export class ConflictError extends Error {}
export class RateLimitError extends Error {}
export class ServerError extends Error {}
export class ServiceUnavailableError extends Error {}
export class PaymentError extends Error {}
export class InvalidInputError extends Error {}
export class OperationFailedError extends Error {}
export class ExternalServiceError extends Error {}
export class ConfigurationError extends Error {}
export class DatabaseError extends Error {}
export class FileSystemError extends Error {}
export class TimeoutError extends Error {}
export class PermissionDeniedError extends Error {}
export class ResourceLockedError extends Error {}
export class QuotaExceededError extends Error {}
export class InvalidStateError extends Error {}
export class ConcurrencyError extends Error {}
export class MaintenanceError extends Error {}
export class DeprecatedError extends Error {}
export class UserCancelledError extends Error {}
export class UnknownError extends Error {}