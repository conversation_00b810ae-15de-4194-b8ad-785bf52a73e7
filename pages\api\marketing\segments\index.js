import supabase, { getCurrentUser } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getSegments(req, res)
    case 'POST':
      return createSegment(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get segments with optional filters
async function getSegments(req, res) {
  const { search, sort_by = 'created_at', sort_order = 'desc', limit = 10, offset = 0 } = req.query

  try {
    // Use Supabase client directly
    const client = supabase;

    let query = client
      .from('customer_segments')
      .select('*', { count: 'exact' })

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    if (limit) {
      query = query.limit(limit)
    }
    if (offset) {
      query = query.offset(offset)
    }

    // Execute query
    const { data, error, count } = await query

    if (error) {
      throw error
    }

    // Get customer counts for each segment
    const segmentsWithCounts = await Promise.all(
      data.map(async (segment) => {
        const customerCount = await getSegmentCustomerCount(segment)
        return {
          ...segment,
          customer_count: customerCount
        }
      })
    )

    return res.status(200).json({
      segments: segmentsWithCounts,
      total: count
    })
  } catch (error) {
    console.error('Error fetching segments:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Create a new segment
async function createSegment(req, res) {
  const { name, description, segment_query } = req.body

  try {
    // Get current user
    const { user } = await getCurrentUserFromRequest(req)

    // Validate required fields
    if (!name || !segment_query) {
      return res.status(400).json({ error: 'Name and segment query are required' })
    }

    // Create segment
    const { data, error } = await supabase
      .from('customer_segments')
      .insert([
        {
          name,
          description,
          segment_query,
          created_by: user.id
        }
      ])
      .select()

    if (error) {
      throw error
    }

    return res.status(201).json(data[0])
  } catch (error) {
    console.error('Error creating segment:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to get customer count for a segment
async function getSegmentCustomerCount(segment) {
  try {
    const query = buildSegmentQuery(segment.segment_query)
    const { count } = await query.select('id', { count: 'exact', head: true })
    return count || 0
  } catch (error) {
    console.error(`Error getting customer count for segment ${segment.id}:`, error)
    return 0
  }
}

// Helper function to build a Supabase query from segment conditions
function buildSegmentQuery(segmentQuery) {
  let query = supabase.from('customers').select('*')

  if (!segmentQuery || !segmentQuery.groups || segmentQuery.groups.length === 0) {
    return query
  }

  // Process each group
  segmentQuery.groups.forEach((group, groupIndex) => {
    const { operator, conditions } = group

    if (!conditions || conditions.length === 0) {
      return
    }

    // Start a new filter group
    let filterExpression = ''

    // Process each condition in the group
    conditions.forEach((condition, conditionIndex) => {
      const { type, field, operator: condOperator, value } = condition

      // Skip invalid conditions
      if (!field || !condOperator) {
        return
      }

      let conditionFilter = ''

      // Build filter based on condition type and operator
      if (type === 'customer') {
        // Direct customer field filters
        if (field === 'marketing_consent') {
          conditionFilter = `${field}.eq.${value}`
        } else if (['city', 'state', 'country'].includes(field)) {
          switch (condOperator) {
            case 'equals':
              conditionFilter = `${field}.eq.${value}`
              break
            case 'not_equals':
              conditionFilter = `${field}.neq.${value}`
              break
            case 'contains':
              conditionFilter = `${field}.ilike.%${value}%`
              break
            case 'in':
              // Handle comma-separated values
              const inValues = value.split(',').map(v => v.trim())
              conditionFilter = `${field}.in.(${inValues.join(',')})`
              break
            case 'not_in':
              const notInValues = value.split(',').map(v => v.trim())
              conditionFilter = `${field}.not.in.(${notInValues.join(',')})`
              break
          }
        }
      } else if (type === 'booking') {
        // Booking-related filters require joins or subqueries
        // This is a simplified implementation
        if (field === 'booking_count') {
          // This would require a more complex query in a real implementation
          // For now, we'll use a placeholder
          conditionFilter = `id.in.(SELECT customer_id FROM bookings GROUP BY customer_id HAVING COUNT(*) ${getOperatorSymbol(condOperator)} ${value})`
        } else if (field === 'service_id') {
          conditionFilter = `id.in.(SELECT customer_id FROM bookings WHERE service_id = '${value}')`
        }
      } else if (type === 'time') {
        // Time-based filters
        if (field === 'created_at') {
          const dateValue = parseTimeValue(value)
          switch (condOperator) {
            case 'greater_than':
              conditionFilter = `${field}.gte.${dateValue}`
              break
            case 'less_than':
              conditionFilter = `${field}.lte.${dateValue}`
              break
          }
        }
      }

      // Add the condition to the filter expression
      if (conditionFilter) {
        if (filterExpression) {
          filterExpression += `,${conditionFilter}`
        } else {
          filterExpression = conditionFilter
        }
      }
    })

    // Add the group filter to the query
    if (filterExpression) {
      if (groupIndex === 0) {
        query = query.or(filterExpression)
      } else {
        // For subsequent groups, we need to use the logical operator
        if (operator === 'and') {
          query = query.and(filterExpression)
        } else {
          query = query.or(filterExpression)
        }
      }
    }
  })

  return query
}

// Helper function to get operator symbol
function getOperatorSymbol(operator) {
  switch (operator) {
    case 'equals':
      return '='
    case 'not_equals':
      return '!='
    case 'greater_than':
      return '>'
    case 'less_than':
      return '<'
    case 'between':
      return 'BETWEEN'
    default:
      return '='
  }
}

// Helper function to parse time values like "30d" (30 days)
function parseTimeValue(value) {
  if (!value) return null

  // If it's already a date string, return it
  if (value.includes('-') || value.includes('/')) {
    return value
  }

  // Parse time periods like "30d", "6m", "1y"
  const match = value.match(/^(\d+)([dmy])$/)
  if (match) {
    const amount = parseInt(match[1])
    const unit = match[2]

    const date = new Date()

    switch (unit) {
      case 'd': // days
        date.setDate(date.getDate() - amount)
        break
      case 'm': // months
        date.setMonth(date.getMonth() - amount)
        break
      case 'y': // years
        date.setFullYear(date.getFullYear() - amount)
        break
    }

    return date.toISOString()
  }

  return value
}
