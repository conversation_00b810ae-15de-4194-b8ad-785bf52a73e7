.userFormContainer {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Role Selection Section - Priority styling */
.roleSelectionSection {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border: 2px solid #6a0dad;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 10px;
}

.primaryLabel {
  font-weight: 600;
  color: #6a0dad;
  font-size: 1.1rem;
  margin-bottom: 8px;
}

.roleSelect {
  font-size: 1.1rem;
  font-weight: 500;
  border: 2px solid #6a0dad;
  background-color: white;
}

.roleDescription {
  font-size: 0.95rem;
  color: #555;
  margin-top: 8px;
  font-weight: 500;
}

.workflowInfo {
  font-size: 0.9rem;
  color: #666;
  margin-top: 6px;
  padding: 8px 12px;
  background-color: rgba(106, 13, 173, 0.05);
  border-radius: 6px;
  border-left: 3px solid #6a0dad;
}

/* Form Sections */
.formSection {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.sectionTitle {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #6a0dad;
  padding-bottom: 8px;
}

/* Onboarding Info Section */
.onboardingInfo {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 1px solid #4caf50;
  border-radius: 8px;
  padding: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formRow {
  display: flex;
  gap: 20px;
}

.formRow .formGroup {
  flex: 1;
}

.formGroup label {
  font-weight: 500;
  color: #444;
  font-size: 0.95rem;
}

.input,
.select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.textarea {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.input:focus,
.select:focus,
.textarea:focus {
  border-color: #6a0dad;
  outline: none;
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
}

.inputError {
  border-color: #f44336;
}

.inputError:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
}

.errorMessage {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 4px;
}

.helpText {
  font-size: 0.85rem;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

/* Enhanced Info Message Styling */
.infoMessage {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid #4caf50;
  border-radius: 8px;
  color: #2e7d32;
}

.infoMessage svg {
  flex-shrink: 0;
  margin-top: 2px;
}

.infoMessage div {
  flex: 1;
}

.infoMessage strong {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
}

.infoMessage p {
  margin: 0;
  line-height: 1.4;
}

.passwordInputContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.passwordInputContainer .input {
  width: 100%;
  padding-right: 40px;
}

.passwordToggle {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.passwordToggle:hover {
  color: #495057;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 10px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancelButton,
.saveButton {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background-color: #e9e9e9;
}

.saveButton {
  background-color: #6a0dad;
  color: white;
  border: none;
}

.saveButton:hover {
  background-color: #5a0b9d;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #444;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #6a0dad;
  cursor: pointer;
}

@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: 15px;
  }
}
