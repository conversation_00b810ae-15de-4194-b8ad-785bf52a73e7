/* ServiceList.module.css */
.serviceListContainer {
  background-color: #fff;
}

.filters {
  margin-bottom: 1.5rem;
}

.searchBox {
  margin-bottom: 1rem;
}

.searchInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filterControls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filterItem {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filterItem label {
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: #666;
}

.filterItem select,
.filterItem input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.loadingSpinner {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.errorMessage {
  background-color: #fff0f0;
  color: #d32f2f;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  border-left: 4px solid #d32f2f;
}

.serviceList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.serviceItem {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 1rem 0;
  transition: background-color 0.2s;
  cursor: pointer;
}

.serviceItem:hover {
  background-color: #f9f9f9;
}

.serviceItem.selected {
  background-color: #f0f7ff;
}

.serviceImageContainer {
  width: 80px;
  height: 80px;
  position: relative;
  margin-right: 1rem;
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.serviceImage {
  object-fit: cover;
}

.servicePlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  color: #999;
  font-size: 1.5rem;
}

.serviceDetails {
  flex-grow: 1;
}

.serviceHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.serviceName {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

.servicePrice {
  font-weight: 600;
  color: #2e7d32;
  font-size: 1rem;
}

.serviceCategory {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1565c0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.serviceStatus {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.statusActive {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.statusDraft {
  background-color: #fffde7;
  color: #f57f17;
}

.statusInactive {
  background-color: #f5f5f5;
  color: #757575;
}

.serviceDescription {
  color: #666;
  font-size: 0.9rem;
  margin: 0.5rem 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.serviceFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: #757575;
}

.serviceActions {
  display: flex;
  gap: 10px;
}

.actionButton {
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  color: #1976d2;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
}

.actionButton:hover {
  text-decoration: underline;
}

.noResults {
  text-align: center;
  padding: 2rem;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 4px;
}

/* Table styles */
.tableContainer {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.serviceTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.serviceTable th {
  background-color: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  cursor: pointer;
  user-select: none;
}

.serviceTable th:hover {
  background-color: #e9ecef;
}

.serviceTable td {
  padding: 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.serviceTable tr:hover {
  background-color: #f8f9fa;
}

.imageCell {
  width: 70px;
  text-align: center;
}

.noImage {
  width: 50px;
  height: 50px;
  background-color: #f0f0f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: #999;
}

.imageFallback {
  width: 50px;
  height: 50px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 1.2rem;
  margin: 0 auto;
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.statusActive {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.statusInactive {
  background-color: #ffebee;
  color: #c62828;
}

.actions {
  text-align: center;
  width: 100px;
}

.editButton {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.editButton:hover {
  background-color: #1565c0;
}

.sortIndicator {
  margin-left: 4px;
  color: #666;
}

/* Pricing tiers display */
.pricingTiers {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.mainPrice {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.tierCount {
  font-size: 0.75rem;
  color: #7f8c8d;
  font-style: italic;
}

.durationRange {
  font-size: 0.85rem;
  color: #2c3e50;
}

/* Enhanced Dashboard Styles */

/* Bulk Actions */
.bulkActions {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.bulkInfo {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.bulkButtons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.bulkButton {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.bulkButton:hover:not(:disabled) {
  background-color: #0056b3;
}

.bulkButton:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.bulkButtonCancel {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.bulkButtonCancel:hover {
  background-color: #545b62;
}

/* Checkbox Column */
.checkboxColumn {
  width: 40px;
  text-align: center;
  padding: 8px !important;
}

.checkboxColumn input[type="checkbox"] {
  cursor: pointer;
  transform: scale(1.1);
}

/* Visibility Column */
.visibilityColumn {
  width: 120px;
  padding: 8px !important;
}

.visibilityIndicators {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.visibilityBadge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  cursor: help;
}

.visibilityVisible {
  background-color: #d4edda;
  color: #155724;
}

.visibilityHidden {
  background-color: #f8d7da;
  color: #721c24;
}



/* Enhanced Filter Controls */
.filterControls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: end;
}

.filterItem {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filterItem:last-child {
  min-width: 180px; /* Slightly wider for visibility filter */
}

/* Responsive Design */
@media (max-width: 1200px) {
  .visibilityIndicators {
    flex-direction: row;
    gap: 2px;
  }

  .visibilityBadge {
    font-size: 0.6rem;
    padding: 1px 4px;
  }


}

@media (max-width: 768px) {
  .bulkActions {
    flex-direction: column;
    align-items: stretch;
  }

  .bulkButtons {
    justify-content: center;
  }

  .filterControls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filterItem {
    min-width: 100%;
  }

  /* Hide some columns on mobile */
  .visibilityColumn {
    display: none;
  }

  .serviceTable th:nth-child(5), /* Duration */
  .serviceTable td:nth-child(5) {
    display: none;
  }
}
