import { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';

/**
 * Authentication Test Page
 *
 * This page provides a UI for running authentication tests and viewing the results.
 * It loads the auth-test-helper.js script and provides buttons to run various tests.
 */
export default function AuthTestPage() {
  const { user, role, loading } = useAuth();
  const router = useRouter();
  const [testResults, setTestResults] = useState(null);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [authInfo, setAuthInfo] = useState(null);

  // Check if user is authenticated and has admin role
  useEffect(() => {
    if (!loading && (!user || role !== 'admin')) {
      toast.error('You must be an admin to access this page');
      router.push('/admin/login');
    }
  }, [user, role, loading, router]);

  // Get authentication information
  useEffect(() => {
    if (user && typeof window !== 'undefined' && window.authTest) {
      const getAuthInfo = async () => {
        try {
          const token = window.authTest.extractToken();
          const authCheck = await window.authTest.checkAuth();

          setAuthInfo({
            user: user,
            role: role,
            tokenAvailable: !!token,
            tokenPrefix: token ? token.substring(0, 10) + '...' : 'N/A',
            authCheckStatus: authCheck.status,
            authCheckSuccess: authCheck.status === 200
          });
        } catch (error) {
          console.error('Error getting auth info:', error);
        }
      };

      getAuthInfo();
    }
  }, [user, role]);

  // Run all tests
  const runAllTests = async () => {
    if (typeof window === 'undefined' || !window.authTest) {
      toast.error('Auth test helper not loaded');
      return;
    }

    setIsRunningTests(true);
    toast.info('Running authentication tests...');

    try {
      const results = await window.authTest.runAllTests();
      setTestResults(results);

      const passCount = Object.values(results.tests).filter(t => t.status === 'Pass').length;
      const totalCount = Object.keys(results.tests).length;

      if (passCount === totalCount) {
        toast.success(`All tests passed (${passCount}/${totalCount})`);
      } else {
        toast.warning(`Some tests failed (${passCount}/${totalCount} passed)`);
      }
    } catch (error) {
      console.error('Error running tests:', error);
      toast.error('Error running tests: ' + error.message);
    } finally {
      setIsRunningTests(false);
    }
  };

  // Test session persistence
  const testSessionPersistence = () => {
    if (typeof window === 'undefined' || !window.authTest) {
      toast.error('Auth test helper not loaded');
      return;
    }

    toast.info('Testing session persistence. Page will refresh in 3 seconds...');
    window.authTest.testSessionPersistence();
  };

  // Monitor token refresh
  const monitorTokenRefresh = async () => {
    if (typeof window === 'undefined' || !window.authTest) {
      toast.error('Auth test helper not loaded');
      return;
    }

    const duration = 5 * 60 * 1000; // 5 minutes
    toast.info(`Monitoring token refresh for ${duration / 1000 / 60} minutes...`);

    setIsRunningTests(true);
    try {
      const results = await window.authTest.monitorTokenRefresh(duration);
      toast.info(`Token refresh monitoring completed. Refreshes: ${results.refreshCount}`);
    } catch (error) {
      console.error('Error monitoring token refresh:', error);
      toast.error('Error monitoring token refresh: ' + error.message);
    } finally {
      setIsRunningTests(false);
    }
  };

  return (
    <>
      <Head>
        <title>Authentication Tests | Ocean Soul Sparkles Admin</title>
        <script src="/js/auth-test-helper.js" />
      </Head>

      <AdminLayout>
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-6">Authentication Tests</h1>

          {/* Authentication Information */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Authentication Information</h2>

            {authInfo ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><strong>User:</strong> {authInfo.user?.email || 'Not authenticated'}</p>
                  <p><strong>Role:</strong> {authInfo.role || 'None'}</p>
                  <p><strong>Token Available:</strong> {authInfo.tokenAvailable ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <p><strong>Token Prefix:</strong> {authInfo.tokenPrefix}</p>
                  <p><strong>Auth Check Status:</strong> {authInfo.authCheckStatus}</p>
                  <p><strong>Auth Check Success:</strong> {authInfo.authCheckSuccess ? 'Yes' : 'No'}</p>
                </div>
              </div>
            ) : (
              <p>Loading authentication information...</p>
            )}
          </div>

          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>

            <div className="flex flex-wrap gap-4">
              <button
                onClick={runAllTests}
                disabled={isRunningTests}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                Run All Tests
              </button>

              <button
                onClick={testSessionPersistence}
                disabled={isRunningTests}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                Test Session Persistence
              </button>

              <button
                onClick={monitorTokenRefresh}
                disabled={isRunningTests}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                Monitor Token Refresh (5 min)
              </button>
            </div>

            {isRunningTests && (
              <p className="mt-4 text-blue-500">
                Tests are running. Please check the browser console for detailed output.
              </p>
            )}
          </div>

          {/* Test Results */}
          {testResults && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Test Results</h2>

              <p className="mb-2">
                <strong>Timestamp:</strong> {testResults.timestamp}
              </p>

              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                  <thead>
                    <tr>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Test</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Name</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Status</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Details</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(testResults.tests).map(([testId, test]) => (
                      <tr key={testId}>
                        <td className="py-2 px-4 border-b border-gray-200">{testId}</td>
                        <td className="py-2 px-4 border-b border-gray-200">{test.name}</td>
                        <td className={`py-2 px-4 border-b border-gray-200 ${
                          test.status === 'Pass' ? 'text-green-500' :
                          test.status === 'Fail' ? 'text-red-500' : 'text-yellow-500'
                        }`}>
                          {test.status}
                        </td>
                        <td className="py-2 px-4 border-b border-gray-200">
                          <pre className="text-xs overflow-x-auto">
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-4">
                <h3 className="text-lg font-semibold mb-2">Summary</h3>
                <p>
                  <strong>Total Tests:</strong> {Object.keys(testResults.tests).length}
                </p>
                <p>
                  <strong>Passed:</strong> {Object.values(testResults.tests).filter(t => t.status === 'Pass').length}
                </p>
                <p>
                  <strong>Failed:</strong> {Object.values(testResults.tests).filter(t => t.status === 'Fail').length}
                </p>
                <p>
                  <strong>Errors:</strong> {Object.values(testResults.tests).filter(t => t.status === 'Error').length}
                </p>
              </div>
            </div>
          )}
        </div>
      </AdminLayout>
    </>
  );
}
