import { useState } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import CustomerForm from '@/components/admin/CustomerForm'
import styles from '@/styles/admin/CustomersPage.module.css'

export default function NewCustomerPage() {
  return (
    <ProtectedRoute>
      <AdminLayout title="Add New Customer">
        <div className={styles.customersPage}>
          <div className={styles.header}>
            <h2>Add New Customer</h2>
          </div>
          <CustomerForm />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
