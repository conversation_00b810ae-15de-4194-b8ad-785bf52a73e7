# Deployment Test Runner for Ocean Soul Sparkles
# This script tests the application in both development and production modes

# Set strict error handling
$ErrorActionPreference = "Stop"

# Colors for output
$CYAN = [System.ConsoleColor]::Cyan
$YELLOW = [System.ConsoleColor]::Yellow
$GREEN = [System.ConsoleColor]::Green
$RED = [System.ConsoleColor]::Red

# Helper functions
function Write-Header {
    param([string]$text)
    Write-Host "`n============================================" -ForegroundColor $CYAN
    Write-Host $text -ForegroundColor $CYAN
    Write-Host "============================================`n" -ForegroundColor $CYAN
}

function Write-Step {
    param([string]$text)
    Write-Host "➤ $text" -ForegroundColor $YELLOW
}

function Write-Success {
    param([string]$text)
    Write-Host "✅ $text" -ForegroundColor $GREEN
}

function Write-Error {
    param([string]$text)
    Write-Host "❌ $text" -ForegroundColor $RED
}

# Main test runner
$START_TIME = Get-Date

Write-Header "OCEAN SOUL SPARKLES DEPLOYMENT TEST RUNNER"
Write-Host "Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Host "Environment: $(if ($env:NODE_ENV -eq 'production') { 'Production' } else { 'Development' })"

try {
    # Step 1: Clean the environment
    Write-Step "Cleaning build artifacts and caches..."
    
    if (Test-Path ".next") {
        Remove-Item -Recurse -Force .next
    }
    
    if (Test-Path "node_modules/.cache") {
        Remove-Item -Recurse -Force node_modules/.cache
    }
    
    Write-Success "Environment cleaned"

    # Step 2: Check environment variables
    Write-Step "Checking environment variables..."
    
    $env:NODE_ENV = "development"
    node scripts/check-env.js
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Environment check failed for development mode"
        exit 1
    }
    
    Write-Success "Environment variables verified for development mode"

    # Step 3: Run development mode tests
    Write-Header "DEVELOPMENT MODE TESTING"
    
    Write-Step "Starting development server..."
    $devProcess = Start-Process -FilePath "npm" -ArgumentList "run", "dev" -PassThru -NoNewWindow
    
    Write-Step "Waiting for development server to start..."
    Start-Sleep -Seconds 10
    
    Write-Step "Testing development server connectivity..."
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 30
        if ($response.StatusCode -eq 200) {
            Write-Success "Development server is running correctly"
        } else {
            Write-Error "Development server returned status code $($response.StatusCode)"
            throw "Development server test failed"
        }
    }
    catch {
        Write-Error "Failed to connect to development server: $_"
        throw "Development server connection failed"
    }
    
    Write-Step "Stopping development server..."
    Stop-Process -Id $devProcess.Id -ErrorAction SilentlyContinue
    Write-Success "Development tests completed"

    # Step 4: Build for production
    Write-Header "PRODUCTION BUILD"
    
    Write-Step "Setting production environment..."
    $env:NODE_ENV = "production"
    
    Write-Step "Building for production..."
    npm run build
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Production build failed"
        exit 1
    }
    
    Write-Success "Production build completed successfully"

    # Step 5: Test production build
    Write-Header "PRODUCTION MODE TESTING"
    
    Write-Step "Starting production server..."
    $prodProcess = Start-Process -FilePath "npm" -ArgumentList "run", "start" -PassThru -NoNewWindow
    
    Write-Step "Waiting for production server to start..."
    Start-Sleep -Seconds 10
    
    Write-Step "Testing production server connectivity..."
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 30
        if ($response.StatusCode -eq 200) {
            Write-Success "Production server is running correctly"
        } else {
            Write-Error "Production server returned status code $($response.StatusCode)"
            throw "Production server test failed"
        }
    }
    catch {
        Write-Error "Failed to connect to production server: $_"
        throw "Production server connection failed"
    }
      Write-Step "Testing admin authentication..."
    try {
        $adminResponse = Invoke-WebRequest -Uri "http://localhost:3000/admin/login" -TimeoutSec 30
        if ($adminResponse.StatusCode -eq 200) {
            Write-Success "Admin login page is accessible"
        } else {
            Write-Error "Admin login page returned status code $($adminResponse.StatusCode)"
        }
    }
    catch {
        Write-Error "Failed to access admin login page: $_"
    }
    
    Write-Step "Testing API authentication..."
    try {
        $apiResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/auth/test" -TimeoutSec 10 -Method GET
        if ($apiResponse.StatusCode -eq 200) {
            Write-Success "API authentication endpoints are accessible"
        } else {
            Write-Error "API authentication test returned status code $($apiResponse.StatusCode)"
        }
        
        # Try to parse the response
        $responseContent = $apiResponse.Content | ConvertFrom-Json
        if ($responseContent.success -eq $true) {
            Write-Success "API authentication is properly configured"
        } else {
            Write-Error "API authentication configuration may have issues: $($responseContent.message)"
        }
    }
    catch {
        Write-Error "Failed to test API authentication: $_"
        Write-Host "This likely indicates a 401 Unauthorized error in the API" -ForegroundColor $YELLOW
    }
    
    Write-Step "Stopping production server..."
    Stop-Process -Id $prodProcess.Id -ErrorAction SilentlyContinue
    
    Write-Success "Production tests completed"

    # Final report
    $END_TIME = Get-Date
    $DURATION = $END_TIME - $START_TIME

    Write-Header "TEST SUMMARY"
    Write-Host "Start time: $($START_TIME.ToString('yyyy-MM-dd HH:mm:ss'))"
    Write-Host "End time:   $($END_TIME.ToString('yyyy-MM-dd HH:mm:ss'))"
    Write-Host "Duration:   $($DURATION.Minutes) minutes, $($DURATION.Seconds) seconds"
    Write-Success "All tests completed successfully!"

    Write-Host "`nThe application is ready for deployment!`n" -ForegroundColor $GREEN

}
catch {
    Write-Error "Test runner failed: $_"
    exit 1
}
