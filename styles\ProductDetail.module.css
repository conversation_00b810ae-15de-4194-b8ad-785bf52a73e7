.main {
  padding: 2rem 1rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: var(--light-text-color);
}

.breadcrumbs {
  margin-bottom: 2rem;
  color: var(--light-text-color);
}

.breadcrumbs a {
  color: var(--primary-color);
  margin: 0 0.5rem;
}

.breadcrumbs a:first-child {
  margin-left: 0;
}

/* Product detail */
.productDetail {
  display: flex;
  gap: 3rem;
  margin-bottom: 3rem;
}

.productImage {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.imagePlaceholder {
  width: 100%;
  height: 400px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.productImg {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 8px;
}

.productInfo {
  flex: 1;
}

.productName {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.productPrice {
  font-size: 1.5rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.productDescription {
  margin-bottom: 2rem;
  line-height: 1.8;
}

.productDetails {
  margin-bottom: 2rem;
}

.productDetails h2 {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.productDetails ul {
  list-style-position: inside;
  margin-left: 1rem;
}

.productDetails li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.addToCartButton {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.addToCartButton:hover {
  background-color: #156a87;
}

/* Related products */
.relatedProducts {
  margin-bottom: 3rem;
}

.relatedProducts h2 {
  text-align: center;
  color: var(--primary-color);
  margin-bottom: 2rem;
}

.relatedProductGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
}

.relatedProductCard {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.relatedProductCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.relatedProductImage {
  height: 200px;
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.relatedProductImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.relatedProductImg:hover {
  transform: scale(1.05);
}

.relatedProductCard h3 {
  padding: 1rem 1rem 0.5rem;
  font-size: 1.2rem;
}

.price {
  padding: 0 1rem 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

.button {
  display: block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  text-align: center;
  font-weight: 500;
  transition: background-color 0.3s ease;
  margin: 0 1rem 1rem;
  border-radius: var(--border-radius);
}

.button:hover {
  background-color: #156a87;
  text-decoration: none;
}

/* Responsive styles */
@media (max-width: 768px) {
  .productDetail {
    flex-direction: column;
  }

  .productImage {
    min-width: auto;
    max-width: 100%;
  }

  .imagePlaceholder, .productImg {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .relatedProductGrid {
    grid-template-columns: 1fr;
  }
}
