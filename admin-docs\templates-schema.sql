-- Marketing templates table
CREATE TABLE public.marketing_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  subject TEXT,
  content TEXT NOT NULL,
  template_type TEXT NOT NULL, -- 'email', 'sms', 'push'
  category TEXT, -- 'welcome', 'promotion', 'event', 'reminder', etc.
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.marketing_templates ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all marketing templates" ON public.marketing_templates
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert marketing templates" ON public.marketing_templates
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update their own marketing templates" ON public.marketing_templates
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  ) WITH CHECK (
    get_user_role(auth.uid()) = 'admin' OR 
    (get_user_role(auth.uid()) = 'staff' AND created_by = auth.uid())
  );

CREATE POLICY "Only admins can delete marketing templates" ON public.marketing_templates
  FOR DELETE USING (
    get_user_role(auth.uid()) = 'admin'
  );
