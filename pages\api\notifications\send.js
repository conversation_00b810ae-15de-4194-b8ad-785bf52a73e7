import supabase, { getCurrentUserFromRequest } from '@/lib/supabase';
import { sendCustomerNotification } from '@/lib/notifications-server';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { customer_id, title, message, type = 'email' } = req.body

  // Validate required fields
  if (!customer_id || !title || !message) {
    return res.status(400).json({ error: 'Customer ID, title, and message are required' })
  }

  try {
    // Use Supabase client directly
    const client = supabase;
    if (!client) {
      console.error("Supabase client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get customer details
    const { data: customer, error: customerError } = await client
      .from('customers')
      .select('name, email, phone')
      .eq('id', customer_id)
      .single()

    if (customerError) {
      throw customerError
    }

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' })
    }

    // Check if customer has required contact information for the notification type
    if (type === 'email' && !customer.email) {
      return res.status(400).json({ error: 'Customer does not have an email address' })
    }

    if (type === 'sms' && !customer.phone) {
      return res.status(400).json({ error: 'Customer does not have a phone number' })
    }

    // Record notification in database
    const { data: notification, error: notificationError } = await client
      .from('notifications')
      .insert([
        {
          title,
          message,
          notification_type: type,
          related_id: customer_id,
          user_id: null // This would be the customer's user ID if they have an account
        }
      ])
      .select()

    if (notificationError) {
      throw notificationError
    }

    // Send notification using the sendCustomerNotification function
    const notificationResult = await sendCustomerNotification({
      customerId: customer_id,
      title,
      message,
      type
    })

    // Return success with notification details
    return res.status(200).json({
      success: true,
      message: `Notification sent to ${customer.name} via ${type}`,
      notification: notification[0],
      notificationResult
    })
  } catch (error) {
    console.error('Error sending notification:', error)
    return res.status(500).json({ error: 'Failed to send notification' })
  }
}
