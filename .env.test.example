# Supabase configuration for testing
NEXT_PUBLIC_SUPABASE_URL=https://your-test-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# API URL for testing (use localhost for local testing)
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# OneSignal configuration (can be the same as development)
NEXT_PUBLIC_ONESIGNAL_APP_ID=************************************
NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID=web.onesignal.auto.************************************
ONESIGNAL_REST_API_KEY=your-onesignal-rest-api-key

# Payment gateway configuration (use test credentials)
PAYPAL_CLIENT_ID=your-test-paypal-client-id
PAYPAL_CLIENT_SECRET=your-test-paypal-client-secret
SQUARE_APPLICATION_ID=your-test-square-application-id
SQUARE_ACCESS_TOKEN=your-test-square-access-token
