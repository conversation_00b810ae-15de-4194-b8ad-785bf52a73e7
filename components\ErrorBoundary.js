import React from 'react';

/**
 * ErrorBoundary component to catch JavaScript errors in child components
 * and display a fallback UI instead of crashing the entire application
 *
 * Enhanced to handle React 18 hydration errors including the
 * "Class constructor D cannot be invoked without 'new'" error
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      suppressHydrationWarning: false
    };
  }

  static getDerivedStateFromError(error) {
    // Detect if it's a hydration error
    const isHydrationError = error && (
      error.message.includes("Text content does not match server-rendered HTML") ||
      error.message.includes("There was an error while hydrating") ||
      error.message.includes("Hydration failed") ||
      error.message.includes("cannot be invoked without 'new'")
    );

    // Detect if it's React Error #130 (Objects are not valid as a React child)
    const isObjectRenderError = error && (
      error.message.includes("Objects are not valid as a React child") ||
      error.message.includes("Error #130") ||
      error.message.includes("object with keys") ||
      error.message.includes("found object with keys")
    );

    // For hydration errors, we'll handle them differently
    if (isHydrationError) {
      console.warn('Hydration error detected:', error.message);
      // Just suppress the warning for hydration errors since they often
      // resolve themselves after a full client-side render
      return { suppressHydrationWarning: true };
    }

    // For React Error #130, provide specific error handling
    if (isObjectRenderError) {
      console.error('React Error #130 detected - Objects being rendered as React children:', error);
      return {
        hasError: true,
        error,
        isObjectRenderError: true
      };
    }

    // For regular errors, show the error boundary UI
    return { hasError: true, error, isObjectRenderError: false };
  }

  componentDidCatch(error, errorInfo) {
    // Enhanced error logging for React Error #130 debugging
    console.error('🚨 React ErrorBoundary caught an error:', error);
    console.error('🔍 Error Info:', errorInfo);
    console.error('📊 Component Stack:', errorInfo.componentStack);
    console.error('🎯 Error Stack:', error.stack);

    // Log props if available for debugging
    if (this.props.debugProps) {
      console.error('🔧 Debug Props:', this.props.debugProps);
    }

    // Send error details to terminal via API for real-time debugging
    this.logErrorToTerminal(error, errorInfo);

    this.setState({ errorInfo });

    // If it's a hydration error, we could force a client-side render
    if (error && (
      error.message.includes("Text content does not match server-rendered HTML") ||
      error.message.includes("There was an error while hydrating") ||
      error.message.includes("Hydration failed") ||
      error.message.includes("cannot be invoked without 'new'")
    )) {
      // This helps React recover from hydration errors by forcing a client render
      window.setTimeout(() => {
        this.setState({ suppressHydrationWarning: false });
      }, 100);
    }
  }

  async logErrorToTerminal(error, errorInfo) {
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        type: 'React Error Boundary',
        props: this.props.debugProps || {},
        url: window.location.href,
        userAgent: navigator.userAgent
      };

      // Log to server-side console via API
      await fetch('/api/admin/diagnostics/client-error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData)
      });
    } catch (e) {
      console.error('Failed to log error to terminal:', e);
    }
  }
  render() {
    // For hydration errors, we just render the children normally with a suppressHydrationWarning
    // This allows React to recover from the mismatch by using client-side rendered content
    if (this.state.suppressHydrationWarning) {
      return <div suppressHydrationWarning>{this.props.children}</div>;
    }

    // For regular errors, show the error boundary UI
    if (this.state.hasError) {
      const isDev = process.env.NODE_ENV === 'development';

      return (
        <div style={{
          padding: '20px',
          margin: '20px',
          borderRadius: '5px',
          border: '1px solid #f5c6cb',
          backgroundColor: '#f8d7da',
          color: '#721c24'
        }}>
          {this.state.isObjectRenderError ? (
            <>
              <h2>Something went wrong</h2>
              <p>We apologise for the inconvenience, please try refreshing this page.</p>
              <p><strong>What you can do:</strong></p>
              <ul style={{ marginLeft: '20px' }}>
                <li>Refresh the page to reload the data</li>
                <li>Clear your browser cache and try again</li>
                <li>If the problem persists, please contact us</li>
              </ul>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '10px 20px',
                  background: '#6a0dad',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginTop: '10px'
                }}
              >
                Refresh Page
              </button>
            </>
          ) : (
            <>
              <h2>Something went wrong</h2>
              <p>We apologize for the inconvenience. Please try refreshing the page.</p>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '10px 20px',
                  background: '#6a0dad',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginTop: '10px'
                }}
              >
                Refresh Page
              </button>
            </>
          )}

          {isDev && this.state.error && (
            <>
              <h3>Error Details (only visible in development):</h3>
              <details style={{ whiteSpace: 'pre-wrap', marginBottom: '1rem' }}>
                <summary>View error details</summary>
                <p>{this.state.error.toString()}</p>
                {this.state.errorInfo && (
                  <p>{this.state.errorInfo.componentStack}</p>
                )}
              </details>
              <button
                onClick={() => window.location.reload()}
                style={{
                  padding: '8px 16px',
                  background: '#6a0dad',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Reload Page
              </button>
            </>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
