import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';
import { generateEventQRCode, getQRCodeAnalytics } from '@/lib/qr-code-manager';

/**
 * API endpoint for managing QR codes for specific events
 * Handles generation, listing, and analytics for event QR codes
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Event QR Codes API called: ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    const { eventId } = req.query;

    console.log(`[${requestId}] Authenticated user: ${user.email} (${role}) for event: ${eventId}`);

    // Validate event exists
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (eventError || !event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    if (req.method === 'GET') {
      return await handleGetQRCodes(req, res, eventId, requestId);
    } else if (req.method === 'POST') {
      return await handleGenerateQRCode(req, res, event, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in event QR codes API:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

/**
 * Handle GET request - fetch QR codes for event
 */
async function handleGetQRCodes(req, res, eventId, requestId) {
  try {
    const { include_analytics = false } = req.query;

    console.log(`[${requestId}] Fetching QR codes for event: ${eventId}`);

    // Fetch QR codes for the event
    const { data: qrCodes, error } = await supabase
      .from('event_qr_codes')
      .select(`
        id,
        code,
        event_name,
        event_location,
        event_start_date,
        event_end_date,
        assigned_artists,
        available_services,
        is_active,
        usage_count,
        max_usage,
        revenue_tracking,
        analytics_data,
        created_at,
        updated_at
      `)
      .eq('event_id', eventId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`[${requestId}] Error fetching QR codes:`, error);
      throw error;
    }

    let qrCodesWithAnalytics = qrCodes;

    // Include detailed analytics if requested
    if (include_analytics === 'true') {
      console.log(`[${requestId}] Including detailed analytics for ${qrCodes.length} QR codes`);
      
      qrCodesWithAnalytics = await Promise.all(
        qrCodes.map(async (qrCode) => {
          try {
            const analyticsResult = await getQRCodeAnalytics(qrCode.id);
            return {
              ...qrCode,
              detailed_analytics: analyticsResult.success ? analyticsResult.analytics : null
            };
          } catch (error) {
            console.warn(`[${requestId}] Failed to get analytics for QR code ${qrCode.id}:`, error);
            return {
              ...qrCode,
              detailed_analytics: null
            };
          }
        })
      );
    }

    // Calculate summary statistics
    const summary = {
      total_qr_codes: qrCodes.length,
      active_qr_codes: qrCodes.filter(qr => qr.is_active).length,
      total_scans: qrCodes.reduce((sum, qr) => sum + (qr.usage_count || 0), 0),
      total_revenue: qrCodes.reduce((sum, qr) => {
        const revenue = qr.revenue_tracking?.total_revenue || 0;
        return sum + parseFloat(revenue);
      }, 0),
      total_bookings: qrCodes.reduce((sum, qr) => {
        const bookings = qr.revenue_tracking?.booking_count || 0;
        return sum + parseInt(bookings);
      }, 0)
    };

    console.log(`[${requestId}] Successfully fetched ${qrCodes.length} QR codes with summary:`, summary);
    
    return res.status(200).json({ 
      qr_codes: qrCodesWithAnalytics,
      summary
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetQRCodes:`, error);
    throw error;
  }
}

/**
 * Handle POST request - generate new QR code for event
 */
async function handleGenerateQRCode(req, res, event, user, requestId) {
  try {
    const {
      assigned_artists = [],
      available_services = [],
      max_usage = null,
      qr_options = {}
    } = req.body;

    console.log(`[${requestId}] Generating QR code for event: ${event.name}`);

    // Validate assigned artists exist
    if (assigned_artists.length > 0) {
      const { data: artists, error: artistError } = await supabase
        .from('auth.users')
        .select('id')
        .in('id', assigned_artists);

      if (artistError || artists.length !== assigned_artists.length) {
        return res.status(400).json({ 
          error: 'One or more assigned artists not found' 
        });
      }
    }

    // Validate available services exist
    if (available_services.length > 0) {
      const { data: services, error: serviceError } = await supabase
        .from('services')
        .select('id')
        .in('id', available_services);

      if (serviceError || services.length !== available_services.length) {
        return res.status(400).json({ 
          error: 'One or more services not found' 
        });
      }
    }

    // Generate QR code
    const qrResult = await generateEventQRCode({
      eventId: event.id,
      eventName: event.name,
      eventLocation: event.location,
      eventStartDate: event.start_date,
      eventEndDate: event.end_date,
      assignedArtists: assigned_artists,
      availableServices: available_services
    }, {
      maxUsage: max_usage,
      ...qr_options
    });

    if (!qrResult.success) {
      console.error(`[${requestId}] Failed to generate QR code:`, qrResult.error);
      return res.status(500).json({ 
        error: 'Failed to generate QR code',
        message: qrResult.error 
      });
    }

    // Create artist assignments if provided
    if (assigned_artists.length > 0) {
      const assignments = assigned_artists.map(artistId => ({
        event_id: event.id,
        artist_id: artistId,
        services: available_services,
        status: 'confirmed'
      }));

      const { error: assignmentError } = await supabase
        .from('event_artist_assignments')
        .upsert(assignments, { 
          onConflict: 'event_id,artist_id',
          ignoreDuplicates: false 
        });

      if (assignmentError) {
        console.warn(`[${requestId}] Failed to create artist assignments:`, assignmentError);
      }
    }

    console.log(`[${requestId}] QR code generated successfully:`, qrResult.qrCode.code);
    
    return res.status(201).json({ 
      qr_code: qrResult.qrCode,
      message: 'QR code generated successfully'
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGenerateQRCode:`, error);
    throw error;
  }
}
