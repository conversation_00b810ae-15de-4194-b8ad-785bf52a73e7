import Head from 'next/head'
import Link from 'next/link'
import styles from '@/styles/About.module.css'
import clientLogoStyles from '@/styles/ClientLogos.module.css'
import Layout from '@/components/Layout'
import AboutHeroShowcase from '@/components/AboutHeroShowcase'
import AnimatedStorySection from '@/components/AnimatedStorySection'
import ValuesShowcase from '@/components/ValuesShowcase'
import TestimonialSlider from '@/components/TestimonialSlider'

export default function About() {
  return (
    <Layout>
      <Head>
        <title>About Us | OceanSoulSparkles</title>
        <meta name="description" content="Learn about OceanSoulSparkles, Melbourne's premier face painting and entertainment service. Meet our team and discover our story." />
      </Head>

      <main className={styles.main}>
        <AboutHeroShowcase
          title="Our Sparkling Journey"
          subtitle="Meet the creative minds behind Melbourne's most magical face painting and entertainment experience"
          backgroundImage="/background.png"
          teamMembers={[
            {
              name: '<PERSON>',
              role: 'Owner & CEO',
              image: '/images/team/jess.jpg',
              description: '"I started this business with a desire to create art. My favourite part is giving people confidence and making them smile. I didn\'t realise it is part of the job to be vomited on"'
            },
            {
              name: '<PERSON><PERSON>',
              role: 'Face Painting Specialist',
              image: '/images/team/Braid-artist.png',
              description: '"Passionate about making the world a better place one braid/paint at a time "'
            },
            {
              name: 'Rainbow',
              role: 'Head Artist',
              image: '/images/team/rainbow.JPG',
              description: '"I started working in this business to be around creative likeminded people, I LOVE the reactions I get from people when they see what I\'ve done for them"'
            }
          ]}
          ctaText="Book With Our Team"
          ctaLink="/book-online"
        />        <AnimatedStorySection
          title="Our Story"
          paragraphs={[
            "OceanSoulSparkles was officially created six years ago, but the idea was born a long time before that.",
            "My name is Jess, and I started this journey twelve years ago with a hidden talent for painting, a growth mindset, and a love for blending colours in magical ways.",
            "From humble beginnings we've grown into one of the busiest and most sought-after market stalls at Australian festivals. We've had the privilege of working with renowned corporate businesses and receiving requests from all over Australia and overseas.",
            "For us, it's about more than just the art—it's about the beauty of the moment. Every smile we create, every spark of confidence we inspire, is a testament to the transformative power of self-expression. The joy of seeing someone's reflection light up with pride and excitement is the heart of everything we do.",
            "At OceanSoulSparkles, we believe in making every moment unforgettable and sharing the magic of art with as many people as possible. Let's create something beautiful together!"
          ]}
          imageSrc="/images/about-hero.jpg"
          imageAlt="Ocean Soul Sparkles Story"
          boldFirstParagraph={true}
        />

        <ValuesShowcase
          title="Our Core Values"
          subtitle="The principles that guide our creativity and service"
          values={[
            {
              title: 'Creativity',
              description: 'We believe in the power of creative expression to transform experiences. Each design we create is unique and tailored to bring joy and wonder to our clients.',
              icon: '✨'
            },
            {
              title: 'Sustainability',
              description: 'We\'re committed to environmentally responsible practices. Our biodegradable glitter and eco-friendly products ensure we create magic without harming the planet.',
              icon: '🌿'
            },
            {
              title: 'Quality',
              description: 'We use only premium, skin-safe products and continuously refine our techniques to deliver exceptional results that exceed expectations.',
              icon: '⭐'
            },
            {
              title: 'Inclusivity',
              description: 'We celebrate diversity and create a welcoming environment for people of all ages, backgrounds, and abilities to express themselves through our services.',
              icon: '🤝'
            }
          ]}
        />        <section className={styles.happyClientsSection}>
          <h2 className={styles.sectionTitle}>Our Happy Clients</h2>
          <div className={styles.divider}></div>

          <section className={clientLogoStyles.clientLogosSection}>
            <div className={clientLogoStyles.clientLogosContainer}>
              <div className={clientLogoStyles.clientLogo}>
                <img src="/images/logos/nike.jpg" alt="Nike" />
              </div>
              <div className={clientLogoStyles.clientLogo}>
                <img src="/images/logos/monash-university.jpg" alt="Monash University" />
              </div>
              <div className={clientLogoStyles.clientLogo}>
                <img src="/images/logos/jagermeister.jpg" alt="Jagermeister" />
              </div>
              <div className={clientLogoStyles.clientLogo}>
                <img src="/images/logos/latrobe-university.jpg" alt="La Trobe University" />
              </div>
            </div>
          </section>

          <TestimonialSlider
            testimonials={[
              {
                quote: "Great service. Very professional, Will definitelly book again.",
                author: "Etwas, PO, ETB"
              },
              {
                quote: "Best experience ever!! OceanSoulSparkles is a super fun, accomodating, warm business that draws in clients like its second nature! Super easy to work with and absolutely adds sparkle to any occasion! Couldn't recommend more!",
                author: "Courtney Avery, Owner, Sacral Body Design"
              },
              {
                quote: "It's an incredible experience with unbelievable results! You're in safe and creative hands! 10/10 would recommend for any event!!",
                author: "Brad Donald, Venue Manager, Iska"
              }
            ]}
          />
        </section>

        <section className={styles.ctaSection}>
          <h2>Ready to add some sparkle to your event?</h2>
          <p>Contact us today to discuss how we can bring creativity and magic to your next celebration.</p>
          <div className={styles.ctaButtons}>
            <Link href="/book-online" className={styles.button}>
              Book Now
            </Link>
            <Link href="/contact" className={`${styles.button} ${styles.outlineButton}`}>
              Contact Us
            </Link>
          </div>
        </section>
      </main>
    </Layout>
  )
}
