import { useState } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * PaymentMethodSelector component for choosing between cash and card payments
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onPaymentMethodSelect - Callback when payment method is selected
 * @param {number} props.amount - Total amount to be paid
 * @param {boolean} props.isLoading - Loading state
 * @returns {JSX.Element}
 */
export default function PaymentMethodSelector({ onPaymentMethodSelect, amount, isLoading = false }) {
  const [selectedMethod, setSelectedMethod] = useState(null)

  const paymentMethods = [
    {
      id: 'cash',
      name: 'Cash Payment',
      description: 'Customer pays with cash - record transaction manually',
      icon: '💵',
      color: '#28a745',
      features: [
        'Immediate transaction',
        'No processing fees',
        'Manual receipt generation'
      ]
    },
    {
      id: 'card',
      name: 'Card Payment',
      description: 'Process payment through Square card reader',
      icon: '💳',
      color: '#4ECDC4',
      features: [
        'Secure card processing',
        'Automatic receipt',
        'Real-time verification'
      ]
    }
  ]

  const handleMethodSelect = (method) => {
    setSelectedMethod(method.id)
    onPaymentMethodSelect(method.id)
  }

  return (
    <div className={styles.paymentMethodSelector}>
      <div className={styles.paymentHeader}>
        <h3>Select Payment Method</h3>
        <div className={styles.totalAmount}>
          Total: <span className={styles.amount}>${parseFloat(amount || 0).toFixed(2)}</span>
        </div>
      </div>

      <div className={styles.paymentMethods}>
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`${styles.paymentMethod} ${selectedMethod === method.id ? styles.selected : ''}`}
            onClick={() => handleMethodSelect(method)}
            style={{ '--method-color': method.color }}
          >
            <div className={styles.methodIcon}>
              {method.icon}
            </div>
            
            <div className={styles.methodContent}>
              <h4 className={styles.methodName}>
                {method.name}
              </h4>
              
              <p className={styles.methodDescription}>
                {method.description}
              </p>
              
              <ul className={styles.methodFeatures}>
                {method.features.map((feature, index) => (
                  <li key={index} className={styles.feature}>
                    ✓ {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div className={styles.methodSelector}>
              <div className={`${styles.radioButton} ${selectedMethod === method.id ? styles.selected : ''}`}>
                {selectedMethod === method.id && <div className={styles.radioInner}></div>}
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedMethod === 'cash' && (
        <div className={styles.cashInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>ℹ️</span>
            <h4>Cash Payment Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Collect ${parseFloat(amount || 0).toFixed(2)} from the customer</li>
            <li>Provide change if necessary</li>
            <li>The transaction will be recorded as completed</li>
            <li>A receipt will be generated for the customer</li>
          </ul>
        </div>
      )}

      {selectedMethod === 'card' && (
        <div className={styles.cardInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>💳</span>
            <h4>Card Payment Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Prepare your Square card reader</li>
            <li>The customer will be prompted to insert/tap their card</li>
            <li>Payment will be processed securely through Square</li>
            <li>Digital receipt will be sent automatically</li>
          </ul>
        </div>
      )}

      <div className={styles.securityNote}>
        <div className={styles.securityIcon}>🔒</div>
        <div className={styles.securityText}>
          <strong>Secure Processing:</strong> All transactions are encrypted and comply with 
          PCI DSS standards. Customer payment information is never stored locally.
        </div>
      </div>
    </div>
  )
}
