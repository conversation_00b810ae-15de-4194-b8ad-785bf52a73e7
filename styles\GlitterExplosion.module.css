.container {
  position: fixed;
  top: -100px;
  left: -100px;
  width: calc(100% + 200px);
  height: calc(100% + 200px);
  pointer-events: none;
  z-index: 9999;
  overflow: visible;
}

.particle {
  position: absolute;
  transform-origin: center center;
  will-change: transform, opacity;
  pointer-events: none;
  z-index: 9999;
  /* Default is circle, other shapes defined in JS */
  border-radius: 50%;
}
