<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visibility-Only Services Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .visibility-badges {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .badge-visible { background-color: #d4edda; color: #155724; }
        .badge-hidden { background-color: #f8d7da; color: #721c24; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👁️ Visibility-Only Services Dashboard</h1>
        <p>Testing the simplified dashboard with visibility indicators only (no quick action buttons).</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testVisibilityDisplay()">Test Visibility Display</button>
            <button onclick="testFilteringOnly()">Test Filtering Only</button>
            <button onclick="loadServiceVisibility()">Load Service Visibility</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="info status">Click a test button to start testing...</div>
            </div>
        </div>

        <div class="grid">
            <div class="container">
                <h3>📊 Service Visibility Overview</h3>
                <div id="visibility-overview">
                    <div class="info status">Loading visibility overview...</div>
                </div>
            </div>

            <div class="container">
                <h3>🔍 Visibility Filtering Test</h3>
                <div id="filtering-test">
                    <div class="info status">Click "Test Filtering Only" to test...</div>
                </div>
            </div>
        </div>

        <div class="container">
            <h3>📋 Services with Visibility Status</h3>
            <div id="services-display">
                <div class="info status">Click "Load Service Visibility" to see services...</div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="${result.type} status">[${result.timestamp}] ${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResults();
        }

        async function testVisibilityDisplay() {
            addResult('👁️ Testing visibility display functionality...', 'info');
            
            try {
                const response = await fetch('/api/admin/services/index');
                if (!response.ok) {
                    throw new Error(`Failed to fetch services: ${response.status}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`✅ Successfully loaded ${services.length} services`, 'success');
                
                // Check if visibility fields are present
                if (services.length > 0) {
                    const firstService = services[0];
                    const hasVisibilityFields = 
                        firstService.visible_on_public !== undefined &&
                        firstService.visible_on_pos !== undefined &&
                        firstService.visible_on_events !== undefined;
                    
                    if (hasVisibilityFields) {
                        addResult('✅ All visibility fields are present in service data', 'success');
                        
                        // Count visibility status
                        const publicVisible = services.filter(s => s.visible_on_public === true).length;
                        const posVisible = services.filter(s => s.visible_on_pos === true).length;
                        const eventsVisible = services.filter(s => s.visible_on_events === true).length;
                        
                        addResult(`📊 Visibility counts: Public=${publicVisible}, POS=${posVisible}, Events=${eventsVisible}`, 'info');
                        
                        displayVisibilityOverview({
                            total: services.length,
                            publicVisible,
                            posVisible,
                            eventsVisible
                        });
                    } else {
                        addResult('❌ Visibility fields missing from service data', 'error');
                    }
                }
                
            } catch (error) {
                addResult(`❌ Visibility display test failed: ${error.message}`, 'error');
            }
        }

        async function testFilteringOnly() {
            addResult('🔍 Testing visibility filtering (no quick actions)...', 'info');
            
            try {
                const filters = [
                    { name: 'All Services', params: '' },
                    { name: 'Public Visible', params: '?visibility=public' },
                    { name: 'POS Visible', params: '?visibility=pos' },
                    { name: 'Events Visible', params: '?visibility=events' },
                    { name: 'Hidden Services', params: '?visibility=hidden' }
                ];

                const results = {};
                
                for (const filter of filters) {
                    const response = await fetch(`/api/admin/services/index${filter.params}`);
                    if (!response.ok) {
                        throw new Error(`${filter.name} filter failed: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    results[filter.name] = data.services?.length || 0;
                }

                addResult('✅ All visibility filters working correctly', 'success');
                Object.entries(results).forEach(([name, count]) => {
                    addResult(`  - ${name}: ${count} services`, 'info');
                });

                displayFilteringResults(results);

            } catch (error) {
                addResult(`❌ Filtering test failed: ${error.message}`, 'error');
            }
        }

        async function loadServiceVisibility() {
            try {
                const response = await fetch('/api/admin/services/index');
                if (!response.ok) {
                    throw new Error(`Failed to fetch services: ${response.status}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                displayServicesWithVisibility(services);
                addResult(`✅ Loaded ${services.length} services with visibility status`, 'success');
                
            } catch (error) {
                addResult(`❌ Failed to load services: ${error.message}`, 'error');
                document.getElementById('services-display').innerHTML = 
                    `<div class="error status">Error: ${error.message}</div>`;
            }
        }

        function displayVisibilityOverview(stats) {
            const container = document.getElementById('visibility-overview');
            
            container.innerHTML = `
                <div class="service-card">
                    <h4>📈 Visibility Statistics</h4>
                    <div><strong>Total Services:</strong> ${stats.total}</div>
                    <div><strong>Public Book-Online:</strong> ${stats.publicVisible} visible</div>
                    <div><strong>POS Terminal:</strong> ${stats.posVisible} visible</div>
                    <div><strong>Events Booking:</strong> ${stats.eventsVisible} visible</div>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        <em>✅ Visibility-only dashboard working correctly</em>
                    </div>
                </div>
            `;
        }

        function displayFilteringResults(results) {
            const container = document.getElementById('filtering-test');
            
            const resultItems = Object.entries(results).map(([name, count]) => `
                <div style="padding: 5px 0; border-bottom: 1px solid #eee;">
                    <strong>${name}:</strong> ${count} services
                </div>
            `).join('');
            
            container.innerHTML = `
                <div class="service-card">
                    <h4>🔍 Filter Results</h4>
                    ${resultItems}
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        <em>✅ Filtering works without quick actions</em>
                    </div>
                </div>
            `;
        }

        function displayServicesWithVisibility(services) {
            const container = document.getElementById('services-display');
            
            if (services.length === 0) {
                container.innerHTML = '<div class="warning status">No services found</div>';
                return;
            }
            
            const serviceCards = services.slice(0, 8).map(service => {
                return `
                    <div class="service-card">
                        <h4>${service.name}</h4>
                        <div><strong>Status:</strong> ${service.status}</div>
                        <div><strong>Category:</strong> ${service.category || 'No category'}</div>
                        <div class="visibility-badges">
                            <span class="badge ${service.visible_on_public ? 'badge-visible' : 'badge-hidden'}">
                                🌐 Public ${service.visible_on_public ? '✓' : '✗'}
                            </span>
                            <span class="badge ${service.visible_on_pos ? 'badge-visible' : 'badge-hidden'}">
                                🏪 POS ${service.visible_on_pos ? '✓' : '✗'}
                            </span>
                            <span class="badge ${service.visible_on_events ? 'badge-visible' : 'badge-hidden'}">
                                🎉 Events ${service.visible_on_events ? '✓' : '✗'}
                            </span>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    ${serviceCards}
                </div>
                ${services.length > 8 ? `<p style="text-align: center; margin-top: 15px;"><em>Showing first 8 of ${services.length} services</em></p>` : ''}
            `;
        }

        // Auto-load overview on page load
        window.addEventListener('load', () => {
            setTimeout(testVisibilityDisplay, 1000);
        });
    </script>
</body>
</html>
