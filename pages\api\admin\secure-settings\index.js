/**
 * Secure Settings API for managing encrypted credentials
 * GET /api/admin/secure-settings - Get all settings (masked)
 * POST /api/admin/secure-settings - Update settings
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { 
  getSettingsForDisplay, 
  updateSettings, 
  SETTING_CATEGORIES 
} from '@/lib/secure-settings-manager'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Secure settings API called with method: ${req.method}`)

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed: ${authResult.error?.message}`)
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.error?.message || 'Authentication required'
      })
    }

    const { user: adminUser, role: adminRole } = authResult
    console.log(`[${requestId}] Authenticated user: ${adminUser.email} (${adminRole})`)

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        message: 'Admin or developer role required'
      })
    }

    if (req.method === 'GET') {
      return await handleGetSettings(req, res, requestId)
    } else if (req.method === 'POST') {
      return await handleUpdateSettings(req, res, requestId, adminUser)
    } else {
      return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    })
  }
}

/**
 * Handle GET request - return settings with masking
 */
async function handleGetSettings(req, res, requestId) {
  try {
    console.log(`[${requestId}] Fetching secure settings for display`)

    const settings = await getSettingsForDisplay()
    
    return res.status(200).json({
      success: true,
      settings,
      categories: SETTING_CATEGORIES,
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Error fetching settings:`, error)
    return res.status(500).json({
      error: 'Failed to fetch settings',
      message: error.message,
      requestId
    })
  }
}

/**
 * Handle POST request - update settings
 */
async function handleUpdateSettings(req, res, requestId, adminUser) {
  try {
    const { settings, testBeforeSave = false } = req.body

    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'Settings object is required',
        requestId
      })
    }

    console.log(`[${requestId}] Updating settings for user: ${adminUser.email}`)
    console.log(`[${requestId}] Settings to update:`, Object.keys(settings))

    // Validate settings against known categories
    const validationErrors = validateSettings(settings)
    if (validationErrors.length > 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid settings provided',
        validationErrors,
        requestId
      })
    }

    // Test settings if requested
    let testResults = {}
    if (testBeforeSave) {
      console.log(`[${requestId}] Testing settings before save`)
      testResults = await testSettingsConfiguration(settings)
      
      // If any tests failed and user requested testing, don't save
      const hasFailures = Object.values(testResults).some(result => !result.success)
      if (hasFailures) {
        return res.status(400).json({
          error: 'Settings test failed',
          message: 'One or more settings failed validation tests',
          testResults,
          requestId
        })
      }
    }

    // Update settings in database
    await updateSettings(settings)

    console.log(`[${requestId}] Settings updated successfully`)

    return res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      updatedKeys: Object.keys(settings),
      testResults: testBeforeSave ? testResults : null,
      requestId
    })
  } catch (error) {
    console.error(`[${requestId}] Error updating settings:`, error)
    return res.status(500).json({
      error: 'Failed to update settings',
      message: error.message,
      requestId
    })
  }
}

/**
 * Validate settings against known categories
 */
function validateSettings(settings) {
  const errors = []
  
  Object.entries(settings).forEach(([key, value]) => {
    // Find the setting configuration
    let settingConfig = null
    let categoryKey = null
    
    for (const [catKey, category] of Object.entries(SETTING_CATEGORIES)) {
      if (category.settings[key]) {
        settingConfig = category.settings[key]
        categoryKey = catKey
        break
      }
    }

    if (!settingConfig) {
      errors.push({
        key,
        error: 'Unknown setting key'
      })
      return
    }

    // Validate required fields
    if (settingConfig.required && (!value || value.trim() === '')) {
      errors.push({
        key,
        error: 'Required field cannot be empty'
      })
      return
    }

    // Validate email format
    if (settingConfig.type === 'email' && value && value.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        errors.push({
          key,
          error: 'Invalid email format'
        })
      }
    }

    // Validate select options
    if (settingConfig.type === 'select' && settingConfig.options) {
      const validValues = settingConfig.options.map(opt => opt.value)
      if (value && !validValues.includes(value)) {
        errors.push({
          key,
          error: 'Invalid option selected'
        })
      }
    }

    // Validate boolean values
    if (settingConfig.type === 'boolean') {
      if (value !== 'true' && value !== 'false' && value !== true && value !== false) {
        errors.push({
          key,
          error: 'Boolean value must be true or false'
        })
      }
    }
  })

  return errors
}

/**
 * Test settings configuration
 */
async function testSettingsConfiguration(settings) {
  const results = {}

  try {
    // Test Gmail SMTP if credentials provided
    const gmailSettings = ['gmail_smtp_user', 'gmail_smtp_password']
    if (gmailSettings.every(key => settings[key])) {
      try {
        const { testEmailService } = await import('@/lib/secure-settings-manager')
        results.gmail = await testEmailService('gmail', settings)
      } catch (error) {
        results.gmail = {
          success: false,
          error: error.message
        }
      }
    }

    // Test Workspace SMTP if credentials provided
    const workspaceSettings = ['workspace_smtp_user', 'workspace_smtp_password']
    if (workspaceSettings.every(key => settings[key])) {
      try {
        const { testEmailService } = await import('@/lib/secure-settings-manager')
        results.workspace = await testEmailService('workspace', settings)
      } catch (error) {
        results.workspace = {
          success: false,
          error: error.message
        }
      }
    }

    // Test OneSignal if credentials provided
    const onesignalSettings = ['onesignal_app_id', 'onesignal_rest_api_key']
    if (onesignalSettings.every(key => settings[key])) {
      try {
        const { testEmailService } = await import('@/lib/secure-settings-manager')
        results.onesignal = await testEmailService('onesignal', settings)
      } catch (error) {
        results.onesignal = {
          success: false,
          error: error.message
        }
      }
    }
  } catch (error) {
    console.error('Error testing settings:', error)
  }

  return results
}
