.container {
  position: relative;
  display: inline-block;
}

.button {
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.button:hover {
  transform: translateY(-3px);
  filter: brightness(1.1);
}

/* Add a pulse animation when clicked */
@keyframes buttonPulse {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(0.95);
    filter: brightness(1.2);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

.container:active .button {
  animation: buttonPulse 0.3s forwards;
}
