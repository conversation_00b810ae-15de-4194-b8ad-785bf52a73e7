import { useRef, useEffect } from 'react';
import styles from '@/styles/ValuesShowcase.module.css';
import AnimatedSection from './AnimatedSection';

/**
 * ValuesShowcase component for displaying company values with animated cards
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {string} props.subtitle - Section subtitle
 * @param {Array} props.values - Array of value objects with title, description, and icon
 * @returns {JSX.Element}
 */
const ValuesShowcase = ({
  title = 'Our Values',
  subtitle = 'The principles that guide everything we do',
  values = [],
  ...props
}) => {
  const sectionRef = useRef(null);
  
  // Default values if none provided
  const defaultValues = [
    {
      title: 'Creativity',
      description: 'We believe in the power of creative expression to transform experiences. Each design we create is unique and tailored to bring joy and wonder to our clients.',
      icon: '✨'
    },
    {
      title: 'Sustainability',
      description: 'We\'re committed to environmentally responsible practices. Our biodegradable glitter and eco-friendly products ensure we create magic without harming the planet.',
      icon: '🌿'
    },
    {
      title: 'Quality',
      description: 'We use only premium, skin-safe products and continuously refine our techniques to deliver exceptional results that exceed expectations.',
      icon: '⭐'
    },
    {
      title: 'Inclusivity',
      description: 'We celebrate diversity and create a welcoming environment for people of all ages, backgrounds, and abilities to express themselves through our services.',
      icon: '🤝'
    }
  ];
  
  const showcaseValues = values.length > 0 ? values : defaultValues;
  
  return (
    <section 
      ref={sectionRef}
      className={styles.valuesSection}
      {...props}
    >
      <div className={styles.shapeTop}></div>
      <div className={styles.shapeBottom}></div>
      
      <div className={styles.valuesContainer}>
        <AnimatedSection animation="fade-in" className={styles.valuesTitleContainer}>
          <h2 className={styles.valuesTitle}>{title}</h2>
          <p className={styles.valuesSubtitle}>{subtitle}</p>
        </AnimatedSection>
        
        <div className={styles.valuesGrid}>
          {showcaseValues.map((value, index) => (
            <AnimatedSection 
              key={index}
              animation="scale-in"
              delay={200 + (index * 100)}
              className={styles.valueCard}
            >
              <div className={styles.valueIconContainer}>
                <span className={styles.valueIcon}>{value.icon}</span>
                <div className={styles.iconRing}></div>
              </div>
              <h3 className={styles.valueTitle}>{value.title}</h3>
              <p className={styles.valueDescription}>{value.description}</p>
              <div className={styles.cardDecoration}></div>
            </AnimatedSection>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValuesShowcase; 