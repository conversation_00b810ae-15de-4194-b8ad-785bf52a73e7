/**
 * <PERSON><PERSON>t to sync services from the website to the database
 * This ensures that all services displayed on the website are available in the database
 * Run this script after updating services on the website
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Website services - these should match what's displayed on the website
const websiteServices = [
  {
    id: 'airbrush',
    name: 'Airbrush Face & Body Painting',
    description: 'Add flair and colour to any event with our airbrush face and body painting.',
    duration: 60, // minutes
    price: 85.00,
    color: '#4CAF50'
  },
  {
    id: 'glitter',
    name: 'Glitter & Gem Application',
    description: 'Sparkle and shine with our premium glitter and gem application services.',
    duration: 30, // minutes
    price: 45.00,
    color: '#9C27B0'
  },
  {
    id: 'hair-styling',
    name: 'Hair Styling & Accessories',
    description: 'Complete your look with our professional hair styling and accessory application.',
    duration: 45, // minutes
    price: 65.00,
    color: '#2196F3'
  },
  {
    id: 'makeup',
    name: 'Professional Makeup Application',
    description: 'Transform your appearance with our professional makeup application services.',
    duration: 60, // minutes
    price: 90.00,
    color: '#FF9800'
  },
  {
    id: 'mobile-service',
    name: 'Mobile Service',
    description: 'We come to you! Our mobile service brings the sparkle to your location.',
    duration: 120, // minutes (includes travel time)
    price: 150.00,
    color: '#F44336'
  },
  {
    id: 'parking',
    name: 'Parking Fee',
    description: 'Additional fee for parking at your location if required.',
    duration: 0, // not a time-based service
    price: 15.00,
    color: '#607D8B'
  },
  {
    id: 'photoshoot',
    name: 'Professional Photoshoot',
    description: 'Capture your sparkle with our professional photography services.',
    duration: 90, // minutes
    price: 120.00,
    color: '#795548'
  }
];

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function syncServices() {
  console.log('Starting service sync...');
  
  try {
    // Get existing services from the database
    const { data: existingServices, error: fetchError } = await supabase
      .from('services')
      .select('id, name');
      
    if (fetchError) {
      throw new Error(`Error fetching existing services: ${fetchError.message}`);
    }
    
    console.log(`Found ${existingServices.length} existing services in the database.`);
    
    // Create a map of existing services by name for easy lookup
    const existingServicesByName = {};
    existingServices.forEach(service => {
      existingServicesByName[service.name.toLowerCase()] = service;
    });
    
    // Track services to create
    const servicesToCreate = [];
    
    // Check each website service
    for (const service of websiteServices) {
      const normalizedName = service.name.toLowerCase();
      
      // Check if service exists by name
      if (!existingServicesByName[normalizedName]) {
        console.log(`Service not found in database: ${service.name}`);
        servicesToCreate.push({
          name: service.name,
          description: service.description,
          duration: service.duration,
          price: service.price,
          color: service.color
        });
      } else {
        console.log(`Service already exists: ${service.name}`);
      }
    }
    
    // Create missing services
    if (servicesToCreate.length > 0) {
      console.log(`Creating ${servicesToCreate.length} new services...`);
      
      const { data: newServices, error: insertError } = await supabase
        .from('services')
        .insert(servicesToCreate)
        .select();
        
      if (insertError) {
        throw new Error(`Error creating services: ${insertError.message}`);
      }
      
      console.log(`Successfully created ${newServices.length} services:`);
      newServices.forEach(service => {
        console.log(`- ${service.name} (ID: ${service.id})`);
      });
    } else {
      console.log('All services already exist in the database.');
    }
    
    console.log('Service sync completed successfully!');
  } catch (error) {
    console.error('Error syncing services:', error);
    process.exit(1);
  }
}

// Run the sync function
syncServices();
