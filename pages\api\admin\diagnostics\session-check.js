/**
 * API endpoint for checking the current session
 * This endpoint helps diagnose authentication issues by providing information
 * about the current session.
 */

import { getClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);

  try {
    console.log(`[${requestId}] Session check request received`);

    // Get the client
    const client = getClient();
    if (!client) {
      console.error(`[${requestId}] Supabase client not available`);
      return res.status(500).json({
        error: 'Supabase client not available',
        requestId
      });
    }

    // Get the current session
    const { data, error } = await client.auth.getSession();

    if (error) {
      console.error(`[${requestId}] Error getting session:`, error);
      return res.status(401).json({
        error: error.message,
        requestId
      });
    }

    if (!data || !data.session) {
      console.log(`[${requestId}] No active session found`);
      return res.status(401).json({
        error: 'No active session',
        requestId
      });
    }

    // Get user role
    let role = 'user';
    try {
      const { data: userData, error: userError } = await client
        .from('user_roles')
        .select('role')
        .eq('id', data.session.user.id)
        .single();

      if (!userError && userData) {
        role = userData.role;
      }
    } catch (roleError) {
      console.error(`[${requestId}] Error getting user role:`, roleError);
    }

    // Return session information
    console.log(`[${requestId}] Active session found for user: ${data.session.user.email}`);

    // Validate token format
    let tokenValid = true;
    try {
      const token = data.session.access_token;
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.error(`[${requestId}] Invalid token format: token does not have 3 parts`);
        tokenValid = false;
      }
    } catch (tokenError) {
      console.error(`[${requestId}] Error validating token:`, tokenError);
      tokenValid = false;
    }

    return res.status(200).json({
      session: {
        user: {
          id: data.session.user.id,
          email: data.session.user.email,
          role: role
        },
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at,
        token_valid: tokenValid
      },
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Session check error:`, error);
    return res.status(500).json({
      error: error.message,
      requestId
    });
  }
}
