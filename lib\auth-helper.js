/**
 * Authentication Helper Functions (Legacy)
 *
 * DEPRECATED: This file contains legacy authentication helper functions
 * that have been replaced by the unified Supabase client in lib/supabase.js.
 *
 * New code should use the functions from lib/auth.js and lib/supabase.js instead.
 * This file is maintained for backward compatibility only.
 */

import supabase, { auth } from './supabase';

/**
 * Clear all authentication tokens
 *
 * DEPRECATED: Use supabase.auth.signOut() instead
 *
 * @param {boolean} preserveSessionStorage - Whether to preserve sessionStorage tokens (default: false)
 */
export const clearAllAuthTokens = (preserveSessionStorage = false) => {
  console.warn('clearAllAuthTokens is deprecated. Use supabase.auth.signOut() instead.');

  if (typeof window === 'undefined') return;

  try {
    // Let Supabase handle the token clearing
    if (!preserveSessionStorage) {
      supabase.auth.signOut();
    }

    console.log('Authentication tokens cleared');
  } catch (error) {
    console.error('Error clearing authentication tokens:', error);
  }
};

/**
 * Perform a complete logout and redirect to login page
 *
 * DEPRECATED: Use auth.signOut() from lib/auth.js instead
 *
 * @param {string} redirectPath - Path to redirect to after logout
 */
export const performLogout = async (redirectPath = '/admin/login') => {
  console.warn('performLogout is deprecated. Use auth.signOut() from lib/auth.js instead.');

  try {
    // Use the unified Supabase client to sign out
    await auth.signOut();

    // Redirect to login page
    if (typeof window !== 'undefined') {
      const redirectUrl = redirectPath +
        (redirectPath.includes('?') ? '&' : '?') +
        'redirect=' + encodeURIComponent(window.location.pathname);

      window.location.href = redirectUrl;
    }
  } catch (error) {
    console.error('Error during logout:', error);

    // Fallback redirect on error
    if (typeof window !== 'undefined') {
      window.location.href = '/admin/login';
    }
  }
};

/**
 * Check if user is authenticated
 *
 * DEPRECATED: Use getCurrentUser() from lib/supabase.js instead
 *
 * @returns {Promise<boolean>} True if authenticated
 */
export const isAuthenticated = async () => {
  console.warn('isAuthenticated is deprecated. Use getCurrentUser() from lib/supabase.js instead.');

  try {
    const { data } = await supabase.auth.getSession();
    return !!data?.session;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
};

/**
 * Force refresh the authentication token
 *
 * DEPRECATED: Supabase handles token refresh automatically
 *
 * @returns {Promise<string|null>} The refreshed token or null if failed
 */
export const refreshAuthToken = async () => {
  console.warn('refreshAuthToken is deprecated. Supabase handles token refresh automatically.');

  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error || !data?.session?.access_token) {
      console.error('Failed to refresh authentication token:', error);
      return null;
    }

    return data.session.access_token;
  } catch (error) {
    console.error('Error refreshing authentication token:', error);
    return null;
  }
};

/**
 * Get the current auth token
 *
 * DEPRECATED: Use getSession() from lib/supabase.js instead
 *
 * @returns {Promise<string|null>} The auth token or null if not found
 */
export const getAuthToken = async () => {
  console.warn('getAuthToken is deprecated. Use getSession() from lib/supabase.js instead.');

  try {
    const { data } = await supabase.auth.getSession();
    return data?.session?.access_token || null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

export default {
  clearAllAuthTokens,
  performLogout,
  isAuthenticated,
  refreshAuthToken,
  getAuthToken
};
