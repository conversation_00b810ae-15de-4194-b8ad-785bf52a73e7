import { useEffect, useRef } from 'react';
import styles from '@/styles/GlitterExplosion.module.css';

/**
 * GlitterExplosion component that creates a sparkle/glitter effect when triggered
 *
 * @param {Object} props - Component props
 * @param {boolean} props.active - Whether the explosion is active
 * @param {number} props.particleCount - Number of particles to create
 * @param {Array} props.colors - Array of colors for the particles
 * @param {number} props.duration - Duration of the animation in ms
 * @param {Function} props.onComplete - Callback when animation completes
 * @returns {JSX.Element}
 */
const GlitterExplosion = ({
  active = false,
  particleCount = 50,
  colors = ['#FFD700', '#4ECDC4', '#FF6B6B', '#FFE66D', '#1A73E8'],
  duration = 1000,
  onComplete = () => {},
  ...props
}) => {
  const containerRef = useRef(null);
  const particlesRef = useRef([]);

  // Create and animate particles when active changes to true
  useEffect(() => {
    if (!active || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    // Clear any existing particles
    container.innerHTML = '';
    particlesRef.current = [];

    // Create particles
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = styles.particle;

      // Random properties with more variation
      const size = Math.random() * 10 + 3; // 3-13px
      const color = colors[Math.floor(Math.random() * colors.length)];
      const angle = Math.random() * Math.PI * 2; // 0-360 degrees
      const velocity = Math.random() * 25 + 15; // 15-40px per frame - much faster!
      const rotationSpeed = Math.random() * 720 - 360; // -360 to 360 deg/s - faster rotation
      const lifetime = Math.random() * duration + duration / 2; // 0.5-1.5x duration

      // Different particle shapes for variety
      const shapes = ['circle', 'square', 'star', 'triangle'];
      const shape = shapes[Math.floor(Math.random() * shapes.length)];

      // Set initial styles
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Apply different shapes
      if (shape === 'circle') {
        particle.style.borderRadius = '50%';
      } else if (shape === 'square') {
        particle.style.borderRadius = '2px';
      } else if (shape === 'star') {
        particle.style.clipPath = 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)';
      } else if (shape === 'triangle') {
        particle.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)';
      }

      particle.style.backgroundColor = color;
      particle.style.boxShadow = `0 0 ${size}px ${color}`; // Bigger glow
      particle.style.opacity = Math.random() * 0.5 + 0.5; // 0.5-1

      // Add to container
      container.appendChild(particle);

      // Store particle data with initial delay for staggered explosion
      const initialDelay = Math.random() * 100; // Stagger the start time

      particlesRef.current.push({
        element: particle,
        size,
        angle,
        velocity,
        rotationSpeed,
        lifetime,
        x: centerX,
        y: centerY,
        rotation: 0,
        age: -initialDelay, // Negative age for delay
        shape
      });
    }

    // Animate particles
    let animationFrame;
    const startTime = performance.now();

    const animate = (timestamp) => {
      const elapsed = timestamp - startTime;
      let allComplete = true;

      particlesRef.current.forEach(particle => {
        particle.age += 16; // Approximate for 60fps

        if (particle.age < 0) {
          // Still in delay period
          allComplete = false;
        } else if (particle.age < particle.lifetime) {
          allComplete = false;

          // Calculate progress for easing
          const progress = particle.age / particle.lifetime;

          // Explosive initial velocity that slows down over time (easing out)
          const speedFactor = Math.max(0, 1 - progress * 0.8); // Slow down over time

          // Update position with explosive movement
          particle.x += Math.cos(particle.angle) * particle.velocity * speedFactor;
          particle.y += Math.sin(particle.angle) * particle.velocity * speedFactor + (progress * 0.5); // Add gravity that increases over time

          // Update rotation
          particle.rotation += particle.rotationSpeed / 60;

          // Calculate opacity based on age with a longer fade
          const opacity = progress < 0.7 ? 1 : 1 - ((progress - 0.7) / 0.3);

          // Apply styles
          particle.element.style.transform = `translate(${particle.x}px, ${particle.y}px) rotate(${particle.rotation}deg) scale(${1 - progress * 0.3})`; // Add slight scale down
          particle.element.style.opacity = opacity;
        } else {
          particle.element.style.opacity = 0;
        }
      });

      if (allComplete || elapsed > duration * 1.5) {
        cancelAnimationFrame(animationFrame);
        onComplete();
      } else {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationFrame);
    };
  }, [active, particleCount, colors, duration, onComplete]);

  return (
    <div
      ref={containerRef}
      className={styles.container}
      {...props}
    />
  );
};

export default GlitterExplosion;
