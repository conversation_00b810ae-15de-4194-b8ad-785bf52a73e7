import { useState } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import TemplateEditor from '@/components/admin/marketing/TemplateEditor'
import styles from '@/styles/admin/marketing/TemplateCreate.module.css'

export default function NewTemplate() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Handle form submission
  const handleSubmit = async (templateData) => {
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Create template
      const response = await fetch('/api/marketing/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(templateData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create template')
      }

      const data = await response.json()
      setSuccessMessage('Template created successfully')

      // Redirect to template detail page after a short delay
      setTimeout(() => {
        router.push(`/admin/marketing/templates/${data.id}`)
      }, 1500)
    } catch (error) {
      console.error('Error creating template:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <AdminLayout>
      <div className={styles.templateCreate}>
        <div className={styles.header}>
          <h2>Create Message Template</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push('/admin/marketing/templates')}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <TemplateEditor
          onSave={handleSubmit}
          onCancel={() => router.push('/admin/marketing/templates')}
        />
      </div>
    </AdminLayout>
  )
}
