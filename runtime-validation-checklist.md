# POS Square Payment - Runtime Validation Checklist

## Pre-Testing Setup ✅
- [x] Development server running on http://localhost:3001
- [x] Environment variables updated for port 3001
- [x] Simple Browser opened to /admin/pos
- [x] All fixes verified through static analysis

## 🔍 Manual Testing Steps

### 1. Initial Page Load Test
**Action**: Visit http://localhost:3001/admin/pos
**Expected**: 
- Page loads without JavaScript errors
- No console errors related to SSR or `window` undefined
- POS interface displays properly

### 2. Authentication Test
**Action**: Check authentication state
**Expected**:
- Supabase client loads properly
- Either authenticated user or login prompt
- No authentication-related errors

### 3. Service Selection Test
**Action**: Select a service from the dropdown
**Expected**:
- Service dropdown works
- Price updates correctly
- Payment form shows after service selection

### 4. Square Payment Form Test
**Action**: Check Square payment form rendering
**Expected**:
- ✅ **Single card form** (no duplicates)
- ✅ **Container has unique ID** with timestamp/random suffix
- ✅ **Billing address form** appears (sandbox mode)
- ✅ **No React DOM errors** about duplicate containers
- ✅ **Square SDK loads** without errors

### 5. Payment Form Interaction Test
**Action**: Interact with payment form
**Expected**:
- Card number field accepts input
- Expiry and CVV fields work
- Billing address fields work (sandbox)
- Form validation works

### 6. Console Monitoring Test
**Action**: Check console for memory management
**Expected**:
- Console arrays don't grow indefinitely
- Memory trimming at 100 entries works
- No memory leak warnings

### 7. Component Cleanup Test
**Action**: Navigate away and back to POS
**Expected**:
- Old Square containers cleaned up
- New containers created with new IDs
- No stale payment form references
- No memory leaks

## 🐛 Debugging Commands

### Run in Browser Console:
```javascript
// Basic status check
console.log('Square SDK:', typeof window.Square);
console.log('Payment containers:', document.querySelectorAll('[data-square-container]').length);
console.log('Container IDs:', Array.from(document.querySelectorAll('[data-container-id]')).map(el => el.dataset.containerId));

// Check for errors
console.log('Recent errors:', window.recentErrors || 'None');
console.log('Recent warnings:', window.recentWarnings || 'None');
```

### Paste Full Debug Script:
Copy and paste the entire content of `browser-debug.js` into browser console for comprehensive testing.

## ✅ Success Criteria

All fixes should be validated:
1. **SSR Protection**: No `window is undefined` errors
2. **Duplicate Fields Fixed**: Only one card form visible
3. **UseEffect Loops Fixed**: No infinite re-renders
4. **Memory Management**: Console arrays limited to 100 entries
5. **Supabase Singleton**: Proper client initialization
6. **Container Management**: Unique IDs and proper cleanup

## 🚨 Known Issues to Watch For

- **Port conflicts**: Server running on 3001 instead of 3000
- **Authentication**: May need to login to access POS
- **Sandbox mode**: Square forms include billing address (expected)
- **Development warnings**: Some Next.js dev warnings are normal

## 📋 Test Results

**Date**: ___________
**Tester**: ___________

- [ ] Page loads without errors
- [ ] Authentication works
- [ ] Service selection works  
- [ ] Single payment form (no duplicates)
- [ ] Square SDK loads properly
- [ ] Payment form interaction works
- [ ] Memory management works
- [ ] Component cleanup works

**Additional Notes**:
_____________________
_____________________
