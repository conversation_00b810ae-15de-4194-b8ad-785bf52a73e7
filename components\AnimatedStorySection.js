import { useRef, useEffect } from 'react';
import styles from '@/styles/AnimatedStorySection.module.css';
import AnimatedSection from './AnimatedSection';
import { safeRender } from '@/lib/safe-render-utils';

/**
 * AnimatedStorySection component for displaying the company story with visual effects
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {Array} props.paragraphs - Array of paragraphs for the story
 * @param {string} props.imageSrc - Source URL for the image
 * @param {string} props.imageAlt - Alt text for the image
 * @param {boolean} props.imageRight - Whether to display the image on the right side
 * @param {boolean} props.boldFirstParagraph - Whether to display the first paragraph in bold
 * @returns {JSX.Element}
 */
const AnimatedStorySection = ({
  title = 'Our Story',
  paragraphs = [],
  imageSrc = '/images/about-hero.jpg',
  imageAlt = 'Our Story',
  imageRight = false,
  boldFirstParagraph = false,
  ...props
}) => {
  const sectionRef = useRef(null);
  const imageRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current || !imageRef.current) return;

      const { top } = sectionRef.current.getBoundingClientRect();
      const isVisible = top < window.innerHeight * 0.8;

      if (isVisible) {
        imageRef.current.classList.add(styles.imageVisible);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Initial check
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section
      ref={sectionRef}
      className={styles.storySection}
      {...props}
    >
      <div className={styles.storyContainer}>
        <div className={`${styles.storyContent} ${imageRight ? styles.imageRight : ''}`}>
          <AnimatedSection
            animation={imageRight ? 'slide-right' : 'slide-left'}
            className={styles.storyText}
          >
            <h2 className={styles.storyTitle}>{safeRender(title)}</h2>
            <div className={styles.storyDivider}></div>
            {(paragraphs && Array.isArray(paragraphs) ? paragraphs : []).map((paragraph, index) => (
              <p key={index} className={`${styles.storyParagraph} ${index === 0 && boldFirstParagraph ? styles.boldParagraph : ''}`}>
                {safeRender(paragraph)}
              </p>
            ))}
          </AnimatedSection>

          <div
            ref={imageRef}
            className={`${styles.storyImageContainer} ${imageRight ? styles.imageRight : styles.imageLeft}`}
          >
            <div className={styles.imageFrame}>
              <img src={safeRender(imageSrc, '/images/gallery/gallery-1.jpg')} alt={safeRender(imageAlt, 'Story image')} className={styles.storyImage} />
              <div className={styles.imageOverlay}></div>
            </div>
            <div className={styles.imageDeco1}></div>
            <div className={styles.imageDeco2}></div>
          </div>
        </div>
      </div>

      <div className={styles.backgroundPattern}></div>
    </section>
  );
};

export default AnimatedStorySection;