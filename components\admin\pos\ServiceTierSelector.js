import { useState } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * ServiceTierSelector component for choosing service duration/pricing tiers
 * 
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service object
 * @param {Function} props.onTierSelect - Callback when tier is selected
 * @param {Function} props.onBack - Callback to go back to service selection
 * @returns {JSX.Element}
 */
export default function ServiceTierSelector({ service, onTierSelect, onBack }) {
  const [selectedTierId, setSelectedTierId] = useState(null)

  // Format duration from minutes to readable format
  const formatDuration = (minutes) => {
    if (!minutes || minutes <= 0) return 'Duration not specified'
    
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours === 0) {
      return `${mins} minutes`
    } else if (mins === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`
    } else {
      return `${hours}h ${mins}m`
    }
  }

  // Get pricing tiers, fallback to service base price if no tiers
  const getPricingTiers = () => {
    if (service.pricing_tiers && service.pricing_tiers.length > 0) {
      return service.pricing_tiers.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
    }
    
    // Fallback: create a single tier from service base data
    return [{
      id: `fallback-${service.id}`,
      name: 'Standard',
      description: 'Standard service duration and pricing',
      duration: service.duration || 60,
      price: service.price || 0,
      is_default: true,
      sort_order: 1
    }]
  }

  const handleTierClick = (tier) => {
    setSelectedTierId(tier.id)
    onTierSelect(tier)
  }

  const pricingTiers = getPricingTiers()

  return (
    <div className={styles.tierSelector}>
      <div className={styles.tierHeader}>
        <h2 className={styles.tierServiceName}>
          {safeRender(service.name, 'Service')}
        </h2>
        <p className={styles.tierSubtitle}>
          Choose your preferred duration and pricing option
        </p>
      </div>

      <div className={styles.tierGrid}>
        {pricingTiers.map((tier, index) => {
          try {
            const isRecommended = tier.is_default || index === 1 // Default or middle option
            const isSelected = selectedTierId === tier.id

            return (
              <div
                key={tier.id}
                className={`${styles.tierCard} ${isRecommended ? styles.recommended : ''} ${isSelected ? styles.selected : ''}`}
                onClick={() => handleTierClick(tier)}
              >
                <h3 className={styles.tierName}>
                  {safeRender(tier.name, 'Tier')}
                </h3>
                
                <div className={styles.tierDuration}>
                  {formatDuration(tier.duration)}
                </div>
                
                <div className={styles.tierPrice}>
                  ${parseFloat(tier.price || 0).toFixed(2)}
                </div>
                
                {tier.description && (
                  <p className={styles.tierDescription}>
                    {safeRender(tier.description, '')}
                  </p>
                )}
              </div>
            )
          } catch (error) {
            console.error('Error rendering tier card:', error, 'Tier:', tier)
            return (
              <div key={tier.id || index} className={styles.tierCard}>
                <div className={styles.tierError}>
                  <h3 className={styles.tierName}>Error Loading Tier</h3>
                  <p className={styles.tierDescription}>
                    Unable to display this pricing option.
                  </p>
                </div>
              </div>
            )
          }
        })}
      </div>

      <div className={styles.navigationButtons}>
        <button 
          className={styles.backButton}
          onClick={onBack}
        >
          ← Back to Services
        </button>
        
        <div className={styles.tierInfo}>
          <p>Select a duration option above to continue to checkout</p>
        </div>
      </div>
    </div>
  )
}
