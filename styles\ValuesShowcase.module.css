.valuesSection {
  position: relative;
  padding: 6rem 2rem;
  overflow: hidden;
  background: linear-gradient(to bottom, rgba(249, 249, 249, 0.7), rgba(233, 247, 246, 0.7)); /* Changed to translucent */
  z-index: 1;
}

.shapeTop, .shapeBottom {
  position: absolute;
  width: 100%;
  height: 100px;
  left: 0;
  z-index: -1;
}

.shapeTop {
  top: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1200 120' preserveAspectRatio='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%234ECDC4'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%234ECDC4'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%234ECDC4'%3E%3C/path%3E%3C/svg%3E");
  background-position: bottom;
  background-size: 100% 100px;
  transform: rotate(180deg);
}

.shapeBottom {
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 1200 120' preserveAspectRatio='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23FF6B6B'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23FF6B6B'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23FF6B6B'%3E%3C/path%3E%3C/svg%3E");
  background-position: top;
  background-size: 100% 100px;
}

.valuesContainer {
  max-width: var(--max-width);
  margin: 0 auto;
}

.valuesTitleContainer {
  text-align: center;
  margin-bottom: 4rem;
}

.valuesTitle {
  font-size: 2.5rem;
  color: var(--text-color);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.valuesTitle::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 3px;
}

.valuesSubtitle {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.valuesGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

@media (min-width: 1200px) {
  .valuesGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .valuesGrid {
    grid-template-columns: repeat(1, 1fr);
  }
}

.valueCard {
  background-color: rgba(255, 255, 255, 0.8); /* Changed to translucent */
  padding: 3rem 2rem;
  border-radius: 12px;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  z-index: 1;
}

.valueCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  z-index: 2;
}

.valueCard:hover {
  transform: translateY(-15px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.valueCard:hover::before {
  transform: scaleX(1);
}

.valueIconContainer {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
}

.valueIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.5rem;
  z-index: 3;
}

.iconRing {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(255, 107, 107, 0.1));
  animation: pulseRing 4s infinite;
}

.iconRing::before, .iconRing::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid rgba(78, 205, 196, 0.3);
  animation: pulseRing 3s infinite;
}

.iconRing::after {
  animation-delay: 1s;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.valueTitle {
  font-size: 1.5rem;
  color: var(--text-color);
  margin-bottom: 1.25rem;
  font-weight: 600;
  position: relative;
}

.valueDescription {
  color: var(--light-text-color);
  line-height: 1.7;
  font-size: 1rem;
}

.cardDecoration {
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.05), rgba(255, 107, 107, 0.05));
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s ease;
}

.valueCard:hover .cardDecoration {
  opacity: 1;
  transform: scale(1);
}

/* Media Queries */
@media (max-width: 1200px) {
  .valuesTitle {
    font-size: 2.2rem;
  }

  .valuesSubtitle {
    font-size: 1.1rem;
  }
}

@media (max-width: 992px) {
  .valuesSection {
    padding: 5rem 1.5rem;
  }

  .valuesTitle {
    font-size: 2rem;
  }

  .valuesTitleContainer {
    margin-bottom: 3rem;
  }

  .valuesGrid {
    gap: 1.5rem;
  }

  .valueCard {
    padding: 2.5rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .valuesSection {
    padding: 4rem 1rem;
  }

  .shapeTop, .shapeBottom {
    height: 60px;
    background-size: 100% 60px;
  }

  .valuesTitle {
    font-size: 1.8rem;
  }

  .valuesSubtitle {
    font-size: 1rem;
  }

  .valueCard {
    padding: 2rem 1.5rem;
  }

  .valueIconContainer {
    width: 70px;
    height: 70px;
    margin-bottom: 1.5rem;
  }

  .valueIcon {
    font-size: 2.2rem;
  }

  .valueTitle {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .valuesSection {
    padding: 3rem 1rem;
  }

  .valuesTitle {
    font-size: 1.6rem;
  }

  .valuesTitle::after {
    width: 60px;
  }

  .valueCard {
    padding: 1.5rem 1rem;
  }

  .valueIcon {
    font-size: 2rem;
  }

  .valueTitle {
    font-size: 1.2rem;
  }

  .valueDescription {
    font-size: 0.95rem;
  }
}