import { createClient } from '@supabase/supabase-js'
import { Pool } from 'pg'

// Initialize Supabase client with service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Extract database connection info from service role key
// This is a workaround for direct database access
const getConnectionInfo = () => {
  try {
    // Parse the JWT to get the connection info
    const base64Payload = serviceRoleKey.split('.')[1]
    const payload = Buffer.from(base64Payload, 'base64').toString('utf8')
    const { db_host, db_password, db_user } = JSON.parse(payload)
    
    return {
      host: db_host,
      user: db_user,
      password: db_password,
      database: 'postgres',
      port: 5432,
      ssl: true
    }
  } catch (error) {
    console.error('Error extracting connection info:', error)
    return null
  }
}

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get userId and role from request body
    const { userId, role } = req.body

    if (!userId || !role) {
      return res.status(400).json({ error: 'User ID and role are required' })
    }    // Validate role
    const validRoles = ['dev', 'admin', 'artist', 'braider', 'user']
    if (!validRoles.includes(role)) {
      return res.status(400).json({ error: 'Invalid role. Must be one of: dev, admin, artist, braider, user' })
    }

    console.log(`Simple role set attempt for user ID: ${userId} to role: ${role}`)

    // First try with Supabase client
    try {
      const supabase = createClient(supabaseUrl, serviceRoleKey)
      
      // Try to insert directly
      const { data, error } = await supabase
        .from('user_roles')
        .upsert([{ id: userId, role }], { onConflict: 'id' })
        .select()
      
      if (error) {
        console.error('Supabase upsert error:', error)
        // Continue to next approach if this fails
      } else {
        console.log('Supabase upsert success:', data)
        return res.status(200).json({ 
          success: true, 
          message: `Role set to ${role} for user ${userId} using Supabase client`,
          data
        })
      }
    } catch (supabaseError) {
      console.error('Supabase client error:', supabaseError)
      // Continue to next approach if this fails
    }

    // If we get here, the first approach failed
    // Return a helpful error message
    return res.status(500).json({ 
      error: 'Failed to set user role using Supabase client', 
      message: 'The service role key might not have the necessary permissions',
      hint: 'Check your Supabase project settings and ensure the service role key has the correct permissions'
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return res.status(500).json({ 
      error: 'An unexpected error occurred',
      details: error.message
    })
  }
}
