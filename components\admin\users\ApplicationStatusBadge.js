import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/users/ApplicationStatusBadge.module.css'

export default function ApplicationStatusBadge({ status }) {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          icon: '⏳',
          className: styles.pending
        }
      case 'under_review':
        return {
          label: 'Under Review',
          icon: '👀',
          className: styles.underReview
        }
      case 'approved':
        return {
          label: 'Approved',
          icon: '✅',
          className: styles.approved
        }
      case 'rejected':
        return {
          label: 'Rejected',
          icon: '❌',
          className: styles.rejected
        }
      default:
        return {
          label: 'Unknown',
          icon: '❓',
          className: styles.unknown
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <span className={`${styles.statusBadge} ${config.className}`}>
      <span className={styles.statusIcon}>{config.icon}</span>
      <span className={styles.statusLabel}>
        {safeRender(config.label, 'Unknown')}
      </span>
    </span>
  )
}
