// Client-side notification functions and hooks - SAFE FOR CLIENT COMPONENTS
// This file only contains browser-safe code and React hooks

import { useEffect, useState } from 'react';
import { supabase, getAdminClient } from './supabase';
import { toast } from 'react-toastify';

// Custom hook to manage notifications
// Enhanced with better error handling, timeout mechanisms, and resource cleanup
export const useNotifications = (userId) => {
  const [notifications, setNotifications] = useState([]);
  const [newNotification, setNewNotification] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!userId) return;

    // Track if component is mounted
    let isMounted = true;
    let channel = null;
    let timeoutId = null;

    // Safe state update functions
    const safeSetNotifications = (value) => {
      if (isMounted) setNotifications(value);
    };

    const safeSetNewNotification = (value) => {
      if (isMounted) setNewNotification(value);
    };

    const safeSetError = (value) => {
      if (isMounted) setError(value);
    };

    const safeSetIsLoading = (value) => {
      if (isMounted) setIsLoading(value);
    };

    const initializeSubscription = async () => {
      // Set a timeout to prevent hanging initialization
      timeoutId = setTimeout(() => {
        if (isMounted) {
          console.error('Notification initialization timed out');
          safeSetError('Notification system initialization timed out');
          safeSetIsLoading(false);
        }
      }, 10000); // 10 second timeout

      try {
        safeSetIsLoading(true);

        // Use Supabase client directly
        let client;
        try {
          // Use the Supabase client directly
          client = supabase;

          if (!client) {
            throw new Error('Supabase client not available for notifications');
          }
        } catch (clientError) {
          console.error('Error getting Supabase client for notifications:', clientError);
          safeSetError(`Failed to initialize notification system: ${clientError.message}`);
          safeSetIsLoading(false);

          // Clear the timeout
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          return null;
        }

        // Fetch initial notifications with timeout
        try {
          const fetchPromise = client
            .from('notifications')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Notification fetch timeout')), 5000)
          );

          const { data, error } = await Promise.race([fetchPromise, timeoutPromise]);

          if (error) {
            console.error('Error fetching notifications:', error);
            safeSetError(`Error fetching notifications: ${error.message}`);
            // Continue with subscription even if initial fetch fails
          } else if (isMounted) {
            safeSetNotifications(data || []);
          }
        } catch (fetchError) {
          console.error('Exception during notification fetch:', fetchError);
          safeSetError(`Error fetching notifications: ${fetchError.message}`);
          // Continue with subscription even if initial fetch fails
        }

        // Subscribe to new notifications with error handling
        try {
          channel = client
            .channel(`notifications:${userId}`)
            .on(
              'postgres_changes',
              { event: 'INSERT', schema: 'public', table: 'notifications', filter: `user_id=eq.${userId}` },
              (payload) => {
                try {
                  if (!isMounted) return;

                  console.log('New notification received:', payload);
                  const newNotif = payload.new;

                  safeSetNotifications((prevNotifications) => [newNotif, ...prevNotifications]);
                  safeSetNewNotification(newNotif);

                  // Show toast notification if message exists
                  if (newNotif && newNotif.message) {
                    toast.success(newNotif.message, {
                      position: 'bottom-right',
                      autoClose: 5000
                    });
                  } else {
                    toast.success('You have a new notification!', {
                      position: 'bottom-right',
                      autoClose: 5000
                    });
                  }
                } catch (handlerError) {
                  console.error('Error handling new notification:', handlerError);
                  // Don't propagate the error to prevent breaking the subscription
                }
              }
            )
            .subscribe((status, err) => {
              if (!isMounted) return;

              if (status === 'SUBSCRIBED') {
                console.log('Subscribed to notifications for user:', userId);
                safeSetIsLoading(false);
                safeSetError(null);
              } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
                console.error('Notification subscription error:', status, err);
                safeSetError(`Notification subscription error: ${err?.message || status}`);
                safeSetIsLoading(false);

                // Show toast only for critical errors
                if (err?.message && err.message.includes('authentication')) {
                  toast.error(`Notification system error: ${err.message}`);
                }
              } else if (status === 'CLOSED') {
                console.log('Notification subscription closed for user:', userId);
                safeSetIsLoading(false);
              }
            });
        } catch (subscriptionError) {
          console.error('Error setting up notification subscription:', subscriptionError);
          safeSetError(`Error setting up notification subscription: ${subscriptionError.message}`);
          safeSetIsLoading(false);
        }

        // Clear the timeout since initialization completed
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        return () => {
          // Cleanup function for subscription
          if (channel && client) {
            try {
              client.removeChannel(channel).then(status => {
                console.log(`Channel for user ${userId} removed with status:`, status);
              }).catch(error => {
                console.error(`Error removing channel for user ${userId}:`, error);
              });
            } catch (cleanupError) {
              console.error('Error during notification channel cleanup:', cleanupError);
            }
          }
        };
      } catch (e) {
        // Clear the timeout
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        console.error('Exception during notification subscription initialization:', e);
        safeSetError(`Critical error setting up notifications: ${e.message}`);
        safeSetIsLoading(false);

        // Show toast for critical errors
        toast.error(`Notification system error: ${e.message}`);
        return null;
      }
    };

    // Initialize the subscription
    const cleanupPromise = initializeSubscription();

    // Return cleanup function
    return () => {
      isMounted = false;

      // Clear any pending timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // Execute cleanup function if available
      if (cleanupPromise) {
        cleanupPromise.then(cleanup => {
          if (typeof cleanup === 'function') {
            try {
              cleanup();
            } catch (cleanupError) {
              console.error('Error during notification cleanup execution:', cleanupError);
            }
          }
        }).catch(e => console.error('Error during notification cleanup promise resolution:', e));
      }
    };
  }, [userId]);

  // Return the hook state and functions
  return {
    notifications,
    newNotification,
    error,
    isLoading
  };
};

// Mark a notification as read
// Enhanced with better error handling and timeout protection
export const markNotificationAsRead = async (notificationId) => {
  // Create a timeout ID for cleanup
  let timeoutId = null;

  try {
    // Use Supabase client directly
    let client;
    try {
      // Use the Supabase client directly
      client = supabase;

      if (!client) {
        throw new Error('Supabase client not available for marking notification as read');
      }
    } catch (clientError) {
      console.error('Error getting Supabase client for marking notification as read:', clientError);
      toast.error(`Error: ${clientError.message}`);
      return null;
    }

    // Update notification with timeout protection
    try {
      const updatePromise = client
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .select(); // select() to get the updated record back

      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => reject(new Error('Update notification timeout')), 5000);
      });

      const { data, error } = await Promise.race([updatePromise, timeoutPromise]);

      // Clear the timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (error) {
        console.error('Error marking notification as read:', error);
        toast.error(`Error: ${error.message}`);
        return null;
      }

      console.log('Notification marked as read:', data);
      return data;
    } catch (updateError) {
      console.error('Error updating notification:', updateError);
      toast.error(`Failed to mark notification as read: ${updateError.message}`);
      return null;
    }
  } catch (error) {
    // Clear any pending timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    console.error('Exception in markNotificationAsRead:', error);
    toast.error(`Failed to mark notification as read: ${error.message}`);
    return null;
  }
};

// Clear all notifications for a user
// Enhanced with better error handling and timeout protection
export const clearAllNotifications = async (userId) => {
  // Create a timeout ID for cleanup
  let timeoutId = null;

  try {
    // Use Supabase client directly
    let client;
    try {
      // Use the Supabase client directly
      client = supabase;

      if (!client) {
        throw new Error('Supabase client not available for clearing notifications');
      }
    } catch (clientError) {
      console.error('Error getting Supabase client for clearing notifications:', clientError);
      toast.error(`Error: ${clientError.message}`);
      return false;
    }

    // Delete notifications with timeout protection
    try {
      const deletePromise = client
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => reject(new Error('Delete notifications timeout')), 5000);
      });

      const { error } = await Promise.race([deletePromise, timeoutPromise]);

      // Clear the timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (error) {
        console.error('Error clearing notifications:', error);
        toast.error(`Error: ${error.message}`);
        return false;
      }

      toast.success('All notifications cleared.');
      return true;
    } catch (deleteError) {
      console.error('Error deleting notifications:', deleteError);
      toast.error(`Failed to clear notifications: ${deleteError.message}`);
      return false;
    }
  } catch (error) {
    // Clear any pending timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    console.error('Exception in clearAllNotifications:', error);
    toast.error(`Failed to clear notifications: ${error.message}`);
    return false;
  }
};

// Client-side function to send booking notification via API call
export const sendBookingNotification = async (notificationData) => {
  try {
    const response = await fetch('/api/notifications/booking', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notificationData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to send booking notification');
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error sending booking notification:', error);
    throw error;
  }
};
