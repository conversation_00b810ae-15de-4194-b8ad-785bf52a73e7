import { supabaseAdmin } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * API endpoint for admin booking management
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Bookings API endpoint called: ${req.method} ${req.url}`);

  // Log headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));
  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }
  if (req.headers.cookie) {
    console.log(`[${requestId}] Cookie header present`);
  }
  if (req.headers['x-auth-token']) {
    console.log(`[${requestId}] x-auth-token header present`);
  }

  // Authenticate request using the new auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}, Role: ${authResult.user?.role}`);

  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch bookings
    if (req.method === 'GET') {
      console.log(`[${requestId}] Processing GET request for bookings`);

      // Get query parameters
      const { startDate, endDate, customerId, serviceId, status } = req.query;

      // Start building the query using Supabase directly
      let query = supabaseAdmin
        .from('bookings')
        .select(`
          id,
          customer_id,
          service_id,
          start_time,
          end_time,
          status,
          location,
          notes,
          customers:customer_id (name, email, phone),
          services:service_id (name, color)
        `);

      // Apply filters if provided
      if (startDate) {
        query = query.gte('start_time', new Date(startDate).toISOString());
      }

      if (endDate) {
        query = query.lte('end_time', new Date(endDate).toISOString());
      }

      if (customerId) {
        query = query.eq('customer_id', customerId);
      }

      if (serviceId) {
        query = query.eq('service_id', serviceId);
      }

      if (status) {
        query = query.eq('status', status);
      }

      try {
        // Execute the query
        const { data, error } = await query.order('start_time', { ascending: true });

        if (error) {
          console.error(`[${requestId}] Error fetching bookings:`, error);
          throw error;
        }

        console.log(`[${requestId}] Successfully fetched ${data?.length || 0} bookings`);

        // Set appropriate cache headers for bookings data
        setCacheHeaders(res, 'bookings', 'GET', true, req.query);

        return res.status(200).json({
          bookings: data || [],
          requestId,
          timestamp: new Date().toISOString()
        });
      } catch (queryError) {
        console.error(`[${requestId}] Query execution error:`, queryError);
        throw queryError;
      }
    }

    // POST - Create a new booking
    else if (req.method === 'POST') {
      const {
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        location,
        notes
      } = req.body;

      // Validate required fields
      if (!customer_id || !service_id || !start_time || !end_time || !status) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      // Create the booking using Supabase directly
      const { data, error } = await supabaseAdmin
        .from('bookings')
        .insert([{
          customer_id,
          service_id,
          start_time: new Date(start_time).toISOString(),
          end_time: new Date(end_time).toISOString(),
          status,
          location: location || '',
          notes: notes || ''
        }])
        .select();

      if (error) {
        console.error('Error creating booking:', error);
        throw error;
      }

      return res.status(201).json({ booking: data[0] });
    }

    // PUT - Update an existing booking
    else if (req.method === 'PUT') {
      const { id } = req.query;
      const {
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        location,
        notes
      } = req.body;

      // Validate booking ID
      if (!id) {
        return res.status(400).json({ error: 'Booking ID is required' });
      }

      // Update the booking using Supabase directly
      const { data, error } = await supabaseAdmin
        .from('bookings')
        .update({
          customer_id,
          service_id,
          start_time: new Date(start_time).toISOString(),
          end_time: new Date(end_time).toISOString(),
          status,
          location: location || '',
          notes: notes || ''
        })
        .eq('id', id)
        .select();

      if (error) {
        console.error('Error updating booking:', error);
        throw error;
      }

      return res.status(200).json({ booking: data[0] });
    }

    // DELETE - Delete a booking
    else if (req.method === 'DELETE') {
      const { id } = req.query;

      // Validate booking ID
      if (!id) {
        return res.status(400).json({ error: 'Booking ID is required' });
      }

      // Delete the booking using Supabase directly
      const { error } = await supabaseAdmin
        .from('bookings')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting booking:', error);
        throw error;
      }

      return res.status(200).json({ success: true });
    }
  } catch (error) {
    console.error(`[${requestId}] Booking API Error:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process booking request';

    if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out while processing';
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested resource not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    }

    return res.status(statusCode).json({
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
