import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// Configuration
const SITE_URL = 'https://www.oceansoulsparkles.com.au';
const PUBLIC_DIR = path.join(process.cwd(), 'public');
const PAGES_DIR = path.join(process.cwd(), 'pages');
const SITEMAP_PATH = path.join(PUBLIC_DIR, 'sitemap.xml');
const EXCLUDED_PATHS = [
  '_app.js',
  '_document.js',
  '_error.js',
  'api',
  '404.js',
  '500.js',
  'admin'
];

// Get all pages
async function getPages() {
  const pages = await glob(`${PAGES_DIR}/**/*.js`);

  return pages
    .filter(page => {
      const relativePath = path.relative(PAGES_DIR, page);
      return !EXCLUDED_PATHS.some(excluded => relativePath.includes(excluded));
    })
    .map(page => {
      // Use path.relative to get clean relative path
      let route = path.relative(PAGES_DIR, page)
        .replace(/\.js$/, '')
        .replace(/index$/, '')
        .replace(/\\/g, '/'); // Fix Windows path separators

      // Ensure route starts with /
      if (!route.startsWith('/')) {
        route = '/' + route;
      }

      // Clean up double slashes and trailing slashes
      route = route.replace(/\/+/g, '/').replace(/\/$/, '');

      // Handle root route
      if (route === '' || route === '/index') {
        route = '/';
      }

      return {
        path: route,
        lastModified: new Date().toISOString().split('T')[0],
        changeFreq: route === '/' ? 'daily' : 'weekly',
        priority: route === '/' ? '1.0' : '0.8'
      };
    });
}

// Generate sitemap XML
function generateSitemap(pages) {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `  <url>
    <loc>${SITE_URL}${page.path}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

  fs.writeFileSync(SITEMAP_PATH, sitemap);
  console.log(`Sitemap generated at ${SITEMAP_PATH}`);
  console.log(`Generated URLs:`);
  pages.forEach(page => console.log(`  ${SITE_URL}${page.path}`));
}

// Main function
async function main() {
  const pages = await getPages();
  generateSitemap(pages);
}

main();
