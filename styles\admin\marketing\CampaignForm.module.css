.campaignForm {
  padding: 0;
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.formSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.formSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.formGroup {
  margin-bottom: 20px;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #6e8efb;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.helpText {
  margin-top: 4px;
  font-size: 0.8rem;
  color: #666;
}

.loading {
  padding: 10px 0;
  color: #666;
  font-style: italic;
}

.noSegments {
  background-color: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
  border-radius: 4px;
  padding: 16px;
  text-align: center;
}

.noSegments p {
  margin: 0 0 12px 0;
  color: #ff9800;
}

.createSegmentButton {
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.createSegmentButton:hover {
  background-color: #f57c00;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancelButton,
.submitButton {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.submitButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cancelButton:disabled,
.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .formRow {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .cancelButton,
  .submitButton {
    width: 100%;
  }
}
