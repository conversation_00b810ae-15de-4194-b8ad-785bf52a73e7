import { getAdminClient } from '@/lib/supabase'; // Use admin client for server-side operations
import { v4 as uuidv4 } from 'uuid';

/**
 * API endpoint for creating recurring bookings
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  // Generate a request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log(`[${requestId}] Processing ${req.method} request to /api/admin/bookings/recurring`);

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get admin client for server-side operations
    const supabase = getAdminClient();

    // Get booking data and recurring options from request body
    const {
      customer_id,
      service_id,
      start_time,
      end_time,
      status,
      location,
      notes,
      recurring
    } = req.body;

    // Validate required fields
    if (!customer_id || !service_id || !start_time || !end_time || !recurring) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Parse dates
    const startDate = new Date(start_time);
    const endDate = new Date(end_time);

    // Calculate duration in milliseconds
    const durationMs = endDate.getTime() - startDate.getTime();

    // Generate booking series ID
    const bookingSeriesId = uuidv4();

    // Create first booking (parent booking)
    const { data: parentBooking, error: parentError } = await supabase
      .from('bookings')
      .insert([{
        customer_id,
        service_id,
        start_time: startDate.toISOString(),
        end_time: endDate.toISOString(),
        status,
        location,
        notes,
        is_recurring: true,
        recurring_pattern: recurring.pattern,
        booking_series_id: bookingSeriesId
      }])
      .select();

    if (parentError) {
      console.error(`[${requestId}] Error creating parent booking:`, parentError);
      return res.status(500).json({ error: 'Error creating parent booking' });
    }

    // Generate recurring bookings
    const recurringBookings = [];
    let currentDate = new Date(startDate);
    let occurrenceCount = 1; // Start at 1 because we already created the parent booking

    // Determine end date or max occurrences
    let maxOccurrences = 52; // Default max (1 year for weekly)
    let endDateTime = null;

    if (recurring.endType === 'count' && recurring.count) {
      maxOccurrences = recurring.count;
    } else if (recurring.endType === 'date' && recurring.endDate) {
      endDateTime = new Date(recurring.endDate).getTime();
    }

    // Generate dates based on pattern
    while (occurrenceCount < maxOccurrences) {
      let nextDate = null;

      // Calculate next date based on pattern
      if (recurring.pattern === 'daily') {
        nextDate = new Date(currentDate);
        nextDate.setDate(nextDate.getDate() + recurring.interval);
      } else if (recurring.pattern === 'weekly') {
        // For weekly pattern, we need to handle multiple days of week
        if (recurring.daysOfWeek) {
          const daysOfWeek = Object.entries(recurring.daysOfWeek)
            .filter(([_, selected]) => selected)
            .map(([day]) => ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].indexOf(day));

          // Find the next day of week
          let found = false;
          nextDate = new Date(currentDate);

          // Try each day until we find the next one
          for (let i = 1; i <= 7 && !found; i++) {
            nextDate.setDate(nextDate.getDate() + 1);
            if (daysOfWeek.includes(nextDate.getDay())) {
              found = true;
            }
          }

          // If we didn't find a day in the current week, move to the next interval
          if (!found) {
            nextDate = new Date(currentDate);
            nextDate.setDate(nextDate.getDate() + (7 * recurring.interval));
          }
        } else {
          // Simple weekly pattern (same day each week)
          nextDate = new Date(currentDate);
          nextDate.setDate(nextDate.getDate() + (7 * recurring.interval));
        }
      } else if (recurring.pattern === 'monthly') {
        nextDate = new Date(currentDate);
        nextDate.setMonth(nextDate.getMonth() + recurring.interval);
      }

      // Check if we've reached the end date
      if (endDateTime && nextDate.getTime() > endDateTime) {
        break;
      }

      // Update current date for next iteration
      currentDate = nextDate;

      // Calculate end time based on duration
      const nextEndDate = new Date(nextDate.getTime() + durationMs);

      // Add to recurring bookings array
      recurringBookings.push({
        customer_id,
        service_id,
        start_time: nextDate.toISOString(),
        end_time: nextEndDate.toISOString(),
        status,
        location,
        notes,
        is_recurring: true,
        recurring_pattern: recurring.pattern,
        booking_series_id: bookingSeriesId,
        parent_booking_id: parentBooking[0].id
      });

      occurrenceCount++;
    }

    // Insert recurring bookings in batches to avoid hitting API limits
    const batchSize = 10;
    const results = [];

    for (let i = 0; i < recurringBookings.length; i += batchSize) {
      const batch = recurringBookings.slice(i, i + batchSize);

      const { data, error } = await supabase
        .from('bookings')
        .insert(batch)
        .select();

      if (error) {
        console.error(`[${requestId}] Error creating recurring bookings batch:`, error);
        // Continue with other batches even if one fails
      } else if (data) {
        results.push(...data);
      }
    }

    // Return the parent booking and count of created recurring bookings
    return res.status(200).json({
      parentBooking: parentBooking[0],
      recurringBookingsCount: results.length,
      bookingSeriesId
    });
  } catch (error) {
    console.error(`[${requestId}] Unhandled error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
