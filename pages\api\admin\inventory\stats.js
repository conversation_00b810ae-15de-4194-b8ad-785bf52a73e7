import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for inventory statistics
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response with inventory statistics
 */
export default async function handler(req, res) {
  // Authenticate request using our robust auth module
  const { authorized, error } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({ error: 'Unauthorized access' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get admin client
    const supabase = getAdminClient();
    if (!supabase) {
      console.error('Failed to initialize admin client');
      return res.status(500).json({ error: 'Failed to initialize admin client' });
    }

    console.log('Fetching inventory statistics...');

    // Get total products count
    const { count: totalProducts, error: productsError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (productsError) {
      console.error('Error fetching total products:', productsError);
      throw productsError;
    }

    // Get active products count (using both status and is_active fields for compatibility)
    const { count: activeProducts, error: activeProductsError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .or('status.eq.active,and(status.is.null,is_active.eq.true)');

    if (activeProductsError) {
      console.error('Error fetching active products:', activeProductsError);
      throw activeProductsError;
    }

    // Get total services count
    const { count: totalServices, error: servicesError } = await supabase
      .from('services')
      .select('*', { count: 'exact', head: true });

    if (servicesError) {
      console.error('Error fetching total services:', servicesError);
      throw servicesError;
    }

    // For services, we don't have a status column, so active services = total services
    // All services are considered active unless explicitly marked otherwise in the future
    const activeServices = totalServices;

    console.log('Inventory stats fetched successfully:', {
      totalProducts,
      activeProducts,
      totalServices,
      activeServices
    });

    return res.status(200).json({
      totalProducts: totalProducts || 0,
      activeProducts: activeProducts || 0,
      totalServices: totalServices || 0,
      activeServices: activeServices || 0,
    });
  } catch (error) {
    console.error('Inventory stats error:', error);
    return res.status(500).json({
      error: 'Failed to fetch inventory statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
