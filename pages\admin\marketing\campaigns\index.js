import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import styles from '@/styles/admin/marketing/CampaignList.module.css'
import { debounce } from 'lodash'

export default function CampaignList() {
  const router = useRouter()
  const [campaigns, setCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState({
    status: '',
    campaign_type: ''
  })
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  })

  // Initialize filters from URL query params
  useEffect(() => {
    if (router.query.status) {
      setFilters(prev => ({ ...prev, status: router.query.status }))
    }
    if (router.query.campaign_type) {
      setFilters(prev => ({ ...prev, campaign_type: router.query.campaign_type }))
    }
  }, [router.query])

  // Debounce search input
  const debouncedSetSearch = useCallback(
    debounce((value) => {
      setDebouncedSearch(value)
    }, 500),
    []
  )

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    debouncedSetSearch(e.target.value)
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({ ...prev, [name]: value }))
  }

  // Handle sort change
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? ' ↑' : ' ↓'
  }

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination({ ...pagination, page: newPage })
  }

  // Fetch campaigns
  const fetchCampaigns = async () => {
    setLoading(true)
    setError(null)

    try {
      const offset = (pagination.page - 1) * pagination.limit
      const queryParams = new URLSearchParams({
        limit: pagination.limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder
      })

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch)
      }

      if (filters.status) {
        queryParams.append('status', filters.status)
      }

      if (filters.campaign_type) {
        queryParams.append('campaign_type', filters.campaign_type)
      }

      const response = await fetch(`/api/marketing/campaigns?${queryParams.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch campaigns')
      }

      const data = await response.json()
      setCampaigns(data.campaigns || [])
      setPagination({ ...pagination, total: data.total || 0 })
    } catch (error) {
      console.error('Error fetching campaigns:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Fetch campaigns when dependencies change
  useEffect(() => {
    fetchCampaigns()
  }, [debouncedSearch, filters, sortBy, sortOrder, pagination.page, pagination.limit])

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Ongoing'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get status class
  const getStatusClass = (status) => {
    switch (status) {
      case 'active':
        return styles.statusActive
      case 'scheduled':
        return styles.statusScheduled
      case 'completed':
        return styles.statusCompleted
      case 'draft':
        return styles.statusDraft
      case 'paused':
        return styles.statusPaused
      case 'canceled':
        return styles.statusCanceled
      default:
        return ''
    }
  }

  // Calculate total pages
  const totalPages = Math.ceil(pagination.total / pagination.limit)

  return (
    <AdminLayout>
      <div className={styles.campaignList}>
        <div className={styles.header}>
          <h2>Marketing Campaigns</h2>
          <div className={styles.actions}>
            <Link href="/admin/marketing/campaigns/new" className={styles.addButton}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              Create Campaign
            </Link>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Search campaigns..."
              value={search}
              onChange={handleSearchChange}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filterControls}>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Statuses</option>
              <option value="draft">Draft</option>
              <option value="scheduled">Scheduled</option>
              <option value="active">Active</option>
              <option value="paused">Paused</option>
              <option value="completed">Completed</option>
              <option value="canceled">Canceled</option>
            </select>

            <select
              name="campaign_type"
              value={filters.campaign_type}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Types</option>
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="push">Push</option>
              <option value="multi">Multi-channel</option>
            </select>
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>Loading campaigns...</div>
        ) : (
          <>
            <div className={styles.tableContainer}>
              <table className={styles.campaignTable}>
                <thead>
                  <tr>
                    <th onClick={() => handleSort('name')}>
                      Name {renderSortIndicator('name')}
                    </th>
                    <th onClick={() => handleSort('status')}>
                      Status {renderSortIndicator('status')}
                    </th>
                    <th onClick={() => handleSort('campaign_type')}>
                      Type {renderSortIndicator('campaign_type')}
                    </th>
                    <th onClick={() => handleSort('start_date')}>
                      Dates {renderSortIndicator('start_date')}
                    </th>
                    <th>Target Segment</th>
                    <th>Metrics</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {campaigns.length === 0 ? (
                    <tr>
                      <td colSpan="7" className={styles.noResults}>
                        No campaigns found. Create your first campaign to get started.
                      </td>
                    </tr>
                  ) : (
                    campaigns.map((campaign) => (
                      <tr key={campaign.id}>
                        <td>{campaign.name}</td>
                        <td>
                          <span className={`${styles.status} ${getStatusClass(campaign.status)}`}>
                            {campaign.status}
                          </span>
                        </td>
                        <td>{campaign.campaign_type}</td>
                        <td>
                          {formatDate(campaign.start_date)} - {formatDate(campaign.end_date)}
                        </td>
                        <td>
                          {campaign.target_segment ? (
                            <Link href={`/admin/marketing/segments/${campaign.target_segment.id}`}>
                              <a className={styles.segmentLink}>
                                {campaign.target_segment.name}
                              </a>
                            </Link>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className={styles.metrics}>
                          {campaign.metrics && campaign.metrics.sent ? (
                            <div className={styles.metricItem}>
                              <span className={styles.metricLabel}>Sent:</span>
                              <span className={styles.metricValue}>{campaign.metrics.sent}</span>
                            </div>
                          ) : (
                            '-'
                          )}
                        </td>
                        <td className={styles.actions}>
                          <Link href={`/admin/marketing/campaigns/${campaign.id}`}>
                            <a className={styles.viewButton}>View</a>
                          </Link>
                          <Link href={`/admin/marketing/campaigns/${campaign.id}/edit`}>
                            <a className={styles.editButton}>Edit</a>
                          </Link>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(1)}
                  disabled={pagination.page === 1}
                >
                  &laquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  &lsaquo;
                </button>
                <span className={styles.paginationInfo}>
                  Page {pagination.page} of {totalPages}
                </span>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === totalPages}
                >
                  &rsaquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(totalPages)}
                  disabled={pagination.page === totalPages}
                >
                  &raquo;
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  )
}
