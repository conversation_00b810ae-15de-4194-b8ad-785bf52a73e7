// Customer Profile Component for account dashboard
import { useState, useEffect } from 'react';
import { useCustomer } from '@/contexts/CustomerContext';
import CustomerForm from './CustomerForm';
import styles from '@/styles/CustomerProfile.module.css';

/**
 * CustomerProfile component displays customer information and allows editing
 * 
 * @returns {JSX.Element}
 */
const CustomerProfile = () => {
  const { customer, loading, error } = useCustomer();
  const [isEditing, setIsEditing] = useState(false);

  if (loading) {
    return <div className={styles.loading}>Loading your profile information...</div>;
  }

  if (error) {
    return <div className={styles.error}>Error loading profile: {error.message}</div>;
  }

  if (!customer) {
    return <div className={styles.notFound}>Profile information not found.</div>;
  }

  const handleEditComplete = () => {
    setIsEditing(false);
  };

  return (
    <div className={styles.profileContainer}>
      {!isEditing ? (
        <div className={styles.profileView}>
          <div className={styles.profileHeader}>
            <h1>My Profile</h1>
            <button 
              className={styles.editButton}
              onClick={() => setIsEditing(true)}
            >
              Edit Profile
            </button>
          </div>

          <div className={styles.profileSection}>
            <h2>Contact Information</h2>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Name</span>
                <span className={styles.infoValue}>{customer.name}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Email</span>
                <span className={styles.infoValue}>{customer.email}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Phone</span>
                <span className={styles.infoValue}>{customer.phone || 'Not provided'}</span>
              </div>
            </div>
          </div>

          <div className={styles.profileSection}>
            <h2>Address</h2>
            {customer.address ? (
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Street</span>
                  <span className={styles.infoValue}>{customer.address}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>City</span>
                  <span className={styles.infoValue}>{customer.city || 'Not provided'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>State</span>
                  <span className={styles.infoValue}>{customer.state || 'Not provided'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Postal Code</span>
                  <span className={styles.infoValue}>{customer.postal_code || 'Not provided'}</span>
                </div>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Country</span>
                  <span className={styles.infoValue}>{customer.country || 'Australia'}</span>
                </div>
              </div>
            ) : (
              <p className={styles.noData}>No address information provided.</p>
            )}
          </div>

          <div className={styles.profileSection}>
            <h2>Communication Preferences</h2>
            <div className={styles.preferencesContainer}>
              <div className={styles.preferenceItem}>
                <span className={styles.preferenceLabel}>Marketing Communications</span>
                <span className={`${styles.preferenceStatus} ${customer.marketing_consent ? styles.active : styles.inactive}`}>
                  {customer.marketing_consent ? 'Subscribed' : 'Not Subscribed'}
                </span>
              </div>
              <p className={styles.preferencesDescription}>
                {customer.marketing_consent
                  ? 'You are currently receiving marketing emails with our latest news, promotions, and updates.'
                  : 'You are not subscribed to our marketing communications. Edit your profile to opt in.'}
              </p>
            </div>
          </div>

          <div className={styles.accountInfo}>
            <h3>Account Created</h3>
            <p>{new Date(customer.created_at).toLocaleDateString('en-AU', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}</p>
          </div>
        </div>
      ) : (
        <div className={styles.editContainer}>
          <div className={styles.editHeader}>
            <h1>Edit Profile</h1>
            <button 
              className={styles.cancelButton}
              onClick={() => setIsEditing(false)}
            >
              Cancel
            </button>
          </div>
          <CustomerForm
            isEditing={true}
            onComplete={handleEditComplete}
          />
        </div>
      )}
    </div>
  );
};

export default CustomerProfile;
