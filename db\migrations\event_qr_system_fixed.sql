-- Event-Based QR Code System Migration (Fixed Version)
-- This migration creates the database structure for event-specific QR codes
-- with comprehensive revenue tracking and analytics

-- =============================================
-- EVENTS TABLE
-- =============================================

-- Create events table for managing festival/event information
CREATE TABLE IF NOT EXISTS public.events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  description TEXT,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
  max_capacity INTEGER,
  current_bookings INTEGER DEFAULT 0,
  created_by UUID,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- EVENT QR CODES TABLE
-- =============================================

-- Create event-specific QR codes table
CREATE TABLE IF NOT EXISTS public.event_qr_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT UNIQUE NOT NULL,
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  event_name TEXT NOT NULL,
  event_location TEXT NOT NULL,
  event_start_date TIMESTAMPTZ NOT NULL,
  event_end_date TIMESTAMPTZ NOT NULL,
  assigned_artists UUID[] DEFAULT '{}',
  available_services UUID[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  usage_count INTEGER DEFAULT 0,
  max_usage INTEGER,
  revenue_tracking JSONB DEFAULT '{}',
  analytics_data JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- EVENT BOOKINGS TABLE
-- =============================================

-- Create event bookings table for tracking bookings made through QR codes
CREATE TABLE IF NOT EXISTS public.event_bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID,
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  qr_code_id UUID REFERENCES public.event_qr_codes(id) ON DELETE SET NULL,
  artist_id UUID,
  service_id UUID,
  customer_id UUID,
  revenue_amount DECIMAL(10,2) NOT NULL,
  commission_rate DECIMAL(5,2) DEFAULT 0.00,
  artist_earnings DECIMAL(10,2),
  platform_earnings DECIMAL(10,2),
  payment_method TEXT,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
  booking_source TEXT DEFAULT 'qr_code',
  customer_acquisition_source TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- EVENT ARTIST ASSIGNMENTS TABLE
-- =============================================

-- Create table for managing artist assignments to events
CREATE TABLE IF NOT EXISTS public.event_artist_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  artist_id UUID,
  services UUID[] DEFAULT '{}',
  hourly_rate DECIMAL(10,2),
  commission_rate DECIMAL(5,2) DEFAULT 15.00,
  availability_start TIME,
  availability_end TIME,
  is_primary_artist BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'pending', 'cancelled')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(event_id, artist_id)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_events_dates ON public.events(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_events_status ON public.events(status);
CREATE INDEX IF NOT EXISTS idx_event_qr_codes_event_id ON public.event_qr_codes(event_id);
CREATE INDEX IF NOT EXISTS idx_event_qr_codes_code ON public.event_qr_codes(code);
CREATE INDEX IF NOT EXISTS idx_event_qr_codes_active ON public.event_qr_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_event_bookings_event_id ON public.event_bookings(event_id);
CREATE INDEX IF NOT EXISTS idx_event_bookings_qr_code_id ON public.event_bookings(qr_code_id);
CREATE INDEX IF NOT EXISTS idx_event_bookings_artist_id ON public.event_bookings(artist_id);
CREATE INDEX IF NOT EXISTS idx_event_bookings_created_at ON public.event_bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_event_artist_assignments_event_id ON public.event_artist_assignments(event_id);
CREATE INDEX IF NOT EXISTS idx_event_artist_assignments_artist_id ON public.event_artist_assignments(artist_id);

-- =============================================
-- FUNCTIONS FOR AUTOMATIC CALCULATIONS
-- =============================================

-- Function to calculate artist earnings based on commission rate
CREATE OR REPLACE FUNCTION calculate_artist_earnings(
  revenue_amount DECIMAL(10,2),
  commission_rate DECIMAL(5,2)
) RETURNS DECIMAL(10,2) AS $$
BEGIN
  RETURN revenue_amount * (commission_rate / 100.0);
END;
$$ LANGUAGE plpgsql;

-- Function to calculate platform earnings
CREATE OR REPLACE FUNCTION calculate_platform_earnings(
  revenue_amount DECIMAL(10,2),
  commission_rate DECIMAL(5,2)
) RETURNS DECIMAL(10,2) AS $$
BEGIN
  RETURN revenue_amount * (1 - commission_rate / 100.0);
END;
$$ LANGUAGE plpgsql;

-- Function to update QR code analytics
CREATE OR REPLACE FUNCTION update_qr_analytics(
  qr_code_id UUID,
  booking_revenue DECIMAL(10,2),
  customer_id UUID
) RETURNS VOID AS $$
DECLARE
  current_analytics JSONB;
BEGIN
  -- Get current analytics
  SELECT analytics_data INTO current_analytics 
  FROM public.event_qr_codes 
  WHERE id = qr_code_id;
  
  -- Initialize if null
  IF current_analytics IS NULL THEN
    current_analytics := '{}'::jsonb;
  END IF;
  
  -- Update analytics
  current_analytics := jsonb_set(
    current_analytics,
    '{total_revenue}',
    to_jsonb(COALESCE((current_analytics->>'total_revenue')::decimal, 0) + booking_revenue)
  );
  
  current_analytics := jsonb_set(
    current_analytics,
    '{booking_count}',
    to_jsonb(COALESCE((current_analytics->>'booking_count')::integer, 0) + 1)
  );
  
  current_analytics := jsonb_set(
    current_analytics,
    '{last_booking_at}',
    to_jsonb(NOW())
  );
  
  -- Update the QR code record
  UPDATE public.event_qr_codes 
  SET 
    analytics_data = current_analytics,
    usage_count = usage_count + 1,
    updated_at = NOW()
  WHERE id = qr_code_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger to automatically calculate earnings when event booking is created
CREATE OR REPLACE FUNCTION trigger_calculate_event_booking_earnings()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate artist and platform earnings
  NEW.artist_earnings := calculate_artist_earnings(NEW.revenue_amount, NEW.commission_rate);
  NEW.platform_earnings := calculate_platform_earnings(NEW.revenue_amount, NEW.commission_rate);
  
  -- Update QR code analytics if QR code was used
  IF NEW.qr_code_id IS NOT NULL THEN
    PERFORM update_qr_analytics(NEW.qr_code_id, NEW.revenue_amount, NEW.customer_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS event_booking_earnings_trigger ON public.event_bookings;
CREATE TRIGGER event_booking_earnings_trigger
  BEFORE INSERT OR UPDATE ON public.event_bookings
  FOR EACH ROW
  EXECUTE FUNCTION trigger_calculate_event_booking_earnings();

-- Trigger to update event booking count
CREATE OR REPLACE FUNCTION trigger_update_event_booking_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.events 
    SET current_bookings = current_bookings + 1 
    WHERE id = NEW.event_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.events 
    SET current_bookings = current_bookings - 1 
    WHERE id = OLD.event_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS event_booking_count_trigger ON public.event_bookings;
CREATE TRIGGER event_booking_count_trigger
  AFTER INSERT OR DELETE ON public.event_bookings
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_event_booking_count();

-- =============================================
-- REVENUE ANALYTICS VIEW
-- =============================================

-- Create view for comprehensive revenue analytics
CREATE OR REPLACE VIEW public.event_revenue_analytics AS
SELECT 
  e.id as event_id,
  e.name as event_name,
  e.location as event_location,
  e.start_date,
  e.end_date,
  e.status as event_status,
  COUNT(DISTINCT eb.id) as total_bookings,
  COUNT(DISTINCT eb.customer_id) as unique_customers,
  COUNT(DISTINCT eb.artist_id) as active_artists,
  COALESCE(SUM(eb.revenue_amount), 0) as total_revenue,
  COALESCE(SUM(eb.artist_earnings), 0) as total_artist_earnings,
  COALESCE(SUM(eb.platform_earnings), 0) as total_platform_earnings,
  COALESCE(AVG(eb.revenue_amount), 0) as average_booking_value,
  COUNT(DISTINCT qr.id) as qr_codes_generated,
  COALESCE(SUM(qr.usage_count), 0) as total_qr_scans
FROM public.events e
LEFT JOIN public.event_bookings eb ON e.id = eb.event_id
LEFT JOIN public.event_qr_codes qr ON e.id = qr.event_id
GROUP BY e.id, e.name, e.location, e.start_date, e.end_date, e.status;

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Insert sample event (only if it doesn't already exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM public.events WHERE name = 'Summer Festival 2024') THEN
        INSERT INTO public.events (name, location, description, start_date, end_date, max_capacity) VALUES
        ('Summer Festival 2024', 'Bondi Beach, Sydney', 'Annual summer festival with face painting and hair braiding', '2024-12-20 09:00:00+10', '2024-12-22 18:00:00+10', 500);
    END IF;
END $$;

-- Add table comments
COMMENT ON TABLE public.events IS 'Events and festivals where QR code bookings are available';
COMMENT ON TABLE public.event_qr_codes IS 'Event-specific QR codes with comprehensive tracking';
COMMENT ON TABLE public.event_bookings IS 'Bookings made through event QR codes with revenue tracking';
COMMENT ON TABLE public.event_artist_assignments IS 'Artist assignments to specific events';
COMMENT ON VIEW public.event_revenue_analytics IS 'Comprehensive revenue analytics per event';
