import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { authenticatedFetch } from '@/lib/auth-utils';
import styles from '@/styles/admin/AdvancedCustomerSearch.module.css';

export default function AdvancedCustomerSearch({ onFiltersChange, onCustomersUpdate }) {
  const [filters, setFilters] = useState({
    search: '',
    tags: [],
    segment: 'all',
    lifetimeValue: { min: '', max: '' },
    bookingCount: { min: '', max: '' },
    customerStatus: 'all',
    customerTier: 'all',
    acquisitionSource: 'all',
    registrationDate: { start: '', end: '' },
    lastBookingDate: { start: '', end: '' },
    healthScore: { min: '', max: '' },
    churnRisk: { min: '', max: '' }
  });

  const [availableTags, setAvailableTags] = useState([]);
  const [savedFilters, setSavedFilters] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [loading, setLoading] = useState(false);

  const customerSegments = [
    { value: 'all', label: 'All Customers' },
    { value: 'new', label: 'New Customers (< 30 days)' },
    { value: 'regular', label: 'Regular Customers (3+ bookings)' },
    { value: 'vip', label: 'VIP Customers' },
    { value: 'inactive', label: 'Inactive (> 90 days)' },
    { value: 'high_value', label: 'High Value (> $1000)' },
    { value: 'at_risk', label: 'At Risk of Churning' }
  ];

  const customerStatuses = [
    { value: 'all', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'suspended', label: 'Suspended' },
    { value: 'churned', label: 'Churned' }
  ];

  const customerTiers = [
    { value: 'all', label: 'All Tiers' },
    { value: 'bronze', label: 'Bronze' },
    { value: 'silver', label: 'Silver' },
    { value: 'gold', label: 'Gold' },
    { value: 'platinum', label: 'Platinum' }
  ];

  // Load available tags on component mount
  useEffect(() => {
    fetchAvailableTags();
    loadSavedFilters();
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchFilters) => {
      if (onFiltersChange) {
        onFiltersChange(searchFilters);
      }
      await performSearch(searchFilters);
    }, 300),
    [onFiltersChange]
  );

  useEffect(() => {
    debouncedSearch(filters);
  }, [filters, debouncedSearch]);

  const fetchAvailableTags = async () => {
    try {
      const data = await authenticatedFetch('/api/admin/customer-tags');
      setAvailableTags(data.tags || []);
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  };

  const loadSavedFilters = () => {
    try {
      const saved = localStorage.getItem('customerFilters');
      if (saved) {
        setSavedFilters(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }
  };

  const performSearch = async (searchFilters) => {
    try {
      setLoading(true);
      
      // Convert filters to query parameters
      const queryParams = new URLSearchParams();
      
      Object.entries(searchFilters).forEach(([key, value]) => {
        if (value && value !== 'all' && value !== '') {
          if (typeof value === 'object' && !Array.isArray(value)) {
            // Handle nested objects like lifetimeValue, bookingCount
            Object.entries(value).forEach(([subKey, subValue]) => {
              if (subValue && subValue !== '') {
                queryParams.append(`${key}.${subKey}`, subValue);
              }
            });
          } else if (Array.isArray(value) && value.length > 0) {
            // Handle arrays like tags
            value.forEach(item => queryParams.append(key, item));
          } else {
            queryParams.append(key, value);
          }
        }
      });

      const data = await authenticatedFetch(`/api/admin/customers/search?${queryParams.toString()}`);
      
      if (onCustomersUpdate) {
        onCustomersUpdate(data.customers || [], data.total || 0);
      }
    } catch (error) {
      console.error('Error performing search:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleNestedFilterChange = (parentKey, childKey, value) => {
    setFilters(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [childKey]: value
      }
    }));
  };

  const handleTagToggle = (tagName) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tagName)
        ? prev.tags.filter(t => t !== tagName)
        : [...prev.tags, tagName]
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      tags: [],
      segment: 'all',
      lifetimeValue: { min: '', max: '' },
      bookingCount: { min: '', max: '' },
      customerStatus: 'all',
      customerTier: 'all',
      acquisitionSource: 'all',
      registrationDate: { start: '', end: '' },
      lastBookingDate: { start: '', end: '' },
      healthScore: { min: '', max: '' },
      churnRisk: { min: '', max: '' }
    });
  };

  const saveCurrentFilters = () => {
    const filterName = prompt('Enter a name for this filter set:');
    if (filterName) {
      const newSavedFilter = {
        id: Date.now(),
        name: filterName,
        filters: { ...filters }
      };
      const updatedFilters = [...savedFilters, newSavedFilter];
      setSavedFilters(updatedFilters);
      localStorage.setItem('customerFilters', JSON.stringify(updatedFilters));
    }
  };

  const loadSavedFilter = (savedFilter) => {
    setFilters(savedFilter.filters);
  };

  const hasActiveFilters = () => {
    return filters.search || 
           filters.tags.length > 0 || 
           filters.segment !== 'all' ||
           filters.customerStatus !== 'all' ||
           filters.customerTier !== 'all' ||
           filters.acquisitionSource !== 'all' ||
           filters.lifetimeValue.min || filters.lifetimeValue.max ||
           filters.bookingCount.min || filters.bookingCount.max ||
           filters.healthScore.min || filters.healthScore.max ||
           filters.churnRisk.min || filters.churnRisk.max ||
           filters.registrationDate.start || filters.registrationDate.end ||
           filters.lastBookingDate.start || filters.lastBookingDate.end;
  };

  return (
    <div className={styles.searchContainer}>
      {/* Quick Search Bar */}
      <div className={styles.quickSearch}>
        <div className={styles.searchInputGroup}>
          <input
            type="text"
            placeholder="Search customers by name, email..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className={styles.searchInput}
          />
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={styles.advancedToggle}
          >
            Advanced {showAdvanced ? '▲' : '▼'}
          </button>
        </div>

        <div className={styles.quickFilters}>
          <select
            value={filters.segment}
            onChange={(e) => handleFilterChange('segment', e.target.value)}
            className={styles.quickSelect}
          >
            {customerSegments.map(segment => (
              <option key={segment.value} value={segment.value}>
                {segment.label}
              </option>
            ))}
          </select>

          <select
            value={filters.customerStatus}
            onChange={(e) => handleFilterChange('customerStatus', e.target.value)}
            className={styles.quickSelect}
          >
            {customerStatuses.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          <select
            value={filters.customerTier}
            onChange={(e) => handleFilterChange('customerTier', e.target.value)}
            className={styles.quickSelect}
          >
            {customerTiers.map(tier => (
              <option key={tier.value} value={tier.value}>
                {tier.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className={styles.advancedFilters}>
          {/* Customer Tags */}
          <div className={styles.filterSection}>
            <h4>Customer Tags</h4>
            <div className={styles.tagsList}>
              {availableTags.map(tag => (
                <button
                  key={tag.id}
                  onClick={() => handleTagToggle(tag.name)}
                  className={`${styles.tagButton} ${
                    filters.tags.includes(tag.name) ? styles.tagSelected : ''
                  }`}
                  style={{ backgroundColor: filters.tags.includes(tag.name) ? tag.color : '#f5f5f5' }}
                >
                  {tag.name}
                </button>
              ))}
            </div>
          </div>

          {/* Value Filters */}
          <div className={styles.filterSection}>
            <h4>Customer Value</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min lifetime value"
                value={filters.lifetimeValue.min}
                onChange={(e) => handleNestedFilterChange('lifetimeValue', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max lifetime value"
                value={filters.lifetimeValue.max}
                onChange={(e) => handleNestedFilterChange('lifetimeValue', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          {/* Booking Count */}
          <div className={styles.filterSection}>
            <h4>Booking History</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min bookings"
                value={filters.bookingCount.min}
                onChange={(e) => handleNestedFilterChange('bookingCount', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max bookings"
                value={filters.bookingCount.max}
                onChange={(e) => handleNestedFilterChange('bookingCount', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          {/* Health Score */}
          <div className={styles.filterSection}>
            <h4>Health Score</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min score (0-100)"
                min="0"
                max="100"
                value={filters.healthScore.min}
                onChange={(e) => handleNestedFilterChange('healthScore', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max score (0-100)"
                min="0"
                max="100"
                value={filters.healthScore.max}
                onChange={(e) => handleNestedFilterChange('healthScore', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          {/* Date Filters */}
          <div className={styles.filterSection}>
            <h4>Registration Date</h4>
            <div className={styles.dateInputs}>
              <input
                type="date"
                value={filters.registrationDate.start}
                onChange={(e) => handleNestedFilterChange('registrationDate', 'start', e.target.value)}
                className={styles.dateInput}
              />
              <span>to</span>
              <input
                type="date"
                value={filters.registrationDate.end}
                onChange={(e) => handleNestedFilterChange('registrationDate', 'end', e.target.value)}
                className={styles.dateInput}
              />
            </div>
          </div>

          <div className={styles.filterActions}>
            <button onClick={clearFilters} className={styles.clearButton}>
              Clear All Filters
            </button>
            <button onClick={saveCurrentFilters} className={styles.saveButton}>
              Save Filter Set
            </button>
          </div>
        </div>
      )}

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div className={styles.savedFilters}>
          <h4>Saved Filters:</h4>
          <div className={styles.savedFiltersList}>
            {savedFilters.map(savedFilter => (
              <button
                key={savedFilter.id}
                onClick={() => loadSavedFilter(savedFilter)}
                className={styles.savedFilterButton}
              >
                {savedFilter.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className={styles.activeFilters}>
          <span className={styles.activeFiltersLabel}>Active Filters:</span>
          {filters.search && <span className={styles.activeFilter}>Search: "{filters.search}"</span>}
          {filters.segment !== 'all' && <span className={styles.activeFilter}>Segment: {filters.segment}</span>}
          {filters.tags.length > 0 && <span className={styles.activeFilter}>Tags: {filters.tags.join(', ')}</span>}
          {/* Add more active filter displays as needed */}
        </div>
      )}

      {loading && (
        <div className={styles.searchLoading}>
          Searching customers...
        </div>
      )}
    </div>
  );
}
