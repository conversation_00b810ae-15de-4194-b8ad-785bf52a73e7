/* Enhanced Customer Form Styles */
.customerForm {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Sticky Header */
.stickyHeader {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.headerInfo {
  flex: 1;
}

.customerName {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.unsavedIndicator {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.headerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.viewProfileButton, .newBookingButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
}

.viewProfileButton:hover, .newBookingButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.viewProfileButton:disabled, .newBookingButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Breadcrumb Navigation */
.breadcrumb {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 30px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #666;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.breadcrumbLink {
  color: #6e8efb;
  cursor: pointer;
  transition: color 0.2s ease;
}

.breadcrumbLink:hover {
  color: #5a7cfa;
  text-decoration: underline;
}

.breadcrumbSeparator {
  color: #999;
  margin: 0 4px;
}

.breadcrumbCurrent {
  color: #333;
  font-weight: 500;
}

/* Error and Success Messages */
.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 16px 20px;
  border-radius: 8px;
  margin: 20px 30px;
  border-left: 4px solid #d32f2f;
  font-weight: 500;
}

.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 16px 20px;
  border-radius: 8px;
  margin: 20px 30px;
  border-left: 4px solid #4caf50;
  font-weight: 500;
}

/* Tabbed Interface */
.tabsContainer {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.tabs {
  display: flex;
  padding: 0 30px;
  overflow-x: auto;
}

.tabButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background: none;
  border: none;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
}

.tabButton:hover {
  color: #6e8efb;
  background: rgba(110, 142, 251, 0.05);
}

.activeTab {
  color: #6e8efb !important;
  border-bottom-color: #6e8efb !important;
  background: rgba(110, 142, 251, 0.05) !important;
}

/* Form Content */
.formContent {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  min-height: calc(100vh - 200px);
}

.tabContent {
  padding: 30px;
}

.tabPanel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Section Headers */
.sectionHeader {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.sectionHeader h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.sectionDescription {
  color: #666;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.sectionDivider {
  margin: 30px 0 20px 0;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.sectionDivider h3 {
  font-size: 1.3rem;
  color: #333;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.sectionSubtext {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  font-style: italic;
}

/* Form Layout */
.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 30px;
}

.fullWidth {
  grid-column: 1 / -1;
}

.formGroup {
  margin-bottom: 20px;
}

.fieldLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.required {
  color: #e74c3c;
  margin-left: 4px;
}

.fieldHelp {
  display: block;
  margin-top: 6px;
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
}

/* Form Inputs */
.input, .select, .textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.input:focus, .select:focus, .textarea:focus {
  border-color: #6e8efb;
  outline: none;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.fieldError {
  border-color: #e74c3c !important;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

.errorText {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 4px;
  display: block;
  font-weight: 500;
}

.textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
}

.checkboxWrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.checkbox {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.checkboxLabel {
  flex: 1;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  line-height: 1.4;
}

/* Marketing Section */
.marketingOptions {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.marketingInfo {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #6e8efb;
}

.infoBox {
  margin-bottom: 16px;
}

.infoBox h4 {
  color: #333;
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.infoBox ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.infoBox li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.complianceNote {
  background: rgba(110, 142, 251, 0.1);
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #333;
  line-height: 1.4;
}

/* Preferences Section */
.preferencesContainer {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.preferencesList {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.preferencesList h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.preferenceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.preferenceItem:hover {
  border-color: #6e8efb;
  box-shadow: 0 2px 4px rgba(110, 142, 251, 0.1);
}

.preferenceContent {
  flex: 1;
  color: #333;
  font-weight: 500;
}

.removeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(231, 76, 60, 0.1);
  border: none;
  border-radius: 6px;
  color: #e74c3c;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeButton:hover {
  background: rgba(231, 76, 60, 0.2);
  transform: scale(1.05);
}

.addPreferenceSection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.addPreferenceSection h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.addPreference {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-bottom: 20px;
}

.preferenceInputs {
  display: flex;
  gap: 16px;
  flex: 1;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.addButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(110, 142, 251, 0.3);
}

.addButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.preferencesHelp {
  background: rgba(110, 142, 251, 0.05);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #6e8efb;
}

.preferencesHelp h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.preferencesHelp ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.preferencesHelp li {
  margin-bottom: 6px;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .headerContent {
    padding: 16px 20px;
    gap: 20px;
  }

  .tabContent {
    padding: 20px;
  }

  .breadcrumb {
    padding: 12px 20px;
  }
}

@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .headerActions {
    flex-wrap: wrap;
    width: 100%;
    justify-content: flex-start;
  }

  .viewProfileButton, .newBookingButton, .cancelButton, .saveButton {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .tabs {
    padding: 0 20px;
    overflow-x: auto;
  }

  .tabButton {
    padding: 12px 16px;
    font-size: 0.9rem;
  }

  .tabContent {
    padding: 16px;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .sectionHeader h2 {
    font-size: 1.5rem;
  }

  .addPreference {
    flex-direction: column;
    gap: 12px;
  }

  .preferenceInputs {
    flex-direction: column;
    gap: 12px;
  }

  .addButton {
    align-self: flex-start;
  }

  .breadcrumb {
    padding: 12px 16px;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .customerName {
    font-size: 1.4rem;
  }

  .headerActions {
    flex-direction: column;
    gap: 8px;
  }

  .viewProfileButton, .newBookingButton, .cancelButton, .saveButton {
    width: 100%;
    min-width: auto;
  }

  .tabs {
    padding: 0 16px;
  }

  .tabButton {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .tabContent {
    padding: 12px;
  }

  .sectionHeader {
    margin-bottom: 20px;
    padding-bottom: 16px;
  }

  .sectionHeader h2 {
    font-size: 1.3rem;
  }

  .formGrid {
    gap: 16px;
  }

  .input, .select, .textarea {
    padding: 10px 12px;
  }

  .breadcrumb {
    padding: 10px 12px;
    flex-wrap: wrap;
  }
}
