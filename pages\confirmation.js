import { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '@/styles/Confirmation.module.css';
import Layout from '@/components/Layout';
import AnimatedSection from '@/components/AnimatedSection';

export default function Confirmation() {
  const router = useRouter();
  const { orderId, status } = router.query;
  const [orderDetails, setOrderDetails] = useState(null);
  
  useEffect(() => {
    // In a real application, you would fetch the order details from your backend
    // For now, we'll just use the query parameters
    if (orderId && status) {
      setOrderDetails({
        id: orderId,
        status: status,
        date: new Date().toLocaleDateString(),
        time: new Date().toLocaleTimeString()
      });
    }
  }, [orderId, status]);
  
  if (!orderDetails) {
    return (
      <Layout>
        <Head>
          <title>Order Confirmation | OceanSoulSparkles</title>
          <meta name="description" content="Thank you for your order from OceanSoulSparkles." />
        </Head>
        
        <main className={styles.main}>
          <section className={styles.confirmationSection}>
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <p>Loading order details...</p>
            </div>
          </section>
        </main>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Head>
        <title>Order Confirmation | OceanSoulSparkles</title>
        <meta name="description" content="Thank you for your order from OceanSoulSparkles." />
      </Head>
      
      <main className={styles.main}>
        <section className={styles.confirmationSection}>
          <AnimatedSection animation="fade-up">
            <div className={styles.confirmationContainer}>
              <div className={styles.confirmationHeader}>
                <div className={styles.checkmarkCircle}>
                  <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="white"/>
                  </svg>
                </div>
                <h1 className={styles.pageTitle}>Order Confirmed!</h1>
                <p className={styles.thankYouMessage}>Thank you for your purchase from OceanSoulSparkles</p>
              </div>
              
              <div className={styles.orderDetails}>
                <div className={styles.orderInfo}>
                  <div className={styles.orderInfoItem}>
                    <span className={styles.orderInfoLabel}>Order Number:</span>
                    <span className={styles.orderInfoValue}>{orderDetails.id}</span>
                  </div>
                  <div className={styles.orderInfoItem}>
                    <span className={styles.orderInfoLabel}>Order Status:</span>
                    <span className={styles.orderInfoValue}>{orderDetails.status}</span>
                  </div>
                  <div className={styles.orderInfoItem}>
                    <span className={styles.orderInfoLabel}>Order Date:</span>
                    <span className={styles.orderInfoValue}>{orderDetails.date}</span>
                  </div>
                  <div className={styles.orderInfoItem}>
                    <span className={styles.orderInfoLabel}>Order Time:</span>
                    <span className={styles.orderInfoValue}>{orderDetails.time}</span>
                  </div>
                </div>
                
                <div className={styles.orderMessage}>
                  <p>We've received your order and are processing it now. You will receive an email confirmation shortly.</p>
                  <p>If you have any questions about your order, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                </div>
              </div>
              
              <div className={styles.nextSteps}>
                <h2 className={styles.nextStepsTitle}>What's Next?</h2>
                <div className={styles.stepsList}>
                  <div className={styles.step}>
                    <div className={styles.stepIcon}>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill="#1A73E8"/>
                      </svg>
                    </div>
                    <div className={styles.stepContent}>
                      <h3 className={styles.stepTitle}>Check Your Email</h3>
                      <p className={styles.stepDescription}>We've sent a confirmation email with your order details.</p>
                    </div>
                  </div>
                  
                  <div className={styles.step}>
                    <div className={styles.stepIcon}>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 8H17V4H3C1.9 4 1 4.9 1 6V17H3C3 18.66 4.34 20 6 20C7.66 20 9 18.66 9 17H15C15 18.66 16.34 20 18 20C19.66 20 21 18.66 21 17H23V12L20 8ZM6 18.5C5.17 18.5 4.5 17.83 4.5 17C4.5 16.17 5.17 15.5 6 15.5C6.83 15.5 7.5 16.17 7.5 17C7.5 17.83 6.83 18.5 6 18.5ZM19.5 9.5L21.46 12H17V9.5H19.5ZM18 18.5C17.17 18.5 16.5 17.83 16.5 17C16.5 16.17 17.17 15.5 18 15.5C18.83 15.5 19.5 16.17 19.5 17C19.5 17.83 18.83 18.5 18 18.5Z" fill="#1A73E8"/>
                      </svg>
                    </div>
                    <div className={styles.stepContent}>
                      <h3 className={styles.stepTitle}>Track Your Order</h3>
                      <p className={styles.stepDescription}>We'll send you shipping updates as your order is processed.</p>
                    </div>
                  </div>
                  
                  <div className={styles.step}>
                    <div className={styles.stepIcon}>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z" fill="#1A73E8"/>
                      </svg>
                    </div>
                    <div className={styles.stepContent}>
                      <h3 className={styles.stepTitle}>Enjoy Your Products</h3>
                      <p className={styles.stepDescription}>We hope you love your OceanSoulSparkles products!</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className={styles.actions}>
                <Link href="/shop" className={styles.primaryButton}>
                  Continue Shopping
                </Link>
                <Link href="/contact" className={styles.secondaryButton}>
                  Contact Us
                </Link>
              </div>
              
              <div className={styles.paymentInfo}>
                <div className={styles.paymentLogos}>
                  <img src="/images/logos/paypal-logo.svg" alt="PayPal" width={100} height={25} />
                  <img src="/images/logos/square-logo.svg" alt="Square" width={100} height={25} />
                </div>
                <p className={styles.securePaymentNote}>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
                  </svg>
                  <span>Secure payment processing</span>
                </p>
              </div>
            </div>
          </AnimatedSection>
        </section>
      </main>
    </Layout>
  );
}
