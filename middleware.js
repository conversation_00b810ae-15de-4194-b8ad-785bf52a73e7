// Enhanced middleware ensures proper CORS headers and authentication are applied
// to all admin API requests with consistent behavior across environments

import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * API middleware configuration
 * Apply to both admin routes and auth-protected routes
 */
export const config = {
  matcher: [
    '/api/admin/:path*',
    '/api/auth/:path*'
  ],
};

/**
 * Middleware function that runs before API requests
 * Adds necessary CORS headers and ensures authentication can work properly
 */
export default async function middleware(req) {
  // Initialize Supabase client with service key for admin operations
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  );

  // Set proper response headers to prevent CORS issues
  const response = NextResponse.next();

  // Get environment variables
  const origin = req.headers.get('origin') || '*';
  const isProd = process.env.NODE_ENV === 'production';
  const allowCrossOrigin = process.env.NEXT_PUBLIC_ALLOW_CROSS_ORIGIN === 'true';
  const devBypass = process.env.ENABLE_AUTH_BYPASS === 'true'; // Single declaration

  // Development auth bypass flag

  // Set CORS headers - allow cross-origin in development if enabled
  if (isProd) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else if (allowCrossOrigin) {
    // In development with cross-origin allowed, accept any origin
    response.headers.set('Access-Control-Allow-Origin', '*');
  } else {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Client-Info, X-Auth-Token, X-OneSignal-Client');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400');

  // Add cache control headers to prevent caching of authenticated responses
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': isProd ? origin : (allowCrossOrigin ? '*' : origin),
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Client-Info, X-Auth-Token, X-OneSignal-Client',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Max-Age': '86400',
      },
    });
  }

  // For admin API routes, enforce authentication
  const isAdminRoute = req.nextUrl.pathname.startsWith('/api/admin/');

  if (isAdminRoute && req.method !== 'OPTIONS') {
    // Use existing devBypass variable from environment config
    if (!devBypass) { // Now referencing the variable declared earlier
      const authHeader = req.headers.get('Authorization');

      // Validate JWT format
      if (!authHeader?.startsWith('Bearer ')) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...response.headers
          }
        });
      }

      // Verify the JWT token with standard supabase client
      try {
        const token = authHeader.split(' ')[1];
        const { data: { user }, error } = await supabase.auth.getUser(token);

        if (error || !user) throw error;

        // Additional role check for admin routes
        const { data: roleData } = await supabase
          .from('user_roles')
          .select('role')
          .eq('id', user.id)
          .single();

        // Updated role check for new 5-tier role system
        const allowedRoles = ['dev', 'admin', 'artist', 'braider'];
        if (!allowedRoles.includes(roleData?.role)) {
          console.warn(`Access denied for role: ${roleData?.role}. Allowed roles: ${allowedRoles.join(', ')}`);
          return new Response(JSON.stringify({ error: 'Insufficient permissions' }), {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
              ...response.headers
            }
          });
        }
      } catch (error) {
        console.error('JWT verification failed:', error.message);
        return new Response(JSON.stringify({
          error: 'Invalid token',
          details: devBypass ? 'Bypass enabled but token validation failed' : error.message
        }), {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
            ...response.headers
          }
        });
      }
    } else {
      console.warn('Development auth bypass enabled - skipping admin route authentication');
    }
  }

  // Pass the request through to the API handlers
  return response;
}
