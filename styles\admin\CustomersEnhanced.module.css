.customersEnhanced {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.titleSection h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.headerActions {
  display: flex;
  gap: 15px;
}

.switchViewButton {
  padding: 10px 20px;
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.switchViewButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.statIcon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.statContent h3 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statValue {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #d32f2f;
}

.customerListSection {
  margin-bottom: 40px;
}

.featuresSection {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.featuresSection h2 {
  margin: 0 0 25px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.featureCard {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  border-left: 4px solid #6e8efb;
  transition: all 0.2s ease;
}

.featureCard:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.featureCard h3 {
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.featureCard p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* Loading states */
.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 1.1rem;
}

.loadingSpinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .customersEnhanced {
    padding: 15px;
  }
  
  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .headerActions {
    justify-content: center;
  }
  
  .titleSection h1 {
    font-size: 1.5rem;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .statCard {
    padding: 15px;
  }
  
  .statIcon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
  
  .statValue {
    font-size: 1.5rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
  }
  
  .featuresSection {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .customersEnhanced {
    padding: 10px;
  }
  
  .statCard {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .featureCard {
    padding: 15px;
  }
}
