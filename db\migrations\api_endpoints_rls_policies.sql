-- =============================================
-- RLS POLICIES FOR NEW API ENDPOINTS
-- =============================================
-- This migration adds any missing RLS policies needed for the new API endpoints
-- Run this in Supabase SQL Editor after applying the main booking_system_enhancements.sql

-- Enable RLS on tables if not already enabled
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.booking_communications ENABLE ROW LEVEL SECURITY;

-- =============================================
-- BOOKINGS TABLE POLICIES
-- =============================================

-- Policy for admin/staff to read all bookings
CREATE POLICY IF NOT EXISTS "Admin can read all bookings" ON public.bookings
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to update all bookings
CREATE POLICY IF NOT EXISTS "Admin can update all bookings" ON public.bookings
  FOR UPDATE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to insert bookings
CREATE POLICY IF NOT EXISTS "Admin can insert bookings" ON public.bookings
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to delete bookings
CREATE POLICY IF NOT EXISTS "Admin can delete bookings" ON public.bookings
  FOR DELETE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- =============================================
-- CUSTOMERS TABLE POLICIES
-- =============================================

-- Policy for admin/staff to read all customers
CREATE POLICY IF NOT EXISTS "Admin can read all customers" ON public.customers
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to update customers
CREATE POLICY IF NOT EXISTS "Admin can update customers" ON public.customers
  FOR UPDATE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to insert customers
CREATE POLICY IF NOT EXISTS "Admin can insert customers" ON public.customers
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- =============================================
-- BOOKING COMMUNICATIONS POLICIES
-- =============================================

-- Policy for admin/staff to read all communications
CREATE POLICY IF NOT EXISTS "Admin can read all communications" ON public.booking_communications
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to insert communications
CREATE POLICY IF NOT EXISTS "Admin can insert communications" ON public.booking_communications
  FOR INSERT WITH CHECK (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for admin/staff to update communications
CREATE POLICY IF NOT EXISTS "Admin can update communications" ON public.booking_communications
  FOR UPDATE USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- =============================================
-- BOOKING STATUS HISTORY POLICIES
-- =============================================

-- Enable RLS on booking_status_history if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'booking_status_history') THEN
    ALTER TABLE public.booking_status_history ENABLE ROW LEVEL SECURITY;
    
    -- Policy for admin/staff to read status history
    CREATE POLICY IF NOT EXISTS "Admin can read status history" ON public.booking_status_history
      FOR SELECT USING (
        auth.role() IN ('service_role', 'supabase_admin') OR
        EXISTS (
          SELECT 1 FROM auth.users
          WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
        )
      );

    -- Policy for admin/staff to insert status history
    CREATE POLICY IF NOT EXISTS "Admin can insert status history" ON public.booking_status_history
      FOR INSERT WITH CHECK (
        auth.role() IN ('service_role', 'supabase_admin') OR
        EXISTS (
          SELECT 1 FROM auth.users
          WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
        )
      );
  END IF;
END $$;

-- =============================================
-- SERVICES TABLE POLICIES (if needed)
-- =============================================

-- Enable RLS on services table
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;

-- Policy for admin/staff to read all services
CREATE POLICY IF NOT EXISTS "Admin can read all services" ON public.services
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policy for public to read services (for booking forms)
CREATE POLICY IF NOT EXISTS "Public can read services" ON public.services
  FOR SELECT USING (true);

-- =============================================
-- USER PROFILES VIEW (if needed for status history)
-- =============================================

-- Create user_profiles view if it doesn't exist (for BookingStatusHistory component)
CREATE OR REPLACE VIEW public.user_profiles AS
SELECT 
  id,
  email,
  raw_user_meta_data->>'display_name' as display_name,
  raw_user_meta_data->>'role' as role,
  created_at,
  updated_at
FROM auth.users;

-- Grant access to the view
GRANT SELECT ON public.user_profiles TO authenticated;
GRANT SELECT ON public.user_profiles TO anon;

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Check if all required tables exist
DO $$
BEGIN
  -- Check bookings table
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'bookings') THEN
    RAISE NOTICE 'WARNING: bookings table does not exist';
  ELSE
    RAISE NOTICE 'OK: bookings table exists';
  END IF;

  -- Check customers table
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'customers') THEN
    RAISE NOTICE 'WARNING: customers table does not exist';
  ELSE
    RAISE NOTICE 'OK: customers table exists';
  END IF;

  -- Check booking_communications table
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'booking_communications') THEN
    RAISE NOTICE 'WARNING: booking_communications table does not exist - run booking_system_enhancements.sql first';
  ELSE
    RAISE NOTICE 'OK: booking_communications table exists';
  END IF;

  -- Check services table
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'services') THEN
    RAISE NOTICE 'WARNING: services table does not exist';
  ELSE
    RAISE NOTICE 'OK: services table exists';
  END IF;
END $$;

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Ensure we have proper indexes for the API queries
CREATE INDEX IF NOT EXISTS bookings_customer_id_start_time_idx ON public.bookings(customer_id, start_time DESC);
CREATE INDEX IF NOT EXISTS bookings_status_idx ON public.bookings(status);
CREATE INDEX IF NOT EXISTS bookings_start_time_idx ON public.bookings(start_time);
CREATE INDEX IF NOT EXISTS customers_email_idx ON public.customers(email);

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

DO $$
BEGIN
  RAISE NOTICE '✅ RLS policies for API endpoints have been created successfully!';
  RAISE NOTICE '📝 Next steps:';
  RAISE NOTICE '1. Test the API endpoints with proper authentication';
  RAISE NOTICE '2. Verify the enhanced booking system works in the admin interface';
  RAISE NOTICE '3. Check that all 404 errors are resolved';
END $$;
