import { useState, useEffect } from 'react';
import { authenticatedFetch } from '@/lib/auth-utils';
import styles from '@/styles/admin/CustomerTagManager.module.css';

/**
 * Component for managing customer tags
 *
 * @param {Object} props - Component props
 * @param {string} props.customerId - Customer ID
 * @returns {JSX.Element}
 */
export default function CustomerTagManager({ customerId, onTagsUpdate }) {
  const [availableTags, setAvailableTags] = useState([]);
  const [customerTags, setCustomerTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Fetch available tags and customer's assigned tags
  useEffect(() => {
    const fetchTags = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all available tags
        const tagsData = await authenticatedFetch('/api/admin/customer-tags');
        setAvailableTags(tagsData.tags || []);

        // Get customer's assigned tags if customerId is provided
        if (customerId) {
          const customerTagsData = await authenticatedFetch(`/api/admin/customers/${customerId}/tags`);
          setCustomerTags(customerTagsData.tags || []);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
        setError('Failed to load tags');
      } finally {
        setLoading(false);
      }
    };

    fetchTags();
  }, [customerId]);

  // Add tag to customer
  const addTag = async (tagId) => {
    if (!customerId) return;

    try {
      setLoading(true);
      setError(null);
      setSuccessMessage(null);

      // Check if tag is already assigned
      if (customerTags.some(tag => tag.id === tagId)) {
        setError('Tag is already assigned to this customer');
        return;
      }

      // Add tag assignment using authenticated API
      await authenticatedFetch(`/api/admin/customers/${customerId}/tags`, {
        method: 'POST',
        body: JSON.stringify({ tagId })
      });

      // Refresh customer tags
      const customerTagsData = await authenticatedFetch(`/api/admin/customers/${customerId}/tags`);
      setCustomerTags(customerTagsData.tags || []);

      setSuccessMessage('Tag added successfully');
      if (onTagsUpdate) onTagsUpdate();
    } catch (error) {
      console.error('Error adding tag:', error);
      setError(error.message || 'Failed to add tag');
    } finally {
      setLoading(false);
    }
  };

  // Remove tag from customer
  const removeTag = async (tagId) => {
    if (!customerId) return;

    try {
      setLoading(true);
      setError(null);
      setSuccessMessage(null);

      // Remove tag assignment using authenticated API
      await authenticatedFetch(`/api/admin/customers/${customerId}/tags/${tagId}`, {
        method: 'DELETE'
      });

      // Refresh customer tags
      const customerTagsData = await authenticatedFetch(`/api/admin/customers/${customerId}/tags`);
      setCustomerTags(customerTagsData.tags || []);

      setSuccessMessage('Tag removed successfully');
      if (onTagsUpdate) onTagsUpdate();
    } catch (error) {
      console.error('Error removing tag:', error);
      setError(error.message || 'Failed to remove tag');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.tagManager}>
      <h3 className={styles.title}>Customer Tags</h3>

      {error && (
        <div className={styles.error}>{error}</div>
      )}

      {successMessage && (
        <div className={styles.success}>{successMessage}</div>
      )}

      {loading && !customerTags.length ? (
        <div className={styles.loading}>Loading tags...</div>
      ) : (
        <>
          <div className={styles.currentTags}>
            {customerTags.length === 0 ? (
              <div className={styles.emptyState}>No tags assigned</div>
            ) : (
              <div className={styles.tagList}>
                {customerTags.map(tag => (
                  <div
                    key={tag.id}
                    className={styles.tag}
                    style={{ backgroundColor: tag.color }}
                  >
                    <span className={styles.tagName}>{tag.name}</span>
                    <button
                      className={styles.removeButton}
                      onClick={() => removeTag(tag.id)}
                      disabled={loading}
                      aria-label={`Remove ${tag.name} tag`}
                    >
                      &times;
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className={styles.addTagSection}>
            <h4 className={styles.subtitle}>Add Tag</h4>
            <div className={styles.availableTags}>
              {availableTags
                .filter(tag => !customerTags.some(ct => ct.id === tag.id))
                .map(tag => (
                  <button
                    key={tag.id}
                    className={styles.addTagButton}
                    style={{
                      backgroundColor: 'transparent',
                      color: tag.color,
                      borderColor: tag.color
                    }}
                    onClick={() => addTag(tag.id)}
                    disabled={loading}
                  >
                    <span className={styles.plusIcon}>+</span>
                    {tag.name}
                  </button>
                ))
              }
            </div>
          </div>
        </>
      )}
    </div>
  );
}
