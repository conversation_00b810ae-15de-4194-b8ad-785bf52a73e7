import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

/**
 * Customer Analytics API Endpoint
 * Provides detailed analytics and insights for individual customers
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer analytics API: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request using the auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}`);

  const { id: customerId } = req.query;

  if (!customerId) {
    return res.status(400).json({
      error: 'Customer ID is required',
      requestId
    });
  }

  try {
    console.log(`[${requestId}] Fetching analytics for customer: ${customerId}`);

    // Get customer analytics summary
    const { data: customer, error: customerError } = await supabaseAdmin
      .from('customer_analytics_summary')
      .select('*')
      .eq('id', customerId)
      .single();

    if (customerError) {
      console.error(`[${requestId}] Error fetching customer analytics:`, customerError);
      if (customerError.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Customer not found',
          requestId
        });
      }
      throw customerError;
    }

    // Get booking history for trends (last 12 months)
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select('start_time, status, actual_revenue, estimated_revenue, services(name)')
      .eq('customer_id', customerId)
      .gte('start_time', twelveMonthsAgo.toISOString())
      .order('start_time', { ascending: false });

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings:`, bookingsError);
      // Don't throw error, just log it and continue with empty bookings
    }

    // Get communication history (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const { data: communications, error: commError } = await supabaseAdmin
      .from('customer_communications')
      .select('communication_type, direction, created_at, status')
      .eq('customer_id', customerId)
      .gte('created_at', sixMonthsAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(50);

    if (commError) {
      console.error(`[${requestId}] Error fetching communications:`, commError);
      // Don't throw error, just log it and continue with empty communications
    }

    // Calculate analytics
    const analytics = {
      customer,
      bookingTrends: calculateBookingTrends(bookings || []),
      servicePreferences: calculateServicePreferences(bookings || []),
      communicationStats: calculateCommunicationStats(communications || []),
      revenueAnalysis: calculateRevenueAnalysis(bookings || []),
      engagementScore: calculateEngagementScore(customer, bookings || [], communications || []),
      insights: generateCustomerInsights(customer, bookings || [], communications || [])
    };

    console.log(`[${requestId}] Successfully calculated analytics for customer`);

    return res.status(200).json({
      ...analytics,
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Customer analytics error:`, error);
    
    return res.status(500).json({
      error: 'Failed to fetch customer analytics',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while fetching analytics',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Calculate booking trends by month
 */
function calculateBookingTrends(bookings) {
  const monthlyBookings = {};
  
  bookings.forEach(booking => {
    const month = new Date(booking.start_time).toISOString().slice(0, 7); // YYYY-MM
    if (!monthlyBookings[month]) {
      monthlyBookings[month] = { count: 0, revenue: 0 };
    }
    monthlyBookings[month].count++;
    monthlyBookings[month].revenue += booking.actual_revenue || booking.estimated_revenue || 0;
  });

  return Object.entries(monthlyBookings)
    .map(([month, data]) => ({ month, ...data }))
    .sort((a, b) => a.month.localeCompare(b.month));
}

/**
 * Calculate service preferences
 */
function calculateServicePreferences(bookings) {
  const serviceCount = {};
  
  bookings.forEach(booking => {
    const serviceName = booking.services?.name || 'Unknown Service';
    serviceCount[serviceName] = (serviceCount[serviceName] || 0) + 1;
  });

  return Object.entries(serviceCount)
    .map(([service, count]) => ({ service, count }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Calculate communication statistics
 */
function calculateCommunicationStats(communications) {
  const stats = {
    total: communications.length,
    byType: {},
    byDirection: { inbound: 0, outbound: 0 },
    responseRate: 0
  };

  let outboundCount = 0;
  let inboundCount = 0;

  communications.forEach(comm => {
    // Count by type
    stats.byType[comm.communication_type] = (stats.byType[comm.communication_type] || 0) + 1;
    
    // Count by direction
    if (comm.direction === 'outbound') {
      outboundCount++;
    } else if (comm.direction === 'inbound') {
      inboundCount++;
    }
  });

  stats.byDirection.outbound = outboundCount;
  stats.byDirection.inbound = inboundCount;

  // Calculate response rate (inbound / outbound)
  if (outboundCount > 0) {
    stats.responseRate = (inboundCount / outboundCount) * 100;
  }

  return stats;
}

/**
 * Calculate revenue analysis
 */
function calculateRevenueAnalysis(bookings) {
  const completedBookings = bookings.filter(b => b.status === 'completed');
  const revenues = completedBookings.map(b => b.actual_revenue || b.estimated_revenue || 0);
  
  if (revenues.length === 0) {
    return {
      totalRevenue: 0,
      averageBookingValue: 0,
      highestBookingValue: 0,
      lowestBookingValue: 0,
      revenueGrowth: 0
    };
  }

  const totalRevenue = revenues.reduce((sum, rev) => sum + rev, 0);
  const averageBookingValue = totalRevenue / revenues.length;
  const highestBookingValue = Math.max(...revenues);
  const lowestBookingValue = Math.min(...revenues);

  // Calculate revenue growth (last 3 months vs previous 3 months)
  const now = new Date();
  const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
  const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());

  const recentRevenue = completedBookings
    .filter(b => new Date(b.start_time) >= threeMonthsAgo)
    .reduce((sum, b) => sum + (b.actual_revenue || b.estimated_revenue || 0), 0);

  const previousRevenue = completedBookings
    .filter(b => {
      const bookingDate = new Date(b.start_time);
      return bookingDate >= sixMonthsAgo && bookingDate < threeMonthsAgo;
    })
    .reduce((sum, b) => sum + (b.actual_revenue || b.estimated_revenue || 0), 0);

  const revenueGrowth = previousRevenue > 0 ? ((recentRevenue - previousRevenue) / previousRevenue) * 100 : 0;

  return {
    totalRevenue,
    averageBookingValue,
    highestBookingValue,
    lowestBookingValue,
    revenueGrowth
  };
}

/**
 * Calculate engagement score
 */
function calculateEngagementScore(customer, bookings, communications) {
  let score = 50; // Base score

  // Booking frequency (0-30 points)
  const bookingCount = customer.total_bookings || 0;
  if (bookingCount >= 10) score += 30;
  else if (bookingCount >= 5) score += 20;
  else if (bookingCount >= 2) score += 10;

  // Recency (0-25 points)
  const daysSinceLastBooking = customer.days_since_last_booking;
  if (daysSinceLastBooking === null) score -= 25;
  else if (daysSinceLastBooking <= 30) score += 25;
  else if (daysSinceLastBooking <= 60) score += 15;
  else if (daysSinceLastBooking <= 90) score += 5;
  else score -= 15;

  // Value (0-25 points)
  const lifetimeValue = customer.lifetime_value || 0;
  if (lifetimeValue >= 1000) score += 25;
  else if (lifetimeValue >= 500) score += 15;
  else if (lifetimeValue >= 200) score += 10;
  else if (lifetimeValue >= 100) score += 5;

  // Communication responsiveness (0-20 points)
  const commStats = calculateCommunicationStats(communications);
  const responseRate = commStats.responseRate / 100;
  score += Math.floor(responseRate * 20);

  return Math.max(0, Math.min(100, score));
}

/**
 * Generate customer insights
 */
function generateCustomerInsights(customer, bookings, communications) {
  const insights = [];

  // Health score insights
  if (customer.customer_health_score < 30) {
    insights.push({
      type: 'warning',
      title: 'Low Health Score',
      message: 'This customer may need attention to prevent churn.',
      action: 'Consider reaching out with a special offer or check-in call.'
    });
  }

  // Churn risk insights
  if (customer.churn_risk_score > 0.7) {
    insights.push({
      type: 'danger',
      title: 'High Churn Risk',
      message: 'This customer is at high risk of churning.',
      action: 'Immediate intervention recommended - personal outreach or retention offer.'
    });
  }

  // Value insights
  if (customer.lifetime_value > 1000 && customer.days_since_last_booking > 60) {
    insights.push({
      type: 'info',
      title: 'High-Value Customer Inactive',
      message: 'A valuable customer hasn\'t booked recently.',
      action: 'Reach out with VIP treatment or exclusive offers.'
    });
  }

  // Booking pattern insights
  const recentBookings = bookings.filter(b => {
    const bookingDate = new Date(b.start_time);
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    return bookingDate >= threeMonthsAgo;
  });

  if (recentBookings.length === 0 && customer.total_bookings > 0) {
    insights.push({
      type: 'warning',
      title: 'No Recent Bookings',
      message: 'Customer hasn\'t booked in the last 3 months.',
      action: 'Send a re-engagement campaign or special offer.'
    });
  }

  return insights;
}
