#!/usr/bin/env node

/**
 * Production Onboarding Fix Script
 * Fixes critical issues in the Artist/Braider onboarding system
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'
import fs from 'fs'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function fixProductionOnboarding() {
  console.log('🔧 Fixing Production Artist/Braider Onboarding System\n')

  let allFixed = true
  const results = []

  // Fix 1: Create admin_settings table
  console.log('1. Creating admin_settings table...')
  try {
    const adminSettingsSQL = fs.readFileSync('supabase/migrations/create_admin_settings_table.sql', 'utf8')
    
    const { error: adminSettingsError } = await supabase.rpc('exec_sql', {
      sql: adminSettingsSQL
    })

    if (adminSettingsError && !adminSettingsError.message.includes('already exists')) {
      console.error('❌ Failed to create admin_settings table:', adminSettingsError.message)
      allFixed = false
      results.push({ fix: 'admin_settings_table', success: false, error: adminSettingsError.message })
    } else {
      console.log('✅ admin_settings table created successfully')
      results.push({ fix: 'admin_settings_table', success: true })
    }
  } catch (error) {
    console.error('❌ Error creating admin_settings table:', error.message)
    allFixed = false
    results.push({ fix: 'admin_settings_table', success: false, error: error.message })
  }

  // Fix 2: Create/fix token validation functions
  console.log('\n2. Creating token validation functions...')
  try {
    const tokenValidationSQL = fs.readFileSync('supabase/migrations/fix_token_validation_function.sql', 'utf8')
    
    const { error: tokenError } = await supabase.rpc('exec_sql', {
      sql: tokenValidationSQL
    })

    if (tokenError) {
      console.error('❌ Failed to create token validation functions:', tokenError.message)
      allFixed = false
      results.push({ fix: 'token_validation_functions', success: false, error: tokenError.message })
    } else {
      console.log('✅ Token validation functions created successfully')
      results.push({ fix: 'token_validation_functions', success: true })
    }
  } catch (error) {
    console.error('❌ Error creating token validation functions:', error.message)
    allFixed = false
    results.push({ fix: 'token_validation_functions', success: false, error: error.message })
  }

  // Fix 3: Test token validation function
  console.log('\n3. Testing token validation function...')
  try {
    const { data: testResult, error: testError } = await supabase
      .rpc('validate_application_token', { token_value: 'test-token-123' })

    if (testError) {
      console.error('❌ Token validation function test failed:', testError.message)
      allFixed = false
      results.push({ fix: 'token_validation_test', success: false, error: testError.message })
    } else {
      console.log('✅ Token validation function is working')
      results.push({ fix: 'token_validation_test', success: true })
    }
  } catch (error) {
    console.error('❌ Error testing token validation function:', error.message)
    allFixed = false
    results.push({ fix: 'token_validation_test', success: false, error: error.message })
  }

  // Fix 4: Test email configuration
  console.log('\n4. Testing email configuration...')
  try {
    const { getEmailConfig } = await import('../lib/email-service-manager.js')
    const emailConfig = await getEmailConfig()
    
    const hasGmail = !!(emailConfig.gmail.user && emailConfig.gmail.password)
    const hasWorkspace = !!(emailConfig.workspace.user && emailConfig.workspace.password)
    const hasOneSignal = !!(emailConfig.onesignal.appId && emailConfig.onesignal.restApiKey)
    
    console.log(`   Gmail SMTP: ${hasGmail ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   Workspace SMTP: ${hasWorkspace ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   OneSignal: ${hasOneSignal ? '✅ Configured' : '❌ Not configured'}`)
    
    if (hasGmail || hasWorkspace || hasOneSignal) {
      console.log('✅ At least one email service is configured')
      results.push({ fix: 'email_configuration', success: true })
    } else {
      console.log('⚠️  No email services are configured')
      results.push({ fix: 'email_configuration', success: false, error: 'No email services configured' })
    }
  } catch (error) {
    console.error('❌ Error testing email configuration:', error.message)
    results.push({ fix: 'email_configuration', success: false, error: error.message })
  }

  // Fix 5: Test token generation
  console.log('\n5. Testing token generation...')
  try {
    const { data: generatedToken, error: generateError } = await supabase
      .rpc('generate_application_token')

    if (generateError) {
      console.error('❌ Token generation failed:', generateError.message)
      allFixed = false
      results.push({ fix: 'token_generation', success: false, error: generateError.message })
    } else if (!generatedToken) {
      console.error('❌ Token generation returned null')
      allFixed = false
      results.push({ fix: 'token_generation', success: false, error: 'Token generation returned null' })
    } else {
      console.log(`✅ Token generation working: ${generatedToken.substring(0, 8)}...`)
      results.push({ fix: 'token_generation', success: true })
    }
  } catch (error) {
    console.error('❌ Error testing token generation:', error.message)
    allFixed = false
    results.push({ fix: 'token_generation', success: false, error: error.message })
  }

  // Fix 6: Verify application tables exist
  console.log('\n6. Verifying application tables...')
  try {
    const { data: applications, error: appError } = await supabase
      .from('artist_braider_applications')
      .select('id')
      .limit(1)

    if (appError && appError.code !== 'PGRST116') {
      console.error('❌ artist_braider_applications table issue:', appError.message)
      allFixed = false
      results.push({ fix: 'application_tables', success: false, error: appError.message })
    } else {
      console.log('✅ artist_braider_applications table exists')
      
      const { data: tokens, error: tokenError } = await supabase
        .from('application_tokens')
        .select('id')
        .limit(1)

      if (tokenError && tokenError.code !== 'PGRST116') {
        console.error('❌ application_tokens table issue:', tokenError.message)
        allFixed = false
        results.push({ fix: 'application_tables', success: false, error: tokenError.message })
      } else {
        console.log('✅ application_tokens table exists')
        results.push({ fix: 'application_tables', success: true })
      }
    }
  } catch (error) {
    console.error('❌ Error verifying application tables:', error.message)
    allFixed = false
    results.push({ fix: 'application_tables', success: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Fix Summary:')
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`   Successful fixes: ${successCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.fix}${error}`)
  })

  console.log(`\n   Overall status: ${allFixed ? '✅ ALL FIXES APPLIED' : '❌ SOME FIXES FAILED'}`)

  if (!allFixed) {
    console.log('\n🔧 Manual Steps Required:')
    console.log('   1. Check Supabase database permissions')
    console.log('   2. Verify environment variables are set correctly')
    console.log('   3. Run migrations manually in Supabase SQL editor if needed')
    console.log('   4. Configure at least one email service (Gmail, Workspace, or OneSignal)')
  } else {
    console.log('\n🎉 All fixes applied successfully!')
    console.log('   The Artist/Braider onboarding system should now work correctly.')
  }

  return allFixed
}

// Run the fix script
fixProductionOnboarding()
  .then(success => {
    if (success) {
      console.log('\n✅ Production onboarding fix completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Production onboarding fix completed with errors!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Fix script error:', error)
    process.exit(1)
  })
