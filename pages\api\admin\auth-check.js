import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Simple authentication check endpoint
 * Used by the unified authentication system to validate tokens in production
 * 
 * GET /api/admin/auth-check - Check if the current token is valid
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Auth check API called: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      error: 'Method not allowed',
      message: `Method ${req.method} not allowed`
    });
  }

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req);
    
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication check failed:`, authResult.error?.message);
      return res.status(401).json({ 
        error: 'Authentication failed',
        message: authResult.error?.message || 'Invalid or expired token',
        authenticated: false
      });
    }

    console.log(`[${requestId}] Authentication check successful. User: ${authResult.user?.email}, Role: ${authResult.role}`);

    // Return success with user info
    return res.status(200).json({
      authenticated: true,
      user: {
        id: authResult.user.id,
        email: authResult.user.email
      },
      role: authResult.role,
      requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${requestId}] Auth check API Error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Authentication check failed',
      authenticated: false
    });
  }
}
