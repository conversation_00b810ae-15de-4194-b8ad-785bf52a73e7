/* PaymentList.module.css */
.paymentListContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.filters {
  margin-bottom: 1.5rem;
}

.searchBox {
  margin-bottom: 1rem;
}

.searchInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filterControls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filterItem {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filterItem label {
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: #666;
}

.filterItem select,
.filterItem input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.loadingSpinner {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.errorMessage {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.errorMessage button {
  background-color: #c62828;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-left: 1rem;
  cursor: pointer;
}

.noResults {
  text-align: center;
  padding: 2rem;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.tableContainer {
  overflow-x: auto;
}

.paymentTable {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.paymentTable th {
  background-color: #f5f5f5;
  color: #333;
  font-weight: 600;
  padding: 12px 16px;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.paymentTable th:hover {
  background-color: #eaeaea;
}

.paymentTable tbody tr {
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.paymentTable tbody tr:hover {
  background-color: #f9f9f9;
}

.paymentTable td {
  padding: 12px 16px;
  vertical-align: middle;
}

.sortIndicator {
  display: inline-block;
  margin-left: 5px;
}

.methodCell {
  text-transform: capitalize;
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  text-transform: capitalize;
  font-weight: 500;
}

.statusCompleted {
  background-color: rgba(46, 125, 50, 0.1);
  color: #2e7d32;
}

.statusPending {
  background-color: rgba(245, 124, 0, 0.1);
  color: #f57c00;
}

.statusFailed {
  background-color: rgba(198, 40, 40, 0.1);
  color: #c62828;
}

.actions {
  white-space: nowrap;
}

.viewButton {
  display: inline-block;
  background-color: #6a0dad;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 2rem;
}

.paginationButton {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.paginationButton:hover:not(:disabled) {
  background-color: #6a0dad;
  color: white;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  margin: 0 10px;
  font-size: 0.9rem;
  color: #666;
}

@media (max-width: 768px) {
  .filterControls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filterItem {
    min-width: auto;
  }

  .paymentTable th,
  .paymentTable td {
    padding: 8px;
  }
}
