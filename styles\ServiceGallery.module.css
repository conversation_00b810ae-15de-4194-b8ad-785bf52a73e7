.gallerySection {
  padding: 6rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.galleryTitle {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #333;
}

.gallerySubtitle {
  font-size: 1.2rem;
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  color: #666;
  line-height: 1.6;
}

.categoryFilters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filterButton {
  padding: 0.8rem 1.5rem;
  background: transparent;
  border: 2px solid #4ECDC4;
  border-radius: 30px;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.filterButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.activeFilter {
  background-color: #4ECDC4;
  color: white;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.galleryGrid {
  display: grid;
  gap: 1.5rem;
  width: 100%;
}

.galleryColumn {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.galleryItem {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.galleryItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.galleryImage {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
  object-fit: cover;
}

.galleryItem:hover .galleryImage {
  transform: scale(1.05);
}

.galleryItemOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(78, 205, 196, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.galleryItem:hover .galleryItemOverlay {
  opacity: 1;
}

.viewPrompt {
  color: white;
  font-weight: 700;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.galleryItem:hover .viewPrompt {
  transform: translateY(0);
}

.viewPrompt svg {
  width: 36px;
  height: 36px;
  stroke-width: 2px;
}

/* Lightbox styles */
.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.lightboxContent {
  max-width: 90%;
  max-height: 90%;
  position: relative;
  animation: scaleIn 0.3s ease;
}

.lightboxImage {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.lightboxCaption {
  color: white;
  text-align: center;
  margin-top: 1rem;
  font-size: 1.1rem;
}

.closeButton {
  position: absolute;
  top: -40px;
  right: -40px;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
}

.closeButton:hover {
  transform: rotate(90deg);
}

.closeButton svg {
  width: 28px;
  height: 28px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
  }
  to {
    transform: scale(1);
  }
}

/* Responsive styles */
@media (max-width: 1200px) {
  .galleryTitle {
    font-size: 2.5rem;
  }
  
  .gallerySubtitle {
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }
}

@media (max-width: 900px) {
  .gallerySection {
    padding: 4rem 1.5rem;
  }
  
  .galleryTitle {
    font-size: 2.2rem;
  }
  
  .filterButton {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
  }
  
  .closeButton {
    top: -30px;
    right: -20px;
  }
}

@media (max-width: 600px) {
  .gallerySection {
    padding: 3rem 1rem;
  }
  
  .galleryTitle {
    font-size: 2rem;
  }
  
  .gallerySubtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
  
  .categoryFilters {
    gap: 0.6rem;
    margin-bottom: 2rem;
  }
  
  .filterButton {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
  
  .closeButton {
    top: -30px;
    right: 0;
  }
} 