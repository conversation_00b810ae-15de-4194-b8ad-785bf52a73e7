import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for storage diagnostics
 * This endpoint checks the storage service
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Storage diagnostics endpoint called: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // For diagnostics endpoints, we'll use a more lenient authentication approach
  // This allows the diagnostics page to work even when auth is having issues
  let authResult;
  try {
    // Authenticate request using our robust auth module
    authResult = await authenticateAdminRequest(req);
    const { authorized, error, user, role } = authResult;

    // Log authentication result
    if (authorized) {
      console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);
    } else {
      console.warn(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');

      // For diagnostics endpoints, we'll continue even if auth fails
      // This allows us to diagnose auth issues
      console.log(`[${requestId}] Continuing with diagnostics despite auth failure`);
    }
  } catch (authError) {
    console.error(`[${requestId}] Authentication error:`, authError);
    // Continue with diagnostics despite auth error
    console.log(`[${requestId}] Continuing with diagnostics despite auth error`);
  }

  // Extract user and role from authResult if available
  const user = authResult?.user || { email: 'diagnostics-user' };
  const role = authResult?.role || 'diagnostics';

  try {
    // Set a timeout to prevent hanging requests
    let timeoutId = null;
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error(`[${requestId}] Storage check timed out`);
        reject(new Error('Storage check timeout'));
      }, 15000); // Increased to 15 second timeout for better reliability
    })

    let adminClient;
    try {
      // Development bypass for easier testing
      const devBypass = process.env.NEXT_PUBLIC_DEV_AUTH_BYPASS === 'true';

      if (devBypass) {
        console.warn(`[${requestId}] DEVELOPMENT MODE: Bypassing admin client auth checks`);
        adminClient = {
          storage: {
            listBuckets: async () => ({
              data: [{ name: 'dev-bucket', id: 'dev-123' }],
              error: null
            })
          }
        };
      } else {
        try {
          // Get admin client directly first
          console.log(`[${requestId}] Attempting to get admin client`);
          adminClient = getAdminClient();

          // If we get here, we have the admin client
          console.log(`[${requestId}] Admin client obtained successfully without timeout`);
        } catch (directError) {
          console.warn(`[${requestId}] Direct admin client initialization failed, trying with timeout:`, directError.message);

          // Fall back to timeout-protected approach
          try {
            // Get admin client with timeout protection
            const adminClientPromise = Promise.resolve(getAdminClient());
            adminClient = await Promise.race([adminClientPromise, timeoutPromise]);

            // Clear the timeout since we got a response
            if (timeoutId) {
              clearTimeout(timeoutId);
              timeoutId = null;
            }
          } catch (timeoutError) {
            console.error(`[${requestId}] Admin client initialization timed out:`, timeoutError.message);
            throw new Error(`Admin client initialization failed: ${timeoutError.message}`);
          }
        }

        if (!adminClient) {
          console.error(`[${requestId}] Admin client not available.`);
          return res.status(500).json({
            error: 'Storage connection failed',
            message: 'Could not establish storage connection',
            requestId
          });
        }

        // Validate required environment variables
        if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
          throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');
        }

        console.log(`[${requestId}] Admin client obtained successfully`);
      }
    } catch (adminClientError) {
      // Clear the timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      console.error(`[${requestId}] Admin client fetch error:`, adminClientError);
      throw adminClientError;
    }

    // List storage buckets to test storage service
    const { data: buckets, error: bucketsError } = await adminClient.storage.listBuckets();

    if (bucketsError) {
      console.error(`[${requestId}] Storage buckets list failed:`, bucketsError);
      return res.status(500).json({
        error: 'Storage service test failed',
        message: bucketsError.message || 'Failed to list storage buckets',
        requestId
      });
    }

    // Return success response
    return res.status(200).json({
      status: 'healthy',
      message: 'Storage service is working properly',
      details: {
        timestamp: new Date().toISOString(),
        buckets: buckets.map(bucket => ({ name: bucket.name, id: bucket.id }))
      },
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Storage diagnostics error:`, error);
    return res.status(500).json({
      error: 'Storage diagnostics failed',
      message: error.message || 'An error occurred while checking storage service',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
