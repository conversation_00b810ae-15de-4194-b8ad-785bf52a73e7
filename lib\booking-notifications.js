import { supabase } from '@/lib/supabase';
import { sendOneSignalEmail, sendOneSignalPush } from '@/lib/notifications-server';

/**
 * Advanced Booking Notification System
 * Handles 10-minute advance notifications for artists and customers
 */

/**
 * Schedule a 10-minute advance notification for a booking
 * @param {Object} booking - The booking object
 * @param {Object} customer - Customer information
 * @param {Object} artist - Artist information
 * @param {Object} service - Service information
 */
export async function scheduleBookingReminder(booking, customer, artist, service) {
  try {
    const bookingStartTime = new Date(booking.start_time);
    const reminderTime = new Date(bookingStartTime.getTime() - (10 * 60 * 1000)); // 10 minutes before

    // Don't schedule if the reminder time is in the past
    if (reminderTime <= new Date()) {
      console.log('Booking starts too soon, skipping reminder scheduling');
      return { success: false, reason: 'Booking starts too soon' };
    }

    // Schedule customer notification
    const customerNotification = await scheduleNotification({
      booking_id: booking.id,
      user_id: customer.id,
      notification_type: 'booking_reminder',
      scheduled_time: reminderTime.toISOString(),
      title: 'Appointment Starting Soon! 🎨',
      message: `Your appointment with ${artist.name} starts in 10 minutes at ${booking.location}`,
      notification_data: {
        booking_id: booking.id,
        customer_id: customer.id,
        artist_id: artist.id,
        service_name: service.name,
        location: booking.location,
        start_time: booking.start_time,
        recipient_type: 'customer'
      }
    });

    // Schedule artist notification
    const artistNotification = await scheduleNotification({
      booking_id: booking.id,
      user_id: artist.id,
      notification_type: 'booking_reminder',
      scheduled_time: reminderTime.toISOString(),
      title: 'Upcoming Appointment! 👩‍🎨',
      message: `Your booking with ${customer.name} starts in 10 minutes at ${booking.location}`,
      notification_data: {
        booking_id: booking.id,
        customer_id: customer.id,
        artist_id: artist.id,
        service_name: service.name,
        location: booking.location,
        start_time: booking.start_time,
        recipient_type: 'artist'
      }
    });

    return {
      success: true,
      customerNotification,
      artistNotification,
      reminderTime: reminderTime.toISOString()
    };

  } catch (error) {
    console.error('Error scheduling booking reminder:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Schedule a notification in the database
 */
async function scheduleNotification(notificationData) {
  try {
    const { data, error } = await supabase
      .from('scheduled_notifications')
      .insert([notificationData])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { success: true, notification: data };
  } catch (error) {
    console.error('Error scheduling notification:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Process pending notifications (to be called by a cron job or scheduled task)
 */
export async function processPendingNotifications() {
  try {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - (5 * 60 * 1000));

    // Get notifications that should be sent now (with 5-minute buffer for missed notifications)
    const { data: pendingNotifications, error } = await supabase
      .from('scheduled_notifications')
      .select(`
        id,
        booking_id,
        user_id,
        notification_type,
        scheduled_time,
        title,
        message,
        notification_data,
        bookings!inner(
          id,
          start_time,
          end_time,
          location,
          status,
          customer_id,
          service_id
        )
      `)
      .eq('sent', false)
      .lte('scheduled_time', now.toISOString())
      .gte('scheduled_time', fiveMinutesAgo.toISOString())
      .eq('bookings.status', 'confirmed'); // Only send for confirmed bookings

    if (error) {
      throw error;
    }

    console.log(`Processing ${pendingNotifications.length} pending notifications`);

    const results = [];
    for (const notification of pendingNotifications) {
      try {
        const result = await sendScheduledNotification(notification);
        results.push(result);

        // Mark as sent
        await supabase
          .from('scheduled_notifications')
          .update({ 
            sent: true, 
            sent_at: new Date().toISOString() 
          })
          .eq('id', notification.id);

      } catch (error) {
        console.error(`Error sending notification ${notification.id}:`, error);
        results.push({ 
          notificationId: notification.id, 
          success: false, 
          error: error.message 
        });
      }
    }

    return {
      success: true,
      processed: pendingNotifications.length,
      results
    };

  } catch (error) {
    console.error('Error processing pending notifications:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Send a scheduled notification
 */
async function sendScheduledNotification(notification) {
  try {
    const { notification_data, user_id, title, message } = notification;
    const data = notification_data || {};

    // Get user information and preferences
    const { data: user, error: userError } = await supabase
      .from('user_profiles')
      .select(`
        id,
        name,
        email,
        notification_preferences(
          email_notifications,
          sms_notifications,
          push_notifications,
          phone_number
        )
      `)
      .eq('id', user_id)
      .single();

    if (userError) {
      throw userError;
    }

    const preferences = user.notification_preferences || {};
    const results = [];

    // Send email notification if enabled
    if (preferences.email_notifications !== false && user.email) {
      try {
        const emailResult = await sendBookingReminderEmail({
          user,
          notification,
          data
        });
        results.push({ type: 'email', ...emailResult });
      } catch (error) {
        console.error('Error sending email notification:', error);
        results.push({ type: 'email', success: false, error: error.message });
      }
    }

    // Send push notification if enabled
    if (preferences.push_notifications !== false) {
      try {
        const pushResult = await sendOneSignalPush({
          userIds: [user_id],
          title,
          message,
          data: {
            type: 'booking_reminder',
            booking_id: data.booking_id,
            ...data
          }
        });
        results.push({ type: 'push', ...pushResult });
      } catch (error) {
        console.error('Error sending push notification:', error);
        results.push({ type: 'push', success: false, error: error.message });
      }
    }

    // TODO: Add SMS notification support
    if (preferences.sms_notifications && preferences.phone_number) {
      // SMS implementation would go here
      console.log('SMS notifications not yet implemented');
    }

    return {
      notificationId: notification.id,
      success: true,
      results
    };

  } catch (error) {
    console.error('Error sending scheduled notification:', error);
    return {
      notificationId: notification.id,
      success: false,
      error: error.message
    };
  }
}

/**
 * Send booking reminder email
 */
async function sendBookingReminderEmail({ user, notification, data }) {
  const { recipient_type, service_name, location, start_time } = data;
  const startTime = new Date(start_time);
  const formattedTime = startTime.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
  const formattedDate = startTime.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  let subject, htmlBody;

  if (recipient_type === 'customer') {
    subject = '🎨 Your Appointment Starts in 10 Minutes!';
    htmlBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(45deg, #4ECDC4, #44A08D); padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
          <h1 style="color: white; margin: 0; font-size: 24px;">🎨 Appointment Reminder</h1>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #374151; margin-top: 0;">Hi ${user.name}!</h2>
          <p style="color: #6b7280; font-size: 16px; line-height: 1.6;">
            Your appointment is starting in just <strong>10 minutes</strong>! Here are your booking details:
          </p>
          
          <div style="background: white; padding: 15px; border-radius: 6px; border-left: 4px solid #4ECDC4;">
            <p style="margin: 5px 0;"><strong>Service:</strong> ${service_name}</p>
            <p style="margin: 5px 0;"><strong>Date:</strong> ${formattedDate}</p>
            <p style="margin: 5px 0;"><strong>Time:</strong> ${formattedTime}</p>
            <p style="margin: 5px 0;"><strong>Location:</strong> ${location}</p>
          </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
          <p style="color: #6b7280;">We're excited to see you soon!</p>
          <p style="color: #374151; font-weight: 600;">Ocean Soul Sparkles ✨</p>
        </div>
      </div>
    `;
  } else {
    subject = '👩‍🎨 Upcoming Appointment in 10 Minutes';
    htmlBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(45deg, #6366f1, #8b5cf6); padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
          <h1 style="color: white; margin: 0; font-size: 24px;">👩‍🎨 Artist Reminder</h1>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #374151; margin-top: 0;">Hi ${user.name}!</h2>
          <p style="color: #6b7280; font-size: 16px; line-height: 1.6;">
            You have an appointment starting in <strong>10 minutes</strong>. Here are the details:
          </p>
          
          <div style="background: white; padding: 15px; border-radius: 6px; border-left: 4px solid #6366f1;">
            <p style="margin: 5px 0;"><strong>Service:</strong> ${service_name}</p>
            <p style="margin: 5px 0;"><strong>Date:</strong> ${formattedDate}</p>
            <p style="margin: 5px 0;"><strong>Time:</strong> ${formattedTime}</p>
            <p style="margin: 5px 0;"><strong>Location:</strong> ${location}</p>
          </div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
          <p style="color: #6b7280;">Time to create some magic! ✨</p>
          <p style="color: #374151; font-weight: 600;">Ocean Soul Sparkles Team</p>
        </div>
      </div>
    `;
  }

  return await sendOneSignalEmail({
    email: user.email,
    subject,
    message: notification.message,
    htmlBody,
    data: {
      type: 'booking_reminder',
      booking_id: data.booking_id,
      recipient_type
    }
  });
}

/**
 * Cancel scheduled notifications for a booking
 */
export async function cancelBookingNotifications(bookingId) {
  try {
    const { error } = await supabase
      .from('scheduled_notifications')
      .delete()
      .eq('booking_id', bookingId)
      .eq('sent', false);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error canceling booking notifications:', error);
    return { success: false, error: error.message };
  }
}
