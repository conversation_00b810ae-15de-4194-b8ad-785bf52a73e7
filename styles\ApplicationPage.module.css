.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 1rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 700;
}

.header p {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.6;
}

.formContainer {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading p {
  margin-top: 1rem;
  font-size: 1.1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.error h1 {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error ul {
  text-align: left;
  margin: 2rem 0;
}

.error a {
  color: #6e8efb;
  text-decoration: none;
}

.error a:hover {
  text-decoration: underline;
}

.success {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.successIcon {
  width: 80px;
  height: 80px;
  background: #27ae60;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0 auto 2rem;
}

.success h1 {
  color: #27ae60;
  margin-bottom: 1.5rem;
}

.success p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.nextSteps {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin: 2rem 0;
  text-align: left;
}

.nextSteps h3 {
  color: #333;
  margin-bottom: 1rem;
}

.nextSteps ol {
  color: #666;
  line-height: 1.6;
}

.nextSteps li {
  margin-bottom: 0.5rem;
}

.homeButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.homeButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(110, 142, 251, 0.4);
}

.errorMessage {
  max-width: 800px;
  margin: 0 auto 2rem;
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 1rem;
  border-radius: 6px;
}

.submittingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.submittingModal {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.submittingModal p {
  margin-top: 1rem;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .success,
  .error {
    padding: 2rem 1rem;
  }
  
  .nextSteps {
    padding: 1.5rem;
  }
}
