import { useState } from 'react'
import ApplicationStatusBadge from './ApplicationStatusBadge'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/users/ApplicationsList.module.css'

export default function ApplicationsList({
  applications,
  loading,
  filters,
  onFilterChange,
  onReviewApplication,
  onDeleteApplication,
  onCancelApplication
}) {
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleFilterChange = (field, value) => {
    onFilterChange({ [field]: value })
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('en-AU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return 'Invalid Date'
    }
  }

  const formatExperience = (years) => {
    if (!years) return 'Not specified'
    return `${years} year${years !== 1 ? 's' : ''}`
  }

  const formatSpecializations = (specializations) => {
    if (!specializations || !Array.isArray(specializations)) return 'None specified'
    return specializations.join(', ')
  }

  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? ' ↑' : ' ↓'
  }

  // Sort applications
  const sortedApplications = [...applications].sort((a, b) => {
    let aValue = a[sortBy]
    let bValue = b[sortBy]

    // Handle date sorting
    if (sortBy === 'created_at' || sortBy === 'updated_at') {
      aValue = new Date(aValue)
      bValue = new Date(bValue)
    }

    // Handle string sorting
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading applications...</p>
      </div>
    )
  }

  return (
    <div className={styles.applicationsContainer}>
      {/* Filters */}
      <div className={styles.filtersSection}>
        <div className={styles.filterGroup}>
          <label htmlFor="status-filter">Status:</label>
          <select
            id="status-filter"
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="under_review">Under Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label htmlFor="type-filter">Type:</label>
          <select
            id="type-filter"
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">All Types</option>
            <option value="artist">Artist</option>
            <option value="braider">Braider</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label htmlFor="search-filter">Search:</label>
          <input
            id="search-filter"
            type="text"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Search by name or experience..."
            className={styles.searchInput}
          />
        </div>
      </div>

      {/* Applications Table */}
      {sortedApplications.length === 0 ? (
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>📝</div>
          <h3>No Applications Found</h3>
          <p>
            {filters.status !== 'all' || filters.type !== 'all' || filters.search
              ? 'No applications match your current filters.'
              : 'No artist or braider applications have been submitted yet.'}
          </p>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.applicationsTable}>
            <thead>
              <tr>
                <th onClick={() => handleSort('userName')}>
                  Applicant {renderSortIndicator('userName')}
                </th>
                <th onClick={() => handleSort('applicationType')}>
                  Type {renderSortIndicator('applicationType')}
                </th>
                <th onClick={() => handleSort('experienceYears')}>
                  Experience {renderSortIndicator('experienceYears')}
                </th>
                <th onClick={() => handleSort('status')}>
                  Status {renderSortIndicator('status')}
                </th>
                <th onClick={() => handleSort('created_at')}>
                  Applied {renderSortIndicator('created_at')}
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedApplications.map(application => (
                <tr key={application.id} className={styles.applicationRow}>
                  <td className={styles.applicantCell}>
                    <div className={styles.applicantInfo}>
                      <div className={styles.applicantName}>
                        {safeRender(application.userName, 'Unknown')}
                      </div>
                      {application.userPhone && (
                        <div className={styles.applicantPhone}>
                          {safeRender(application.userPhone, '')}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className={styles.typeCell}>
                    <span className={`${styles.typeBadge} ${styles[application.applicationType]}`}>
                      {safeRender(application.applicationType, 'Unknown')}
                    </span>
                  </td>
                  <td className={styles.experienceCell}>
                    {formatExperience(application.experienceYears)}
                  </td>
                  <td className={styles.statusCell}>
                    <ApplicationStatusBadge status={application.status} />
                  </td>
                  <td className={styles.dateCell}>
                    {formatDate(application.createdAt)}
                  </td>
                  <td className={styles.actionsCell}>
                    <div className={styles.actionButtons}>
                      <button
                        className={styles.reviewButton}
                        onClick={() => onReviewApplication(application)}
                        title="Review Application"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                        Review
                      </button>

                      {/* Cancel button - only show for pending/under_review applications */}
                      {['pending', 'under_review'].includes(application.status) && onCancelApplication && (
                        <button
                          className={styles.cancelButton}
                          onClick={() => onCancelApplication(application)}
                          title="Cancel Application"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                          </svg>
                          Cancel
                        </button>
                      )}

                      {/* Delete button - only show for pending/under_review applications */}
                      {['pending', 'under_review'].includes(application.status) && onDeleteApplication && (
                        <button
                          className={styles.deleteButton}
                          onClick={() => onDeleteApplication(application)}
                          title="Delete Application (Permanent)"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                            <line x1="10" y1="11" x2="10" y2="17"/>
                            <line x1="14" y1="11" x2="14" y2="17"/>
                          </svg>
                          Delete
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Results Summary */}
      <div className={styles.resultsSummary}>
        <p>
          Showing {safeRender(sortedApplications.length, '0')} of {safeRender(applications.length, '0')} applications
        </p>
      </div>
    </div>
  )
}
