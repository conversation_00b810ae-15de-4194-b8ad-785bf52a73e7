/**
 * OceanSoulSparkles SEO Testing Script
 *
 * This script helps identify SEO issues on the website.
 * It can be run in the browser console to check for common SEO problems.
 */

// Configuration
const config = {
  // Meta tags to check
  metaTags: [
    { name: 'title', required: true },
    { name: 'description', required: true },
    { name: 'viewport', required: true },
    { name: 'robots', required: false },
    { name: 'canonical', required: true },
    { name: 'og:title', required: true },
    { name: 'og:description', required: true },
    { name: 'og:type', required: true },
    { name: 'og:url', required: true },
    { name: 'og:image', required: true },
    { name: 'twitter:card', required: false },
    { name: 'twitter:title', required: false },
    { name: 'twitter:description', required: false },
    { name: 'twitter:image', required: false }
  ],

  // Heading structure to check
  headings: {
    h1: { required: true, maxCount: 1 },
    h2: { required: true, minCount: 1 },
    h3: { required: false, minCount: 0 }
  },

  // Image attributes to check
  imageAttributes: ['alt', 'loading', 'width', 'height'],

  // Structured data types to check
  structuredDataTypes: [
    'Organization',
    'LocalBusiness',
    'Service',
    'Product',
    'BreadcrumbList',
    'FAQPage',
    'Event'
  ],

  // Link attributes to check
  linkAttributes: ['rel', 'title', 'aria-label'],

  // Minimum content length
  minContentLength: 300,

  // Maximum title length
  maxTitleLength: 60,

  // Maximum description length
  maxDescriptionLength: 160
};

// Results storage
const seoResults = {
  metaTags: { pass: [], fail: [] },
  headings: { pass: [], fail: [] },
  images: { pass: [], fail: [] },
  structuredData: { pass: [], fail: [] },
  links: { pass: [], fail: [] },
  content: { pass: [], fail: [] },
  performance: { pass: [], fail: [] }
};

/**
 * Check meta tags
 */
function checkMetaTags() {
  console.log('🔍 Checking meta tags...');

  // Check title tag
  const titleTag = document.querySelector('title');
  if (!titleTag) {
    seoResults.metaTags.fail.push({
      tag: 'title',
      issue: 'Missing title tag'
    });
  } else {
    const titleText = titleTag.textContent;
    if (titleText.length > config.maxTitleLength) {
      seoResults.metaTags.fail.push({
        tag: 'title',
        issue: `Title too long (${titleText.length} chars)`,
        content: titleText
      });
    } else if (titleText.length < 10) {
      seoResults.metaTags.fail.push({
        tag: 'title',
        issue: `Title too short (${titleText.length} chars)`,
        content: titleText
      });
    } else {
      seoResults.metaTags.pass.push({
        tag: 'title',
        content: titleText
      });
    }
  }

  // Check meta description
  const descriptionTag = document.querySelector('meta[name="description"]');
  if (!descriptionTag) {
    seoResults.metaTags.fail.push({
      tag: 'description',
      issue: 'Missing meta description'
    });
  } else {
    const descriptionContent = descriptionTag.getAttribute('content');
    if (descriptionContent.length > config.maxDescriptionLength) {
      seoResults.metaTags.fail.push({
        tag: 'description',
        issue: `Description too long (${descriptionContent.length} chars)`,
        content: descriptionContent
      });
    } else if (descriptionContent.length < 50) {
      seoResults.metaTags.fail.push({
        tag: 'description',
        issue: `Description too short (${descriptionContent.length} chars)`,
        content: descriptionContent
      });
    } else {
      seoResults.metaTags.pass.push({
        tag: 'description',
        content: descriptionContent
      });
    }
  }

  // Check canonical tag
  const canonicalTag = document.querySelector('link[rel="canonical"]');
  if (!canonicalTag) {
    seoResults.metaTags.fail.push({
      tag: 'canonical',
      issue: 'Missing canonical tag'
    });
  } else {
    const canonicalHref = canonicalTag.getAttribute('href');
    if (!canonicalHref) {
      seoResults.metaTags.fail.push({
        tag: 'canonical',
        issue: 'Canonical tag has no href attribute'
      });
    } else {
      seoResults.metaTags.pass.push({
        tag: 'canonical',
        content: canonicalHref
      });
    }
  }

  // Log all meta tags for debugging
  console.log('All meta tags found:', document.querySelectorAll('meta'));

  // Check other meta tags
  config.metaTags.forEach(tag => {
    if (tag.name === 'title' || tag.name === 'description' || tag.name === 'canonical') {
      return; // Already checked above
    }

    let metaTag;
    if (tag.name.startsWith('og:')) {
      metaTag = document.querySelector(`meta[property="${tag.name}"]`);
    } else if (tag.name.startsWith('twitter:')) {
      metaTag = document.querySelector(`meta[name="${tag.name}"]`);
    } else {
      metaTag = document.querySelector(`meta[name="${tag.name}"]`);
    }

    console.log(`Checking meta tag: ${tag.name}`, metaTag);

    if (!metaTag && tag.required) {
      seoResults.metaTags.fail.push({
        tag: tag.name,
        issue: `Missing required meta tag: ${tag.name}`
      });
    } else if (metaTag) {
      const content = metaTag.getAttribute('content');
      if (!content) {
        seoResults.metaTags.fail.push({
          tag: tag.name,
          issue: `Meta tag ${tag.name} has no content`
        });
      } else {
        seoResults.metaTags.pass.push({
          tag: tag.name,
          content: content
        });
      }
    }
  });

  console.log(`✓ Meta tags check complete. Issues found: ${seoResults.metaTags.fail.length}`);
}

/**
 * Check heading structure
 */
function checkHeadings() {
  console.log('🔍 Checking heading structure...');

  // Check h1
  const h1Tags = document.querySelectorAll('h1');
  if (h1Tags.length === 0) {
    seoResults.headings.fail.push({
      tag: 'h1',
      issue: 'Missing h1 tag'
    });
  } else if (h1Tags.length > 1) {
    seoResults.headings.fail.push({
      tag: 'h1',
      issue: `Multiple h1 tags found (${h1Tags.length})`,
      content: Array.from(h1Tags).map(h => h.textContent)
    });
  } else {
    seoResults.headings.pass.push({
      tag: 'h1',
      content: h1Tags[0].textContent
    });
  }

  // Check h2
  const h2Tags = document.querySelectorAll('h2');
  if (h2Tags.length === 0 && config.headings.h2.required) {
    seoResults.headings.fail.push({
      tag: 'h2',
      issue: 'Missing h2 tags'
    });
  } else {
    seoResults.headings.pass.push({
      tag: 'h2',
      count: h2Tags.length,
      content: Array.from(h2Tags).map(h => h.textContent)
    });
  }

  // Check heading hierarchy
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let previousLevel = 0;
  let hierarchyIssues = [];

  Array.from(headings).forEach(heading => {
    const level = parseInt(heading.tagName.substring(1));

    if (level - previousLevel > 1) {
      hierarchyIssues.push({
        tag: heading.tagName.toLowerCase(),
        issue: `Skipped heading level (from h${previousLevel} to h${level})`,
        content: heading.textContent
      });
    }

    previousLevel = level;
  });

  if (hierarchyIssues.length > 0) {
    hierarchyIssues.forEach(issue => {
      seoResults.headings.fail.push(issue);
    });
  }

  console.log(`✓ Heading structure check complete. Issues found: ${seoResults.headings.fail.length}`);
}

/**
 * Check images
 */
function checkImages() {
  console.log('🔍 Checking images...');

  const images = document.querySelectorAll('img');

  images.forEach(img => {
    // Skip very small images (likely icons)
    if (img.width < 50 && img.height < 50) {
      return;
    }

    // Check alt text
    if (!img.hasAttribute('alt')) {
      seoResults.images.fail.push({
        element: img,
        issue: 'Missing alt attribute',
        src: img.src
      });
    } else if (img.getAttribute('alt').length < 5 && img.getAttribute('alt') !== '') {
      seoResults.images.fail.push({
        element: img,
        issue: 'Alt text too short',
        src: img.src,
        alt: img.getAttribute('alt')
      });
    } else {
      seoResults.images.pass.push({
        element: img,
        src: img.src,
        alt: img.getAttribute('alt')
      });
    }

    // Check width and height attributes
    if (!img.hasAttribute('width') || !img.hasAttribute('height')) {
      seoResults.images.fail.push({
        element: img,
        issue: 'Missing width or height attributes',
        src: img.src
      });
    }

    // Check loading attribute
    if (!img.hasAttribute('loading')) {
      seoResults.images.fail.push({
        element: img,
        issue: 'Missing loading attribute (lazy loading)',
        src: img.src
      });
    }
  });

  console.log(`✓ Image check complete. Issues found: ${seoResults.images.fail.length}`);
}

/**
 * Check structured data
 */
function checkStructuredData() {
  console.log('🔍 Checking structured data...');

  const structuredDataScripts = document.querySelectorAll('script[type="application/ld+json"]');

  if (structuredDataScripts.length === 0) {
    seoResults.structuredData.fail.push({
      issue: 'No structured data found'
    });
    console.log('❌ No structured data found');
    return;
  }

  let foundTypes = [];

  structuredDataScripts.forEach(script => {
    try {
      const data = JSON.parse(script.textContent);
      const type = data['@type'] || (data['@graph'] && data['@graph'][0] && data['@graph'][0]['@type']);

      if (type) {
        foundTypes.push(type);
        seoResults.structuredData.pass.push({
          type: type,
          data: data
        });
      } else {
        seoResults.structuredData.fail.push({
          issue: 'Structured data missing @type',
          data: data
        });
      }
    } catch (error) {
      seoResults.structuredData.fail.push({
        issue: 'Invalid JSON in structured data',
        error: error.message,
        content: script.textContent.substring(0, 100) + '...'
      });
    }
  });

  // Check for required structured data types
  config.structuredDataTypes.forEach(type => {
    if (!foundTypes.includes(type)) {
      seoResults.structuredData.fail.push({
        issue: `Missing recommended structured data type: ${type}`
      });
    }
  });

  console.log(`✓ Structured data check complete. Found ${foundTypes.length} types, issues: ${seoResults.structuredData.fail.length}`);
}

/**
 * Check content length
 */
function checkContentLength() {
  console.log('🔍 Checking content length...');

  // Get main content
  const main = document.querySelector('main') || document.body;
  const textContent = main.textContent.trim().replace(/\s+/g, ' ');

  if (textContent.length < config.minContentLength) {
    seoResults.content.fail.push({
      issue: `Content too short (${textContent.length} chars)`,
      recommendation: `Add more content to reach at least ${config.minContentLength} chars`
    });
  } else {
    seoResults.content.pass.push({
      contentLength: textContent.length
    });
  }

  console.log(`✓ Content length check complete. Content length: ${textContent.length} chars`);
}

/**
 * Check links
 */
function checkLinks() {
  console.log('🔍 Checking links...');

  const links = document.querySelectorAll('a[href]');

  links.forEach(link => {
    const href = link.getAttribute('href');
    const text = link.textContent.trim();

    // Check for empty link text
    if (!text && !link.querySelector('img') && !link.getAttribute('aria-label')) {
      seoResults.links.fail.push({
        element: link,
        issue: 'Link has no text content or aria-label',
        href: href
      });
    }

    // Check for generic link text
    if (['click here', 'read more', 'learn more', 'more', 'link'].includes(text.toLowerCase())) {
      seoResults.links.fail.push({
        element: link,
        issue: 'Generic link text',
        text: text,
        href: href
      });
    }

    // Check external links
    if (href.startsWith('http') && !href.includes(window.location.hostname)) {
      if (!link.getAttribute('rel') || !link.getAttribute('rel').includes('noopener')) {
        seoResults.links.fail.push({
          element: link,
          issue: 'External link missing rel="noopener"',
          href: href
        });
      }
    }
  });

  console.log(`✓ Link check complete. Issues found: ${seoResults.links.fail.length}`);
}

/**
 * Run all SEO checks
 */
function runSeoTests() {
  console.log('🚀 Starting SEO tests...');

  checkMetaTags();
  checkHeadings();
  checkImages();
  checkStructuredData();
  checkContentLength();
  checkLinks();

  console.log('🏁 All SEO tests completed!');
  console.log('📊 Test results:', seoResults);

  // Format results as markdown for easy copying
  const markdown = formatResultsAsMarkdown();
  console.log('📋 Markdown results:\n', markdown);

  return seoResults;
}

/**
 * Format results as markdown
 */
function formatResultsAsMarkdown() {
  let markdown = '## SEO Test Results\n\n';

  // Meta tags results
  markdown += '### Meta Tags\n\n';
  if (seoResults.metaTags.fail.length === 0) {
    markdown += 'All meta tags are properly implemented.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';

    seoResults.metaTags.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.tag} |\n`;
    });
    markdown += '\n';
  }

  // Heading structure results
  markdown += '### Heading Structure\n\n';
  if (seoResults.headings.fail.length === 0) {
    markdown += 'Heading structure is properly implemented.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';

    seoResults.headings.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.tag} |\n`;
    });
    markdown += '\n';
  }

  // Image results
  markdown += '### Images\n\n';
  if (seoResults.images.fail.length === 0) {
    markdown += 'All images are properly optimized for SEO.\n\n';
  } else {
    markdown += '| Issue | Image |\n';
    markdown += '|-------|------|\n';

    seoResults.images.fail.forEach(issue => {
      const imagePath = issue.src.split('/').pop();
      markdown += `| ${issue.issue} | ${imagePath} |\n`;
    });
    markdown += '\n';
  }

  // Structured data results
  markdown += '### Structured Data\n\n';
  if (seoResults.structuredData.fail.length === 0 && seoResults.structuredData.pass.length > 0) {
    markdown += `Structured data is properly implemented. Found types: ${seoResults.structuredData.pass.map(item => item.type).join(', ')}\n\n`;
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';

    seoResults.structuredData.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.error || ''} |\n`;
    });
    markdown += '\n';
  }

  // Content results
  markdown += '### Content\n\n';
  if (seoResults.content.fail.length === 0) {
    markdown += 'Content length is sufficient.\n\n';
  } else {
    markdown += '| Issue | Recommendation |\n';
    markdown += '|-------|---------------|\n';

    seoResults.content.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.recommendation} |\n`;
    });
    markdown += '\n';
  }

  // Link results
  markdown += '### Links\n\n';
  if (seoResults.links.fail.length === 0) {
    markdown += 'All links are properly implemented.\n\n';
  } else {
    markdown += '| Issue | Link |\n';
    markdown += '|-------|------|\n';

    seoResults.links.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.href} |\n`;
    });
    markdown += '\n';
  }

  return markdown;
}

// Export functions for use in browser console
window.seoTests = {
  runAll: runSeoTests,
  checkMetaTags,
  checkHeadings,
  checkImages,
  checkStructuredData,
  checkContentLength,
  checkLinks,
  getResults: () => seoResults
};

console.log('💻 SEO test script loaded. Run tests with: seoTests.runAll()');
