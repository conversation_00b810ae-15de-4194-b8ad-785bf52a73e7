import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for artist festival participation data
 * Provides festival attendance history and ticket cost tracking
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  const { artistId } = req.query;
  const { year } = req.query;
  
  console.log(`[${requestId}] Artist Festival Participation API called: ${req.method} for artist ${artistId}, year ${year}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetParticipation(req, res, artistId, year, requestId);
    } else if (req.method === 'POST') {
      return await handleCreateParticipation(req, res, artistId, user, requestId);
    } else if (req.method === 'PUT') {
      return await handleUpdateParticipation(req, res, artistId, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request - fetch artist festival participation
 */
async function handleGetParticipation(req, res, artistId, year, requestId) {
  try {
    console.log(`[${requestId}] Fetching festival participation for artist:`, artistId, 'year:', year);

    // Verify artist exists
    const { data: artist, error: artistError } = await supabase
      .from('user_profiles')
      .select('id, name, email')
      .eq('id', artistId)
      .single();

    if (artistError) {
      if (artistError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Artist not found' });
      }
      throw artistError;
    }

    const currentYear = year || new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    const endDate = `${currentYear}-12-31`;

    // Fetch festival participation data
    const { data: participation, error: participationError } = await supabase
      .from('artist_festival_participation')
      .select(`
        id,
        ticket_cost,
        artist_pays_ticket,
        ticket_paid,
        ticket_payment_date,
        attendance_confirmed,
        check_in_time,
        check_out_time,
        notes,
        created_at,
        events!inner(
          id,
          name,
          location,
          start_date,
          end_date,
          status,
          artist_ticket_cost,
          artists_pay_tickets
        )
      `)
      .eq('artist_id', artistId)
      .gte('events.start_date', startDate)
      .lte('events.start_date', endDate)
      .order('events.start_date', { ascending: false });

    if (participationError) {
      console.error(`[${requestId}] Error fetching participation:`, participationError);
      throw participationError;
    }

    // Format participation data
    const formattedParticipation = participation.map(p => ({
      id: p.id,
      event_id: p.events.id,
      event_name: p.events.name,
      event_location: p.events.location,
      event_start_date: p.events.start_date,
      event_end_date: p.events.end_date,
      event_status: p.events.status,
      ticket_cost: p.ticket_cost || p.events.artist_ticket_cost || 0,
      artist_pays_ticket: p.artist_pays_ticket || p.events.artists_pay_tickets || false,
      ticket_paid: p.ticket_paid || false,
      ticket_payment_date: p.ticket_payment_date,
      attendance_confirmed: p.attendance_confirmed || false,
      check_in_time: p.check_in_time,
      check_out_time: p.check_out_time,
      notes: p.notes,
      created_at: p.created_at
    }));

    // Calculate summary statistics
    const summary = {
      total_events: formattedParticipation.length,
      confirmed_attendance: formattedParticipation.filter(p => p.attendance_confirmed).length,
      total_ticket_costs: formattedParticipation
        .filter(p => p.artist_pays_ticket)
        .reduce((sum, p) => sum + parseFloat(p.ticket_cost || 0), 0),
      unpaid_tickets: formattedParticipation
        .filter(p => p.artist_pays_ticket && !p.ticket_paid).length,
      upcoming_events: formattedParticipation
        .filter(p => new Date(p.event_start_date) > new Date()).length
    };

    console.log(`[${requestId}] Found ${formattedParticipation.length} festival participations`);
    return res.status(200).json({ 
      participation: formattedParticipation,
      summary,
      artist: {
        id: artist.id,
        name: artist.name,
        email: artist.email
      },
      year: currentYear
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetParticipation:`, error);
    throw error;
  }
}

/**
 * Handle POST request - create festival participation record
 */
async function handleCreateParticipation(req, res, artistId, user, requestId) {
  try {
    const {
      event_id,
      ticket_cost,
      artist_pays_ticket,
      notes
    } = req.body;

    console.log(`[${requestId}] Creating festival participation:`, { artistId, event_id });

    // Validate required fields
    if (!event_id) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['event_id']
      });
    }

    // Verify event exists
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('id, name, artist_ticket_cost, artists_pay_tickets')
      .eq('id', event_id)
      .single();

    if (eventError) {
      if (eventError.code === 'PGRST116') {
        return res.status(400).json({ error: 'Event not found' });
      }
      throw eventError;
    }

    // Check if participation already exists
    const { data: existingParticipation, error: checkError } = await supabase
      .from('artist_festival_participation')
      .select('id')
      .eq('artist_id', artistId)
      .eq('event_id', event_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingParticipation) {
      return res.status(400).json({ 
        error: 'Artist is already registered for this event' 
      });
    }

    // Create participation record
    const { data: participation, error: createError } = await supabase
      .from('artist_festival_participation')
      .insert([{
        event_id,
        artist_id: artistId,
        ticket_cost: ticket_cost || event.artist_ticket_cost || 0,
        artist_pays_ticket: artist_pays_ticket !== undefined ? artist_pays_ticket : event.artists_pay_tickets,
        notes: notes || ''
      }])
      .select()
      .single();

    if (createError) {
      console.error(`[${requestId}] Error creating participation:`, createError);
      throw createError;
    }

    console.log(`[${requestId}] Festival participation created successfully:`, participation.id);
    return res.status(201).json({ 
      participation,
      message: 'Festival participation created successfully'
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleCreateParticipation:`, error);
    throw error;
  }
}

/**
 * Handle PUT request - update festival participation
 */
async function handleUpdateParticipation(req, res, artistId, user, requestId) {
  try {
    const {
      participation_id,
      ticket_paid,
      attendance_confirmed,
      check_in_time,
      check_out_time,
      notes
    } = req.body;

    console.log(`[${requestId}] Updating festival participation:`, participation_id);

    if (!participation_id) {
      return res.status(400).json({ 
        error: 'Missing participation_id' 
      });
    }

    // Verify participation exists and belongs to artist
    const { data: existingParticipation, error: fetchError } = await supabase
      .from('artist_festival_participation')
      .select('id, artist_id, event_id')
      .eq('id', participation_id)
      .eq('artist_id', artistId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Participation record not found' });
      }
      throw fetchError;
    }

    // Prepare update data
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (typeof ticket_paid === 'boolean') {
      updateData.ticket_paid = ticket_paid;
      if (ticket_paid) {
        updateData.ticket_payment_date = new Date().toISOString().split('T')[0];
      }
    }

    if (typeof attendance_confirmed === 'boolean') {
      updateData.attendance_confirmed = attendance_confirmed;
    }

    if (check_in_time) {
      updateData.check_in_time = check_in_time;
    }

    if (check_out_time) {
      updateData.check_out_time = check_out_time;
    }

    if (notes !== undefined) {
      updateData.notes = notes;
    }

    // Update participation
    const { data: participation, error: updateError } = await supabase
      .from('artist_festival_participation')
      .update(updateData)
      .eq('id', participation_id)
      .eq('artist_id', artistId)
      .select()
      .single();

    if (updateError) {
      console.error(`[${requestId}] Error updating participation:`, updateError);
      throw updateError;
    }

    console.log(`[${requestId}] Festival participation updated successfully:`, participation.id);
    return res.status(200).json({ 
      participation,
      message: 'Festival participation updated successfully'
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleUpdateParticipation:`, error);
    throw error;
  }
}
