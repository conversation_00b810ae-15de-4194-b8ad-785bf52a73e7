/**
 * Authentication Data Cleanup Script
 * 
 * This script clears all authentication data from browser storage
 * to ensure a clean state for authentication testing.
 * 
 * Usage:
 * 1. Run with: node scripts/clear-auth-data.js
 * 2. Open the browser console and paste the generated code
 */

console.log(`
// ============================================================
// AUTHENTICATION DATA CLEANUP
// ============================================================
// Copy and paste this code into your browser console
// when on the Ocean Soul Sparkles website to clear all auth data
// ============================================================

(function clearAuthData() {
  console.log('Starting authentication data cleanup...');
  
  // Clear session storage
  try {
    console.log('Clearing session storage...');
    sessionStorage.removeItem('oss_auth_token_cache');
    sessionStorage.removeItem('oss_session');
    sessionStorage.removeItem('supabase.auth.token');
    sessionStorage.removeItem('sb-ndlgbcsbidyhxbpqzgqp-auth-token');
  } catch (e) {
    console.error('Error clearing session storage:', e);
  }
  
  // Clear local storage
  try {
    console.log('Clearing local storage...');
    localStorage.removeItem('oss_auth_token');
    localStorage.removeItem('sb_auth_token');
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('sb-ndlgbcsbidyhxbpqzgqp-auth-token');
  } catch (e) {
    console.error('Error clearing local storage:', e);
  }
  
  // Clear cookies
  try {
    console.log('Clearing cookies...');
    const cookies = [
      'oss_auth_token',
      'sb_auth_token',
      'sb-access-token',
      'sb-refresh-token',
      'supabase-auth-token'
    ];
    
    cookies.forEach(name => {
      document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    });
  } catch (e) {
    console.error('Error clearing cookies:', e);
  }
  
  console.log('Authentication data cleanup complete!');
  console.log('Please refresh the page and try logging in again.');
})();
`);

console.log('\nCopy the code above and paste it into your browser console when on the Ocean Soul Sparkles website.');
console.log('Then refresh the page and try logging in again.');
