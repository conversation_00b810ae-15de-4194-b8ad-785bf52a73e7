.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.modalHeader h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: #e9ecef;
  color: #333;
}

.modalBody {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Application Details */
.applicationDetails {
  margin-bottom: 32px;
}

.detailSection {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.detailSection:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detailSection h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.detailGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detailItem label {
  font-weight: 500;
  color: #666;
  font-size: 0.9rem;
}

.detailItem span {
  color: #333;
  font-size: 0.95rem;
}

.typeBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
  width: fit-content;
}

.typeBadge.artist {
  background-color: #e3f2fd;
  color: #1976d2;
}

.typeBadge.braider {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.portfolioLink {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.portfolioLink:hover {
  color: #0056b3;
  text-decoration: underline;
}

.textContent {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #007bff;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Review Form */
.reviewForm {
  border-top: 2px solid #dee2e6;
  padding-top: 24px;
}

.formSection h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.statusOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.statusOption {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.statusOption:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.statusOption input[type="radio"] {
  margin: 0;
}

.statusOption input[type="radio"]:checked + .statusLabel {
  font-weight: 600;
}

.statusOption:has(input[type="radio"]:checked) {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.statusLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  color: #333;
}

.statusIcon {
  font-size: 1.1rem;
}

.notesSection {
  margin-bottom: 24px;
}

.notesSection label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.notesTextarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 100px;
}

.notesTextarea:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.cancelButton {
  padding: 10px 20px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background-color: #545b62;
}

.submitButton {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.submitButton:hover:not(:disabled) {
  background-color: #0056b3;
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .modalHeader {
    padding: 16px 20px;
  }

  .modalHeader h2 {
    font-size: 1.3rem;
  }

  .modalBody {
    padding: 20px;
  }

  .detailGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .statusOptions {
    gap: 8px;
  }

  .statusOption {
    padding: 10px;
  }

  .formActions {
    flex-direction: column-reverse;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .modalHeader {
    padding: 12px 16px;
  }

  .modalHeader h2 {
    font-size: 1.2rem;
  }

  .modalBody {
    padding: 16px;
  }

  .detailSection h3 {
    font-size: 1rem;
  }

  .statusLabel {
    font-size: 0.9rem;
  }

  .notesTextarea {
    font-size: 0.8rem;
  }
}
