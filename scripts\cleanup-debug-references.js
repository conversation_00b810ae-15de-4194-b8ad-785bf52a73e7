#!/usr/bin/env node

/**
 * Debug References Cleanup Script
 * Removes any remaining references to debug files that were removed for production security
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🧹 Cleaning up debug file references...\n');

// Files that were removed
const removedFiles = [
  'browser-debug.js',
  'quick-error-check.js', 
  'node-error-check.js',
  'node-error-check.cjs',
  'console-monitor.js',
  'public/console-monitor.js'
];

// Patterns to find and clean up
const cleanupPatterns = [
  {
    pattern: /<script src="\/console-monitor\.js"><\/script>/g,
    replacement: '<!-- Console monitoring removed for production security -->',
    description: 'Console monitor script tags'
  },
  {
    pattern: /import.*from.*['"]\.\/browser-debug\.js['"]/g,
    replacement: '// Debug import removed for production',
    description: 'Browser debug imports'
  },
  {
    pattern: /require\(['"]\.\/browser-debug\.js['"]\)/g,
    replacement: '// Debug require removed for production',
    description: 'Browser debug requires'
  },
  {
    pattern: /\/\/ Paste the complete content of `browser-debug\.js`.*$/gm,
    replacement: '// Debug script references removed for production',
    description: 'Browser debug documentation references'
  }
];

let totalChanges = 0;

/**
 * Scan and clean a file
 */
function cleanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let fileChanges = 0;
    
    cleanupPatterns.forEach(({ pattern, replacement, description }) => {
      const matches = newContent.match(pattern);
      if (matches) {
        newContent = newContent.replace(pattern, replacement);
        fileChanges += matches.length;
        console.log(`  ✅ Cleaned ${matches.length} ${description} in ${path.relative(rootDir, filePath)}`);
      }
    });
    
    // Check for any remaining references to removed files
    removedFiles.forEach(file => {
      const fileName = path.basename(file);
      const regex = new RegExp(fileName.replace('.', '\\.'), 'g');
      const matches = newContent.match(regex);
      
      if (matches) {
        // Only report if it's not in a comment or documentation
        const nonCommentMatches = matches.filter(match => {
          const index = newContent.indexOf(match);
          const lineStart = newContent.lastIndexOf('\n', index);
          const line = newContent.substring(lineStart, newContent.indexOf('\n', index));
          return !line.trim().startsWith('//') && !line.trim().startsWith('*') && !line.trim().startsWith('#');
        });
        
        if (nonCommentMatches.length > 0) {
          console.log(`  ⚠️  Found ${nonCommentMatches.length} references to removed file ${fileName} in ${path.relative(rootDir, filePath)}`);
        }
      }
    });
    
    if (fileChanges > 0) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      totalChanges += fileChanges;
    }
    
  } catch (error) {
    console.error(`Error cleaning ${filePath}:`, error.message);
  }
}

/**
 * Recursively scan directory
 */
function scanDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      // Skip node_modules, .git, .next, etc.
      if (item.startsWith('.') || item === 'node_modules' || item === 'dist' || item === 'build') {
        return;
      }
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else if (stat.isFile()) {
        // Only scan text files
        const ext = path.extname(item).toLowerCase();
        const textExtensions = ['.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.txt', '.html', '.css', '.scss'];
        
        if (textExtensions.includes(ext) || !ext) {
          cleanFile(itemPath);
        }
      }
    });
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

/**
 * Main execution
 */
function main() {
  console.log('Scanning for debug file references...\n');
  
  // Scan specific directories
  const scanPaths = ['pages', 'components', 'lib', 'public', 'scripts', 'docs'];
  
  scanPaths.forEach(scanPath => {
    const fullPath = path.join(rootDir, scanPath);
    if (fs.existsSync(fullPath)) {
      scanDirectory(fullPath);
    }
  });
  
  // Also scan root files
  const rootFiles = ['README.md', 'package.json', 'next.config.js'];
  rootFiles.forEach(file => {
    const filePath = path.join(rootDir, file);
    if (fs.existsSync(filePath)) {
      cleanFile(filePath);
    }
  });
  
  console.log(`\n📊 Cleanup Summary:`);
  console.log(`  🧹 Total changes made: ${totalChanges}`);
  
  if (totalChanges > 0) {
    console.log(`  ✅ Debug references cleaned up successfully`);
  } else {
    console.log(`  ✅ No debug references found - codebase is clean`);
  }
  
  console.log(`\n🔒 Production security improved by removing debug file references`);
}

// Run the cleanup
main();
