import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { isAuthenticated } from '@/lib/unified-auth-fetch';

/**
 * Authentication Guard Component
 * 
 * This component checks if the user is authenticated and redirects to login if not.
 * It's designed to work properly in both development and production environments.
 */
export default function AuthenticationGuard({ children, fallback = null }) {
  const [authStatus, setAuthStatus] = useState('checking'); // 'checking', 'authenticated', 'unauthenticated'
  const [error, setError] = useState(null);
  const router = useRouter();

  useEffect(() => {
    let mounted = true;

    const checkAuthentication = async () => {
      try {
        console.log('[AuthGuard] Checking authentication status...');
        const authenticated = await isAuthenticated();
        
        if (!mounted) return; // Component was unmounted
        
        if (authenticated) {
          console.log('[AuthGuard] User is authenticated');
          setAuthStatus('authenticated');
        } else {
          console.log('[AuthGuard] User is not authenticated');
          setAuthStatus('unauthenticated');
          
          // In production, redirect to login immediately
          if (process.env.NODE_ENV === 'production') {
            console.log('[AuthGuard] Production mode: redirecting to login');
            const currentPath = router.asPath;
            router.replace(`/admin/login?redirect=${encodeURIComponent(currentPath)}`);
          }
        }
      } catch (error) {
        console.error('[AuthGuard] Authentication check failed:', error);
        if (!mounted) return;
        
        setError(error.message);
        setAuthStatus('unauthenticated');
        
        // On error, also redirect to login in production
        if (process.env.NODE_ENV === 'production') {
          const currentPath = router.asPath;
          router.replace(`/admin/login?redirect=${encodeURIComponent(currentPath)}`);
        }
      }
    };

    checkAuthentication();

    return () => {
      mounted = false;
    };
  }, [router]);

  // Show loading state while checking authentication
  if (authStatus === 'checking') {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column'
      }}>
        <div style={{ marginBottom: '1rem' }}>Checking authentication...</div>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Show error state if authentication check failed
  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        padding: '2rem'
      }}>
        <h2>Authentication Error</h2>
        <p>Failed to verify authentication: {error}</p>
        <button 
          onClick={() => window.location.href = '/admin/login'}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '1rem'
          }}
        >
          Go to Login
        </button>
      </div>
    );
  }

  // Show unauthenticated state (mainly for development)
  if (authStatus === 'unauthenticated') {
    if (fallback) {
      return fallback;
    }
    
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column'
      }}>
        <h2>Authentication Required</h2>
        <p>Please log in to access the admin panel.</p>
        <button 
          onClick={() => router.push('/admin/login')}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '1rem'
          }}
        >
          Go to Login
        </button>
      </div>
    );
  }

  // User is authenticated, render children
  return children;
}
