# Marketing Segments Authentication Fix

## Problem Summary

The Ocean Soul Sparkles website was experiencing 401 Unauthorized errors for marketing segment API endpoints in production:

1. `GET /api/marketing/segments?limit=100` - returning 401 Unauthorized
2. `POST /api/marketing/segments` - returning 401 Unauthorized  
3. `POST /api/marketing/segment-builder/preview` - returning 401 Unauthorized

## Root Cause Analysis

The issue was identified as a **function signature mismatch** in the authentication system:

- **Problem**: Marketing segment endpoints were calling `getCurrentUser(req)` with a request parameter
- **Issue**: The `getCurrentUser` function in `lib/supabase.js` doesn't accept any parameters - it's designed for client-side use
- **Result**: Authentication was failing because the function couldn't extract JWT tokens from the request headers

## Solution Implemented

### 1. Created New Server-Side Authentication Function

Added `getCurrentUserFromRequest(req)` function to `lib/supabase.js`:

```javascript
export const getCurrentUserFromRequest = async (req) => {
  // Extract token from Authorization header
  let token = null;
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7).trim();
  }
  
  // Fallback to cookies if no header token
  if (!token && req.cookies) {
    token = req.cookies['sb-access-token'] || 
            req.cookies['supabase-auth-token'] ||
            req.cookies['oss_auth_token'];
  }
  
  // Validate token and return user/role
  const result = await getCurrentUserWithToken(token);
  return { user: result.user, role: result.role };
}
```

### 2. Updated All Affected Endpoints

Updated the following endpoints to use `getCurrentUserFromRequest(req)`:

#### Marketing Endpoints
- `/api/marketing/segments/index.js`
- `/api/marketing/segments/[id].js`
- `/api/marketing/segment-builder/preview.js`
- `/api/marketing/campaigns/index.js`
- `/api/marketing/campaigns/[id].js`
- `/api/marketing/automations/index.js`
- `/api/marketing/automations/[id].js`
- `/api/marketing/automations/[id]/trigger.js`
- `/api/marketing/campaigns/[id]/messages/index.js`
- `/api/marketing/campaigns/[id]/messages/[messageId].js`
- `/api/marketing/campaigns/[id]/messages/[messageId]/send.js`
- `/api/marketing/templates/index.js`

#### Analytics Endpoints
- `/api/analytics/marketing.js`
- `/api/analytics/engagement.js`
- `/api/analytics/campaigns.js`

#### Customer Endpoints
- `/api/customers/index.js`
- `/api/customers/[id].js`
- `/api/customers/[id]/gdpr-delete.js`

#### Notification Endpoints
- `/api/notifications/send.js`

### 3. Authentication Flow

The new authentication flow works as follows:

1. **Token Extraction**: Extract JWT token from `Authorization: Bearer <token>` header
2. **Fallback**: If no header token, check cookies as fallback
3. **Validation**: Use `getCurrentUserWithToken()` to validate token with admin client
4. **Role Check**: Verify user has `admin` or `staff` role
5. **Return**: Return user and role information or throw error

## Key Features

- ✅ **Server-side compatible**: Properly extracts tokens from HTTP requests
- ✅ **Multiple token sources**: Supports Authorization header and cookie fallback
- ✅ **Robust validation**: Uses admin client for secure token verification
- ✅ **Role-based access**: Enforces admin/staff role requirements
- ✅ **Error handling**: Provides clear error messages for debugging
- ✅ **Logging**: Includes request tracking for monitoring

## Testing

To test the fix:

1. **Run the test script**:
   ```bash
   node test-marketing-auth-fix.js
   ```

2. **Test in browser**:
   - Login to admin dashboard
   - Navigate to Marketing > Segments
   - Verify no 401 errors in console
   - Test creating/viewing segments

3. **API Testing**:
   ```bash
   # Get auth token from browser sessionStorage
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://localhost:3000/api/marketing/segments?limit=100
   ```

## Deployment Notes

- ✅ **Backward Compatible**: Existing authentication patterns remain unchanged
- ✅ **No Breaking Changes**: All existing endpoints continue to work
- ✅ **Production Ready**: Includes proper error handling and logging

## Monitoring

After deployment, monitor:

1. **Server logs** for authentication success/failure messages
2. **Browser console** for any remaining 401 errors
3. **Marketing dashboard** functionality
4. **API response times** for any performance impact

## Files Modified

- `lib/supabase.js` - Added `getCurrentUserFromRequest` function
- 19 API endpoint files - Updated to use new authentication function

The fix resolves the 401 Unauthorized errors by providing proper server-side JWT token extraction and validation for all marketing segment and related API endpoints.
