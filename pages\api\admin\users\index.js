/**
 * API endpoint for admin user management
 * This endpoint handles CRUD operations for users
 */

import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminUserRequest } from '@/lib/admin-user-auth';

export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Processing ${req.method} request to /api/admin/users`);
  
  // Authenticate the request
  const { authorized, error, user, role } = await authenticateAdminUserRequest(req);
  
  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }
  
  console.log(`[${requestId}] Authentication successful for admin: ${user.email}`);
  
  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {    // Get the Supabase admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      throw new Error('Failed to initialize admin client');
    }
    
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getUsers(req, res, adminClient, requestId);
      case 'POST':
        return await createUser(req, res, adminClient, requestId);
      case 'PUT':
        return await updateUser(req, res, adminClient, requestId);
      case 'DELETE':
        return await deleteUser(req, res, adminClient, requestId);
    }
  } catch (error) {
    console.error(`[${requestId}] Error in user management:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    });
  }
}

/**
 * Get users with optional filtering
 */
async function getUsers(req, res, adminClient, requestId) {
  try {
    const { search, role, limit = 50, offset = 0 } = req.query;
    
    console.log(`[${requestId}] Fetching users with filters:`, { search, role });
    
    // Start with a base query
    let query = adminClient
      .from('user_roles')
      .select(`
        id,
        role,
        users:id (
          email,
          created_at,
          last_sign_in_at
        )
      `)
      .order('created_at', { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);
    
    // Apply filters
    if (role) {
      query = query.eq('role', role);
    }
    
    // Execute the query
    const { data, error, count } = await query;
    
    if (error) {
      console.error(`[${requestId}] Error fetching users:`, error);
      throw error;
    }
    
    // Format the response
    const users = data.map(item => ({
      id: item.id,
      email: item.users?.email,
      role: item.role,
      created_at: item.users?.created_at,
      last_sign_in_at: item.users?.last_sign_in_at
    }));
    
    // Apply search filter in memory if needed
    let filteredUsers = users;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = users.filter(user => 
        user.email?.toLowerCase().includes(searchLower) ||
        user.role?.toLowerCase().includes(searchLower)
      );
    }
    
    console.log(`[${requestId}] Found ${filteredUsers.length} users`);
    
    return res.status(200).json({
      users: filteredUsers,
      total: count || filteredUsers.length
    });
  } catch (error) {
    console.error(`[${requestId}] Error in GET users:`, error);
    throw error;
  }
}

/**
 * Create a new user
 */
async function createUser(req, res, adminClient, requestId) {
  try {
    const { email, password, role = 'user' } = req.body;
    
    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Email and password are required'
      });
    }
    
    console.log(`[${requestId}] Creating new user with email: ${email}`);
    
    // Create the user in Supabase Auth
    const { data: userData, error: userError } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    });
    
    if (userError) {
      console.error(`[${requestId}] Error creating user:`, userError);
      return res.status(400).json({
        error: 'User creation failed',
        message: userError.message
      });
    }
    
    const userId = userData.user.id;
    
    // Assign role to the user
    const { error: roleError } = await adminClient
      .from('user_roles')
      .insert([{
        id: userId,
        role
      }]);
    
    if (roleError) {
      console.error(`[${requestId}] Error assigning role:`, roleError);
      return res.status(400).json({
        error: 'Role assignment failed',
        message: roleError.message
      });
    }
    
    console.log(`[${requestId}] User created successfully: ${userId}`);
    
    return res.status(201).json({
      user: {
        id: userId,
        email,
        role
      }
    });
  } catch (error) {
    console.error(`[${requestId}] Error in POST user:`, error);
    throw error;
  }
}

/**
 * Update an existing user
 */
async function updateUser(req, res, adminClient, requestId) {
  try {
    const { id, role, password } = req.body;
    
    // Validate required fields
    if (!id) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'User ID is required'
      });
    }
    
    console.log(`[${requestId}] Updating user: ${id}`);
    
    // Update password if provided
    if (password) {
      const { error: passwordError } = await adminClient.auth.admin.updateUserById(
        id,
        { password }
      );
      
      if (passwordError) {
        console.error(`[${requestId}] Error updating password:`, passwordError);
        return res.status(400).json({
          error: 'Password update failed',
          message: passwordError.message
        });
      }
    }
    
    // Update role if provided
    if (role) {
      const { error: roleError } = await adminClient
        .from('user_roles')
        .upsert([{
          id,
          role
        }]);
      
      if (roleError) {
        console.error(`[${requestId}] Error updating role:`, roleError);
        return res.status(400).json({
          error: 'Role update failed',
          message: roleError.message
        });
      }
    }
    
    console.log(`[${requestId}] User updated successfully: ${id}`);
    
    return res.status(200).json({
      success: true,
      user: { id }
    });
  } catch (error) {
    console.error(`[${requestId}] Error in PUT user:`, error);
    throw error;
  }
}

/**
 * Delete a user
 */
async function deleteUser(req, res, adminClient, requestId) {
  try {
    const { id } = req.query;
    
    // Validate required fields
    if (!id) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'User ID is required'
      });
    }
    
    console.log(`[${requestId}] Deleting user: ${id}`);
    
    // Delete the user
    const { error } = await adminClient.auth.admin.deleteUser(id);
    
    if (error) {
      console.error(`[${requestId}] Error deleting user:`, error);
      return res.status(400).json({
        error: 'User deletion failed',
        message: error.message
      });
    }
    
    console.log(`[${requestId}] User deleted successfully: ${id}`);
    
    return res.status(200).json({
      success: true
    });
  } catch (error) {
    console.error(`[${requestId}] Error in DELETE user:`, error);
    throw error;
  }
}
