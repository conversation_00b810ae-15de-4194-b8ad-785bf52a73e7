import { useEffect } from 'react';
import { PayPalButtons, usePayPalScriptReducer } from '@paypal/react-paypal-js';
import styles from '@/styles/PayPalPaymentButton.module.css';

/**
 * PayPalPaymentButton component for PayPal checkout
 * 
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Function} props.onCancel - Function to call on payment cancellation
 * @param {Object} props.orderDetails - Order details for PayPal
 * @returns {JSX.Element}
 */
const PayPalPaymentButton = ({ 
  amount, 
  currency = 'AUD', 
  onSuccess, 
  onError, 
  onCancel,
  orderDetails = {}
}) => {
  const [{ isPending }] = usePayPalScriptReducer();
  
  // Create order for PayPal
  const createOrder = (data, actions) => {
    return actions.order.create({
      purchase_units: [
        {
          amount: {
            currency_code: currency,
            value: amount.toString(),
          },
          description: orderDetails.description || 'OceanSoulSparkles Purchase',
          // Add more details as needed
        },
      ],
      application_context: {
        shipping_preference: 'SET_PROVIDED_ADDRESS',
        user_action: 'PAY_NOW',
      }
    });
  };
  
  // Handle approved payment
  const onApprove = (data, actions) => {
    return actions.order.capture().then((details) => {
      // Call onSuccess with payment details
      onSuccess({
        paymentId: details.id,
        paymentStatus: details.status,
        payerEmail: details.payer.email_address,
        paymentDetails: details
      });
    });
  };
  
  return (
    <div className={styles.paypalButtonContainer}>
      {isPending ? (
        <div className={styles.loading}>Loading PayPal...</div>
      ) : (
        <PayPalButtons
          style={{
            color: 'gold',
            shape: 'rect',
            label: 'pay',
            height: 50,
          }}
          createOrder={createOrder}
          onApprove={onApprove}
          onError={(err) => onError(err)}
          onCancel={() => onCancel()}
        />
      )}
    </div>
  );
};

export default PayPalPaymentButton;
