import { useState } from 'react'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/ResendWelcomeEmail.module.css'

export default function ResendWelcomeEmailButton({ 
  user, 
  onSuccess = () => {}, 
  disabled = false,
  size = 'medium',
  variant = 'primary'
}) {
  const [loading, setLoading] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)

  const handleResendEmail = async () => {
    if (!user?.id) {
      toast.error('User ID is required')
      return
    }

    setLoading(true)
    setShowConfirmation(false)

    try {
      console.log('Resending welcome email for user:', user.id)

      const response = await fetch('/api/admin/users/resend-welcome', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          forceResend: true
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success(`Welcome email resent successfully to ${result.data.email}`)
        console.log('Welcome email resend result:', result.data)
        
        // Call success callback
        onSuccess({
          userId: user.id,
          email: result.data.email,
          tokenGenerated: result.data.tokenGenerated,
          emailSent: result.data.emailSent,
          applicationId: result.data.applicationId
        })
      } else {
        const errorMessage = result.error || 'Failed to resend welcome email'
        toast.error(errorMessage)
        console.error('Welcome email resend failed:', result)
      }
    } catch (error) {
      console.error('Error resending welcome email:', error)
      toast.error('An unexpected error occurred while resending the email')
    } finally {
      setLoading(false)
    }
  }

  const handleConfirmResend = () => {
    setShowConfirmation(true)
  }

  const handleCancelResend = () => {
    setShowConfirmation(false)
  }

  // Don't show button for non-artist/braider users
  if (!user?.role || !['artist', 'braider'].includes(user.role)) {
    return null
  }

  return (
    <div className={styles.container}>
      {!showConfirmation ? (
        <button
          onClick={handleConfirmResend}
          disabled={disabled || loading}
          className={`${styles.resendButton} ${styles[size]} ${styles[variant]} ${loading ? styles.loading : ''}`}
          title={`Resend welcome email with new application token to ${user.email}`}
        >
          {loading ? (
            <>
              <div className={styles.spinner}></div>
              <span>Sending...</span>
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
                <path d="M12 13l-8-7"/>
                <path d="M20 6l-8 7"/>
              </svg>
              <span>Resend Welcome Email</span>
            </>
          )}
        </button>
      ) : (
        <div className={styles.confirmationDialog}>
          <div className={styles.confirmationContent}>
            <div className={styles.confirmationHeader}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="12"/>
                <line x1="12" y1="16" x2="12.01" y2="16"/>
              </svg>
              <h4>Resend Welcome Email?</h4>
            </div>
            
            <div className={styles.confirmationBody}>
              <p>This will:</p>
              <ul>
                <li>Generate a new secure application token</li>
                <li>Invalidate any existing tokens for this user</li>
                <li>Send a fresh welcome email to <strong>{user.email}</strong></li>
                <li>Reset application status to "pending"</li>
              </ul>
              <p className={styles.warning}>
                <strong>Note:</strong> Any previous application links will stop working.
              </p>
            </div>

            <div className={styles.confirmationActions}>
              <button
                onClick={handleCancelResend}
                className={styles.cancelButton}
                disabled={loading}
              >
                Cancel
              </button>
              <button
                onClick={handleResendEmail}
                className={styles.confirmButton}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className={styles.spinner}></div>
                    Sending...
                  </>
                ) : (
                  'Yes, Resend Email'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
