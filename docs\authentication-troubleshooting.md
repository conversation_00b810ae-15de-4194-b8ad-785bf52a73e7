# Ocean Soul Sparkles Authentication Troubleshooting Guide

This guide will help you diagnose and fix authentication issues in the Ocean Soul Sparkles admin panel.

## Table of Contents

1. [Understanding the Authentication System](#understanding-the-authentication-system)
2. [Common Authentication Issues](#common-authentication-issues)
3. [Step-by-Step Troubleshooting](#step-by-step-troubleshooting)
4. [Using the Troubleshooting Scripts](#using-the-troubleshooting-scripts)
5. [Manual Fixes](#manual-fixes)

## Understanding the Authentication System

The Ocean Soul Sparkles admin panel uses Supabase for authentication with the following components:

- **Supabase Auth**: Handles user registration, login, and session management
- **User Roles Table**: Stores admin/staff role assignments in the `user_roles` table
- **Row Level Security (RLS)**: Controls access to database tables based on user roles
- **Supabase Client**: The unified client that manages authentication state and API requests
- **Token Storage**: Uses browser storage (localStorage, sessionStorage, cookies) to persist authentication

## Common Authentication Issues

1. **Missing User Role**: User exists in Supabase but doesn't have an entry in the `user_roles` table
2. **Incorrect Role Assignment**: User has a role but it's not 'admin' or 'staff'
3. **Token Storage Issues**: Conflicting or corrupted tokens in browser storage
4. **Session Management Problems**: Session not being properly maintained between requests
5. **RLS Policy Configuration**: Incorrect database permissions preventing access

## Step-by-Step Troubleshooting

### 1. Verify Environment Variables

First, ensure your `.env.local` file has the correct Supabase configuration:

```
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. Clear Authentication Data

Authentication issues are often caused by stale or conflicting tokens. Clear all authentication data:

1. Run the clear-auth-data script: `node scripts/clear-auth-data.js`
2. Copy the generated code and paste it into your browser console
3. Refresh the page and try logging in again

### 3. Verify Admin User Configuration

Ensure your admin user is properly configured in Supabase:

1. Run the verify-admin-user script: `node scripts/verify-admin-user.js`
2. Enter your admin email when prompted
3. The script will check if the user exists and has the correct role
4. If issues are found, the script will offer to fix them

### 4. Test Authentication Flow

Test the complete authentication flow to identify where issues might be occurring:

1. Run the test-auth script: `node scripts/test-auth.js`
2. Enter your admin credentials when prompted
3. The script will test each step of the authentication process
4. Review the results to identify any specific issues

## Using the Troubleshooting Scripts

This repository includes several scripts to help diagnose and fix authentication issues:

### Clear Authentication Data

```bash
node scripts/clear-auth-data.js
```

This script generates code to clear all authentication data from browser storage. Copy the output and paste it into your browser console.

### Verify Admin User

```bash
node scripts/verify-admin-user.js
```

This script checks if your admin user is properly configured in Supabase and fixes any issues it finds.

### Test Authentication

```bash
node scripts/test-auth.js
```

This script tests the complete authentication flow and identifies where issues might be occurring.

## Manual Fixes

If the scripts don't resolve your issues, try these manual fixes:

### Fix 1: Manually Add User Role

If your user doesn't have the correct role, you can add it directly in the Supabase dashboard:

1. Go to the [Supabase Dashboard](https://app.supabase.io)
2. Select your project
3. Go to SQL Editor
4. Run the following SQL (replace USER_ID with your actual user ID):

```sql
INSERT INTO user_roles (id, role)
VALUES ('USER_ID', 'admin')
ON CONFLICT (id) DO UPDATE SET role = 'admin';
```

### Fix 2: Check RLS Policies

Verify that your RLS policies are correctly configured:

1. Go to the Supabase Dashboard
2. Select your project
3. Go to Authentication > Policies
4. Ensure the `user_roles` table has appropriate policies
5. Verify that other tables have policies that check user roles

### Fix 3: Rebuild and Restart

Sometimes a clean rebuild can resolve authentication issues:

```bash
# Stop the server
# Clear node_modules and build artifacts
rm -rf node_modules/.cache
rm -rf .next

# Reinstall dependencies
npm install

# Start the server
npm run dev
```

### Fix 4: Check for Browser Issues

Some authentication issues can be browser-specific:

1. Try using a different browser
2. Try using incognito/private browsing mode
3. Clear browser cache and cookies

## Still Having Issues?

If you're still experiencing authentication problems after trying all these steps, the issue might be more complex. Consider:

1. Checking Supabase logs for any authentication errors
2. Reviewing network requests in browser dev tools to see where authentication is failing
3. Adding additional logging to the authentication flow
4. Checking for any recent changes to the authentication system

For further assistance, please contact the development team.
