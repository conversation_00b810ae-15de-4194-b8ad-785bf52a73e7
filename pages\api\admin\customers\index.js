import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * API endpoint for admin customer management
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customers API endpoint called: ${req.method} ${req.url}`);

  // Log headers for debugging
  console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));
  if (req.headers.authorization) {
    console.log(`[${requestId}] Authorization header present`);
  }
  if (req.headers.cookie) {
    console.log(`[${requestId}] Cookie header present`);
  }
  if (req.headers['x-auth-token'] || req.headers['X-Auth-Token']) {
    console.log(`[${requestId}] Auth token header present`);
  }

  // Authenticate request using our robust auth module
  const authResult = await authenticateAdminRequest(req);
  const { authorized, error, user, role } = authResult;

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error');
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`);

  // Check if the request method is allowed
  if (!['GET', 'POST', 'PUT', 'DELETE'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // GET - Fetch customers
    if (req.method === 'GET') {
      console.log(`[${requestId}] Processing GET request for customers`);

      // Get query parameters
      const { search, limit, offset, sortBy, sortOrder } = req.query;

      // Start building the query using Supabase directly
      let query = supabaseAdmin
        .from('customers')
        .select('id, name, email, phone, created_at, updated_at', { count: 'exact' });

      // Apply search filter if provided
      if (search) {
        query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,phone.ilike.%${search}%`);
      }

      // Apply sorting if provided
      if (sortBy) {
        const order = sortOrder === 'desc' ? { ascending: false } : { ascending: true };
        query = query.order(sortBy, order);
      } else {
        // Default sort by name
        query = query.order('name', { ascending: true });
      }

      // Apply pagination if provided
      if (limit) {
        query = query.limit(parseInt(limit, 10));
      }

      if (offset) {
        query = query.range(parseInt(offset, 10), parseInt(offset, 10) + (parseInt(limit, 10) || 10) - 1);
      }

      try {
        // Execute the query
        const { data, error, count } = await query;

        if (error) {
          console.error(`[${requestId}] Error fetching customers:`, error);
          throw error;
        }

        console.log(`[${requestId}] Successfully fetched ${data?.length || 0} customers`);

        // Set appropriate cache headers for customers data
        setCacheHeaders(res, 'customers', 'GET', true, req.query);

        // Return data wrapped in customers property for compatibility with BookingForm
        // This ensures the BookingForm component can properly destructure the response
        return res.status(200).json({
          customers: data || [],
          total: count || 0,
          requestId
        });
      } catch (queryError) {
        console.error(`[${requestId}] Query execution error:`, queryError);
        throw queryError;
      }
    }

    // POST - Create a new customer
    else if (req.method === 'POST') {
      const { name, email, phone } = req.body;

      // Validate required fields
      if (!name || !email) {
        return res.status(400).json({ error: 'Name and email are required' });
      }

      // Create the customer using Supabase directly
      const { data, error } = await supabaseAdmin
        .from('customers')
        .insert([{ name, email, phone: phone || '' }])
        .select();

      if (error) {
        console.error('Error creating customer:', error);
        throw error;
      }

      return res.status(201).json({ customer: data[0] });
    }

    // PUT - Update an existing customer
    else if (req.method === 'PUT') {
      const { id } = req.query;
      const { name, email, phone } = req.body;

      // Validate customer ID
      if (!id) {
        return res.status(400).json({ error: 'Customer ID is required' });
      }

      // Update the customer using Supabase directly
      const { data, error } = await supabaseAdmin
        .from('customers')
        .update({ name, email, phone })
        .eq('id', id)
        .select();

      if (error) {
        console.error('Error updating customer:', error);
        throw error;
      }

      return res.status(200).json({ customer: data[0] });
    }

    // DELETE - Delete a customer
    else if (req.method === 'DELETE') {
      const { id } = req.query;

      // Validate customer ID
      if (!id) {
        return res.status(400).json({ error: 'Customer ID is required' });
      }

      // Delete the customer using Supabase directly
      const { error } = await supabaseAdmin
        .from('customers')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting customer:', error);
        throw error;
      }

      return res.status(200).json({ success: true });
    }
  } catch (error) {
    console.error(`[${requestId}] Customer API Error:`, error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    let errorMessage = 'Failed to process customer request';

    if (error.message && error.message.includes('timeout')) {
      statusCode = 504; // Gateway Timeout
      errorMessage = 'Request timed out while processing';
    } else if (error.message && error.message.includes('not found')) {
      statusCode = 404; // Not Found
      errorMessage = 'Requested resource not found';
    } else if (error.message && (
      error.message.includes('permission') ||
      error.message.includes('access') ||
      error.message.includes('unauthorized')
    )) {
      statusCode = 403; // Forbidden
      errorMessage = 'Permission denied';
    } else if (error.message && error.message.includes('validation')) {
      statusCode = 400; // Bad Request
      errorMessage = 'Validation error';
    }

    return res.status(statusCode).json({
      error: errorMessage,
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString(),
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}
