.bookingDetails {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.closeButton:hover {
  color: #343a40;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 15px 20px;
  font-size: 0.9rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 15px 20px;
  font-size: 0.9rem;
}

.bookingInfo {
  padding: 20px;
}

.serviceInfo {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.serviceColor {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 10px;
}

.serviceName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.statusBadge {
  margin-bottom: 20px;
}

.status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.confirmed {
  background-color: #d4edda;
  color: #155724;
}

.pending {
  background-color: #fff3cd;
  color: #856404;
}

.canceled {
  background-color: #f8d7da;
  color: #721c24;
}

.inProgress {
  background-color: #cce5ff;
  color: #004085;
}

.completed {
  background-color: #d1e7dd;
  color: #0f5132;
}

.noShow {
  background-color: #e2e3e5;
  color: #383d41;
}

.rescheduled {
  background-color: #e0cffc;
  color: #5a2c82;
}

.statusManagement {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.infoSection {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.infoSection:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.infoSection h3 {
  font-size: 1rem;
  color: #495057;
  margin-top: 0;
  margin-bottom: 10px;
}

.infoRow {
  display: flex;
  margin-bottom: 8px;
}

.infoRow:last-child {
  margin-bottom: 0;
}

.infoLabel {
  width: 80px;
  font-weight: 500;
  color: #6c757d;
}

.infoValue {
  flex: 1;
  color: #212529;
}

.actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.editButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton:hover {
  background-color: #5a0b9d;
}

.cancelBookingButton {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancelBookingButton:hover {
  background-color: #c82333;
}

.cancelBookingButton:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Form styles */
.form {
  padding: 20px;
}

.formGroup {
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancelButton {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.cancelButton:hover {
  background-color: #e9ecef;
}

.saveButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover {
  background-color: #5a0b9d;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}
