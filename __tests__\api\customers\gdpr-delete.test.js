import handler from '@/pages/api/customers/[id]/gdpr-delete';
import { 
  createApiMocks, 
  mockAuthenticatedUser, 
  mockUnauthenticatedUser,
  mockUnauthorizedUser,
  mockSupabaseResponse,
  resetMocks
} from '../../../utils/api-test-utils';

describe('API: /api/customers/[id]/gdpr-delete', () => {
  beforeEach(() => {
    resetMocks();
  });

  describe('Authentication', () => {
    it('should return 403 if user is not authenticated', async () => {
      // Mock unauthenticated user
      mockUnauthenticatedUser();

      // Create mock request and response
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(403);
      expect(res._getJSONData()).toEqual({ error: 'Authorization failed' });
    });

    it('should return 403 if user is not an admin', async () => {
      // Mock authenticated staff user (non-admin)
      mockAuthenticatedUser({ id: 'staff-user' }, 'staff');

      // Create mock request and response
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(403);
      expect(res._getJSONData()).toEqual({ error: 'Forbidden' });
    });

    it('should allow authenticated admin users', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase responses
      const supabase = require('@/lib/supabase').supabase;
      
      // First call for customer
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { email: '<EMAIL>' }, 
          error: null 
        })
      }));
      
      // Second call for update
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: {}, error: null })
      }));
      
      // Third call for preferences deletion
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: {}, error: null })
      }));
      
      // Fourth call for logging
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        insert: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: {}, error: null })
      }));

      // Create mock request and response
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: '1' },
        body: { reason: 'Customer request' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
    });
  });

  describe('POST /api/customers/[id]/gdpr-delete', () => {
    it('should anonymize customer data', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase responses
      const supabase = require('@/lib/supabase').supabase;
      
      // First call for customer
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { email: '<EMAIL>' }, 
          error: null 
        })
      }));
      
      // Second call for update
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: {}, error: null })
      }));
      
      // Third call for preferences deletion
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: {}, error: null })
      }));
      
      // Fourth call for logging
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        insert: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: {}, error: null })
      }));

      // Create mock request with reason
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: '1' },
        body: { reason: 'Customer request' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getJSONData()).toEqual({ message: 'Customer data anonymized successfully' });
      
      // Verify that update was called with anonymized data
      expect(supabase.update).toHaveBeenCalledWith(expect.objectContaining({
        name: 'Anonymized User',
        email: expect.stringContaining('anonymized-'),
        phone: null,
        address: null,
        city: null,
        state: null,
        postal_code: null,
        country: null,
        notes: 'Data deleted per GDPR request',
        marketing_consent: false
      }));
      
      // Verify that preferences were deleted
      expect(supabase.delete).toHaveBeenCalled();
      
      // Verify that the action was logged
      expect(supabase.insert).toHaveBeenCalledWith([expect.objectContaining({
        customer_id: '1',
        original_email: '<EMAIL>',
        reason: 'Customer request'
      })]);
    });

    it('should return 404 if customer is not found', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response for non-existent customer
      mockSupabaseResponse({ data: null, error: null });

      // Create mock request and response
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: 'non-existent' },
        body: { reason: 'Customer request' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(404);
      expect(res._getJSONData()).toEqual({ error: 'Customer not found' });
    });

    it('should handle database errors during customer lookup', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error during lookup' } });

      // Create mock request and response
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: '1' },
        body: { reason: 'Customer request' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Failed to process GDPR deletion request' });
    });

    it('should handle database errors during update', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase responses
      const supabase = require('@/lib/supabase').supabase;
      
      // First call for customer
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: { email: '<EMAIL>' }, 
          error: null 
        })
      }));
      
      // Second call for update - with error
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ 
          data: null, 
          error: { message: 'Database error during update' } 
        })
      }));

      // Create mock request and response
      const { req, res } = createApiMocks({
        method: 'POST',
        query: { id: '1' },
        body: { reason: 'Customer request' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Failed to process GDPR deletion request' });
    });
  });

  describe('Method Not Allowed', () => {
    it('should return 405 for unsupported methods', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Create mock request with unsupported method
      const { req, res } = createApiMocks({
        method: 'GET',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(405);
      expect(res._getJSONData()).toEqual({ error: 'Method not allowed' });
    });
  });
});
