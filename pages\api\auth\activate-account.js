import { createClient } from '@supabase/supabase-js'

/**
 * API endpoint for activating user accounts
 * POST /api/auth/activate-account - Activate account with password
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  
  console.log(`[${requestId}] Account activation API called`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { token, password } = req.body

    if (!token || !password) {
      return res.status(400).json({ 
        success: false,
        error: 'Activation token and password are required' 
      })
    }

    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 8 characters long'
      })
    }

    console.log(`[${requestId}] Activating account with token: ${token.substring(0, 8)}...`)

    // Initialize Supabase admin client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    )

    // Validate the activation token (reuse validation logic)
    const { data: tokenData, error: tokenError } = await supabase
      .from('application_tokens')
      .select(`
        *,
        artist_braider_applications (
          id,
          user_id,
          application_type,
          status
        )
      `)
      .eq('token', token)
      .eq('token_type', 'account_activation')
      .single()

    if (tokenError || !tokenData) {
      console.log(`[${requestId}] Token not found or invalid`)
      return res.status(400).json({
        success: false,
        error: 'Invalid activation token'
      })
    }

    // Check if token has expired
    const now = new Date()
    const expiresAt = new Date(tokenData.expires_at)
    
    if (now > expiresAt) {
      console.log(`[${requestId}] Token has expired`)
      return res.status(400).json({
        success: false,
        error: 'Activation token has expired'
      })
    }

    // Check if token has already been used
    if (tokenData.used_at) {
      console.log(`[${requestId}] Token has already been used`)
      return res.status(400).json({
        success: false,
        error: 'Activation token has already been used'
      })
    }

    const application = tokenData.artist_braider_applications
    if (!application || application.status !== 'approved') {
      console.log(`[${requestId}] Application not approved`)
      return res.status(400).json({
        success: false,
        error: 'Application is not approved'
      })
    }

    // Get user information
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(application.user_id)
    
    if (userError || !userData.user) {
      console.error(`[${requestId}] Error fetching user data:`, userError)
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch user information'
      })
    }

    console.log(`[${requestId}] Setting password for user: ${userData.user.email}`)

    // Update user password using Supabase Admin API
    const { data: updateResult, error: updateError } = await supabase.auth.admin.updateUserById(
      application.user_id,
      {
        password: password,
        email_confirm: true, // Mark email as confirmed
        user_metadata: {
          account_activated: true,
          activated_at: new Date().toISOString(),
          role: application.application_type
        }
      }
    )

    if (updateError) {
      console.error(`[${requestId}] Error updating user password:`, updateError)
      return res.status(500).json({
        success: false,
        error: 'Failed to set password'
      })
    }

    console.log(`[${requestId}] ✅ Password set successfully`)

    // Mark token as used
    const { error: tokenUpdateError } = await supabase
      .from('application_tokens')
      .update({
        used_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenData.id)

    if (tokenUpdateError) {
      console.error(`[${requestId}] Error marking token as used:`, tokenUpdateError)
      // Continue anyway - password was set successfully
    }

    // Update application status to activated
    const { error: appUpdateError } = await supabase
      .from('artist_braider_applications')
      .update({
        status: 'activated',
        updated_at: new Date().toISOString()
      })
      .eq('id', application.id)

    if (appUpdateError) {
      console.error(`[${requestId}] Error updating application status:`, appUpdateError)
      // Continue anyway - account was activated successfully
    }

    // Update user role to ensure it's set correctly
    const { error: roleUpdateError } = await supabase
      .from('user_roles')
      .upsert({
        id: application.user_id,
        role: application.application_type,
        updated_at: new Date().toISOString()
      })

    if (roleUpdateError) {
      console.error(`[${requestId}] Error updating user role:`, roleUpdateError)
      // Continue anyway - account was activated successfully
    }

    console.log(`[${requestId}] ✅ Account activation completed successfully`)

    return res.status(200).json({
      success: true,
      message: 'Account activated successfully',
      user: {
        id: application.user_id,
        email: userData.user.email,
        role: application.application_type
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Error activating account:`, error)
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    })
  }
}
