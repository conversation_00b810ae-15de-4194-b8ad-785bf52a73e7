.servicesSection {
  padding: 6rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.categoryFilters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.categoryButton {
  padding: 0.8rem 1.5rem;
  background: transparent;
  border: 2px solid #4ECDC4;
  border-radius: 30px;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.categoryButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.categoryButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.2), transparent);
  transition: all 0.6s ease;
}

.categoryButton:hover::before {
  left: 100%;
}

.activeCategory {
  background-color: #4ECDC4;
  color: white;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.serviceCardsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-top: 2rem;
}

.cardWrapper {
  perspective: 1000px;
  height: 100%;
}

.serviceCard {
  border-radius: 16px;
  height: 480px;
  width: 100%;
  transition: all 0.3s ease;
  position: relative;
  transform-style: preserve-3d;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.serviceCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.cardInner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
  border-radius: 16px;
  overflow: hidden;
}

.cardWrapper:hover .cardInner {
  transform: rotateY(180deg);
}

.cardFront, .cardBack {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 16px;
  overflow: hidden;
}

.cardFront {
  display: flex;
  flex-direction: column;
  background-color: white;
}

.cardBack {
  background-color: #4ECDC4;
  color: white;
  transform: rotateY(180deg); /* Flip card */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.cardBackContent {
  transform: scaleX(-1); /* Counter-flip the content to make it readable */
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cardImage {
  height: 220px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.imageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%;
  opacity: 0.8;
}

.cardContent {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cardTitle {
  font-size: 1.5rem;
  margin: 0 0 1rem;
  position: relative;
  display: inline-block;
  font-weight: 700;
}

.cardTitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 80px;
  height: 3px;
  background: #4ECDC4;
  border-radius: 2px;
}

.cardDescription {
  color: #555;
  line-height: 1.6;
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.flipPrompt {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #4ECDC4;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: auto;
}

.flipPrompt svg {
  width: 18px;
  height: 18px;
  animation: bounceRight 1.5s infinite;
}

@keyframes bounceRight {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(5px);
  }
}

.pricingTitle {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  position: relative;
  font-weight: 700;
  color: white;
  margin-top: 0;
}

.pricingTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: white;
  border-radius: 2px;
}

.pricingList {
  list-style: none;
  padding: 0;
  margin: 2rem 0;
  width: 100%;
}

.pricingItem {
  display: flex;
  justify-content: space-between;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.pricingItem:last-child {
  border-bottom: none;
}

.pricingItemTitle {
  font-weight: 500;
  text-align: left;
  font-size: 1rem;
}

.pricingItemPrice {
  font-weight: 700;
  font-size: 1rem;
}

.bookButton {
  background-color: white;
  color: #333;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  margin-top: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 80%;
  font-size: 1.1rem;
}

.bookButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.flipBack {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 2rem;
  cursor: pointer;
}

.flipBack svg {
  width: 18px;
  height: 18px;
  animation: bounceLeft 1.5s infinite;
}

@keyframes bounceLeft {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-5px);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .serviceCardsGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
  }

  .serviceCard {
    height: 450px;
  }

  .cardImage {
    height: 200px;
  }
}

@media (max-width: 768px) {
  .servicesSection {
    padding: 4rem 1.5rem;
  }

  .categoryButton {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
  }

  .serviceCardsGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .cardTitle {
    font-size: 1.3rem;
  }

  .cardDescription {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .serviceCardsGrid {
    grid-template-columns: 1fr;
  }

  .categoryFilters {
    gap: 0.6rem;
    margin-bottom: 2rem;
  }

  .categoryButton {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .serviceCard {
    height: 420px;
  }

  .cardImage {
    height: 180px;
  }

  .pricingList {
    margin: 1.5rem 0;
  }
}

/* Touch device overrides to make cards work on mobile */
@media (hover: none) {
  .cardWrapper:active .cardInner {
    transform: rotateY(180deg);
  }

  /* Add a tap indicator for mobile users */
  .flipPrompt, .flipBack {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }
}