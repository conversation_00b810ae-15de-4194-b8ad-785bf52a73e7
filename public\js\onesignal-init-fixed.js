/**
 * OneSignal Web Push Notifications Script
 * Enhanced with authentication checks and error handling
 */

(function() {
  // Only run in browser environment
  if (typeof window === 'undefined') return;

  // Configuration
  const ONESIGNAL_APP_ID = window.__ONESIGNAL_APP_ID__ || "************************************";
  const ONESIGNAL_SAFARI_WEB_ID = window.__ONESIGNAL_SAFARI_WEB_ID__ || "web.onesignal.auto.************************************";
  const DEBUG = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // In development/local environment, log but continue with initialization
  // This allows testing the integration without showing notifications
  if (DEBUG) {
    console.log('OneSignal running in development/local environment - notifications disabled');
    // Continue with initialization but don't show actual notifications
  }

  // Helper to check if user is authenticated
  function isAuthenticated() {
    try {
      // Check for auth token in storage
      const hasSessionStorage = typeof sessionStorage !== 'undefined' &&
        (sessionStorage.getItem('oss_auth_token_cache') || sessionStorage.getItem('oss_auth_token'));

      const hasLocalStorage = typeof localStorage !== 'undefined' &&
        localStorage.getItem('oss_auth_token');

      // Check for auth cookie
      const hasCookie = document.cookie.indexOf('oss_auth_token=') !== -1;

      return hasSessionStorage || hasLocalStorage || hasCookie;
    } catch (e) {
      console.error('Error checking authentication status:', e);
      return false;
    }
  }

  // Initialize OneSignal
  function initializeOneSignal() {
    try {
      // Load OneSignal script
      window.OneSignal = window.OneSignal || [];

      // Add initialization settings
      window.OneSignal.push(function() {
        window.OneSignal.init({
          appId: ONESIGNAL_APP_ID,
          safari_web_id: ONESIGNAL_SAFARI_WEB_ID,
          notifyButton: {
            enable: true,
            size: 'medium',
            theme: 'default',
            position: 'bottom-right',
            offset: {
              bottom: '20px',
              right: '20px',
            },
            prenotify: true,
            showCredit: false,
            text: {
              'tip.state.unsubscribed': 'Subscribe to notifications',
              'tip.state.subscribed': 'You\'re subscribed to notifications',
              'tip.state.blocked': 'You\'ve blocked notifications',
              'message.prenotify': 'Click to subscribe to notifications',
              'message.action.subscribed': 'Thanks for subscribing!',
              'message.action.resubscribed': 'You\'re subscribed to notifications',
              'message.action.unsubscribed': 'You won\'t receive notifications again',
              'dialog.main.title': 'Manage Site Notifications',
              'dialog.main.button.subscribe': 'SUBSCRIBE',
              'dialog.main.button.unsubscribe': 'UNSUBSCRIBE',
              'dialog.blocked.title': 'Unblock Notifications',
              'dialog.blocked.message': 'Follow these instructions to allow notifications:'
            }
          },
          welcomeNotification: {
            title: 'Welcome to OceanSoulSparkles',
            message: 'Thanks for subscribing to notifications!',
            url: 'https://www.oceansoulsparkles.com.au'
          },
          promptOptions: {
            slidedown: {
              prompts: [
                {
                  type: "category",
                  autoPrompt: false,
                  text: {
                    actionMessage: "Would you like to receive notifications about:",
                    acceptButton: "Allow",
                    cancelButton: "Cancel",
                    negativeUpdateButton: "No Thanks",
                    positiveUpdateButton: "Yes Please",
                    updateMessage: "Update your notification preferences:"
                  },
                  categories: [
                    {
                      tag: "promotions",
                      label: "Promotions & Discounts"
                    },
                    {
                      tag: "booking_updates",
                      label: "Booking Updates"
                    },
                    {
                      tag: "new_products",
                      label: "New Products"
                    }
                  ]
                }
              ]
            }
          }
        });

        // Dispatch custom event when OneSignal is initialized
        const event = new CustomEvent('onesignal:initialized');
        document.dispatchEvent(event);

        // Set global flag for initialization status
        window.__ONESIGNAL_INITIALIZED__ = true;

        console.log('OneSignal initialized successfully');
      });
    } catch (error) {
      console.error('Error initializing OneSignal:', error);
    }
  }

  // Wait for authentication before initializing
  function checkAuthAndInitialize() {
    // If already authenticated, initialize immediately
    if (isAuthenticated()) {
      initializeOneSignal();
      return;
    }

    // Otherwise, wait for authentication events
    // Check periodically for authentication
    const authCheckInterval = setInterval(function() {
      if (isAuthenticated()) {
        clearInterval(authCheckInterval);
        initializeOneSignal();
      }
    }, 2000); // Check every 2 seconds

    // Set a timeout to prevent checking indefinitely
    setTimeout(function() {
      clearInterval(authCheckInterval);
      // If still not authenticated after timeout, initialize anyway
      // for public pages that don't require authentication
      if (!window.__ONESIGNAL_INITIALIZED__) {
        initializeOneSignal();
      }
    }, 30000); // 30 second timeout
  }

  // Start the initialization process
  checkAuthAndInitialize();
})();
