import { supabaseAdmin } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';

/**
 * Booking Communications API
 *
 * GET /api/admin/bookings/{bookingId}/communications
 * POST /api/admin/bookings/{bookingId}/communications
 *
 * Manages communication history for a specific booking
 * Used by EnhancedBookingDetails component for communications tab
 */
export default async function handler(req, res) {
  // Generate request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Booking communications request started`);

  try {
    // Verify authentication
    const authResult = await authTokenManager.verifyToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract booking ID from URL
    const { bookingId } = req.query;

    if (!bookingId) {
      return res.status(400).json({ error: 'Booking ID is required' });
    }

    // Handle GET request - fetch communications
    if (req.method === 'GET') {
      return await getCommunications(bookingId, requestId, res);
    }

    // Handle POST request - create new communication
    else if (req.method === 'POST') {
      return await createCommunication(bookingId, req.body, authResult.user, requestId, res);
    }

    else {
      console.log(`[${requestId}] Method ${req.method} not allowed`);
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in booking communications API:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while managing communications'
    });
  }
}

// Get communications for a booking
async function getCommunications(bookingId, requestId, res) {
  console.log(`[${requestId}] Fetching communications for booking: ${bookingId}`);

  // First verify the booking exists and get customer info
  const { data: booking, error: bookingError } = await supabaseAdmin
    .from('bookings')
    .select(`
      id,
      customer_id,
      customers:customer_id (
        id,
        name,
        email,
        phone
      )
    `)
    .eq('id', bookingId)
    .single();

  if (bookingError) {
    if (bookingError.code === 'PGRST116') {
      return res.status(404).json({ error: 'Booking not found' });
    }
    console.error(`[${requestId}] Error fetching booking:`, bookingError);
    throw bookingError;
  }

  // Fetch communications for this booking
  const { data: communications, error: communicationsError } = await supabaseAdmin
    .from('booking_communications')
    .select(`
      id,
      booking_id,
      customer_id,
      communication_type,
      direction,
      subject,
      content,
      status,
      sent_by,
      external_id,
      metadata,
      created_at
    `)
    .eq('booking_id', bookingId)
    .order('created_at', { ascending: false });

  if (communicationsError) {
    console.error(`[${requestId}] Error fetching communications:`, communicationsError);
    throw communicationsError;
  }

  console.log(`[${requestId}] Found ${communications?.length || 0} communications for booking`);

  // Transform the data for frontend consumption
  const transformedCommunications = communications.map(comm => ({
    id: comm.id,
    booking_id: comm.booking_id,
    customer_id: comm.customer_id,
    communication_type: comm.communication_type,
    direction: comm.direction,
    subject: comm.subject,
    content: comm.content,
    status: comm.status,
    external_id: comm.external_id,
    metadata: comm.metadata,
    created_at: comm.created_at,
    sent_by: comm.sent_by,
    sent_by_email: 'System', // TODO: Look up user email from sent_by ID if needed
    // Add computed fields
    is_recent: new Date() - new Date(comm.created_at) < 24 * 60 * 60 * 1000, // Within 24 hours
    formatted_date: new Date(comm.created_at).toLocaleString()
  }));

  // Calculate communication statistics
  const stats = {
    total: communications.length,
    byType: communications.reduce((acc, comm) => {
      acc[comm.communication_type] = (acc[comm.communication_type] || 0) + 1;
      return acc;
    }, {}),
    byDirection: communications.reduce((acc, comm) => {
      acc[comm.direction] = (acc[comm.direction] || 0) + 1;
      return acc;
    }, {}),
    lastCommunication: communications.length > 0 ? communications[0].created_at : null
  };

  const response = {
    booking: {
      id: booking.id,
      customer_id: booking.customer_id,
      customer_name: booking.customers?.name,
      customer_email: booking.customers?.email,
      customer_phone: booking.customers?.phone
    },
    communications: transformedCommunications,
    stats: stats
  };

  console.log(`[${requestId}] Communications fetched successfully`);
  return res.status(200).json(response);
}

// Create a new communication record
async function createCommunication(bookingId, body, user, requestId, res) {
  console.log(`[${requestId}] Creating communication for booking: ${bookingId}`);

  const {
    communication_type,
    direction,
    subject,
    content,
    status = 'sent',
    external_id,
    metadata = {}
  } = body;

  // Validate required fields
  if (!communication_type || !direction || !content) {
    return res.status(400).json({
      error: 'Missing required fields: communication_type, direction, content'
    });
  }

  // Validate enum values
  const validTypes = ['email', 'sms', 'phone', 'in_person', 'system'];
  const validDirections = ['inbound', 'outbound'];
  const validStatuses = ['pending', 'sent', 'delivered', 'failed', 'read'];

  if (!validTypes.includes(communication_type)) {
    return res.status(400).json({
      error: `Invalid communication_type. Must be one of: ${validTypes.join(', ')}`
    });
  }

  if (!validDirections.includes(direction)) {
    return res.status(400).json({
      error: `Invalid direction. Must be one of: ${validDirections.join(', ')}`
    });
  }

  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      error: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
    });
  }

  // Get booking and customer info
  const { data: booking, error: bookingError } = await supabaseAdmin
    .from('bookings')
    .select('id, customer_id')
    .eq('id', bookingId)
    .single();

  if (bookingError) {
    if (bookingError.code === 'PGRST116') {
      return res.status(404).json({ error: 'Booking not found' });
    }
    throw bookingError;
  }

  // Create the communication record
  const communicationData = {
    booking_id: bookingId,
    customer_id: booking.customer_id,
    communication_type,
    direction,
    subject,
    content,
    status,
    sent_by: user.id,
    external_id,
    metadata
  };

  const { data: communication, error: createError } = await supabaseAdmin
    .from('booking_communications')
    .insert([communicationData])
    .select(`
      id,
      booking_id,
      customer_id,
      communication_type,
      direction,
      subject,
      content,
      status,
      sent_by,
      external_id,
      metadata,
      created_at
    `)
    .single();

  if (createError) {
    console.error(`[${requestId}] Error creating communication:`, createError);
    throw createError;
  }

  console.log(`[${requestId}] Communication created successfully:`, communication.id);

  return res.status(201).json({
    communication: {
      ...communication,
      sent_by_email: user.email || 'Unknown',
      is_recent: true,
      formatted_date: new Date(communication.created_at).toLocaleString()
    }
  });
}
