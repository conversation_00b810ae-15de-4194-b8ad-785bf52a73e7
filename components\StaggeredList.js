import { useEffect, useRef, useState, Children, cloneElement } from 'react';

/**
 * StaggeredList component that animates its children with a staggered delay
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child elements to animate
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.threshold - Intersection observer threshold (0-1)
 * @param {number} props.baseDelay - Base delay before starting animations (ms)
 * @param {number} props.staggerDelay - Delay between each child animation (ms)
 * @param {boolean} props.once - Whether to animate only once or every time element enters viewport
 * @returns {JSX.Element}
 */
const StaggeredList = ({ 
  children, 
  className = '', 
  threshold = 0.1,
  baseDelay = 0,
  staggerDelay = 100,
  once = true,
  ...props 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const listRef = useRef(null);
  
  useEffect(() => {
    const list = listRef.current;
    
    if (!list) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (once) {
            observer.unobserve(list);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      { 
        threshold,
        rootMargin: '0px 0px -50px 0px' // Trigger slightly before element enters viewport
      }
    );
    
    observer.observe(list);
    
    return () => {
      if (list) {
        observer.unobserve(list);
      }
    };
  }, [threshold, once]);
  
  // Clone children and add staggered animation classes and delays
  const staggeredChildren = Children.map(children, (child, index) => {
    if (!child) return null;
    
    const delay = baseDelay + (index * staggerDelay);
    
    return cloneElement(child, {
      className: `stagger-item ${isVisible ? 'visible' : ''} ${child.props.className || ''}`,
      style: { 
        ...child.props.style,
        transitionDelay: `${delay}ms` 
      }
    });
  });
  
  return (
    <div 
      ref={listRef}
      className={className}
      {...props}
    >
      {staggeredChildren}
    </div>
  );
};

export default StaggeredList;
