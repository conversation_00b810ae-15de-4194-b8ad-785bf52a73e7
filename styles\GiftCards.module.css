.main {
  width: 100%;
  overflow-x: hidden;
  background-image: url('/background.png'); /* Added pink cloud background */
  background-size: cover; /* Ensure background covers the area */
  background-position: center; /* Center the background image */
  background-attachment: fixed; /* Keep background fixed during scroll */
}

/* Hero Section */
.heroSection {
  position: relative;
  height: 60vh;
  min-height: 500px;
  width: 100%;
  background-image: url('/images/gift-card-hero.jpg');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.8) 0%, rgba(26, 115, 232, 0.8) 100%);
  z-index: 1;
}

.heroContent {
  position: relative;
  z-index: 2;
  max-width: 900px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.heroSubtitle {
  font-size: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Section Titles */
.sectionTitle {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #333;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: #1A73E8;
  border-radius: 2px;
}

/* How It Works Section */
.howItWorksSection {
  padding: 6rem 2rem;
  background-color: rgba(249, 249, 249, 0.7); /* Translucent light gray */
  text-align: center;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.stepsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.step {
  flex: 1;
  min-width: 220px;
  max-width: 280px;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.85); /* Translucent white */
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.step:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stepNumber {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4ECDC4 0%, #1A73E8 100%);
  color: white;
  font-size: 1.8rem;
  font-weight: 700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 8px 20px rgba(26, 115, 232, 0.3);
}

.step h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.step p {
  color: #666;
  line-height: 1.6;
}

/* FAQ Section */
.faqSection {
  padding: 6rem 2rem;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.7); /* Translucent white */
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.faqContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.faqItem {
  text-align: left;
  padding: 2rem;
  background-color: rgba(249, 249, 249, 0.8); /* Translucent light gray */
  border-radius: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.faqItem:hover {
  background-color: rgba(245, 245, 245, 0.9); /* Slightly more opaque on hover */
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.faqItem h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
  position: relative;
  padding-left: 1.5rem;
}

.faqItem h3::before {
  content: '•';
  color: #1A73E8;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1.5rem;
  line-height: 1;
}

.faqItem p {
  color: #666;
  line-height: 1.6;
}

/* CTA Section */
.ctaSection {
  padding: 6rem 2rem;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.7) 0%, rgba(26, 115, 232, 0.7) 100%); /* More translucent */
  color: white;
  text-align: center;
  backdrop-filter: blur(5px); /* Add blur effect for better readability */
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaContent h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.ctaContent p {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
}

.ctaButton {
  display: inline-block;
  background-color: white;
  color: #1A73E8;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.ctaButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

/* Media Queries */
@media (max-width: 1200px) {
  .heroTitle {
    font-size: 3.5rem;
  }

  .heroSubtitle {
    font-size: 1.4rem;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .faqContainer {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}

@media (max-width: 992px) {
  .heroTitle {
    font-size: 3rem;
  }

  .heroSubtitle {
    font-size: 1.3rem;
  }

  .stepsContainer {
    flex-wrap: wrap;
  }

  .step {
    min-width: 240px;
  }

  .faqContainer {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  .ctaContent h2 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .heroSection {
    min-height: 400px;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.2rem;
  }

  .sectionTitle {
    font-size: 2.2rem;
  }

  .faqContainer {
    grid-template-columns: 1fr;
  }

  .ctaContent h2 {
    font-size: 2.2rem;
  }

  .ctaButton {
    padding: 0.9rem 2rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .heroSection {
    min-height: 350px;
  }

  .heroTitle {
    font-size: 2.2rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .howItWorksSection, .faqSection, .ctaSection {
    padding: 4rem 1.5rem;
  }

  .step {
    padding: 1.5rem;
  }

  .stepNumber {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .step h3 {
    font-size: 1.3rem;
  }

  .faqItem {
    padding: 1.5rem;
  }

  .ctaContent h2 {
    font-size: 1.8rem;
  }

  .ctaContent p {
    font-size: 1.1rem;
  }

  .ctaButton {
    padding: 0.8rem 1.8rem;
    font-size: 1rem;
  }
}