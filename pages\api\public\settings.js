import { getAdminClient } from '@/lib/supabase';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * Public API endpoint for fetching limited settings data
 * This endpoint provides only public-safe settings for use on public pages
 * No authentication required - only returns non-sensitive settings
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Default public settings to return if database operations fail
    const defaultPublicSettings = {
      site_name: 'Ocean Soul Sparkles',
      site_description: 'Face painting and body art services',
      contact_email: '<EMAIL>',
      contact_phone: '',
      business_hours: 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed',
      enable_online_bookings: 'true',
      enable_online_payments: 'true',
      theme_primary_color: '#3788d8',
      theme_secondary_color: '#2c3e50',
      theme_accent_color: '#e74c3c',
      google_search_console_verification: '',
      google_analytics_id: '',
      facebook_pixel_id: ''
    };

    try {
      // Get admin client for database access
      const supabaseAdmin = getAdminClient();

      if (!supabaseAdmin) {
        console.warn('Supabase admin client not available, using default settings');
        setCacheHeaders(res, 'settings', 'GET', false, req.query);
        return res.status(200).json(defaultPublicSettings);
      }

      // Fetch settings from database
      const { data: settings, error } = await supabaseAdmin
        .from('settings')
        .select('key, value');

      if (error) {
        console.error('Error fetching settings from database:', error);
        setCacheHeaders(res, 'settings', 'GET', false, req.query);
        return res.status(200).json(defaultPublicSettings);
      }

      // Convert settings array to object
      const settingsObject = {};
      if (settings && Array.isArray(settings)) {
        settings.forEach(setting => {
          settingsObject[setting.key] = setting.value;
        });
      }

      // Merge with defaults and filter to only include public-safe settings
      const publicSettings = {
        site_name: settingsObject.site_name || defaultPublicSettings.site_name,
        site_description: settingsObject.site_description || defaultPublicSettings.site_description,
        contact_email: settingsObject.contact_email || defaultPublicSettings.contact_email,
        contact_phone: settingsObject.contact_phone || defaultPublicSettings.contact_phone,
        business_hours: settingsObject.business_hours || defaultPublicSettings.business_hours,
        enable_online_bookings: settingsObject.enable_online_bookings || defaultPublicSettings.enable_online_bookings,
        enable_online_payments: settingsObject.enable_online_payments || defaultPublicSettings.enable_online_payments,
        theme_primary_color: settingsObject.theme_primary_color || defaultPublicSettings.theme_primary_color,
        theme_secondary_color: settingsObject.theme_secondary_color || defaultPublicSettings.theme_secondary_color,
        theme_accent_color: settingsObject.theme_accent_color || defaultPublicSettings.theme_accent_color,
        google_search_console_verification: settingsObject.google_search_console_verification || '',
        google_analytics_id: settingsObject.google_analytics_id || '',
        facebook_pixel_id: settingsObject.facebook_pixel_id || ''
      };

      // Set appropriate cache headers for public settings
      setCacheHeaders(res, 'settings', 'GET', false, req.query);

      return res.status(200).json(publicSettings);

    } catch (dbError) {
      console.error('Database error fetching settings:', dbError);

      // Return default settings on database error
      setCacheHeaders(res, 'settings', 'GET', false, req.query);
      return res.status(200).json(defaultPublicSettings);
    }

  } catch (error) {
    console.error('Error in public settings API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
