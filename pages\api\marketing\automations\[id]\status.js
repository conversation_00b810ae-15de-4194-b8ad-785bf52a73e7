import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Only allow PATCH requests
  if (req.method !== 'PATCH') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query
  const { is_active } = req.body

  // Validate is_active parameter
  if (typeof is_active !== 'boolean') {
    return res.status(400).json({ error: 'is_active must be a boolean value' })
  }

  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Update automation status
    const { data, error } = await client
      .from('marketing_automations')
      .update({
        is_active,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Automation not found or you do not have permission to update it' })
    }

    return res.status(200).json({
      id: data[0].id,
      is_active: data[0].is_active
    })
  } catch (error) {
    console.error('Error updating automation status:', error)
    return res.status(500).json({ error: error.message })
  }
}
