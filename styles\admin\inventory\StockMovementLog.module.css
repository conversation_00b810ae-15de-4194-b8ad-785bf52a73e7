.stockMovementLog {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
}

.filterGroup {
  flex: 1;
  min-width: 200px;
}

.searchInput,
.filterSelect,
.dateInput {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.searchInput:focus,
.filterSelect:focus,
.dateInput:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.dateSeparator {
  display: inline-block;
  margin: 0 8px;
  color: #666;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a90e2;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.errorMessage {
  color: #d32f2f;
  margin-bottom: 16px;
  text-align: center;
}

.retryButton {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #3a7bc8;
}

.noData {
  padding: 40px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.tableContainer {
  width: 100%;
  overflow-x: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.movementsTable {
  width: 100%;
  border-collapse: collapse;
}

.movementsTable th,
.movementsTable td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.movementsTable th {
  font-weight: 600;
  color: #555;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  position: relative;
}

.movementsTable th:hover {
  background-color: #f5f5f5;
}

.sortIndicator {
  margin-left: 4px;
  font-size: 12px;
}

.movementsTable td {
  font-size: 14px;
  color: #333;
}

.transactionType {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.restock {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.sale {
  background-color: #e3f2fd;
  color: #1565c0;
}

.adjustment {
  background-color: #fff8e1;
  color: #f57f17;
}

.return {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.damaged {
  background-color: #ffebee;
  color: #c62828;
}

.lost {
  background-color: #fafafa;
  color: #616161;
}

.positive {
  color: #2e7d32;
  font-weight: 500;
}

.negative {
  color: #c62828;
  font-weight: 500;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.paginationButton {
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.paginationButton:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  font-size: 14px;
  color: #666;
}
