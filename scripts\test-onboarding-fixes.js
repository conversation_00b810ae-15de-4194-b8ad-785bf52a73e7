#!/usr/bin/env node

/**
 * Test Onboarding Fixes Script
 * Simplified testing for critical fixes
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function testOnboardingFixes() {
  console.log('🧪 Testing Critical Onboarding Fixes\n')

  let allPassed = true
  const results = []

  // Test 1: Database Token Retrieval
  console.log('1. Testing database token retrieval...')
  try {
    const testUserId = 'd29e3ede-8e90-49ee-8f10-af8539a60ccd'
    
    const { data: tokens, error: tokenError } = await supabase
      .from('application_tokens')
      .select('id, token, user_id, created_at')
      .eq('user_id', testUserId)

    if (tokenError) {
      console.error('❌ Token retrieval failed:', tokenError.message)
      allPassed = false
      results.push({ test: 'token_retrieval', passed: false, error: tokenError.message })
    } else {
      console.log(`✅ Token retrieval working (found ${tokens?.length || 0} tokens)`)
      results.push({ test: 'token_retrieval', passed: true, count: tokens?.length || 0 })
    }
  } catch (error) {
    console.error('❌ Token retrieval test failed:', error.message)
    allPassed = false
    results.push({ test: 'token_retrieval', passed: false, error: error.message })
  }

  // Test 2: Token Generation
  console.log('\n2. Testing token generation...')
  try {
    // Generate a simple token without using RPC
    const newToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    
    const { data: insertResult, error: insertError } = await supabase
      .from('application_tokens')
      .insert({
        token: `test_${newToken}`,
        user_id: 'd29e3ede-8e90-49ee-8f10-af8539a60ccd',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select()

    if (insertError) {
      console.error('❌ Token generation failed:', insertError.message)
      allPassed = false
      results.push({ test: 'token_generation', passed: false, error: insertError.message })
    } else {
      console.log(`✅ Token generation working`)
      results.push({ test: 'token_generation', passed: true })
      
      // Clean up test token
      await supabase
        .from('application_tokens')
        .delete()
        .eq('token', `test_${newToken}`)
    }
  } catch (error) {
    console.error('❌ Token generation test failed:', error.message)
    allPassed = false
    results.push({ test: 'token_generation', passed: false, error: error.message })
  }

  // Test 3: Nodemailer Import
  console.log('\n3. Testing nodemailer import...')
  try {
    const nodemailer = await import('nodemailer')
    console.log('   Available exports:', Object.keys(nodemailer))
    
    // Try different ways to access createTransport (correct nodemailer function name)
    let createTransporter = null

    if (nodemailer.default && typeof nodemailer.default.createTransport === 'function') {
      createTransporter = nodemailer.default.createTransport
      console.log('   ✅ Found createTransport in default export')
    } else if (typeof nodemailer.createTransport === 'function') {
      createTransporter = nodemailer.createTransport
      console.log('   ✅ Found createTransport as named export')
    } else if (typeof nodemailer.default === 'function') {
      createTransporter = nodemailer.default
      console.log('   ✅ Using default export as createTransport')
    }

    if (!createTransporter) {
      throw new Error('createTransport not found in any export pattern')
    }

    // Test creating a transporter (don't send email)
    const testTransporter = createTransporter({
      service: 'gmail',
      auth: {
        user: '<EMAIL>',
        pass: 'test-password'
      }
    })

    if (testTransporter) {
      console.log('✅ Nodemailer import and createTransport working')
      results.push({ test: 'nodemailer_import', passed: true })
    }
  } catch (error) {
    console.error('❌ Nodemailer import test failed:', error.message)
    allPassed = false
    results.push({ test: 'nodemailer_import', passed: false, error: error.message })
  }

  // Test 4: Email Configuration
  console.log('\n4. Testing email configuration access...')
  try {
    // Test environment variables
    const hasGmailEnv = !!(process.env.GMAIL_SMTP_USER && process.env.GMAIL_SMTP_APP_PASSWORD)
    const hasWorkspaceEnv = !!(process.env.WORKSPACE_SMTP_USER && process.env.WORKSPACE_SMTP_PASSWORD)
    const hasOneSignalEnv = !!(process.env.ONESIGNAL_APP_ID && process.env.ONESIGNAL_REST_API_KEY)
    
    console.log(`   Gmail ENV: ${hasGmailEnv ? '✅ Available' : '❌ Missing'}`)
    console.log(`   Workspace ENV: ${hasWorkspaceEnv ? '✅ Available' : '❌ Missing'}`)
    console.log(`   OneSignal ENV: ${hasOneSignalEnv ? '✅ Available' : '❌ Missing'}`)
    
    // Test database settings
    const { data: dbSettings, error: dbError } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value')
      .in('setting_key', [
        'gmail_smtp_user',
        'gmail_smtp_password',
        'workspace_smtp_user',
        'workspace_smtp_password',
        'onesignal_app_id',
        'onesignal_rest_api_key'
      ])

    const hasDbSettings = !dbError && dbSettings && dbSettings.length > 0
    console.log(`   Database Settings: ${hasDbSettings ? '✅ Available' : '❌ Missing'}`)
    
    if (hasGmailEnv || hasWorkspaceEnv || hasOneSignalEnv || hasDbSettings) {
      console.log('✅ Email configuration accessible')
      results.push({ test: 'email_configuration', passed: true })
    } else {
      console.log('⚠️  No email configuration found')
      results.push({ test: 'email_configuration', passed: false, error: 'No email services configured' })
    }
  } catch (error) {
    console.error('❌ Email configuration test failed:', error.message)
    results.push({ test: 'email_configuration', passed: false, error: error.message })
  }

  // Test 5: Application Table Structure
  console.log('\n5. Testing application table structure...')
  try {
    const { data: tableInfo, error: tableError } = await supabase
      .from('artist_braider_applications')
      .select('*')
      .limit(1)

    if (tableError && !tableError.message.includes('0 rows')) {
      console.error('❌ Application table test failed:', tableError.message)
      allPassed = false
      results.push({ test: 'application_table', passed: false, error: tableError.message })
    } else {
      console.log('✅ Application table structure working')
      results.push({ test: 'application_table', passed: true })
    }
  } catch (error) {
    console.error('❌ Application table test failed:', error.message)
    allPassed = false
    results.push({ test: 'application_table', passed: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Test Results Summary:')
  const passedCount = results.filter(r => r.passed).length
  const totalCount = results.length
  
  console.log(`   Passed tests: ${passedCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.test}${error}`)
  })

  console.log(`\n   Overall status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

  if (allPassed) {
    console.log('\n🎉 All critical fixes are working!')
    console.log('\n📋 Next Steps:')
    console.log('   1. Deploy the fixes to production')
    console.log('   2. Configure email credentials in /admin/secure-settings')
    console.log('   3. Test creating a new Artist/Braider user')
    console.log('   4. Verify the complete onboarding workflow')
  } else {
    console.log('\n🔧 Issues to Address:')
    results.filter(r => !r.passed).forEach(result => {
      console.log(`   - ${result.test}: ${result.error}`)
    })
  }

  return allPassed
}

// Run the test script
testOnboardingFixes()
  .then(success => {
    if (success) {
      console.log('\n✅ Onboarding fixes test completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Onboarding fixes test completed with failures!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Test script error:', error)
    process.exit(1)
  })
