/* Client Logos Section */
.clientLogosSection {
  padding: 2rem 2rem 3rem;
  background-color: #ffffff;
  text-align: center;
  margin-bottom: 2rem;
}

.clientLogosContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

.clientLogo {
  width: 180px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.clientLogo:hover {
  filter: grayscale(0);
  opacity: 1;
}

.clientLogo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Responsive styles */
@media (max-width: 768px) {
  .clientLogosContainer {
    gap: 2rem;
  }

  .clientLogo {
    width: 150px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .clientLogosContainer {
    gap: 1.5rem;
  }

  .clientLogo {
    width: 120px;
    height: 70px;
  }
}
