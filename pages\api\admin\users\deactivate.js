import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * User Deactivation API
 * Allows admins to deactivate user accounts instead of deleting them
 * This preserves data integrity while preventing user access
 */
export default async function handler(req, res) {
  // Only allow PATCH requests
  if (req.method !== 'PATCH') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      console.error('Deactivate user API: Authentication failed:', error?.message || 'Unknown error')
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    console.log('Deactivate user API: Authentication successful. User:', user?.email, 'Role:', role)
    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ error: 'Unauthorized. Only administrators and developers can deactivate users.' })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    const { userId, isActive, reason } = req.body

    // Validate required fields
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ error: 'isActive must be a boolean value' })
    }

    // Check if user exists in auth.users (the authoritative source)
    let userToUpdate = null
    try {
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers()
      if (authError) {
        console.error('Error fetching auth users:', authError)
        return res.status(500).json({ error: 'Failed to fetch user data' })
      }

      userToUpdate = authData.users.find(u => u.id === userId)
      if (!userToUpdate) {
        console.error('User not found in auth.users:', userId)
        return res.status(404).json({ error: 'User not found' })
      }
    } catch (error) {
      console.error('Error checking user existence:', error)
      return res.status(500).json({ error: 'Failed to verify user existence' })
    }

    // Get user role to check permissions
    const { data: userRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', userId)
      .single()

    // Note: roleError is acceptable here as user might not have a role record
    if (roleError && roleError.code !== 'PGRST116') {
      console.error('Error fetching user role:', roleError)
      return res.status(500).json({ error: 'Failed to fetch user role' })
    }

    // Prevent deactivation of dev users unless current user is also dev
    if (userRole?.role === 'dev' && role !== 'dev') {
      return res.status(403).json({ error: 'Only developers can deactivate other developer accounts' })
    }

    // Prevent self-deactivation
    if (userId === user.id) {
      return res.status(403).json({ error: 'You cannot deactivate your own account' })
    }

    // Update user profile active status
    const { data: updatedProfile, error: profileError } = await adminClient
      .from('user_profiles')
      .update({
        is_active: isActive,
        updated_at: new Date().toISOString(),
        notes: reason ? `${isActive ? 'Reactivated' : 'Deactivated'} by ${user.email}: ${reason}` : `${isActive ? 'Reactivated' : 'Deactivated'} by ${user.email}`
      })
      .eq('id', userId)
      .select()
      .single()

    if (profileError) {
      // If profile doesn't exist, create one
      if (profileError.code === 'PGRST116') {
        const { data: newProfile, error: createError } = await adminClient
          .from('user_profiles')
          .insert([
            {
              id: userId,
              name: userToUpdate.user_metadata?.name || userToUpdate.email,
              is_active: isActive,
              notes: reason ? `${isActive ? 'Reactivated' : 'Deactivated'} by ${user.email}: ${reason}` : `${isActive ? 'Reactivated' : 'Deactivated'} by ${user.email}`,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ])
          .select()
          .single()

        if (createError) {
          console.error('Error creating user profile:', createError)
          return res.status(500).json({ error: 'Failed to create user profile' })
        }

        updatedProfile = newProfile
      } else {
        console.error('Error updating user profile:', profileError)
        return res.status(500).json({ error: 'Failed to update user profile' })
      }
    }

    // If deactivating, also update any pending applications
    if (!isActive && (userRole?.role === 'artist' || userRole?.role === 'braider')) {
      try {
        const { error: appUpdateError } = await adminClient
          .from('artist_braider_applications')
          .update({
            status: 'rejected',
            reviewed_by: user.id,
            reviewed_at: new Date().toISOString(),
            review_notes: `Application automatically rejected due to account deactivation. ${reason || ''}`
          })
          .eq('user_id', userId)
          .eq('application_type', userRole.role)
          .eq('status', 'pending')

        if (appUpdateError) {
          console.warn('Warning: Could not update application status:', appUpdateError)
          // Continue anyway - profile deactivation is more important
        }
      } catch (appError) {
        console.warn('Warning: Error updating application status:', appError)
        // Continue anyway
      }
    }

    // Log the deactivation activity
    try {
      await adminClient
        .from('user_activity_log')
        .insert([
          {
            user_id: userId,
            activity_type: isActive ? 'user_reactivated' : 'user_deactivated',
            activity_description: `User account ${isActive ? 'reactivated' : 'deactivated'} by ${user.email}. ${reason ? `Reason: ${reason}` : ''}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          }
        ])
    } catch (activityError) {
      console.error('Error logging deactivation activity:', activityError)
      // Continue anyway - activity logging is not critical
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: `User ${userToUpdate.email} ${isActive ? 'reactivated' : 'deactivated'} successfully`,
      user: {
        id: userId,
        email: userToUpdate.email,
        is_active: isActive,
        role: userRole?.role || 'unknown'
      },
      profile: updatedProfile
    })

  } catch (error) {
    console.error('Unexpected error in user deactivation:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
