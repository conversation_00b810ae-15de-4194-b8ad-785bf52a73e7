/**
 * Enhanced Admin Panel Testing Script
 * Tests the new tabbed settings interface and connection testing functionality
 */

// Test the enhanced settings form functionality
async function testEnhancedAdminPanel() {
  console.log('🧪 Testing Enhanced Admin Panel...');
  
  const tests = [
    {
      name: 'Settings Form Component Load',
      test: () => {
        // Check if the settings form loads with all tabs
        const tabButtons = document.querySelectorAll('[class*="tab"]');
        return tabButtons.length >= 6; // Should have 6 tabs
      }
    },
    {
      name: 'Tab Navigation',
      test: () => {
        // Check if tab navigation works
        const tabs = ['general', 'payments', 'google', 'seo', 'email', 'advanced'];
        return tabs.every(tab => {
          const tabButton = document.querySelector(`[onclick*="${tab}"]`);
          return tabButton !== null;
        });
      }
    },
    {
      name: 'Square API Fields',
      test: () => {
        // Check if Square API fields are present
        const squareFields = [
          'square_application_id',
          'square_access_token',
          'square_environment',
          'square_location_id'
        ];
        return squareFields.every(field => {
          const input = document.querySelector(`[name="${field}"]`);
          return input !== null;
        });
      }
    },
    {
      name: 'Google Integration Fields',
      test: () => {
        // Check if Google integration fields are present
        const googleFields = [
          'google_analytics_measurement_id',
          'google_business_profile_id',
          'google_maps_api_key'
        ];
        return googleFields.every(field => {
          const input = document.querySelector(`[name="${field}"]`);
          return input !== null;
        });
      }
    },
    {
      name: 'Email Settings Fields',
      test: () => {
        // Check if email settings fields are present
        const emailFields = [
          'smtp_host',
          'smtp_port',
          'smtp_username',
          'smtp_password'
        ];
        return emailFields.every(field => {
          const input = document.querySelector(`[name="${field}"]`);
          return input !== null;
        });
      }
    },
    {
      name: 'Connection Test Buttons',
      test: () => {
        // Check if connection test buttons are present
        const testButtons = document.querySelectorAll('button[onclick*="test"]');
        return testButtons.length >= 3; // Square, Email, Analytics
      }
    }
  ];
  
  const results = tests.map(test => {
    try {
      const passed = test.test();
      console.log(`${passed ? '✅' : '❌'} ${test.name}: ${passed ? 'PASS' : 'FAIL'}`);
      return { name: test.name, passed };
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
      return { name: test.name, passed: false, error: error.message };
    }
  });
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Enhanced admin panel is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the implementation.');
  }
  
  return results;
}

// API endpoint testing
async function testConnectionAPI() {
  console.log('\n🔌 Testing Connection API...');
  
  const testData = {
    square: {
      type: 'square',
      settings: {
        square_application_id: 'test_app_id',
        square_access_token: 'test_token',
        square_environment: 'sandbox'
      }
    },
    email: {
      type: 'email',
      settings: {
        smtp_host: 'smtp.gmail.com',
        smtp_port: '587',
        smtp_username: '<EMAIL>',
        smtp_password: 'test_password',
        smtp_encryption: 'tls'
      }
    }
  };
  
  for (const [testName, testConfig] of Object.entries(testData)) {
    try {
      const response = await fetch('/api/admin/test-connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testConfig)
      });
      
      const result = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${testName} API test: Response received`);
        console.log(`   Status: ${result.success ? 'Success' : 'Failed'}`);
        console.log(`   Message: ${result.message}`);
      } else {
        console.log(`❌ ${testName} API test: HTTP ${response.status}`);
        console.log(`   Error: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ ${testName} API test: Network error - ${error.message}`);
    }
  }
}

// Run tests when page loads
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      testEnhancedAdminPanel();
      testConnectionAPI();
    }, 2000); // Wait 2 seconds for components to load
  });
}

// Export for Node.js testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testEnhancedAdminPanel,
    testConnectionAPI
  };
}

console.log('Enhanced Admin Panel Testing Script Loaded 🚀');
