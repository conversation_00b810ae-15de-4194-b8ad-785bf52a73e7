# Ocean Soul Sparkles User Deletion Restrictions & Account Management

## Overview

The Ocean Soul Sparkles user management system now implements strict deletion restrictions to protect active users and maintain data integrity. This document outlines the new policies and procedures for user account management.

## User Activity Status Classification

### **Fully Active Users** (Cannot be deleted by admins)
A user is considered "fully active" if they meet ANY of the following criteria:

1. **Has logged in at least once** (`login_count > 0`)
2. **Has an approved application** (for artist/braider roles)
3. **Has successful subscription status** (`subscription_status = 'successful'`)
4. **Has an active profile with elevated role** (non-'user' role + `is_active = true`)
5. **Account older than 7 days with profile** (indicates completed signup process)

### **Inactive/Incomplete Users** (Can be safely deleted)
Users who do NOT meet any of the above criteria, typically:
- Never logged in
- No approved applications
- Pending or failed subscription status
- Recently created accounts without completed signup
- Accounts created but never activated

## Deletion Restrictions by Role

### **Admin Users**
- ✅ **Can deactivate** any user account (except their own)
- ✅ **Can delete** inactive/incomplete users only
- ❌ **Cannot delete** fully active users
- ❌ **Cannot delete** dev or admin accounts
- ❌ **Cannot delete** their own account

### **Developer Users**
- ✅ **Can deactivate** any user account (except their own)
- ✅ **Can delete** any user account (including active users - not recommended)
- ✅ **Can delete** other dev and admin accounts
- ❌ **Cannot delete** their own account

## Account Management Actions

### **1. User Status Check**
**Endpoint:** `GET /api/admin/users/status?userId={userId}`

**Purpose:** Determine if a user can be deleted or should be deactivated

**Response includes:**
- User activity status
- Deletion/deactivation permissions
- Recommended action
- Detailed status summary

**Example:**
```bash
GET /api/admin/users/status?userId=123e4567-e89b-12d3-a456-************
```

### **2. Account Deactivation** (Recommended for active users)
**Endpoint:** `PATCH /api/admin/users/deactivate`

**Purpose:** Disable user access while preserving data integrity

**Features:**
- Sets `is_active = false` in user profile
- Automatically rejects pending applications
- Logs deactivation activity
- Preserves all user data and history
- Can be reversed (reactivation)

**Example:**
```bash
PATCH /api/admin/users/deactivate
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "isActive": false,
  "reason": "Account suspended for policy violation"
}
```

### **3. Account Reactivation**
**Endpoint:** `PATCH /api/admin/users/deactivate`

**Purpose:** Restore access to previously deactivated accounts

**Example:**
```bash
PATCH /api/admin/users/deactivate
{
  "userId": "123e4567-e89b-12d3-a456-************",
  "isActive": true,
  "reason": "Account restored after review"
}
```

### **4. Account Deletion** (Restricted)
**Endpoint:** `DELETE /api/admin/users/delete`

**Purpose:** Permanently remove user accounts (with restrictions)

**Restrictions:**
- Only for inactive/incomplete users (admins)
- Only developers can delete active users (not recommended)
- Cannot delete dev/admin accounts (unless requester is dev)
- Cannot delete own account

**Example:**
```bash
DELETE /api/admin/users/delete
{
  "userId": "123e4567-e89b-12d3-a456-************"
}
```

## Error Handling

### **Deletion Blocked for Active Users**
When an admin tries to delete a fully active user:

```json
{
  "error": "Cannot delete active user",
  "message": "This user is fully active and cannot be deleted. Use the deactivate function instead. Only developers can delete active users (not recommended).",
  "userStatus": {
    "role": "artist",
    "isActive": true,
    "hasLoggedIn": true,
    "applicationStatus": "approved",
    "lastLogin": "2023-12-01T10:30:00Z"
  },
  "recommendation": "Use /api/admin/users/deactivate to deactivate this account instead"
}
```

## Best Practices

### **For Administrators**

1. **Always check user status first** before attempting deletion
   ```bash
   GET /api/admin/users/status?userId={userId}
   ```

2. **Use deactivation for active users** instead of deletion
   ```bash
   PATCH /api/admin/users/deactivate
   ```

3. **Only delete inactive/incomplete users** who haven't finished signup

4. **Provide clear reasons** for deactivation actions

5. **Monitor user activity** through diagnostics regularly

### **For Developers**

1. **Avoid deleting active users** even though technically possible
2. **Use deactivation as the primary account management tool**
3. **Only delete accounts when absolutely necessary** (GDPR requests, etc.)
4. **Always backup data** before permanent deletions
5. **Document reasons** for any permanent deletions

## Workflow Examples

### **Scenario 1: Problematic Active User**
```bash
# 1. Check user status
GET /api/admin/users/status?userId={userId}

# 2. If fully active, deactivate instead of delete
PATCH /api/admin/users/deactivate
{
  "userId": "{userId}",
  "isActive": false,
  "reason": "Account suspended for policy violation"
}

# 3. User can be reactivated later if needed
PATCH /api/admin/users/deactivate
{
  "userId": "{userId}",
  "isActive": true,
  "reason": "Issue resolved, account restored"
}
```

### **Scenario 2: Incomplete Signup**
```bash
# 1. Check user status
GET /api/admin/users/status?userId={userId}

# 2. If not fully active, safe to delete
DELETE /api/admin/users/delete
{
  "userId": "{userId}"
}
```

### **Scenario 3: Data Consistency Issues**
```bash
# 1. Run diagnostics
GET /api/admin/users/diagnostics

# 2. Fix orphaned records
POST /api/admin/users/cleanup
{
  "action": "create_missing_profiles"
}

# 3. Retry user management operations
```

## Data Synchronization

### **Ensuring Profile Consistency**
The system now ensures that:
- User deletion checks `auth.users` (authoritative source)
- User creation validates against `auth.users`
- Missing profiles/roles are automatically created
- Orphaned records are identified and can be cleaned up

### **Application Status Integration**
For artist/braider users:
- Deactivation automatically rejects pending applications
- Application status affects deletion eligibility
- Approved applications indicate fully active users

## Monitoring and Maintenance

### **Regular Health Checks**
```bash
# Check overall system health
GET /api/admin/users/diagnostics

# Check specific user
GET /api/admin/users/find-by-email?email=<EMAIL>
```

### **Cleanup Operations**
```bash
# Create missing profiles
POST /api/admin/users/cleanup
{ "action": "create_missing_profiles" }

# Create missing roles
POST /api/admin/users/cleanup
{ "action": "create_missing_roles" }

# Remove orphaned records
POST /api/admin/users/cleanup
{ "action": "delete_orphaned_profiles" }
```

## Security Considerations

1. **Role-based access control** prevents unauthorized deletions
2. **Activity logging** tracks all account management actions
3. **Self-deletion prevention** protects admin accounts
4. **Data preservation** through deactivation instead of deletion
5. **Audit trail** maintains history of account changes

## Migration from Previous System

If you have existing users that were causing the "user not found" / "user already exists" issues:

1. **Run diagnostics** to identify inconsistencies
2. **Use cleanup operations** to fix orphaned records
3. **Check user status** before attempting any deletions
4. **Use deactivation** for users who should not be deleted
5. **Test the complete workflow** with the provided test script

This new system ensures data integrity while providing administrators with the tools they need to manage user accounts safely and effectively.
