.servicesShowcase {
  position: relative;
  height: 100vh;
  min-height: 700px;
  width: 100%;
  background-size: cover;
  background-position: center 50%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
  color: white;
  will-change: background-position-y; /* Optimize for animations */
}

.overlayGradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.9) 0%, rgba(85, 98, 255, 0.8) 100%);
  z-index: -1;
}

.floatingCircles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  will-change: transform;
  transition: transform 0.2s ease-out;
}

.floatingCircle {
  position: absolute;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  opacity: 0.95;
  animation: floatAnimation 8s infinite ease-in-out;
  overflow: hidden;
  transition: transform 0.3s ease-out;
}

.floatingCircle:hover {
  transform: scale(1.05);
  z-index: 10;
}

.floatingCircle:nth-child(1) {
  width: 180px;
  height: 180px;
  top: 15%;
  left: 10%;
}

.floatingCircle:nth-child(2) {
  width: 220px;
  height: 220px;
  top: 70%;
  left: 20%;
  animation-duration: 9s;
}

.floatingCircle:nth-child(3) {
  width: 160px;
  height: 160px;
  top: 20%;
  right: 10%;
  animation-duration: 10s;
}

.floatingCircle:nth-child(4) {
  width: 200px;
  height: 200px;
  top: 65%;
  right: 20%;
  animation-duration: 11s;
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(-15px, 15px) rotate(5deg);
  }
  50% {
    transform: translate(10px, -10px) rotate(-5deg);
  }
  75% {
    transform: translate(-10px, -15px) rotate(3deg);
  }
}

.circleImageContainer {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.circleImage {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  border-radius: 50%;
  transition: transform 0.5s ease;
}

.floatingCircle:hover .circleImage {
  transform: scale(1.1);
}

.circleOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
  mix-blend-mode: color-burn;
  border-radius: 50%;
  pointer-events: none;
}

.circleIcon {
  font-size: 3rem;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
  z-index: 2;
}

.showcaseContent {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
}

.showcaseTitle {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  display: inline-block;
}

.showcaseTitle::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: white;
  border-radius: 2px;
}

.showcaseSubtitle {
  font-size: 1.6rem;
  max-width: 800px;
  margin: 2rem auto 4rem;
  line-height: 1.6;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

.servicesHighlight {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 4rem;
}

.serviceHighlightItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}

.serviceHighlightItem:hover {
  transform: translateY(-10px);
}

.serviceIconCircle {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.serviceIconCircle::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(45deg);
  transition: all 0.3s ease;
  opacity: 0;
}

.serviceHighlightItem:hover .serviceIconCircle::before {
  animation: shineEffect 1.5s forwards;
}

@keyframes shineEffect {
  0% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) rotate(45deg);
    opacity: 0;
  }
}

.serviceIcon {
  font-size: 2.7rem;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.1));
}

.serviceName {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.showcaseCta {
  margin-top: 3rem;
}

.scrollIndicator {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 0.9rem;
  opacity: 0.8;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 3;
}

.scrollIndicator:hover {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.mouseIcon {
  width: 30px;
  height: 50px;
  border: 2px solid white;
  border-radius: 20px;
  margin-bottom: 10px;
  position: relative;
}

.mouseIcon::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  animation: mouseScroll 2s infinite;
}

@keyframes mouseScroll {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  75% {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  76% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .showcaseTitle {
    font-size: 3.5rem;
  }

  .showcaseSubtitle {
    font-size: 1.4rem;
  }

  .floatingCircle:nth-child(1) {
    width: 150px;
    height: 150px;
    top: 15%;
    left: 8%;
  }

  .floatingCircle:nth-child(2) {
    width: 180px;
    height: 180px;
    top: 70%;
    left: 15%;
  }

  .floatingCircle:nth-child(3) {
    width: 140px;
    height: 140px;
    top: 20%;
    right: 8%;
  }

  .floatingCircle:nth-child(4) {
    width: 170px;
    height: 170px;
    top: 65%;
    right: 15%;
  }
}

@media (max-width: 992px) {
  .showcaseTitle {
    font-size: 3rem;
  }

  .showcaseSubtitle {
    font-size: 1.3rem;
    margin-bottom: 3rem;
  }

  .servicesHighlight {
    gap: 1.5rem;
  }

  .serviceIconCircle {
    width: 80px;
    height: 80px;
  }

  .serviceIcon {
    font-size: 2.3rem;
  }
}

@media (max-width: 768px) {
  .servicesShowcase {
    min-height: 800px;
    height: auto;
    padding: 6rem 1rem;
  }

  .showcaseTitle {
    font-size: 2.5rem;
  }

  .showcaseTitle::after {
    width: 80px;
    height: 3px;
  }

  .showcaseSubtitle {
    font-size: 1.2rem;
    max-width: 90%;
    margin-bottom: 2.5rem;
  }

  .servicesHighlight {
    gap: 2rem 1rem;
  }

  .serviceHighlightItem {
    width: 45%;
  }

  .serviceIconCircle {
    width: 70px;
    height: 70px;
  }

  .serviceIcon {
    font-size: 2rem;
  }

  .serviceName {
    font-size: 1rem;
  }

  .scrollIndicator {
    display: none;
  }

  .floatingCircle:nth-child(1) {
    width: 120px;
    height: 120px;
    top: 10%;
    left: 5%;
  }

  .floatingCircle:nth-child(2) {
    width: 140px;
    height: 140px;
    top: 75%;
    left: 10%;
  }

  .floatingCircle:nth-child(3) {
    width: 110px;
    height: 110px;
    top: 15%;
    right: 5%;
  }

  .floatingCircle:nth-child(4) {
    width: 130px;
    height: 130px;
    top: 70%;
    right: 10%;
  }
}

@media (max-width: 480px) {
  .servicesShowcase {
    padding: 5rem 1rem;
  }

  .showcaseTitle {
    font-size: 2.2rem;
  }

  .showcaseSubtitle {
    font-size: 1.1rem;
  }

  .serviceHighlightItem {
    width: 100%;
    max-width: 150px;
  }

  .floatingCircle:nth-child(1),
  .floatingCircle:nth-child(3) {
    width: 100px;
    height: 100px;
  }

  .floatingCircle:nth-child(2),
  .floatingCircle:nth-child(4) {
    width: 120px;
    height: 120px;
  }

  .floatingCircle:nth-child(1) {
    top: 8%;
    left: 3%;
  }

  .floatingCircle:nth-child(2) {
    top: 78%;
    left: 8%;
  }

  .floatingCircle:nth-child(3) {
    top: 12%;
    right: 3%;
  }

  .floatingCircle:nth-child(4) {
    top: 72%;
    right: 8%;
  }
}