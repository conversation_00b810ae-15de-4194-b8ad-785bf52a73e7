// Gmail OAuth callback handler
import { exchangeGmailCode } from '../../../../lib/gmail-api.js';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { code, error, state } = req.query;

    if (error) {
      console.error('Gmail OAuth error:', error);
      return res.redirect(`/admin/settings?error=oauth_error&details=${encodeURIComponent(error)}`);
    }

    if (!code) {
      return res.redirect('/admin/settings?error=no_code');
    }

    // Exchange code for tokens
    const tokenResult = await exchangeGmailCode(code);

    if (!tokenResult.success) {
      console.error('Token exchange failed:', tokenResult.error);
      return res.redirect(`/admin/settings?error=token_exchange&details=${encodeURIComponent(tokenResult.error)}`);
    }    // Store tokens securely in Supabase
    try {
      const { data, error: dbError } = await supabase
        .from('admin_settings')
        .upsert({
          setting_key: 'gmail_api_tokens',
          setting_value: JSON.stringify(tokenResult.tokens)
        }, {
          onConflict: 'setting_key'
        });

      if (dbError) {
        console.error('Database error storing tokens:', dbError);
        return res.redirect(`/admin/settings?error=db_error&details=${encodeURIComponent(dbError.message)}`);
      }

      console.log('Gmail API tokens stored successfully');
      return res.redirect('/admin/settings?success=gmail_authorized');
    } catch (dbError) {
      console.error('Database connection error:', dbError);
      return res.redirect(`/admin/settings?error=db_connection&details=${encodeURIComponent(dbError.message)}`);
    }

  } catch (error) {
    console.error('Gmail OAuth callback error:', error);
    return res.redirect(`/admin/settings?error=callback_error&details=${encodeURIComponent(error.message)}`);
  }
}
