import { useState } from 'react';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/analytics/ReportExporter.module.css';

/**
 * ReportExporter component for exporting analytics reports
 * 
 * @param {Object} props - Component props
 * @param {string} props.period - Time period for data (day, week, month, year)
 * @param {boolean} props.customDateRange - Whether to use custom date range
 * @param {Date} props.startDate - Start date for custom range
 * @param {Date} props.endDate - End date for custom range
 * @returns {JSX.Element}
 */
export default function ReportExporter({
  period = 'month',
  customDateRange = false,
  startDate = null,
  endDate = null
}) {
  const [loading, setLoading] = useState(false);
  const [reportType, setReportType] = useState('sales');
  const [exportFormat, setExportFormat] = useState('csv');

  // Handle export
  const handleExport = async () => {
    try {
      setLoading(true);
      
      // Build query params
      const queryParams = new URLSearchParams({
        report_type: reportType,
        format: exportFormat
      });
      
      if (customDateRange && startDate && endDate) {
        queryParams.append('start_date', startDate.toISOString());
        queryParams.append('end_date', endDate.toISOString());
      } else {
        queryParams.append('period', period);
      }
      
      // Make API request
      const response = await fetch(`/api/admin/analytics/export?${queryParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export report');
      }
      
      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'report';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      // Handle different export formats
      if (exportFormat === 'csv' || exportFormat === 'excel') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else if (exportFormat === 'pdf') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');
      }
      
      toast.success('Report exported successfully');
    } catch (err) {
      console.error('Error exporting report:', err);
      toast.error(err.message || 'Failed to export report');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.reportExporter}>
      <h3>Export Reports</h3>
      
      <div className={styles.exportForm}>
        <div className={styles.formGroup}>
          <label htmlFor="report-type">Report Type</label>
          <select
            id="report-type"
            value={reportType}
            onChange={(e) => setReportType(e.target.value)}
            className={styles.formControl}
          >
            <option value="sales">Sales Report</option>
            <option value="products">Product Sales Report</option>
            <option value="categories">Category Sales Report</option>
            <option value="customers">Customer Report</option>
            <option value="inventory">Inventory Report</option>
          </select>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="export-format">Export Format</label>
          <select
            id="export-format"
            value={exportFormat}
            onChange={(e) => setExportFormat(e.target.value)}
            className={styles.formControl}
          >
            <option value="csv">CSV</option>
            <option value="excel">Excel</option>
            <option value="pdf">PDF</option>
          </select>
        </div>
        
        <button
          className={styles.exportButton}
          onClick={handleExport}
          disabled={loading}
        >
          {loading ? 'Exporting...' : 'Export Report'}
        </button>
      </div>
      
      <div className={styles.reportDescription}>
        {reportType === 'sales' && (
          <p>Sales report includes revenue data, order counts, and sales trends over time.</p>
        )}
        {reportType === 'products' && (
          <p>Product sales report includes detailed sales data for each product, including quantity sold and revenue.</p>
        )}
        {reportType === 'categories' && (
          <p>Category sales report includes sales data aggregated by product category.</p>
        )}
        {reportType === 'customers' && (
          <p>Customer report includes customer acquisition data, purchase history, and lifetime value metrics.</p>
        )}
        {reportType === 'inventory' && (
          <p>Inventory report includes current stock levels, product valuation, and stock movement history.</p>
        )}
      </div>
    </div>
  );
}
