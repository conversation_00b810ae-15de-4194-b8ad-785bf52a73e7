# Manual Booking System Testing Guide

This guide provides step-by-step instructions to manually test all booking system functionality.

## Prerequisites

1. ✅ Development server running (`npm run dev`)
2. ✅ Admin access to the system
3. ✅ Browser with developer tools available

## Test Scenarios

### 1. Database Connectivity Test

**Objective:** Verify Supabase database connection and data access

**Steps:**
1. Open browser developer tools (F12)
2. Navigate to `/admin/dashboard`
3. Check console for any database connection errors
4. Verify dashboard loads customer and booking statistics

**Expected Results:**
- ✅ No database connection errors in console
- ✅ Dashboard displays current statistics
- ✅ Data loads within 3 seconds

### 2. Customer Management Test

**Objective:** Test customer creation, editing, and management

**Steps:**

#### 2.1 Create New Customer
1. Navigate to `/admin/customers`
2. Click "Add New Customer" button
3. Fill in customer form:
   - Name: "Test Customer"
   - Email: "<EMAIL>"
   - Phone: "+61400000000"
   - Address: "123 Test Street"
   - City: "Test City"
4. Click "Save Customer"

**Expected Results:**
- ✅ Customer created successfully
- ✅ Customer appears in customer list
- ✅ Success message displayed

#### 2.2 Edit Customer
1. Find the test customer in the list
2. Click "Edit" button
3. Update phone number to "+61400000001"
4. Click "Save Changes"

**Expected Results:**
- ✅ Customer updated successfully
- ✅ Phone number changed in the list
- ✅ Update confirmation message

#### 2.3 Search Customer
1. Use search box to search for "Test Customer"
2. Verify customer appears in filtered results

**Expected Results:**
- ✅ Search returns correct customer
- ✅ Search is case-insensitive

### 3. Booking Creation Test

**Objective:** Test booking creation through admin interface

**Steps:**

#### 3.1 Create Booking via Calendar
1. Navigate to `/admin/bookings`
2. Click on a time slot in the calendar
3. Fill in booking form:
   - Customer: Select "Test Customer" (created above)
   - Service: Select any available service
   - Start Time: Current date + 1 day, 10:00 AM
   - End Time: Auto-calculated based on service
   - Status: "Confirmed"
   - Location: "Test Location"
   - Notes: "Test booking"
4. Click "Save Booking"

**Expected Results:**
- ✅ Booking created successfully
- ✅ Booking appears on calendar
- ✅ Booking shows correct color coding
- ✅ Success notification displayed

#### 3.2 Create Booking via Add Form
1. Click "Add New Booking" button
2. Fill in all required fields
3. Select different customer and service
4. Save booking

**Expected Results:**
- ✅ Second booking created
- ✅ Both bookings visible on calendar
- ✅ No conflicts or errors

### 4. Booking Management Test

**Objective:** Test booking editing, status changes, and deletion

**Steps:**

#### 4.1 Edit Booking
1. Click on a booking in the calendar
2. Click "Edit" in the booking details modal
3. Change status to "Pending"
4. Update notes to "Updated test booking"
5. Save changes

**Expected Results:**
- ✅ Booking updated successfully
- ✅ Status change reflected in calendar color
- ✅ Notes updated in booking details

#### 4.2 Move Booking (Drag & Drop)
1. Drag a booking to a different time slot
2. Confirm the move in the dialog

**Expected Results:**
- ✅ Booking moved to new time slot
- ✅ Database updated with new time
- ✅ No conflicts with existing bookings

#### 4.3 Delete Booking
1. Click on a booking
2. Click "Delete" button
3. Confirm deletion

**Expected Results:**
- ✅ Booking removed from calendar
- ✅ Confirmation dialog appears
- ✅ Database record deleted

### 5. Public Booking Test

**Objective:** Test public booking creation through website

**Steps:**

#### 5.1 Public Booking Form
1. Navigate to `/book-online`
2. Fill in booking form:
   - Name: "Public Test Customer"
   - Email: "<EMAIL>"
   - Phone: "+61400000002"
   - Service: Select any service
   - Date: Future date
   - Time: Available time slot
   - Location: "Public Test Location"
   - Message: "Public booking test"
3. Submit form

**Expected Results:**
- ✅ Booking submitted successfully
- ✅ Confirmation message displayed
- ✅ Booking appears in admin calendar
- ✅ Customer created automatically

### 6. Error Handling Test

**Objective:** Verify proper error handling and validation

**Steps:**

#### 6.1 Invalid Data Test
1. Try to create booking with missing required fields
2. Try to create customer with invalid email format
3. Try to create overlapping bookings

**Expected Results:**
- ✅ Validation errors displayed
- ✅ Form prevents submission
- ✅ User-friendly error messages

#### 6.2 Database Error Simulation
1. Open browser developer tools
2. Go to Network tab
3. Block requests to Supabase domain
4. Try to create a booking

**Expected Results:**
- ✅ Error message displayed
- ✅ User informed of connection issue
- ✅ No data corruption

### 7. Performance Test

**Objective:** Verify system performance under normal load

**Steps:**

#### 7.1 Calendar Loading
1. Navigate to `/admin/bookings`
2. Measure calendar load time
3. Switch between different views (day, week, month)

**Expected Results:**
- ✅ Calendar loads within 3 seconds
- ✅ View switching is smooth
- ✅ No performance warnings in console

#### 7.2 Large Data Set
1. Create multiple bookings (10+)
2. Test calendar performance
3. Test search and filtering

**Expected Results:**
- ✅ Performance remains acceptable
- ✅ Search results return quickly
- ✅ Calendar rendering is smooth

### 8. Mobile Responsiveness Test

**Objective:** Verify mobile compatibility

**Steps:**

#### 8.1 Mobile View Test
1. Open browser developer tools
2. Switch to mobile device simulation
3. Test booking creation on mobile
4. Test calendar navigation

**Expected Results:**
- ✅ Interface adapts to mobile screen
- ✅ All functions work on touch devices
- ✅ Text is readable without zooming

## Test Results Checklist

### Database Integration ✅
- [ ] Database connection successful
- [ ] Data retrieval working
- [ ] Data insertion working
- [ ] Data updates working
- [ ] Data deletion working

### Customer Management ✅
- [ ] Customer creation working
- [ ] Customer editing working
- [ ] Customer search working
- [ ] Customer validation working

### Booking Operations ✅
- [ ] Booking creation working
- [ ] Booking editing working
- [ ] Booking deletion working
- [ ] Booking status updates working
- [ ] Calendar display working

### Error Handling ✅
- [ ] Form validation working
- [ ] Error messages displayed
- [ ] Database errors handled
- [ ] Network errors handled

### User Experience ✅
- [ ] Interface is intuitive
- [ ] Performance is acceptable
- [ ] Mobile compatibility confirmed
- [ ] Accessibility features working

## Troubleshooting

### Common Issues and Solutions

**Issue:** Calendar not loading
- **Solution:** Check browser console for errors, verify database connection

**Issue:** Booking creation fails
- **Solution:** Verify all required fields are filled, check service availability

**Issue:** Customer search not working
- **Solution:** Check search input format, verify database connection

**Issue:** Mobile interface problems
- **Solution:** Clear browser cache, check CSS loading

## Conclusion

If all tests pass, the booking system is fully functional and ready for production use. Any failing tests should be investigated and resolved before going live.

**Test Completion Date:** ___________
**Tester Name:** ___________
**Overall Result:** ✅ PASS / ❌ FAIL
**Notes:** ___________
