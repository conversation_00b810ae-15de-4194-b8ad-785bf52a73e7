/**
 * Comprehensive Browser Extension Error Suppression
 * Prevents browser extension errors from polluting the console
 * and interfering with application functionality
 */

(function() {
  'use strict';

  // Store original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;

  // Comprehensive patterns for browser extension errors
  const EXTENSION_ERROR_PATTERNS = [
    // Chrome extension errors
    'runtime.lastError',
    'unchecked runtime.lastError',
    'the message port closed before a response was received',
    'message port closed',
    'extension context invalidated',
    'chrome-extension://',
    'could not establish connection',
    'receiving end does not exist',
    'extension context',
    'chrome.runtime',
    'browser.runtime',
    'cannot access chrome://',
    'cannot access about:',
    
    // Firefox extension errors
    'moz-extension://',
    'webextension',
    
    // Safari extension errors
    'safari-extension://',
    'safari-web-extension://',
    
    // Generic extension errors
    'script error',
    'non-error promise rejection captured',
    'loading css chunk',
    'loading chunk',
    'dynamically imported module',
    'failed to fetch dynamically imported module',
    'network error when attempting to fetch resource',
    'chunk load failed',
    'loading chunk failed',
    
    // Ad blocker and privacy extension errors
    'adblock',
    'ublock',
    'ghostery',
    'privacy badger',
    'disconnect',
    'duckduckgo',
    
    // Password manager extension errors
    'lastpass',
    '1password',
    'bitwarden',
    'dashlane',
    'keeper',
    
    // Other common extension errors
    'grammarly',
    'honey',
    'metamask',
    'web3',
    'coinbase wallet',

    // Square iframe and payment form errors (when caused by extensions)
    'main-iframe.html',
    'single-card-element-iframe.html',
    'card-wrapper.css',
    'pos:1',
    'pos.js',

    // Additional iframe-based extension errors
    'iframe',
    'frame',
    'cross-origin',
    'postmessage',
    'message port',
    'port closed',
    'receiving end does not exist',
    'connection closed',
    'context invalidated',

    // Specific Square iframe extension interference
    'square iframe',
    'payment iframe',
    'card element iframe'
  ];

  // Additional patterns for specific error types
  const RUNTIME_ERROR_PATTERNS = [
    /unchecked runtime\.lastError/i,
    /the message port closed before a response was received/i,
    /could not establish connection\. receiving end does not exist/i,
    /extension context invalidated/i,
    /chrome-extension:\/\//i,
    /moz-extension:\/\//i,
    /safari-extension:\/\//i,
    /loading chunk \d+ failed/i,
    /loading css chunk \d+ failed/i,

    // Iframe-based extension error patterns
    /main-iframe\.html.*runtime\.lastError/i,
    /single-card-element-iframe\.html.*runtime\.lastError/i,
    /pos:1.*runtime\.lastError/i,
    /pos\.js.*runtime\.lastError/i,
    /iframe.*message port closed/i,
    /frame.*extension/i,
    /postmessage.*extension/i,
    /cross-origin.*extension/i,

    // More aggressive patterns for persistent extension errors
    /^pos:1\s+unchecked runtime\.lastError/i,
    /^main-iframe\.html:1\s+unchecked runtime\.lastError/i,
    /^single-card-element-iframe\.html:1\s+unchecked runtime\.lastError/i,
    /the message port closed before a response was received.*pos/i,
    /the message port closed before a response was received.*iframe/i,
    /runtime\.lastError.*iframe/i,
    /runtime\.lastError.*pos/i
  ];

  /**
   * Check if an error message matches extension error patterns
   * @param {string} message - The error message to check
   * @param {string} source - The source URL or file where the error occurred
   * @returns {boolean} - True if it's an extension error
   */
  function isExtensionError(message, source = '') {
    if (typeof message !== 'string') {
      message = String(message);
    }

    const lowerMessage = message.toLowerCase();
    const lowerSource = String(source).toLowerCase();

    // Check if source is from an extension
    if (lowerSource.includes('chrome-extension://') ||
        lowerSource.includes('moz-extension://') ||
        lowerSource.includes('safari-extension://')) {
      return true;
    }

    // Check for iframe-based extension errors
    if ((lowerSource.includes('iframe') || lowerMessage.includes('iframe')) &&
        (lowerMessage.includes('runtime.lasterror') ||
         lowerMessage.includes('message port closed') ||
         lowerMessage.includes('extension'))) {
      return true;
    }

    // Enhanced detection for Square iframe extension errors
    if (lowerMessage.includes('runtime.lasterror') &&
        (lowerMessage.includes('pos:1') ||
         lowerMessage.includes('main-iframe.html') ||
         lowerMessage.includes('single-card-element-iframe.html') ||
         lowerMessage.includes('sandbox.web.squarecdn.com') ||
         lowerMessage.includes('web.squarecdn.com') ||
         lowerSource.includes('pos:1') ||
         lowerSource.includes('main-iframe.html') ||
         lowerSource.includes('single-card-element-iframe.html') ||
         lowerSource.includes('sandbox.web.squarecdn.com') ||
         lowerSource.includes('web.squarecdn.com'))) {
      return true;
    }

    // Specific Square iframe runtime error patterns
    if ((lowerMessage.includes('unchecked runtime.lasterror') ||
         lowerMessage.includes('message port closed before a response')) &&
        (lowerSource.includes('squarecdn.com') ||
         lowerSource.includes('square') ||
         lowerMessage.includes('square') ||
         lowerMessage.includes('iframe'))) {
      return true;
    }

    // Check for specific message port errors in Square contexts
    if (lowerMessage.includes('message port closed') &&
        (lowerMessage.includes('pos') ||
         lowerMessage.includes('iframe') ||
         lowerMessage.includes('square'))) {
      return true;
    }

    // Check against text patterns
    const hasTextPattern = EXTENSION_ERROR_PATTERNS.some(pattern =>
      lowerMessage.includes(pattern.toLowerCase())
    );

    // Check against regex patterns
    const hasRegexPattern = RUNTIME_ERROR_PATTERNS.some(pattern =>
      pattern.test(message)
    );

    return hasTextPattern || hasRegexPattern;
  }

  /**
   * Filter console method to suppress extension errors
   * @param {Function} originalMethod - Original console method
   * @param {string} methodName - Name of the console method
   * @returns {Function} - Filtered console method
   */
  function createFilteredConsoleMethod(originalMethod, methodName) {
    return function(...args) {
      // Convert all arguments to a single message string
      const message = args.map(arg => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg);
          } catch (e) {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' ');

      // Check if this is an extension error
      if (isExtensionError(message)) {
        // Silently suppress extension errors
        // Optionally, you can log them to a separate debug channel
        if (window.DEBUG_EXTENSION_ERRORS) {
          originalMethod.call(console, `[SUPPRESSED ${methodName.toUpperCase()}]`, ...args);
        }
        return;
      }

      // Log non-extension errors normally
      originalMethod.apply(console, args);
    };
  }

  /**
   * Suppress window.onerror for extension errors
   */
  function suppressWindowErrors() {
    const originalOnError = window.onerror;
    
    window.onerror = function(message, source, lineno, colno, error) {
      // Check if this is an extension error (now with source parameter)
      if (isExtensionError(message, source)) {
        // Suppress extension errors
        return true; // Prevents the error from being logged
      }
      
      // Call original error handler for non-extension errors
      if (originalOnError) {
        return originalOnError.call(this, message, source, lineno, colno, error);
      }
      
      return false; // Let the error be handled normally
    };
  }

  /**
   * Suppress unhandled promise rejections from extensions
   */
  function suppressUnhandledRejections() {
    const originalHandler = window.onunhandledrejection;

    window.addEventListener('unhandledrejection', function(event) {
      const reason = event.reason;
      let message = '';

      if (reason && reason.message) {
        message = reason.message;
      } else if (typeof reason === 'string') {
        message = reason;
      } else {
        message = String(reason);
      }

      // Check if this is an extension error
      if (isExtensionError(message)) {
        event.preventDefault(); // Suppress the error
        return;
      }

      // Call original handler for non-extension errors
      if (originalHandler) {
        originalHandler.call(this, event);
      }
    });
  }

  /**
   * Suppress iframe message errors from extensions
   */
  function suppressIframeExtensionErrors() {
    // Listen for postMessage events that might be from extensions
    window.addEventListener('message', function(event) {
      try {
        // Check if the message is from an extension
        const origin = event.origin || '';
        if (origin.includes('chrome-extension://') ||
            origin.includes('moz-extension://') ||
            origin.includes('safari-extension://')) {
          // Silently ignore extension messages
          event.stopPropagation();
          return;
        }

        // Check if the message data contains extension-related content
        const data = event.data;
        if (data && typeof data === 'object' && data.type) {
          const dataString = JSON.stringify(data);
          if (isExtensionError(dataString)) {
            event.stopPropagation();
            return;
          }
        }
      } catch (error) {
        // Silently handle any errors in message processing
      }
    }, true); // Use capture phase to catch early
  }

  /**
   * Monitor and suppress iframe-specific extension errors
   */
  function monitorIframeExtensionErrors() {
    // Override iframe error handling
    const originalAddEventListener = EventTarget.prototype.addEventListener;

    EventTarget.prototype.addEventListener = function(type, listener, options) {
      if (type === 'error' && this.tagName === 'IFRAME') {
        // Wrap the listener to filter extension errors
        const wrappedListener = function(event) {
          const error = event.error || event.message || '';
          if (isExtensionError(String(error), event.filename || '')) {
            // Suppress iframe extension errors
            event.stopPropagation();
            event.preventDefault();
            return;
          }
          // Call original listener for non-extension errors
          if (typeof listener === 'function') {
            listener.call(this, event);
          }
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      }

      // Call original addEventListener for non-iframe error events
      return originalAddEventListener.call(this, type, listener, options);
    };

    // Monitor iframe creation and add error suppression
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'IFRAME') {
            // Add error suppression to new iframes
            node.addEventListener('error', function(event) {
              const error = event.error || event.message || '';
              if (isExtensionError(String(error), event.filename || '')) {
                event.stopPropagation();
                event.preventDefault();
              }
            }, true);
          }
        });
      });
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Initialize error suppression
   */
  function initializeErrorSuppression() {
    try {
      // Override console methods
      console.error = createFilteredConsoleMethod(originalConsoleError, 'error');
      console.warn = createFilteredConsoleMethod(originalConsoleWarn, 'warn');
      console.log = createFilteredConsoleMethod(originalConsoleLog, 'log');
      
      // Suppress window errors
      suppressWindowErrors();

      // Suppress unhandled promise rejections
      suppressUnhandledRejections();

      // Suppress iframe extension errors
      suppressIframeExtensionErrors();

      // Monitor iframe-specific extension errors
      monitorIframeExtensionErrors();
      
      // Store original methods for potential restoration
      window.__originalConsole = {
        error: originalConsoleError,
        warn: originalConsoleWarn,
        log: originalConsoleLog
      };
      
      console.log('🛡️ Extension error suppression initialized');
      
    } catch (error) {
      // If initialization fails, restore original console methods
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      console.log = originalConsoleLog;
      
      originalConsoleError('Failed to initialize extension error suppression:', error);
    }
  }

  /**
   * Restore original console methods (for debugging)
   */
  window.restoreConsole = function() {
    if (window.__originalConsole) {
      console.error = window.__originalConsole.error;
      console.warn = window.__originalConsole.warn;
      console.log = window.__originalConsole.log;
      console.log('Console methods restored to original state');
    }
  };

  /**
   * Enable debug mode for extension errors
   */
  window.debugExtensionErrors = function(enable = true) {
    window.DEBUG_EXTENSION_ERRORS = enable;
    console.log(`Extension error debugging ${enable ? 'enabled' : 'disabled'}`);
  };

  // Initialize when DOM is ready or immediately if already loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeErrorSuppression);
  } else {
    initializeErrorSuppression();
  }

})();
