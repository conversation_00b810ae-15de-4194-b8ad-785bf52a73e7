// Script to run compatibility tests
const puppeteer = require('puppeteer');

async function runCompatibilityTests() {
  console.log('Starting compatibility tests...');
  
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  // Navigate to the website
  await page.goto('http://localhost:3000', { waitUntil: 'networkidle2' });
  console.log('Loaded website');
  
  // Inject the compatibility test script
  await page.evaluate(() => {
    const script = document.createElement('script');
    script.src = '/compatibility-test-script.js';
    document.head.appendChild(script);
    console.log('Compatibility test script loaded');
  });
  
  // Wait for script to load
  await page.waitForFunction(() => typeof window.compatibilityTests !== 'undefined', { timeout: 5000 });
  console.log('Test script loaded successfully');
  
  // Run the tests
  const results = await page.evaluate(async () => {
    console.log('Running compatibility tests...');
    return await window.compatibilityTests.runAllTests();
  });
  
  console.log('Compatibility test results:');
  console.log(JSON.stringify(results, null, 2));
  
  // Save results to a file
  const fs = require('fs');
  fs.writeFileSync('compatibility-test-results.json', JSON.stringify(results, null, 2));
  console.log('Results saved to compatibility-test-results.json');
  
  // Keep the browser open for manual inspection
  console.log('Tests completed. Browser will remain open for inspection.');
  console.log('Press Ctrl+C to close the browser and exit.');
}

runCompatibilityTests().catch(error => {
  console.error('Error running compatibility tests:', error);
  process.exit(1);
});
