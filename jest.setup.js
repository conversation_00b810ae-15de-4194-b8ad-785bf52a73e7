require('@testing-library/jest-dom');

const mockStorage = {};
global.sessionStorage = {
  getItem: (key) => mockStorage[key],
  setItem: (key, value) => {
    mockStorage[key] = value;
  },
  clear: () => {
    Object.keys(mockStorage).forEach(key => delete mockStorage[key]);
  }
};

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
);

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test-url.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-key';

// Mock Node.js modules
jest.mock('fs', () => ({
  readFileSync: jest.fn(),
  promises: {
    readFile: jest.fn().mockResolvedValue('NEXT_PUBLIC_SUPABASE_URL=https://test-url.supabase.co\nNEXT_PUBLIC_SUPABASE_ANON_KEY=test-key')
  }
}));

jest.mock('path', () => ({
  resolve: jest.fn().mockReturnValue('/test/path/.env.local')
}));

jest.mock('util', () => ({
  promisify: jest.fn(cb => cb)
}));

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: () => ({
    auth: {
      signInWithPassword: jest.fn().mockResolvedValue({ data: { user: { id: 'test-user' } }, error: null }),
      signOut: jest.fn().mockResolvedValue({ error: null }),
      getSession: jest.fn().mockResolvedValue({ data: { session: { user: { id: 'test-user' } } }, error: null }),
      onAuthStateChange: jest.fn().mockImplementation((callback) => {
        callback('SIGNED_IN', { user: { id: 'test-user' } });
        return () => {};
      }),
      refreshSession: jest.fn().mockResolvedValue({ data: { session: { user: { id: 'test-user' } } }, error: null })
    },
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: { role: 'admin' }, error: null }),
      limit: jest.fn().mockReturnThis()
    })
  })
}));
