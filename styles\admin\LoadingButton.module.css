/* LoadingButton.module.css */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 38px;
  position: relative;
  overflow: hidden;
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.25);
}

.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Button variants */
.primary {
  background-color: #6a0dad;
  color: white;
  border-color: #6a0dad;
}

.primary:hover:not(:disabled) {
  background-color: #5a0c93;
  border-color: #5a0c93;
}

.secondary {
  background-color: #f0f0f0;
  color: #333;
  border-color: #ddd;
}

.secondary:hover:not(:disabled) {
  background-color: #e5e5e5;
  border-color: #ccc;
}

.danger {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.danger:hover:not(:disabled) {
  background-color: #c82333;
  border-color: #bd2130;
}

.success {
  background-color: #28a745;
  color: white;
  border-color: #28a745;
}

.success:hover:not(:disabled) {
  background-color: #218838;
  border-color: #1e7e34;
}

.outline {
  background-color: transparent;
  color: #6a0dad;
  border-color: #6a0dad;
}

.outline:hover:not(:disabled) {
  background-color: rgba(106, 13, 173, 0.1);
}

.outline.danger {
  color: #dc3545;
  border-color: #dc3545;
}

.outline.danger:hover:not(:disabled) {
  background-color: rgba(220, 53, 69, 0.1);
}

.outline.success {
  color: #28a745;
  border-color: #28a745;
}

.outline.success:hover:not(:disabled) {
  background-color: rgba(40, 167, 69, 0.1);
}

/* Full width button */
.fullWidth {
  width: 100%;
}

/* Loading state */
.loading {
  position: relative;
  color: transparent !important;
}

.loading .loadingContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
}

.loading.primary .loadingContent,
.loading.danger .loadingContent,
.loading.success .loadingContent {
  color: white;
}

.loading.secondary .loadingContent,
.loading.outline .loadingContent {
  color: inherit;
}

/* Spinner */
.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-right-color: transparent;
  margin-right: 0.5rem;
  animation: spin 0.75s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 0.875rem;
}
