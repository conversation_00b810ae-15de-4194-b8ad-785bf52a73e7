// Customer authentication endpoint
import supabase, { getAdminClient } from '@/lib/supabase';

/**
 * Public API endpoint for customer authentication
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  switch (req.method) {
    case 'POST':
      return handleAuthentication(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Handle customer authentication
 */
async function handleAuthentication(req, res) {
  try {
    const { email, password, action } = req.body;

    if (!email || !action) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Get clients
    const client = supabase;
    if (!client) {
      console.error("Supabase client not available.");
      return res.status(500).json({ error: 'Authentication service unavailable' });
    }

    // Get admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Supabase admin client not available.");
      return res.status(500).json({ error: 'Admin authentication service unavailable' });
    }

    // Handle different authentication actions
    switch (action) {
      case 'signin': {
        if (!password) {
          return res.status(400).json({ error: 'Password is required for sign in' });
        }

        // Sign in with Supabase Auth
        const { data: authData, error: authError } = await client.auth.signInWithPassword({
          email,
          password,
        });

        if (authError) {
          return res.status(401).json({ error: authError.message });
        }        // Get customer data
        const { data: customer, error: customerError } = await adminClient
          .from('customers')
          .select('*')
          .eq('email', email)
          .single();

        if (customerError) {
          console.error('Error fetching customer after authentication:', customerError);
          throw customerError;
        }

        return res.status(200).json({
          message: 'Authentication successful',
          session: authData.session,
          customer
        });
      }

      case 'signup': {
        if (!password) {
          return res.status(400).json({ error: 'Password is required for sign up' });
        }

        const { name, phone, marketingConsent = false } = req.body;

        if (!name || !phone) {
          return res.status(400).json({ error: 'Name and phone are required for sign up' });
        }        // Check if customer exists already
        const { data: existingCustomer, error: customerCheckError } = await adminClient
          .from('customers')
          .select('id, email')
          .eq('email', email)
          .single();

        if (customerCheckError && customerCheckError.code !== 'PGRST116') {
          console.error('Error checking for existing customer:', customerCheckError);
          throw customerCheckError;
        }

        let customerId;        // If customer doesn't exist, create customer record
        if (!existingCustomer) {
          const { data: newCustomer, error: createCustomerError } = await adminClient
            .from('customers')
            .insert([{
              name,
              email,
              phone,
              marketing_consent: marketingConsent
            }])
            .select();

          if (createCustomerError) {
            console.error('Error creating customer:', createCustomerError);
            throw createCustomerError;
          }

          customerId = newCustomer[0].id;
        } else {
          customerId = existingCustomer.id;
        }        // Create auth account with Supabase
        const { data: authData, error: authError } = await adminClient.auth.admin.createUser({
          email,
          password,
          email_confirm: true,
          user_metadata: {
            name,
            customer_id: customerId
          }
        });

        if (authError) {
          return res.status(400).json({ error: authError.message });
        }

        return res.status(201).json({
          message: 'User registered successfully',
          user: {
            id: authData.user.id,
            email: authData.user.email,
            customer_id: customerId
          }
        });
      }      case 'reset': {
        // Password reset request
        const { error: resetError } = await client.auth.resetPasswordForEmail(email, {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/account/reset-password`
        });

        if (resetError) {
          return res.status(400).json({ error: resetError.message });
        }

        return res.status(200).json({
          message: 'Password reset email sent'
        });
      }      case 'signout': {
        // Sign out
        const { error: signoutError } = await client.auth.signOut();

        if (signoutError) {
          return res.status(400).json({ error: signoutError.message });
        }

        return res.status(200).json({
          message: 'Signed out successfully'
        });
      }

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Authentication failed',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}
