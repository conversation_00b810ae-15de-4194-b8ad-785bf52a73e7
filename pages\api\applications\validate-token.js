import { getAdminClient } from '@/lib/supabase'

/**
 * API endpoint for validating application tokens
 * POST /api/applications/validate-token - Validate a secure application token
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Token validation API called`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { token, role } = req.body

    if (!token) {
      return res.status(400).json({ 
        valid: false,
        error: 'Token is required' 
      })
    }

    if (!role || !['artist', 'braider'].includes(role)) {
      return res.status(400).json({ 
        valid: false,
        error: 'Valid role is required' 
      })
    }

    // Get admin client to validate token
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ 
        valid: false,
        error: 'Failed to initialize admin client' 
      })
    }

    console.log(`[${requestId}] Validating token for role: ${role}`)

    // Validate token using the database function
    const { data: validationResult, error: validationError } = await adminClient
      .rpc('validate_application_token', { token_value: token })

    if (validationError) {
      console.error(`[${requestId}] Token validation error:`, validationError)
      return res.status(500).json({
        valid: false,
        error: 'Failed to validate token',
        details: validationError.message
      })
    }

    // Handle case where function returns no results
    if (!validationResult || validationResult.length === 0) {
      console.log(`[${requestId}] Token validation returned no results`)
      return res.status(400).json({
        valid: false,
        error: 'Invalid token - no validation result'
      })
    }

    const validation = validationResult[0]

    if (!validation || !validation.is_valid) {
      const errorMsg = validation?.error_message || 'Token validation failed'
      console.log(`[${requestId}] Token validation failed: ${errorMsg}`)
      return res.status(400).json({
        valid: false,
        error: errorMsg
      })
    }

    // Get user details and application info
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(validation.user_id)
    
    if (userError || !userData.user) {
      console.error(`[${requestId}] Error fetching user data:`, userError)
      return res.status(500).json({ 
        valid: false,
        error: 'Failed to fetch user information' 
      })
    }

    // Get user profile
    const { data: userProfile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('name, phone')
      .eq('id', validation.user_id)
      .single()

    // Get application details
    const { data: application, error: appError } = await adminClient
      .from('artist_braider_applications')
      .select('application_type, status')
      .eq('id', validation.application_id)
      .single()

    if (appError || !application) {
      console.error(`[${requestId}] Error fetching application:`, appError)
      return res.status(500).json({ 
        valid: false,
        error: 'Failed to fetch application information' 
      })
    }

    // Verify role matches application type
    if (application.application_type !== role) {
      console.log(`[${requestId}] Role mismatch: expected ${role}, got ${application.application_type}`)
      return res.status(400).json({ 
        valid: false,
        error: 'Token role does not match requested application type' 
      })
    }

    console.log(`[${requestId}] Token validation successful for user: ${userData.user.email}`)

    return res.status(200).json({
      valid: true,
      user: {
        id: userData.user.id,
        email: userData.user.email,
        name: userProfile?.name || userData.user.email,
        phone: userProfile?.phone
      },
      application: {
        id: validation.application_id,
        type: application.application_type,
        status: application.status
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in token validation:`, error)
    return res.status(500).json({ 
      valid: false,
      error: 'Internal server error' 
    })
  }
}
