.main {
  width: 100%;
  margin: 0 auto;
  background-color: var(--background-off-white); /* Consistent background */
}

/* Hero section */
.hero {
  text-align: center;
  padding: 5rem 2rem;
  /* background-color: var(--background-color); // Covered by inline style in JS */
  /* background-image: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('/images/booking-hero.jpg'); // Now set inline in JS */
  background-size: cover;
  background-position: center;
  margin-bottom: 0; /* Remove bottom margin if sections below have their own padding */
  border-bottom: 1px solid var(--border-color-light);
}

.heroContent {
  background-color: rgba(255, 255, 255, 0.6); /* Light overlay for text readability */
  padding: 2rem;
  border-radius: var(--border-radius-large);
  display: inline-block; /* So background only covers content */
  max-width: 800px;
}

.title {
  font-size: 3rem;
  color: var(--primary-dark);
  margin-bottom: 1rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.description {
  font-size: 1.2rem;
  color: var(--text-color-dark);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Intro Section */
.introSection {
  padding: 3rem 2rem;
  text-align: center;
  background-color: var(--background-color); /* White or very light pastel */
}

.introSection h2 {
  font-size: 2.2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.introSection p {
  font-size: 1.1rem;
  color: var(--text-color-light);
  max-width: 800px;
  margin: 0 auto 1rem auto;
  line-height: 1.7;
}

/* Services Section */
.servicesSection {
  padding: 3rem 2rem;
  background-color: var(--background-off-white); /* Light pastel */
}

.servicesSection h2 {
  text-align: center;
  font-size: 2.2rem;
  color: var(--primary-color);
  margin-bottom: 2.5rem;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

/* Loading container */
.loadingContainer {
  text-align: center;
  padding: 3rem;
  color: var(--text-color-light);
}

/* Service card wrapper for highlighting */
.serviceCardWrapper {
  transition: all 0.3s ease;
}

.serviceCardWrapper.highlighted {
  transform: scale(1.02);
  position: relative;
}

.serviceCardWrapper.highlighted::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border-radius: calc(var(--border-radius-large) + 4px);
  z-index: -1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* Service Card */
.serviceCard {
  background-color: var(--background-color); /* White */
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.serviceCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-large);
}

.serviceImageContainer {
  width: 100%;
  height: 200px; /* Fixed height for image container */
  overflow: hidden;
}

.serviceImage {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Cover the area, might crop */
  transition: transform 0.3s ease;
}

.serviceCard:hover .serviceImage {
  transform: scale(1.05);
}

.serviceCardContent {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Allows content to fill space, pushing button down */
}

.serviceName {
  font-size: 1.6rem;
  color: var(--primary-dark);
  margin-bottom: 0.75rem;
}

.serviceDescription {
  font-size: 1rem;
  color: var(--text-color-light);
  margin-bottom: 1rem;
  line-height: 1.6;
  white-space: pre-line; /* To respect newlines in description */
  flex-grow: 1; /* Allows description to take available space */
}

.serviceDetails {
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  color: var(--text-color);
}

.serviceDetails p {
  margin-bottom: 0.25rem;
}

/* How to Book, Additional Info, Policies, Contact Sections */
.howToBookSection,
.additionalInfoSection,
.policiesSection,
.contactSection {
  padding: 3rem 2rem;
  background-color: var(--background-color); /* White or very light pastel */
}

.additionalInfoSection {
  background-color: var(--background-off-white); /* Alternate for visual separation */
  display: flex; /* For side-by-side info cards */
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
}

.additionalInfoSection > .infoCard {
  flex: 1;
  min-width: 300px;
  max-width: 500px; /* Limit width of individual cards */
}

.howToBookSection .infoCard,
.contactSection .infoCard {
  max-width: 800px;
  margin: 0 auto;
}

.infoCard {
  background-color: var(--background-color-light); /* Slightly off-white for cards */
  padding: 2rem;
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
  margin-bottom: 2rem; /* Default margin, can be overridden */
}

.infoCard h3 {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.infoCard p, .infoCard li {
  color: var(--text-color-light);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.infoCard ul {
  padding-left: 1.5rem;
  list-style: disc; /* Or use custom icons */
}

.infoCard li {
  margin-bottom: 0.75rem;
}

.contactInfo a {
  color: var(--accent-color); /* Use accent for links */
  text-decoration: none;
  font-weight: 500;
}

.contactInfo a:hover {
  text-decoration: underline;
  color: var(--primary-dark);
}

/* Policy Links */
.policyLinks {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin: 1rem 0;
}

.policyLink {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.policyLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transition: width 0.3s ease;
}

.policyLink:hover {
  color: var(--accent-color);
}

.policyLink:hover::after {
  width: 100%;
}

.policyNote {
  font-size: 0.9rem;
  font-style: italic;
  color: var(--text-color-light);
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

/* Buttons - ensure these are defined or adjust as needed */
.button {
  display: inline-block;
  padding: 0.8rem 1.8rem;
  border-radius: var(--border-radius-small);
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  transition: background-color 0.3s ease, transform 0.2s ease;
  cursor: pointer;
  border: none;
  margin-top: auto; /* Pushes button to bottom of flex container (ServiceCardContent) */
}

.buttonPrimary {
  background-color: var(--primary-color);
  color: white;
}

.buttonPrimary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.buttonSecondary {
  background-color: #FF6B6B;
  color: white;
}

.buttonSecondary:hover {
  background-color: #e05c5c;
  transform: translateY(-2px);
}

/* FAQ section */
.faq {
  background-color: #f8f8f8;
  padding: 4rem 2rem;
  margin-bottom: 4rem;
}

.faq h2 {
  text-align: center;
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 3rem;
}

.faqGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
}

.faqItem {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.faqItem h3 {
  font-size: 1.3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.faqItem p {
  color: var(--light-text-color);
  line-height: 1.6;
}

/* Responsive styles */
@media (max-width: 992px) {
  .bookingContainer {
    flex-direction: column;
  }

  .bookingInfo, .bookingForm {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }
  .description {
    font-size: 1.1rem;
  }
  .hero {
    padding: 4rem 1rem;
  }
  .heroContent {
    padding: 1.5rem;
  }
  .introSection, .servicesSection, .howToBookSection, .additionalInfoSection, .contactSection {
    padding: 2rem 1rem;
  }
  .servicesGrid {
    grid-template-columns: 1fr; /* Stack cards on smaller screens */
    gap: 1.5rem;
  }
  .serviceName {
    font-size: 1.4rem;
  }
  .additionalInfoSection {
    flex-direction: column; /* Stack info cards */
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .faqGrid {
    grid-template-columns: 1fr;
  }
}
