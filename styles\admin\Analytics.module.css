.analyticsContainer {
  padding: 2rem;
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
}

.header h1 {
  margin: 0 0 1rem 0;
  font-size: 1.75rem;
  color: #2d3748;
}

.tabsContainer {
  display: flex;
  margin-top: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.tabButton {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 1rem;
  color: #718096;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tabButton:hover {
  color: #4ECDC4;
}

.activeTab {
  color: #4ECDC4;
  border-bottom: 2px solid #4ECDC4;
  font-weight: 600;
}

.timeframeSelector {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeframeButton {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeframeButton:first-child {
  border-radius: 8px 0 0 8px;
}

.timeframeButton:last-child {
  border-radius: 0 8px 8px 0;
}

.timeframeButton.active {
  background-color: #4ECDC4;
  color: white;
  border-color: #4ECDC4;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metricCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.metricCard h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.metricValue {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #2d3748;
}

.columnsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (max-width: 900px) {
  .columnsContainer {
    grid-template-columns: 1fr;
  }
}

.column {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.column h2 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #2d3748;
}

.tableContainer {
  overflow-x: auto;
}

.dataTable {
  width: 100%;
  border-collapse: collapse;
}

.dataTable th {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 2px solid #e2e8f0;
  color: #4a5568;
  font-weight: 600;
  font-size: 0.875rem;
}

.dataTable td {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  font-size: 0.875rem;
}

.dataTable tr:last-child td {
  border-bottom: none;
}

.emptyState {
  text-align: center;
  color: #718096;
  font-style: italic;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem 0;
}

.errorMessage {
  color: #e53e3e;
  margin-bottom: 1rem;
  text-align: center;
}

.retryButton {
  padding: 0.5rem 1.5rem;
  background-color: #4ECDC4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: #3db1a8;
}

.periodControls {
  margin-bottom: 2rem;
}

.periodButtons {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.periodButton {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.periodButton:first-child {
  border-radius: 8px 0 0 8px;
}

.periodButton:last-child {
  border-radius: 0 8px 8px 0;
}

.periodButton.activePeriod {
  background-color: #4ECDC4;
  color: white;
  border-color: #4ECDC4;
}

.dateRangePicker {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f7fafc;
  border-radius: 8px;
}

.dateInput {
  display: flex;
  flex-direction: column;
}

.dateInput label {
  font-size: 0.75rem;
  color: #718096;
  margin-bottom: 0.25rem;
}

.dateInput input {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.applyButton {
  padding: 0.5rem 1rem;
  background-color: #4ECDC4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
  align-self: flex-end;
}

.applyButton:hover {
  background-color: #3db1a8;
}

.contentContainer {
  margin-bottom: 2rem;
}

.exporterContainer {
  margin-top: 2rem;
}

.comingSoon {
  background-color: #fff;
  border-radius: 8px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.comingSoon h3 {
  font-size: 1.5rem;
  color: #2d3748;
  margin-bottom: 1rem;
}

.comingSoon p {
  color: #718096;
  font-size: 1rem;
}
