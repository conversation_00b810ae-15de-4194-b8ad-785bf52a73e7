/**
 * Manual Booking System Test
 * 
 * This script manually tests the booking system functionality
 * by directly interacting with the Supabase database.
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Configuration
const config = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY
};

// Test results tracking
const results = {
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  if (condition) {
    results.passed++;
    log(`PASS: ${message}`, 'success');
  } else {
    results.failed++;
    results.errors.push(message);
    log(`FAIL: ${message}`, 'error');
  }
}

// Test data
const testData = {
  customer: {
    name: 'Test Customer',
    email: `test.customer.${Date.now()}@example.com`,
    phone: '+61400000000',
    marketing_consent: true
  },
  booking: {
    date: '2024-12-25',
    time: '10:00',
    location: 'Test Location',
    message: 'Manual test booking'
  }
};

async function runTests() {
  log('Starting manual booking system tests...');
  
  // Verify configuration
  if (!config.supabaseUrl || !config.supabaseAnonKey || !config.supabaseServiceKey) {
    log('Missing required environment variables', 'error');
    console.log('Required variables:');
    console.log('- NEXT_PUBLIC_SUPABASE_URL:', config.supabaseUrl ? '✓' : '✗');
    console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', config.supabaseAnonKey ? '✓' : '✗');
    console.log('- SUPABASE_SERVICE_ROLE_KEY:', config.supabaseServiceKey ? '✓' : '✗');
    process.exit(1);
  }

  try {
    // Test 1: Database Connectivity
    log('Testing database connectivity...');
    const supabase = createClient(config.supabaseUrl, config.supabaseAnonKey);
    const adminClient = createClient(config.supabaseUrl, config.supabaseServiceKey);
    
    const { data: servicesData, error: servicesError } = await adminClient
      .from('services')
      .select('id, name, duration, price')
      .limit(1);
    
    assert(!servicesError, 'Should connect to database successfully');
    assert(servicesData && servicesData.length > 0, 'Should have services in database');
    
    if (servicesData && servicesData.length > 0) {
      testData.serviceId = servicesData[0].id;
      testData.serviceName = servicesData[0].name;
      log(`Using service: ${servicesData[0].name} (ID: ${servicesData[0].id})`);
    }

    // Test 2: Customer Operations
    log('Testing customer operations...');
    
    // Create customer
    const { data: newCustomer, error: createError } = await adminClient
      .from('customers')
      .insert([testData.customer])
      .select();
    
    assert(!createError, 'Should create customer without error');
    assert(newCustomer && newCustomer.length === 1, 'Should return created customer');
    
    if (newCustomer && newCustomer.length > 0) {
      testData.customerId = newCustomer[0].id;
      log(`Created customer: ${newCustomer[0].name} (ID: ${newCustomer[0].id})`);
      
      // Read customer
      const { data: readCustomer, error: readError } = await adminClient
        .from('customers')
        .select('*')
        .eq('id', testData.customerId)
        .single();
      
      assert(!readError, 'Should read customer without error');
      assert(readCustomer.name === testData.customer.name, 'Customer name should match');
      
      // Update customer
      const updatedPhone = '+61400000001';
      const { data: updatedCustomer, error: updateError } = await adminClient
        .from('customers')
        .update({ phone: updatedPhone })
        .eq('id', testData.customerId)
        .select();
      
      assert(!updateError, 'Should update customer without error');
      assert(updatedCustomer[0].phone === updatedPhone, 'Customer phone should be updated');
    }

    // Test 3: Booking Operations
    log('Testing booking operations...');
    
    if (testData.customerId && testData.serviceId) {
      // Create booking
      const startTime = new Date(`${testData.booking.date}T${testData.booking.time}:00`);
      const endTime = new Date(startTime);
      endTime.setHours(endTime.getHours() + 2); // 2 hour duration
      
      const bookingData = {
        customer_id: testData.customerId,
        service_id: testData.serviceId,
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
        status: 'confirmed',
        location: testData.booking.location,
        notes: testData.booking.message
      };
      
      const { data: newBooking, error: bookingError } = await adminClient
        .from('bookings')
        .insert([bookingData])
        .select();
      
      assert(!bookingError, 'Should create booking without error');
      assert(newBooking && newBooking.length === 1, 'Should return created booking');
      
      if (newBooking && newBooking.length > 0) {
        testData.bookingId = newBooking[0].id;
        log(`Created booking: ${newBooking[0].id}`);
        
        // Read booking with relations
        const { data: readBooking, error: readBookingError } = await adminClient
          .from('bookings')
          .select(`
            *,
            customers (name, email, phone),
            services (name, duration, price)
          `)
          .eq('id', testData.bookingId)
          .single();
        
        assert(!readBookingError, 'Should read booking with relations without error');
        assert(readBooking.customers.name === testData.customer.name, 'Related customer name should match');
        assert(readBooking.services.name === testData.serviceName, 'Related service name should match');
        
        // Update booking status
        const { data: updatedBooking, error: updateBookingError } = await adminClient
          .from('bookings')
          .update({ status: 'pending' })
          .eq('id', testData.bookingId)
          .select();
        
        assert(!updateBookingError, 'Should update booking status without error');
        assert(updatedBooking[0].status === 'pending', 'Booking status should be updated');
      }
    }

    // Test 4: Data Validation
    log('Testing data validation...');
    
    // Test invalid booking data
    const invalidBooking = {
      customer_id: testData.customerId,
      // Missing required fields
      location: 'Test Location'
    };
    
    const { data: invalidData, error: validationError } = await adminClient
      .from('bookings')
      .insert([invalidBooking])
      .select();
    
    assert(validationError !== null, 'Should return error for invalid booking data');

    // Test 5: Table Structure
    log('Testing table structure...');
    
    // Check bookings table structure
    const { data: bookingsCount, error: bookingsCountError } = await adminClient
      .from('bookings')
      .select('count')
      .limit(1);
    
    assert(!bookingsCountError, 'Bookings table should be accessible');
    
    // Check customers table structure
    const { data: customersCount, error: customersCountError } = await adminClient
      .from('customers')
      .select('count')
      .limit(1);
    
    assert(!customersCountError, 'Customers table should be accessible');

    // Test 6: Cleanup
    log('Cleaning up test data...');
    
    if (testData.bookingId) {
      await adminClient.from('bookings').delete().eq('id', testData.bookingId);
      log('Deleted test booking');
    }
    
    if (testData.customerId) {
      await adminClient.from('customers').delete().eq('id', testData.customerId);
      log('Deleted test customer');
    }

  } catch (error) {
    assert(false, `Test execution failed: ${error.message}`);
    log(`Error details: ${error.stack}`, 'error');
  }
  
  // Report results
  log('\n=== TEST RESULTS ===');
  log(`Passed: ${results.passed}`);
  log(`Failed: ${results.failed}`);
  
  if (results.failed > 0) {
    log('\nFailed tests:');
    results.errors.forEach(error => log(`- ${error}`, 'error'));
    process.exit(1);
  } else {
    log('All tests passed! ✅', 'success');
    process.exit(0);
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(error => {
    log(`Test runner failed: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runTests, testData, results };
