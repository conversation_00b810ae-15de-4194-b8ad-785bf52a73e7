import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Only admins can export customer data
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' })
    }
  } catch (error) {
    return res.status(403).json({ error: 'Authorization failed' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    const {
      marketing_only,
      format = 'csv',
      include_bookings,
      search,
      city,
      state,
      has_bookings,
      date_added,
      marketing_consent
    } = req.query

    // Build query
    let query = client
      .from('customers')
      .select('*')

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,phone.ilike.%${search}%`)
    }

    // Apply location filters
    if (city) {
      query = query.ilike('city', `%${city}%`)
    }
    if (state) {
      query = query.ilike('state', `%${state}%`)
    }

    // Apply booking history filter
    if (has_bookings === 'true') {
      const { data: customersWithBookings } = await client
        .from('bookings')
        .select('customer_id')
        .not('customer_id', 'is', null)
        .order('customer_id')

      if (customersWithBookings && customersWithBookings.length > 0) {
        const customerIds = [...new Set(customersWithBookings.map(b => b.customer_id))]
        query = query.in('id', customerIds)
      } else {
        // No customers with bookings found
        return res.status(200).json([])
      }
    } else if (has_bookings === 'false') {
      const { data: customersWithBookings } = await client
        .from('bookings')
        .select('customer_id')
        .not('customer_id', 'is', null)
        .order('customer_id')

      if (customersWithBookings && customersWithBookings.length > 0) {
        const customerIds = [...new Set(customersWithBookings.map(b => b.customer_id))]
        query = query.not('id', 'in', customerIds)
      }
    }

    // Apply date added filter
    if (date_added) {
      const days = parseInt(date_added)
      if (!isNaN(days)) {
        const cutoffDate = new Date()
        cutoffDate.setDate(cutoffDate.getDate() - days)
        query = query.gte('created_at', cutoffDate.toISOString())
      }
    }

    // Filter by marketing consent if requested
    if (marketing_only === 'true' || marketing_consent === 'true') {
      query = query.eq('marketing_consent', true)
    } else if (marketing_consent === 'false') {
      query = query.eq('marketing_consent', false)
    }

    // Execute query
    const { data, error } = await query

    if (error) {
      throw error
    }

    // Get booking history if requested
    let exportData = [...data]

    if (include_bookings === 'true' && exportData.length > 0) {
      const customerIds = exportData.map(c => c.id)

      const { data: bookings, error: bookingsError } = await client
        .from('bookings')
        .select(`
          id,
          customer_id,
          start_time,
          end_time,
          status,
          services:service_id (name, price)
        `)
        .in('customer_id', customerIds)
        .order('start_time', { ascending: false })

      if (bookingsError) {
        throw bookingsError
      }

      // Add booking history to each customer
      exportData = exportData.map(customer => {
        const customerBookings = bookings.filter(b => b.customer_id === customer.id)
        return {
          ...customer,
          bookings: customerBookings
        }
      })
    }

    // Format data based on requested format
    if (format === 'json') {
      // Return JSON data
      return res.status(200).json(exportData)
    } else if (format === 'excel') {
      // For Excel format, we'll use a library like exceljs in a real implementation
      // For now, we'll just return CSV with a different content type
      const csvData = convertToCSV(exportData, include_bookings === 'true')

      // Set headers for Excel file download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.setHeader('Content-Disposition', 'attachment; filename=customers.xlsx')

      // Send CSV data (in a real implementation, this would be Excel data)
      return res.status(200).send(csvData)
    } else {
      // Convert to CSV
      const csvData = convertToCSV(exportData, include_bookings === 'true')

      // Set headers for file download
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', 'attachment; filename=customers.csv')

      // Send CSV data
      return res.status(200).send(csvData)
    }
  } catch (error) {
    console.error('Error exporting customers:', error)
    return res.status(500).json({ error: 'Failed to export customers' })
  }
}

// Helper function to convert JSON to CSV
function convertToCSV(data, includeBookings = false) {
  if (data.length === 0) {
    return ''
  }

  // Define headers based on data and options
  let headers = [
    'id',
    'name',
    'email',
    'phone',
    'address',
    'city',
    'state',
    'postal_code',
    'country',
    'marketing_consent',
    'created_at'
  ]

  // Create CSV header row
  let csv = headers.join(',')

  // Add booking headers if needed
  if (includeBookings) {
    const bookingHeaders = [
      'last_booking_date',
      'last_booking_service',
      'last_booking_status',
      'total_bookings',
      'total_spent'
    ]
    csv += ',' + bookingHeaders.join(',')
  }

  csv += '\n'

  // Add data rows
  data.forEach(customer => {
    const values = headers.map(header => {
      const value = customer[header]
      // Handle null values, commas, and quotes in the data
      if (value === null || value === undefined) {
        return ''
      }

      // Format boolean values
      if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No'
      }

      // Format dates
      if (header === 'created_at' && value) {
        return new Date(value).toISOString().split('T')[0]
      }

      const stringValue = String(value)
      // Escape quotes and wrap in quotes if contains comma or quote
      if (stringValue.includes(',') || stringValue.includes('"')) {
        return `"${stringValue.replace(/"/g, '""')}"`
      }
      return stringValue
    })

    // Add booking data if needed
    if (includeBookings && customer.bookings) {
      const bookings = customer.bookings || []
      const lastBooking = bookings[0] || {}

      // Calculate booking statistics
      const lastBookingDate = lastBooking.start_time
        ? new Date(lastBooking.start_time).toISOString().split('T')[0]
        : ''

      const lastBookingService = lastBooking.services
        ? lastBooking.services.name
        : ''

      const lastBookingStatus = lastBooking.status || ''

      const totalBookings = bookings.length

      const totalSpent = bookings.reduce((total, booking) => {
        const price = booking.services ? booking.services.price : 0
        return total + parseFloat(price || 0)
      }, 0).toFixed(2)

      // Add booking values
      values.push(
        lastBookingDate,
        lastBookingService,
        lastBookingStatus,
        totalBookings.toString(),
        totalSpent
      )
    }

    csv += values.join(',') + '\n'
  })

  return csv
}
