#!/usr/bin/env node

/**
 * HTTPS Migration Scanner
 * Scans codebase for HTTP URLs that need to be updated to HTTPS
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🔍 HTTPS Migration Scanner for Ocean Soul Sparkles\n');

let foundIssues = [];
let fixedIssues = [];

// Files and directories to scan
const scanPaths = [
  'pages',
  'components',
  'lib',
  'styles',
  'public',
  'scripts',
  'docs',
  '__tests__',
  '.env.example',
  '.env.test',
  '.env.test.example',
  'README.md',
  'next.config.js',
  'package.json'
];

// Files to exclude from scanning
const excludePatterns = [
  'node_modules',
  '.git',
  '.next',
  'dist',
  'build',
  '.env.local',
  '.env.production',
  'HTTPS-IMPLEMENTATION-GUIDE.md',
  'scripts/https-migration-scanner.js'
];

// HTTP patterns to find and fix
const httpPatterns = [
  {
    pattern: /http:\/\/localhost:3000/g,
    replacement: 'https://www.oceansoulsparkles.com.au',
    description: 'localhost:3000 references'
  },
  {
    pattern: /http:\/\/localhost:3001/g,
    replacement: 'https://www.oceansoulsparkles.com.au',
    description: 'localhost:3001 references'
  },
  {
    pattern: /http:\/\/oceansoulsparkles\.com\.au/g,
    replacement: 'https://www.oceansoulsparkles.com.au',
    description: 'HTTP oceansoulsparkles.com.au references'
  },
  {
    pattern: /http:\/\/www\.oceansoulsparkles\.com\.au/g,
    replacement: 'https://www.oceansoulsparkles.com.au',
    description: 'HTTP www.oceansoulsparkles.com.au references'
  },
  {
    pattern: /'http:\/\/[^']*'/g,
    replacement: null, // Will be handled manually
    description: 'Quoted HTTP URLs (manual review needed)'
  },
  {
    pattern: /"http:\/\/[^"]*"/g,
    replacement: null, // Will be handled manually
    description: 'Double-quoted HTTP URLs (manual review needed)'
  }
];

/**
 * Check if file should be excluded
 */
function shouldExclude(filePath) {
  return excludePatterns.some(pattern => filePath.includes(pattern));
}

/**
 * Scan a single file for HTTP URLs
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(rootDir, filePath);
    
    httpPatterns.forEach(({ pattern, replacement, description }) => {
      const matches = content.match(pattern);
      
      if (matches) {
        matches.forEach(match => {
          foundIssues.push({
            file: relativePath,
            match: match,
            pattern: description,
            replacement: replacement,
            line: getLineNumber(content, match)
          });
        });
      }
    });
    
  } catch (error) {
    console.error(`Error scanning ${filePath}:`, error.message);
  }
}

/**
 * Get line number for a match in content
 */
function getLineNumber(content, match) {
  const lines = content.substring(0, content.indexOf(match)).split('\n');
  return lines.length;
}

/**
 * Recursively scan directory
 */
function scanDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (shouldExclude(itemPath)) {
        return;
      }
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else if (stat.isFile()) {
        // Only scan text files
        const ext = path.extname(item).toLowerCase();
        const textExtensions = ['.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.txt', '.css', '.scss', '.html', '.xml', '.yml', '.yaml'];
        
        if (textExtensions.includes(ext) || !ext) {
          scanFile(itemPath);
        }
      }
    });
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

/**
 * Fix issues automatically where possible
 */
function fixIssues(dryRun = true) {
  const fixableIssues = foundIssues.filter(issue => issue.replacement);
  
  if (fixableIssues.length === 0) {
    console.log('✅ No automatically fixable issues found');
    return;
  }
  
  console.log(`\n🔧 ${dryRun ? 'Would fix' : 'Fixing'} ${fixableIssues.length} issues...\n`);
  
  // Group issues by file
  const issuesByFile = {};
  fixableIssues.forEach(issue => {
    if (!issuesByFile[issue.file]) {
      issuesByFile[issue.file] = [];
    }
    issuesByFile[issue.file].push(issue);
  });
  
  Object.entries(issuesByFile).forEach(([file, issues]) => {
    const filePath = path.join(rootDir, file);
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      issues.forEach(issue => {
        if (content.includes(issue.match)) {
          content = content.replace(new RegExp(escapeRegExp(issue.match), 'g'), issue.replacement);
          modified = true;
          
          if (!dryRun) {
            fixedIssues.push(issue);
          }
          
          console.log(`${dryRun ? '📝 Would fix' : '✅ Fixed'}: ${file}:${issue.line} - ${issue.match} → ${issue.replacement}`);
        }
      });
      
      if (modified && !dryRun) {
        fs.writeFileSync(filePath, content, 'utf8');
      }
      
    } catch (error) {
      console.error(`Error fixing ${file}:`, error.message);
    }
  });
}

/**
 * Escape special regex characters
 */
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Generate report
 */
function generateReport() {
  console.log('\n📊 HTTPS Migration Scan Results\n');
  
  if (foundIssues.length === 0) {
    console.log('🎉 No HTTP URLs found! Your codebase is ready for HTTPS.');
    return;
  }
  
  console.log(`Found ${foundIssues.length} HTTP references:\n`);
  
  // Group by pattern
  const issuesByPattern = {};
  foundIssues.forEach(issue => {
    if (!issuesByPattern[issue.pattern]) {
      issuesByPattern[issue.pattern] = [];
    }
    issuesByPattern[issue.pattern].push(issue);
  });
  
  Object.entries(issuesByPattern).forEach(([pattern, issues]) => {
    console.log(`\n🔍 ${pattern} (${issues.length} occurrences):`);
    issues.forEach(issue => {
      const status = issue.replacement ? '🔧 Auto-fixable' : '⚠️  Manual review needed';
      console.log(`  ${status}: ${issue.file}:${issue.line} - ${issue.match}`);
    });
  });
  
  const fixableCount = foundIssues.filter(issue => issue.replacement).length;
  const manualCount = foundIssues.length - fixableCount;
  
  console.log(`\n📈 Summary:`);
  console.log(`  🔧 Auto-fixable: ${fixableCount}`);
  console.log(`  ⚠️  Manual review: ${manualCount}`);
  console.log(`  📁 Total files affected: ${new Set(foundIssues.map(i => i.file)).size}`);
}

/**
 * Main execution
 */
async function main() {
  const args = process.argv.slice(2);
  const shouldFix = args.includes('--fix');
  const dryRun = !shouldFix;
  
  console.log('Scanning for HTTP URLs...\n');
  
  // Scan all specified paths
  scanPaths.forEach(scanPath => {
    const fullPath = path.join(rootDir, scanPath);
    
    if (fs.existsSync(fullPath)) {
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (stat.isFile()) {
        scanFile(fullPath);
      }
    }
  });
  
  generateReport();
  
  if (foundIssues.length > 0) {
    fixIssues(dryRun);
    
    if (dryRun) {
      console.log('\n💡 To automatically fix the auto-fixable issues, run:');
      console.log('   npm run https-scan -- --fix');
    } else {
      console.log(`\n✅ Fixed ${fixedIssues.length} issues automatically.`);
      
      const remainingIssues = foundIssues.filter(issue => !issue.replacement);
      if (remainingIssues.length > 0) {
        console.log(`⚠️  ${remainingIssues.length} issues require manual review.`);
      }
    }
  }
  
  console.log('\n🔒 Next steps:');
  console.log('1. Review and fix any manual issues above');
  console.log('2. Run: npm run build:secure');
  console.log('3. Deploy to Vercel');
  console.log('4. Test HTTPS functionality');
}

// Run the scanner
main().catch(error => {
  console.error('❌ Scanner failed:', error);
  process.exit(1);
});
