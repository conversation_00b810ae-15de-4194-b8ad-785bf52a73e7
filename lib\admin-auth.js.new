// This utility centralizes admin authentication for API endpoints
import { getAdminClient } from '@/lib/supabase';

/**
 * Custom error class for authentication errors
 */
export class AuthenticationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

/**
 * Centralized authentication middleware for admin API endpoints
 * Uses a single, consistent authentication method
 * Includes error handling and timeout protection
 *
 * @param {Object} req - HTTP request object
 * @returns {Object} - { user, role, authorized, error }
 */
export async function authenticateAdminRequest(req) {
  // Generate a unique auth ID for tracking
  const authId = Math.random().toString(36).substring(2, 8);

  // Add basic logging for authentication process
  console.log(`[${authId}] Starting authentication process for: ${req.url}`);

  // Check if this is a diagnostics request
  const isDiagnostics = req.url && (
    req.url.includes('/api/admin/diagnostics') ||
    (req.headers.referer && req.headers.referer.includes('/admin/diagnostics'))
  );

  // Set a timeout to prevent hanging authentication
  // Use a shorter timeout for diagnostics requests, longer for normal requests
  const timeoutDuration = isDiagnostics ? 5000 : 20000; // Increased timeout for normal requests
  let authTimeout = null;

  // Create a timeout promise that doesn't reject but resolves with a timeout indicator
  // This prevents unhandled promise rejections and allows more graceful recovery
  const timeoutPromise = new Promise((resolve) => {
    authTimeout = setTimeout(() => {
      console.warn(`[${authId}] Authentication timeout after ${timeoutDuration}ms`);
      resolve({
        timeoutError: true,
        error: new Error(`Authentication timeout after ${timeoutDuration}ms`)
      });
    }, timeoutDuration);
  });

  // Log the timeout duration for debugging
  console.log(`[${authId}] Authentication timeout set to ${timeoutDuration}ms`);

  try {
    // Log authentication attempt in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${authId}] Processing authentication for ${req.url}`);
    }

    // Handle cross-origin requests consistently
    if (process.env.NEXT_PUBLIC_ALLOW_CROSS_ORIGIN === 'true') {
      const origin = req.headers.origin || '';
      const referer = req.headers.referer || '';

      // Log cross-origin requests in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log(`[${authId}] Cross-origin request detected from ${origin || referer}`);
      }
    }

    // Extract auth token from request using a single, consistent method
    let token = null;

    // PRIMARY METHOD: Check Authorization header (Bearer token)
    // This is the ONLY method we should use for API authentication
    const authHeader = req.headers.authorization;
    if (authHeader) {
      // Handle different formats of Authorization header
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
        console.log(`[${authId}] Using token from Authorization header (Bearer format)`);
      } else {
        // If it doesn't start with Bearer, use the whole header as token
        token = authHeader;
        console.log(`[${authId}] Using token from Authorization header (non-Bearer format)`);
      }
    }

    // SECONDARY METHOD: Check X-Auth-Token header (for cross-origin requests)
    // This is used as a fallback for cross-origin requests
    if (!token && req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
      console.log(`[${authId}] Using token from X-Auth-Token header`);
    }

    // Log token format for debugging (only first few characters for security)
    if (token) {
      const tokenPreview = token.substring(0, 10) + '...';
      console.log(`[${authId}] Token format preview: ${tokenPreview}`);
    }

    // TERTIARY METHOD: Check cookie (for cross-origin requests)
    // This is used as a fallback for cross-origin requests
    if (!token && req.cookies && req.cookies.oss_auth_token) {
      try {
        const cookieData = JSON.parse(req.cookies.oss_auth_token);
        if (cookieData && cookieData.access_token) {
          token = cookieData.access_token;
          console.log(`[${authId}] Using token from cookie`);
        }
      } catch (cookieError) {
        console.warn(`[${authId}] Error parsing cookie:`, cookieError);
      }
    }

    // Check for API key in headers (for system integrations)
    const apiKey = req.headers['x-api-key'];
    if (apiKey && apiKey === process.env.ADMIN_API_KEY) {
      console.log(`[${authId}] Using API key authentication`);

      // Clear the timeout since we've completed successfully
      if (authTimeout) {
        clearTimeout(authTimeout);
        authTimeout = null;
      }

      return {
        user: { id: 'system', email: '<EMAIL>' },
        role: 'system',
        authorized: true,
        error: null
      };
    }

    // If no token was found, return unauthorized
    if (!token) {
      console.warn(`[${authId}] No authentication token found in any supported location`);

      // Clear the timeout since we've completed
      if (authTimeout) {
        clearTimeout(authTimeout);
        authTimeout = null;
      }

      return {
        user: null,
        role: null,
        authorized: false,
        error: new Error('No authentication token provided')
      };
    }

    // Basic validation - check if token is empty or whitespace
    if (token.trim() === '') {
      console.warn(`[${authId}] Token is empty or contains only whitespace`);

      // Clear the timeout
      if (authTimeout) {
        clearTimeout(authTimeout);
        authTimeout = null;
      }

      return {
        user: null,
        role: null,
        authorized: false,
        error: new Error('Invalid token: empty or whitespace')
      };
    }

    // We'll skip strict JWT format validation here and let the Supabase client handle it
    // This allows for more flexibility with token formats while maintaining security
    // through the actual verification process

    // Log that we're proceeding with token verification
    console.log(`[${authId}] Proceeding with token verification`);

    // Check if token looks like a JWT (for logging purposes only)
    const parts = token.split('.');
    if (parts.length === 3) {
      console.log(`[${authId}] Token has standard JWT format (3 parts)`);
    } else {
      console.warn(`[${authId}] Token does not have standard JWT format (has ${parts.length} parts)`);
      // We'll continue anyway and let the verification process determine if it's valid
    }

    // Verify token signature and decode payload
    try {
      // Check if mcpClient has verifyToken method
      if (typeof mcpClient.verifyToken !== 'function') {
        console.warn(`[${authId}] mcpClient.verifyToken is not a function, using getCurrentUserWithToken instead`);

        // Use getCurrentUserWithToken as a fallback
        const { user, role, error } = await mcpClient.getCurrentUserWithToken(token);

        if (error || !user) {
          console.warn(`[${authId}] Token verification failed: ${error?.message}`);
          return {
            user: null,
            role: null,
            authorized: false,
            error: new Error('Invalid token signature')
          };
        }

        // Skip the rest of the validation since we already have user and role
        console.log(`[${authId}] Token verified using getCurrentUserWithToken`);

        // Check if user has admin or staff role
        if (role === 'admin' || role === 'staff') {
          console.log(`[${authId}] Authentication successful for user: ${user.email}`);

          // Clear the timeout since we've completed
          if (authTimeout) {
            clearTimeout(authTimeout);
            authTimeout = null;
          }

          return {
            user,
            role,
            authorized: true,
            error: null
          };
        } else {
          console.warn(`[${authId}] User does not have admin or staff role`);

          return {
            user,
            role,
            authorized: false,
            error: new Error('Insufficient permissions')
          };
        }
      }

      // Use verifyToken with improved error handling and timeout protection
      console.log(`[${authId}] Calling mcpClient.verifyToken`);

      // Create a verification promise
      const verifyPromise = mcpClient.verifyToken(token);

      // Create a timeout promise that resolves instead of rejects
      // This prevents unhandled promise rejections and memory leaks
      const verifyTimeoutPromise = new Promise((resolve) => {
        const verifyTimeoutId = setTimeout(() => {
          console.warn(`[${authId}] Token verification timed out after 15 seconds`);
          // Clear the timeout ID to prevent memory leaks
          clearTimeout(verifyTimeoutId);
          // Resolve with an error object instead of rejecting
          resolve({
            data: null,
            error: new Error('Token verification timed out after 15 seconds')
          });
        }, 15000); // 15 second timeout for verification
      });

      // Race the promises
      let result;
      try {
        result = await Promise.race([verifyPromise, verifyTimeoutPromise]);
        console.log(`[${authId}] Token verification completed`);

        // Check if we got a timeout result
        if (result && result.error && result.error.message &&
            result.error.message.includes('timed out')) {
          console.error(`[${authId}] Token verification timeout detected`);

          // Clear the main timeout since we're returning
          if (authTimeout) {
            clearTimeout(authTimeout);
            authTimeout = null;
          }

          return {
            user: null,
            role: null,
            authorized: false,
            error: result.error
          };
        }
      } catch (verifyError) {
        console.error(`[${authId}] Token verification error:`, verifyError.message);

        // Clear the main timeout since we're returning
        if (authTimeout) {
          clearTimeout(authTimeout);
          authTimeout = null;
        }

        return {
          user: null,
          role: null,
          authorized: false,
          error: new Error(`Token verification error: ${verifyError.message}`)
        };
      }

      // Check if result has expected structure with proper null checks
      if (!result) {
        console.warn(`[${authId}] Token verification returned null result`);
        return {
          user: null,
          role: null,
          authorized: false,
          error: new Error('Token verification returned null result')
        };
      }

      if (result.error) {
        console.warn(`[${authId}] Token verification failed: ${result.error.message || 'Unknown error'}`);
        return {
          user: null,
          role: null,
          authorized: false,
          error: new Error(`Invalid token: ${result.error.message || 'Unknown error'}`)
        };
      }

      // Safely extract user and role with null checks
      const user = result.data?.user || null;
      const role = result.data?.role || null;

      if (!user) {
        console.warn(`[${authId}] Token verification returned no user`);
        return {
          user: null,
          role: null,
          authorized: false,
          error: new Error('Token verification returned no user')
        };
      }

      // Log successful verification
      console.log(`[${authId}] Token verification successful for user: ${user.email}`);

      const payload = { ...user, role };

      // Check if token is expired
      if (payload.exp && payload.exp * 1000 < Date.now()) {
        console.warn(`[${authId}] Token expired at ${new Date(payload.exp * 1000).toISOString()}`);
        return {
          user: null,
          role: null,
          authorized: false,
          error: new Error('Token expired')
        };
      }
    } catch (decodeError) {
      console.warn(`[${authId}] Failed to decode token payload:`, decodeError);
      return {
        user: null,
        role: null,
        authorized: false,
        error: new Error('Token format invalid: payload cannot be decoded')
      };
    }

    // Validate the token and get user information
    try {
      // Race the auth check against a timeout
      const authResult = await Promise.race([
        mcpClient.getCurrentUserWithToken(token),
        timeoutPromise
      ]);

      // Clear the timeout since we've completed
      if (authTimeout) {
        clearTimeout(authTimeout);
        authTimeout = null;
      }

      // Check if we got a timeout error
      if (authResult && authResult.timeoutError) {
        console.error(`[${authId}] Authentication timed out: ${authResult.error?.message}`);

        // Log timeout error in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log(`[${authId}] Authentication timed out, returning error`);
        }

        return {
          user: null,
          role: null,
          authorized: false,
          error: authResult.error || new Error('Authentication timed out')
        };
      }

      // Check if authentication was successful
      if (authResult && !authResult.error && authResult.user &&
          (authResult.role === 'admin' || authResult.role === 'staff')) {

        console.log(`[${authId}] Authentication successful for user: ${authResult.user.email}`);

        return {
          user: authResult.user,
          role: authResult.role,
          authorized: true,
          error: null
        };
      } else if (authResult && authResult.error) {
        // Log specific error for debugging
        console.warn(`[${authId}] Token validation failed: ${authResult.error.message || 'Unknown error'}`);

        return {
          user: null,
          role: null,
          authorized: false,
          error: new Error(`Invalid authentication token: ${authResult.error.message}`)
        };
      } else {
        console.warn(`[${authId}] User does not have admin or staff role`);

        return {
          user: authResult?.user || null,
          role: authResult?.role || null,
          authorized: false,
          error: new Error('Insufficient permissions')
        };
      }
    } catch (error) {
      console.error(`[${authId}] Authentication error:`, error);

      return {
        user: null,
        role: null,
        authorized: false,
        error: error
      };
    }
  } catch (error) {
    // Clear timeout to prevent memory leaks
    if (authTimeout) {
      clearTimeout(authTimeout);
      authTimeout = null;
    }

    console.error(`[${authId}] Admin authentication error:`, error);
    return {
      user: null,
      role: null,
      authorized: false,
      error: error
    };
  }
}