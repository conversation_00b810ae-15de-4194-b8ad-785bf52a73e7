import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/TokenManagement.module.css'

export default function TokenManagement({ userId, userEmail, onClose }) {
  const [tokens, setTokens] = useState([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(null)

  useEffect(() => {
    if (userId) {
      fetchTokens()
    }
  }, [userId])

  const fetchTokens = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/tokens/manage?userId=${userId}`)
      const result = await response.json()

      if (response.ok && result.success) {
        setTokens(result.tokens)
      } else {
        toast.error(result.error || 'Failed to fetch tokens')
      }
    } catch (error) {
      console.error('Error fetching tokens:', error)
      toast.error('Failed to fetch tokens')
    } finally {
      setLoading(false)
    }
  }

  const handleTokenAction = async (tokenId, action) => {
    try {
      setActionLoading(tokenId)
      
      const response = await fetch('/api/admin/tokens/manage', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenId,
          action
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success(result.message)
        await fetchTokens() // Refresh tokens
      } else {
        toast.error(result.error || `Failed to ${action} token`)
      }
    } catch (error) {
      console.error(`Error ${action}ing token:`, error)
      toast.error(`Failed to ${action} token`)
    } finally {
      setActionLoading(null)
    }
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { label: 'Active', className: styles.statusActive },
      expired: { label: 'Expired', className: styles.statusExpired },
      used: { label: 'Used', className: styles.statusUsed }
    }

    const config = statusConfig[status] || { label: status, className: styles.statusDefault }

    return (
      <span className={`${styles.statusBadge} ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('Copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  if (loading) {
    return (
      <div className={styles.overlay}>
        <div className={styles.modal}>
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading tokens...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.header}>
          <h2>Application Tokens</h2>
          <p>Managing tokens for: <strong>{userEmail}</strong></p>
          <button onClick={onClose} className={styles.closeButton}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <div className={styles.content}>
          {tokens.length === 0 ? (
            <div className={styles.emptyState}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="16" r="1"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
              <h3>No Tokens Found</h3>
              <p>No application tokens have been generated for this user yet.</p>
            </div>
          ) : (
            <div className={styles.tokensList}>
              {tokens.map((token) => (
                <div key={token.id} className={styles.tokenCard}>
                  <div className={styles.tokenHeader}>
                    <div className={styles.tokenInfo}>
                      <div className={styles.tokenId}>
                        <span className={styles.label}>Token:</span>
                        <code className={styles.tokenValue}>{token.token}</code>
                        <button
                          onClick={() => copyToClipboard(token.token)}
                          className={styles.copyButton}
                          title="Copy token preview"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                          </svg>
                        </button>
                      </div>
                      {getStatusBadge(token.status)}
                    </div>
                    
                    <div className={styles.tokenActions}>
                      {token.status === 'active' && (
                        <>
                          <button
                            onClick={() => handleTokenAction(token.id, 'extend')}
                            disabled={actionLoading === token.id}
                            className={styles.actionButton}
                            title="Extend expiration by 7 days"
                          >
                            {actionLoading === token.id ? (
                              <div className={styles.miniSpinner}></div>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                              </svg>
                            )}
                            Extend
                          </button>
                          <button
                            onClick={() => handleTokenAction(token.id, 'invalidate')}
                            disabled={actionLoading === token.id}
                            className={`${styles.actionButton} ${styles.dangerButton}`}
                            title="Invalidate this token"
                          >
                            {actionLoading === token.id ? (
                              <div className={styles.miniSpinner}></div>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="15" y1="9" x2="9" y2="15"/>
                                <line x1="9" y1="9" x2="15" y2="15"/>
                              </svg>
                            )}
                            Invalidate
                          </button>
                        </>
                      )}
                    </div>
                  </div>

                  <div className={styles.tokenDetails}>
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Type:</span>
                      <span className={styles.value}>{token.tokenType}</span>
                    </div>
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Created:</span>
                      <span className={styles.value}>{formatDate(token.createdAt)}</span>
                    </div>
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Expires:</span>
                      <span className={styles.value}>{formatDate(token.expiresAt)}</span>
                    </div>
                    {token.usedAt && (
                      <div className={styles.detailRow}>
                        <span className={styles.label}>Used:</span>
                        <span className={styles.value}>{formatDate(token.usedAt)}</span>
                      </div>
                    )}
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Created by:</span>
                      <span className={styles.value}>{token.createdBy}</span>
                    </div>
                    {token.ipAddress && (
                      <div className={styles.detailRow}>
                        <span className={styles.label}>IP Address:</span>
                        <span className={styles.value}>{token.ipAddress}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className={styles.footer}>
          <button onClick={onClose} className={styles.closeFooterButton}>
            Close
          </button>
        </div>
      </div>
    </div>
  )
}
