.heroSection {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: var(--text-inverse);
  overflow: hidden;
  min-height: 600px;
  width: 100%;
  will-change: background-position-y; /* Optimize for animations */
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
  z-index: 1;
}

.heroContent {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 var(--spacing-lg);
}

.heroTitle {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.heroSubtitle {
  font-size: 1.5rem;
  font-weight: 400;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.heroCta {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.ctaButton {
  padding: 0.875rem 2rem;
  font-size: 1.125rem;
}

.secondaryCtaButton {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.secondaryCtaButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Scroll indicator */
.scrollIndicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-inverse);
  font-size: 0.875rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  z-index: 2;
  cursor: pointer;
}

.scrollIndicator:hover {
  opacity: 1;
}

.scrollIcon {
  width: 30px;
  height: 50px;
  border: 2px solid var(--text-inverse);
  border-radius: 15px;
  margin-bottom: 0.5rem;
  position: relative;
}

.scrollIcon::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  width: 6px;
  height: 6px;
  background-color: var(--text-inverse);
  border-radius: 50%;
  transform: translateX(-50%);
  animation: scrollAnimation 2s infinite;
}

@keyframes scrollAnimation {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
}

/* Animation classes */
.animateItem {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.animateItem.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .heroTitle {
    font-size: 3.5rem;
  }

  .heroSubtitle {
    font-size: 1.35rem;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.75rem;
  }

  .heroSubtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
  }

  .heroCta {
    flex-direction: column;
    gap: 1rem;
  }

  .ctaButton,
  .secondaryCtaButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2.25rem;
  }

  .heroSubtitle {
    font-size: 1.125rem;
  }

  .scrollIndicator {
    display: none;
  }
}
