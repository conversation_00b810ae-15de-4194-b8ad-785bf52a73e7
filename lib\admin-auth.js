/**
 * Admin Authentication Middleware
 *
 * This module provides a simplified, consistent approach to server-side authentication
 * for admin API endpoints using the unified Supabase client.
 */

import { getAdminClient, isKnownAdmin } from './supabase';
import { secureLog, isAdminAccessAllowed, sanitizeErrorMessage } from './security-utils';

/**
 * Custom error class for authentication errors
 */
export class AuthenticationError extends Error {
  constructor(message, statusCode = 401) {
    super(message);
    this.name = 'AuthenticationError';
    this.statusCode = statusCode;
  }
}

/**
 * Extract and validate token from request
 * Enhanced with better validation and error handling
 * Updated to match auth-token-manager.js token storage locations
 *
 * @param {Object} req - HTTP request object
 * @returns {Object} - { token, source, error }
 */
function extractToken(req) {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);

  // Check for API key first (for system integrations)
  const apiKey = req.headers['x-api-key'];
  if (apiKey) {
    if (apiKey === process.env.ADMIN_API_KEY) {
      secureLog.debug(`[${requestId}] Using API key authentication`);
      return { token: 'API_KEY', source: 'api-key', error: null };
    } else {
      secureLog.warn(`[${requestId}] Invalid API key provided`);
      return { token: null, source: 'api-key', error: 'Invalid API key' };
    }
  }

  // Check Authorization header (primary method)
  const authHeader = req.headers.authorization;
  if (authHeader) {
    if (authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7).trim();

      // Basic validation of JWT format (header.payload.signature)
      // Use more permissive regex that handles Supabase JWT tokens correctly
      if (token && /^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*$/.test(token)) {
        secureLog.debug(`[${requestId}] Valid JWT token format detected in Authorization header`);
        return { token, source: 'authorization-header', error: null };
      } else {
        secureLog.warn(`[${requestId}] Invalid JWT token format in Authorization header`);
        return { token: null, source: 'authorization-header', error: 'Malformed token' };
      }
    } else {
      // Try to use the raw header value
      console.warn(`[${requestId}] Authorization header doesn't use Bearer scheme, attempting to use raw value`);
      return { token: authHeader.trim(), source: 'authorization-header-raw', error: null };
    }
  }

  // Check X-Auth-Token header (fallback for cross-origin requests)
  const xAuthToken = req.headers['x-auth-token'];
  if (xAuthToken) {
    const token = xAuthToken.trim();

    // Basic validation of JWT format using permissive regex for Supabase tokens
    if (/^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*$/.test(token)) {
      console.log(`[${requestId}] Valid JWT token format detected in X-Auth-Token header`);
      return { token, source: 'x-auth-token', error: null };
    } else {
      console.warn(`[${requestId}] Invalid JWT token format in X-Auth-Token header. Token length: ${token?.length}, Parts: ${token?.split('.').length}`);
      if (token) {
        console.warn(`[${requestId}] Token sample: ${token.substring(0, 20)}...`);
      }
      return { token: null, source: 'x-auth-token', error: 'Malformed token' };
    }
  }

  // Check for token in cookies (tertiary method)
  const cookieToken = req.cookies?.oss_auth_token || req.cookies?.sb_auth_token;
  if (cookieToken) {
    const token = cookieToken.trim();

    // Basic validation of JWT format for cookies (more lenient)
    if (/^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]*$/.test(token)) {
      console.log(`[${requestId}] Valid JWT token format detected in cookies`);
      return { token, source: 'cookie', error: null };
    } else {
      console.warn(`[${requestId}] Invalid JWT token format in cookies. Token length: ${token?.length}, Parts: ${token?.split('.').length}`);
      if (token) {
        console.warn(`[${requestId}] Cookie token sample: ${token.substring(0, 20)}...`);
      }
      // For cookies, we'll still try to use the token even if format is invalid
      // as it might be a different type of token
      console.log(`[${requestId}] Using cookie token despite format issues`);
      return { token, source: 'cookie-unvalidated', error: null };
    }
  }

  // No token found
  console.warn(`[${requestId}] No authentication token found in request`);
  return { token: null, source: null, error: 'No token found' };
}

/**
 * Authenticate an admin API request
 * Simplified middleware for consistent authentication
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @param {Function} next - Next middleware function
 */
export async function authenticateAdmin(req, res, next) {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Starting authentication for: ${req.url}`);

  try {
    // Development mode fallback for testing
    // Only active when NODE_ENV is development and ENABLE_AUTH_BYPASS is true
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(`[${requestId}] DEVELOPMENT MODE: Using auth bypass`);
      // Use a fixed UUID for development admin to avoid database UUID validation errors
      req.user = { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' };
      req.role = 'admin';
      return next();
    }

    // Extract token from request with enhanced validation
    const { token, source, error: tokenError } = extractToken(req);

    // Handle development bypass token
    if (token === 'dev-bypass-token' && process.env.NODE_ENV === 'development') {
      console.log(`[${requestId}] Using development bypass token`);
      // Use a fixed UUID for development admin to avoid database UUID validation errors
      req.user = { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' };
      req.role = 'admin';
      return next();
    }

    // Handle API key authentication
    if (token === 'API_KEY') {
      req.user = { id: 'system', email: '<EMAIL>' };
      req.role = 'system';
      console.log(`[${requestId}] API key authentication successful`);
      return next();
    }

    // Check if token exists
    if (!token) {
      console.warn(`[${requestId}] Authentication token error: ${tokenError}`);
      return res.status(401).json({
        error: 'Unauthorized',
        message: tokenError || 'Authentication required',
        details: { source }
      });
    }

    // Verify token with admin client
    let adminClient;
    try {
      console.log(`[${requestId}] Initializing admin client for token verification`);
      adminClient = getAdminClient();
    } catch (adminError) {
      console.error(`[${requestId}] Failed to initialize admin client:`, adminError);
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to initialize authentication service'
      });
    }

    // Set a timeout for token verification
    let timeoutId = null;
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error(`[${requestId}] Token verification timed out`);
        reject(new Error('Authentication timeout'));
      }, 10000); // 10 second timeout
    });

    let data, error;
    try {
      // Verify token with timeout protection
      const authPromise = adminClient.auth.getUser(token);
      const result = await Promise.race([authPromise, timeoutPromise]);

      // Clear the timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // Extract data and error
      data = result.data;
      error = result.error;
    } catch (verifyError) {
      console.error(`[${requestId}] Token verification error:`, verifyError);
      return res.status(500).json({
        error: 'Authentication Error',
        message: verifyError.message || 'Failed to verify authentication token'
      });
    }

    if (error || !data.user) {
      console.warn(`[${requestId}] Token validation failed:`, error?.message);
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid authentication token'
      });
    }

    // Get user role
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single();

    // Handle role errors
    if (roleError) {
      console.warn(`[${requestId}] Error getting role:`, roleError.message);

      // Special case for known admin users
      if (isKnownAdmin(data.user.id)) {
        console.log(`[${requestId}] Known admin ID detected, granting access`);
        req.user = data.user;
        req.role = 'admin';
        return next();
      }

      return res.status(403).json({
        error: 'Forbidden',
        message: 'User role could not be determined'
      });
    }

    // Check if user has admin role with updated 5-tier role system
    const allowedRoles = ['dev', 'admin', 'artist', 'braider'];
    if (!allowedRoles.includes(roleData.role)) {
      console.warn(`[${requestId}] User does not have required role: ${roleData.role}. Allowed: ${allowedRoles.join(', ')}`);
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      });
    }

    // Authentication successful
    console.log(`[${requestId}] Authentication successful for user: ${data.user.email}, role: ${roleData.role}`);
    req.user = data.user;
    req.role = roleData.role;
    next();
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication process failed'
    });
  }
}

/**
 * Higher-order function to create authenticated API handlers
 *
 * @param {Function} handler - API route handler
 * @returns {Function} - Authenticated handler
 */
export function withAdminAuth(handler) {
  return (req, res) => {
    return authenticateAdmin(req, res, () => handler(req, res));
  };
}

/**
 * Modern authentication function for admin requests
 * Replaces legacy implementation with improved error handling and security
 *
 * @param {Object} req - HTTP request object
 * @returns {Object} - { user, role, authorized, error }
 */
export async function authenticateAdminRequest(req) {
  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Authenticating admin request for: ${req.url}`);

  try {
    // Development mode fallback for testing
    // Only active when NODE_ENV is development and ENABLE_AUTH_BYPASS is true
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(`[${requestId}] DEVELOPMENT MODE: Using auth bypass`);
      return {
        user: { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' },
        role: 'admin',
        authorized: true,
        error: null
      };
    }

    // Extract token from request with enhanced validation
    const { token, source, error: tokenError } = extractToken(req);

    // Handle development bypass token
    if (token === 'dev-bypass-token' && process.env.NODE_ENV === 'development') {
      console.log(`[${requestId}] Using development bypass token`);
      return {
        user: { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' },
        role: 'admin',
        authorized: true,
        error: null
      };
    }

    // Handle API key authentication
    if (token === 'API_KEY') {
      return {
        user: { id: 'system', email: '<EMAIL>' },
        role: 'system',
        authorized: true,
        error: null
      };
    }

    // Check if token exists
    if (!token) {
      console.warn(`[${requestId}] Authentication failed: No token found. Source: ${source}, Error: ${tokenError}`);
      return {
        user: null,
        role: null,
        authorized: false,
        source,
        error: new Error(tokenError || 'No authentication token provided')
      };
    }

    console.log(`[${requestId}] Token found from source: ${source}, Token length: ${token?.length}, Token preview: ${token?.substring(0, 20)}...`);

    // Verify token with admin client
    let adminClient;
    try {
      console.log(`[${requestId}] Initializing admin client for token verification`);
      adminClient = getAdminClient();
    } catch (adminError) {
      console.error(`[${requestId}] Failed to initialize admin client:`, adminError);

      // In development mode with auth bypass, return success even if admin client fails
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Admin client failed but development auth bypass enabled, allowing access`);
        return {
          user: { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' },
          role: 'admin',
          authorized: true,
          error: null
        };
      }

      return {
        user: null,
        role: null,
        authorized: false,
        error: new Error(`Admin client initialization failed: ${adminError.message}`)
      };
    }

    // Set a timeout for token verification
    let timeoutId = null;
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error(`[${requestId}] Token verification timed out`);
        reject(new Error('Authentication timeout'));
      }, 10000); // 10 second timeout
    });

    let data, error;
    try {
      // Verify token with timeout protection
      const authPromise = adminClient.auth.getUser(token);
      const result = await Promise.race([authPromise, timeoutPromise]);

      // Clear the timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // Extract data and error
      data = result.data;
      error = result.error;
    } catch (verifyError) {
      console.error(`[${requestId}] Token verification error:`, verifyError);

      // In development mode with auth bypass, return success even if token verification fails
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Token verification failed but development auth bypass enabled, allowing access`);
        return {
          user: { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' },
          role: 'admin',
          authorized: true,
          error: null
        };
      }

      return {
        user: null,
        role: null,
        authorized: false,
        error: verifyError
      };
    }

    if (error || !data.user) {
      console.warn(`[${requestId}] Token verification failed. Error: ${error?.message || 'No user data'}, Has data: ${!!data}, Has user: ${!!data?.user}`);

      // In development mode with auth bypass, return success even if token verification fails
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Token verification failed but development auth bypass enabled, allowing access`);
        return {
          user: { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' },
          role: 'admin',
          authorized: true,
          error: null
        };
      }

      return {
        user: null,
        role: null,
        authorized: false,
        error: new Error(`Invalid token: ${error?.message || 'Unknown error'}`)
      };
    }

    console.log(`[${requestId}] Token verification successful. User ID: ${data.user.id}, Email: ${data.user.email}`);

    // Get user role
    let roleData, roleError;
    try {
      console.log(`[${requestId}] Querying user role for user ID: ${data.user.id}`);
      const result = await adminClient
        .from('user_roles')
        .select('role')
        .eq('id', data.user.id)
        .single();
      roleData = result.data;
      roleError = result.error;

      console.log(`[${requestId}] Role query result - Data: ${JSON.stringify(roleData)}, Error: ${roleError?.message || 'none'}`);
    } catch (dbError) {
      console.error(`[${requestId}] Role query exception:`, dbError);

      // In development mode with auth bypass, return success even if role query fails
      if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
        console.log(`[${requestId}] Role query failed but development auth bypass enabled, allowing access`);
        return {
          user: { id: '00000000-0000-4000-8000-000000000001', email: '<EMAIL>' },
          role: 'admin',
          authorized: true,
          error: null
        };
      }
      roleError = dbError;
    }

    // Handle role errors
    if (roleError) {
      // Special case for known admin users
      if (isKnownAdmin(data.user.id)) {
        return {
          user: data.user,
          role: 'admin',
          authorized: true,
          error: null
        };
      }

      return {
        user: data.user,
        role: null,
        authorized: false,
        error: new Error(`Role verification failed: ${roleError.message}`)
      };
    }

    // Check if user has admin role with updated 5-tier role system
    const allowedRoles = ['dev', 'admin', 'artist', 'braider'];
    console.log(`[${requestId}] Role authorization check - User role: '${roleData.role}', Allowed roles: [${allowedRoles.join(', ')}]`);

    if (!allowedRoles.includes(roleData.role)) {
      console.warn(`[${requestId}] Role authorization failed - User role '${roleData.role}' not in allowed roles: [${allowedRoles.join(', ')}]`);
      return {
        user: data.user,
        role: roleData.role,
        authorized: false,
        error: new Error(`Insufficient permissions: Required roles: ${allowedRoles.join(', ')}`)
      };
    }

    // Authentication successful
    console.log(`[${requestId}] Authentication and authorization successful - User: ${data.user.email}, Role: ${roleData.role}`);
    return {
      user: data.user,
      role: roleData.role,
      authorized: true,
      error: null
    };
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    return {
      user: null,
      role: null,
      authorized: false,
      error
    };
  }
}