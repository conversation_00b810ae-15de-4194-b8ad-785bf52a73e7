.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: var(--font-secondary);
  padding-top: var(--header-height);
  width: 100%;
}

/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background-color: #000000;
  z-index: 9999;
  transition: var(--transition-normal);
  padding: 0 var(--spacing-lg) 0 0; /* Changed: Removed left padding */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header.scrolled {
  background-color: rgba(0, 0, 0, 0.95);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  height: calc(var(--header-height) - 20px);
}

.headerContainer {
  max-width: 100%; /* Changed: Was var(--max-width) */
  height: 100%;
  margin: 0; /* Changed: Was 0 auto */
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 1001;
}

.logo img {
  height: 50px;
  transition: var(--transition-normal);
}

.scrolled .logo img {
  height: 40px;
}

.nav {
  display: flex;
  height: 100%;
  flex-grow: 1; /* Allow nav to take available space */
  justify-content: center; /* Center the ul (menu items) within nav */
}

.nav ul {
  display: flex;
  list-style: none;
  height: 100%;
  margin: 0;
  padding: 0;
}

.nav li {
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 var(--spacing-md);
}

.nav a {
  position: relative;
  color: white;
  font-family: var(--font-primary);
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  padding: 0.5rem 0;
  transition: var(--transition-normal);
}

.nav a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition-normal);
}

.nav a:hover::after,
.nav li.active a::after {
  width: 100%;
}

.nav li.active a {
  color: var(--primary-color);
}

.bookNowContainer {
  margin-left: 1rem;
}

.bookNowButton {
  background: var(--primary-gradient);
  color: white;
  padding: 0.5rem 1.25rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition-bounce);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bookNowButton:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.mobileToggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 10000;
}

.mobileToggle span {
  width: 100%;
  height: 2px;
  background-color: white;
  transition: var(--transition-normal);
}

.mobileToggle.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
  background-color: var(--primary-color);
}

.mobileToggle.active span:nth-child(2) {
  opacity: 0;
}

.mobileToggle.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
  background-color: var(--primary-color);
}

@media (max-width: 768px) {
  .nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.95);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: right 0.3s ease;
    z-index: 999;
  }

  .nav.open {
    right: 0;
  }

  .nav ul {
    flex-direction: column;
    align-items: center;
    height: auto;
  }

  .nav li {
    margin: 1rem 0;
    height: auto;
  }

  .nav a {
    font-size: 1.5rem;
    color: white;
  }

  .mobileToggle {
    display: flex;
  }

  .bookNowContainer {
    display: none;
  }

  .nav.open + .bookNowContainer {
    display: block;
    position: fixed;
    bottom: 10%;
    left: 0;
    width: 100%;
    text-align: center;
    z-index: 1000;
  }
}

/* Footer styles */
.footer {
  background-color: var(--background-off-white);
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.footerWave {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100px;
  overflow: hidden;
  line-height: 0;
}

.footerWave svg {
  position: relative;
  display: block;
  width: 100%;
  height: 100px;
}

.footerContent {
  padding: 6rem 2rem 3rem;
  max-width: var(--max-width);
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.footerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
}

.footerBranding {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.footerLogo {
  height: auto;
  width: 180px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
}

.rainbowLogoText {
  font-family: var(--font-accent);
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #4ECDC4, #FF6B6B, #FFE66D, #1A73E8, #FF1493);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  animation: gradientAnimation 5s ease infinite;
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.footerTagline {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  line-height: 1.6;
}

.footerSocial {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.footerSocialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  transition: var(--transition-normal);
}

.footerSocialLink:hover {
  transform: translateY(-3px);
  background-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.footerSocialLink img {
  width: 20px;
  height: 20px;
}

.footerLinks h3,
.footerContact h3 {
  font-family: var(--font-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.footerLinks ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerLinks li {
  margin-bottom: 0.75rem;
}

.footerLink {
  color: var(--light-text-color);
  text-decoration: none;
  transition: var(--transition-normal);
  position: relative;
  padding-left: 1.25rem;
  display: inline-block;
}

.footerLink::before {
  content: '→';
  position: absolute;
  left: 0;
  top: 0;
  color: var(--primary-color);
  transition: var(--transition-normal);
}

.footerLink:hover {
  color: var(--primary-color);
  transform: translateX(5px);
}

.footerContactLink {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-normal);
}

.footerContactLink:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.footerContact p {
  margin-bottom: 1rem;
  color: var(--light-text-color);
}

.footerButton {
  margin-top: 1rem;
  display: inline-block;
}

.footerPayments {
  text-align: center;
  padding: 2rem 1.5rem;
  background-color: rgba(255, 255, 255, 0.05);
}

.footerPayments h3 {
  font-family: var(--font-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.paymentLogos {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.paymentLogos img {
  height: 25px;
  width: auto;
  opacity: 0.9;
  transition: var(--transition-normal);
}

.paymentLogos img:hover {
  opacity: 1;
  transform: translateY(-2px);
}

.securePaymentNote {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #4CAF50;
  margin-top: 0.5rem;
}

.securePaymentNote svg {
  margin-right: 0.5rem;
}

.footerBottom {
  text-align: center;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--light-text-color);
  font-size: 0.9rem;
}

.footerBottom p {
  margin-bottom: 0.5rem;
}

.scrollToTop {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-bounce);
  z-index: 99;
}

.scrollToTop:hover {
  transform: translateY(-5px);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .footerContent {
    padding: 5rem 1.5rem 2.5rem;
  }

  .footerGrid {
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .footerContent {
    padding: 4rem 1rem 2rem;
  }

  .footerGrid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .footerBranding {
    align-items: center;
    text-align: center;
  }

  .footerLinks,
  .footerContact {
    text-align: center;
  }

  .footerLink {
    padding-left: 0;
  }

  .footerLink::before {
    display: none;
  }

  .paymentLogos {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .paymentLogos img {
    height: 22px;
  }

  .scrollToTop {
    width: 45px;
    height: 45px;
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

@media (max-width: 480px) {
  .footerContent {
    padding: 3.5rem 1rem 1.5rem;
  }

  .footerGrid {
    gap: 2rem;
  }

  .footerLogo {
    width: 150px;
    height: auto;
  }

  .rainbowLogoText {
    font-size: 1.6rem;
  }

  .scrollToTop {
    width: 40px;
    height: 40px;
    bottom: 1rem;
    right: 1rem;
  }
}

@media (max-width: 360px) {
  .rainbowLogoText {
    font-size: 1.4rem;
  }
}
