#!/usr/bin/env node

/**
 * Investigate Database Schema Error Script
 * Analyzes the artist_braider_applications table structure
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function investigateSchemaError() {
  console.log('🔍 Investigating Database Schema Error\n')

  // 1. Check current table structure
  console.log('1. Examining artist_braider_applications table structure...')
  try {
    // Get table columns using information_schema
    const { data: columns, error: columnsError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length
          FROM information_schema.columns 
          WHERE table_name = 'artist_braider_applications' 
            AND table_schema = 'public'
          ORDER BY ordinal_position;
        `
      })

    if (columnsError) {
      console.log('   Using alternative method to check table structure...')
      
      // Try direct table query to see what columns exist
      const { data: sampleData, error: sampleError } = await supabase
        .from('artist_braider_applications')
        .select('*')
        .limit(1)

      if (sampleError) {
        console.error('❌ Cannot access table:', sampleError.message)
      } else {
        console.log('✅ Table exists, sample record structure:')
        if (sampleData && sampleData.length > 0) {
          console.log('   Columns found:', Object.keys(sampleData[0]))
        } else {
          console.log('   Table is empty, checking with describe...')
        }
      }
    } else {
      console.log('✅ Table structure retrieved:')
      columns.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`)
      })
    }
  } catch (error) {
    console.error('❌ Error examining table structure:', error.message)
  }

  // 2. Check for missing 'references' column specifically
  console.log('\n2. Checking for "references" column...')
  try {
    const { data: refCheck, error: refError } = await supabase
      .from('artist_braider_applications')
      .select('references')
      .limit(1)

    if (refError) {
      if (refError.message.includes('column "references" does not exist')) {
        console.log('❌ CONFIRMED: "references" column is missing from the table')
      } else {
        console.log('❌ Error checking references column:', refError.message)
      }
    } else {
      console.log('✅ "references" column exists and is accessible')
    }
  } catch (error) {
    console.error('❌ Error checking references column:', error.message)
  }

  // 3. Check what the API might be expecting
  console.log('\n3. Analyzing expected table structure...')
  
  const expectedColumns = [
    'id',
    'user_id', 
    'application_type',
    'status',
    'personal_info',
    'experience_info',
    'availability_info',
    'portfolio_info',
    'additional_info',
    'references', // This might be missing!
    'admin_notes',
    'reviewed_by',
    'reviewed_at',
    'created_at',
    'updated_at'
  ]

  console.log('   Expected columns for Artist/Braider applications:')
  expectedColumns.forEach(col => {
    console.log(`   - ${col}`)
  })

  // 4. Check recent applications to see what data is being submitted
  console.log('\n4. Checking recent application submissions...')
  try {
    const { data: recentApps, error: appsError } = await supabase
      .from('artist_braider_applications')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(3)

    if (appsError) {
      console.error('❌ Error fetching recent applications:', appsError.message)
    } else {
      console.log(`✅ Found ${recentApps?.length || 0} recent applications`)
      if (recentApps && recentApps.length > 0) {
        console.log('   Recent application columns:', Object.keys(recentApps[0]))
        
        // Check if any applications have references data
        const hasReferences = recentApps.some(app => 'references' in app)
        console.log(`   Applications have references field: ${hasReferences ? 'Yes' : 'No'}`)
      }
    }
  } catch (error) {
    console.error('❌ Error checking recent applications:', error.message)
  }

  // 5. Check application tokens table for comparison
  console.log('\n5. Checking application_tokens table structure...')
  try {
    const { data: tokenSample, error: tokenError } = await supabase
      .from('application_tokens')
      .select('*')
      .limit(1)

    if (tokenError) {
      console.error('❌ Error accessing application_tokens:', tokenError.message)
    } else {
      console.log('✅ application_tokens table accessible')
      if (tokenSample && tokenSample.length > 0) {
        console.log('   Token table columns:', Object.keys(tokenSample[0]))
      }
    }
  } catch (error) {
    console.error('❌ Error checking application_tokens:', error.message)
  }

  // 6. Test the specific user mentioned in the error
  console.log('\n6. Checking specific user from error...')
  const problemUserId = '17464307-4beb-4156-ab10-96a29df4f367'
  
  try {
    const { data: userApps, error: userError } = await supabase
      .from('artist_braider_applications')
      .select('*')
      .eq('user_id', problemUserId)

    if (userError) {
      console.error(`❌ Error checking user ${problemUserId}:`, userError.message)
    } else {
      console.log(`✅ Found ${userApps?.length || 0} applications for user ${problemUserId}`)
      if (userApps && userApps.length > 0) {
        console.log('   User application data structure:', Object.keys(userApps[0]))
      }
    }
  } catch (error) {
    console.error('❌ Error checking specific user:', error.message)
  }

  // 7. Generate fix recommendations
  console.log('\n📋 Fix Recommendations:')
  
  console.log('\n   If "references" column is missing:')
  console.log('   1. Add references column to store professional references')
  console.log('   2. Update application form to collect references data')
  console.log('   3. Update API to handle references field properly')
  
  console.log('\n   If this is a schema cache issue:')
  console.log('   1. Refresh Supabase schema cache')
  console.log('   2. Restart API services')
  console.log('   3. Verify column exists in database directly')
  
  console.log('\n   If this is an API code issue:')
  console.log('   1. Check application submission API code')
  console.log('   2. Verify column names match database schema')
  console.log('   3. Update API to use correct column names')

  return true
}

// Run the investigation
investigateSchemaError()
  .then(() => {
    console.log('\n✅ Schema investigation completed!')
    process.exit(0)
  })
  .catch(error => {
    console.error('\n💥 Investigation error:', error)
    process.exit(1)
  })
