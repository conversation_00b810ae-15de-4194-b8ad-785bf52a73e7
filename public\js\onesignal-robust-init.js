/**
 * OneSignal Robust Initialization Script
 * 
 * This script provides a completely safe way to initialize OneSignal without
 * modifying prototypes or causing read-only property errors.
 */

(function() {
  // Only run in browser environment
  if (typeof window === 'undefined') return;

  // Configuration
  const ONESIGNAL_APP_ID = window.__ONESIGNAL_APP_ID__ || "************************************";
  const ONESIGNAL_SAFARI_WEB_ID = window.__ONESIGNAL_SAFARI_WEB_ID__ || "web.onesignal.auto.************************************";
  const DEBUG = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // Skip in development environment
  if (DEBUG) {
    console.log('[OneSignal Robust] Skipping initialization in development environment');
    return;
  }

  // Check if already initialized
  if (window.__ONESIGNAL_INITIALIZED__) {
    console.log('[OneSignal Robust] Already initialized');
    return;
  }

  // Set initialization flag
  window.__ONESIGNAL_INITIALIZING__ = true;

  // Safe OneSignal initialization function
  function initializeOneSignal() {
    try {
      // Ensure OneSignal array exists without modifying existing objects
      if (!window.OneSignal) {
        window.OneSignal = [];
      }

      // If OneSignal exists but isn't an array, don't modify it
      if (!Array.isArray(window.OneSignal)) {
        console.log('[OneSignal Robust] OneSignal already loaded, attempting initialization');
        
        // Try direct initialization if OneSignal is already loaded
        if (typeof window.OneSignal.init === 'function') {
          initializeDirectly();
          return;
        } else {
          console.warn('[OneSignal Robust] OneSignal object exists but init method not available');
          return;
        }
      }

      // Use OneSignal's deferred initialization pattern
      window.OneSignal.push(function() {
        initializeDirectly();
      });

    } catch (error) {
      console.error('[OneSignal Robust] Error in initialization:', error);
      window.__ONESIGNAL_INITIALIZING__ = false;
    }
  }

  // Direct initialization function
  function initializeDirectly() {
    try {
      if (typeof window.OneSignal.init !== 'function') {
        console.error('[OneSignal Robust] OneSignal.init is not available');
        window.__ONESIGNAL_INITIALIZING__ = false;
        return;
      }

      const initConfig = {
        appId: ONESIGNAL_APP_ID,
        safari_web_id: ONESIGNAL_SAFARI_WEB_ID,
        allowLocalhostAsSecureOrigin: DEBUG,
        notifyButton: {
          enable: true,
          size: 'medium',
          theme: 'default',
          position: 'bottom-right',
          offset: {
            bottom: '20px',
            right: '20px',
          },
          prenotify: true,
          showCredit: false,
          text: {
            'tip.state.unsubscribed': 'Subscribe to notifications',
            'tip.state.subscribed': 'You\'re subscribed to notifications',
            'tip.state.blocked': 'You\'ve blocked notifications',
            'message.prenotify': 'Click to subscribe to notifications',
            'message.action.subscribed': 'Thanks for subscribing!',
            'message.action.resubscribed': 'You\'re subscribed to notifications',
            'message.action.unsubscribed': 'You won\'t receive notifications again',
            'dialog.main.title': 'Manage Site Notifications',
            'dialog.main.button.subscribe': 'SUBSCRIBE',
            'dialog.main.button.unsubscribe': 'UNSUBSCRIBE',
            'dialog.blocked.title': 'Unblock Notifications',
            'dialog.blocked.message': 'Follow these instructions to allow notifications:'
          }
        },
        promptOptions: {
          slidedown: {
            prompts: [
              {
                type: "push",
                autoPrompt: false,
                text: {
                  actionMessage: "Would you like to receive notifications about your bookings and special offers?",
                  acceptButton: "Yes",
                  cancelButton: "No Thanks"
                },
                delay: {
                  pageViews: 1,
                  timeDelay: 20
                }
              }
            ]
          }
        }
      };

      console.log('[OneSignal Robust] Starting initialization with config:', initConfig);

      // Initialize with promise handling
      const initResult = window.OneSignal.init(initConfig);
      
      // Handle both promise and non-promise returns
      if (initResult && typeof initResult.then === 'function') {
        initResult.then(function() {
          onInitializationSuccess();
        }).catch(function(error) {
          onInitializationError(error);
        });
      } else {
        // Fallback for non-promise initialization
        setTimeout(function() {
          onInitializationSuccess();
        }, 1000);
      }

    } catch (error) {
      onInitializationError(error);
    }
  }

  // Success handler
  function onInitializationSuccess() {
    console.log('[OneSignal Robust] Initialization successful');
    window.__ONESIGNAL_INITIALIZED__ = true;
    window.__ONESIGNAL_INITIALIZING__ = false;
    
    // Dispatch custom event
    try {
      const event = new CustomEvent('onesignal:initialized');
      document.dispatchEvent(event);
    } catch (eventError) {
      console.error('[OneSignal Robust] Error dispatching initialization event:', eventError);
    }
  }

  // Error handler
  function onInitializationError(error) {
    console.error('[OneSignal Robust] Initialization error:', error);
    window.__ONESIGNAL_INITIALIZING__ = false;
    
    // Dispatch error event
    try {
      const event = new CustomEvent('onesignal:error', { detail: error });
      document.dispatchEvent(event);
    } catch (eventError) {
      console.error('[OneSignal Robust] Error dispatching error event:', eventError);
    }
  }

  // Wait for OneSignal SDK to be available
  function waitForOneSignalSDK() {
    let attempts = 0;
    const maxAttempts = 20; // 10 seconds total
    
    function checkSDK() {
      attempts++;
      
      // Check if OneSignal SDK is loaded
      if (window.OneSignal) {
        initializeOneSignal();
        return;
      }
      
      if (attempts < maxAttempts) {
        setTimeout(checkSDK, 500);
      } else {
        console.error('[OneSignal Robust] OneSignal SDK failed to load after', maxAttempts, 'attempts');
        window.__ONESIGNAL_INITIALIZING__ = false;
      }
    }
    
    checkSDK();
  }

  // Start the initialization process
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(waitForOneSignalSDK, 1000);
    });
  } else {
    setTimeout(waitForOneSignalSDK, 1000);
  }

})();
