import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import Modal from '@/components/admin/Modal'
import styles from '@/styles/admin/marketing/AutomationDetail.module.css'

export default function AutomationDetail() {
  const router = useRouter()
  const { id } = router.query
  const [automation, setAutomation] = useState(null)
  const [logs, setLogs] = useState([])
  const [stats, setStats] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteError, setDeleteError] = useState(null)
  const [showTriggerModal, setShowTriggerModal] = useState(false)
  const [triggerLoading, setTriggerLoading] = useState(false)
  const [triggerError, setTriggerError] = useState(null)
  const [triggerSuccess, setTriggerSuccess] = useState(null)
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false)

  // Fetch automation data
  useEffect(() => {
    if (!id) return

    const fetchAutomation = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/automations/${id}`)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch automation')
        }

        const data = await response.json()
        setAutomation(data.automation || null)
        setLogs(data.logs || [])
        setStats(data.stats || {})
      } catch (error) {
        console.error('Error fetching automation:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchAutomation()
  }, [id])

  // Handle delete automation
  const handleDeleteAutomation = async () => {
    setDeleteLoading(true)
    setDeleteError(null)

    try {
      const response = await fetch(`/api/marketing/automations/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete automation')
      }

      // Redirect to automation list
      router.push('/admin/marketing/automations')
    } catch (error) {
      console.error('Error deleting automation:', error)
      setDeleteError(error.message)
      setDeleteLoading(false)
    }
  }

  // Handle toggle automation status
  const handleToggleStatus = async () => {
    if (!automation) return

    setStatusUpdateLoading(true)

    try {
      const response = await fetch(`/api/marketing/automations/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_active: !automation.is_active
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update automation status')
      }

      // Update automation in state
      setAutomation({
        ...automation,
        is_active: !automation.is_active
      })
    } catch (error) {
      console.error('Error updating automation status:', error)
      setError(`Failed to update status: ${error.message}`)
    } finally {
      setStatusUpdateLoading(false)
    }
  }

  // Handle trigger automation
  const handleTriggerAutomation = async () => {
    setTriggerLoading(true)
    setTriggerError(null)
    setTriggerSuccess(null)

    try {
      const response = await fetch(`/api/marketing/automations/${id}/trigger`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          test_mode: true
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to trigger automation')
      }

      const data = await response.json()
      setTriggerSuccess(`Test run successful: ${data.sent} messages sent to ${data.total_customers} customers`)

      // Close modal after a short delay
      setTimeout(() => {
        setShowTriggerModal(false)
        setTriggerSuccess(null)
      }, 3000)
    } catch (error) {
      console.error('Error triggering automation:', error)
      setTriggerError(error.message)
    } finally {
      setTriggerLoading(false)
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get trigger type label
  const getTriggerTypeLabel = (triggerType) => {
    switch (triggerType) {
      case 'event':
        return 'Event-Based'
      case 'schedule':
        return 'Scheduled'
      case 'segment_entry':
        return 'Segment Entry'
      default:
        return triggerType
    }
  }

  // Get trigger configuration description
  const getTriggerDescription = (triggerType, triggerConfig) => {
    if (!triggerConfig) return '-'

    switch (triggerType) {
      case 'event':
        const eventTypes = {
          new_booking: 'New Booking',
          booking_confirmed: 'Booking Confirmed',
          booking_completed: 'Booking Completed',
          new_customer: 'New Customer',
          product_purchased: 'Product Purchased',
          abandoned_cart: 'Abandoned Cart'
        }

        let description = eventTypes[triggerConfig.event_type] || triggerConfig.event_type

        if (triggerConfig.product_category) {
          description += ` (Category: ${triggerConfig.product_category})`
        }

        if (triggerConfig.delay_hours > 0) {
          description += ` with ${triggerConfig.delay_hours} hour delay`
        }

        return description

      case 'schedule':
        const frequencies = {
          daily: 'Daily',
          weekly: 'Weekly',
          monthly: 'Monthly',
          birthday: 'Customer Birthday'
        }

        let scheduleDesc = frequencies[triggerConfig.frequency] || triggerConfig.frequency

        if (triggerConfig.frequency === 'weekly' && triggerConfig.day_of_week !== undefined) {
          const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
          scheduleDesc += ` on ${days[triggerConfig.day_of_week]}`
        }

        if (triggerConfig.frequency === 'monthly' && triggerConfig.day_of_month) {
          scheduleDesc += ` on day ${triggerConfig.day_of_month}`
        }

        if (triggerConfig.frequency === 'birthday' && triggerConfig.days_before > 0) {
          scheduleDesc += ` ${triggerConfig.days_before} days before`
        } else if (triggerConfig.frequency !== 'birthday' && triggerConfig.hour !== undefined) {
          scheduleDesc += ` at ${triggerConfig.hour.toString().padStart(2, '0')}:00`
        }

        return scheduleDesc

      case 'segment_entry':
        return triggerConfig.delay_hours > 0
          ? `${triggerConfig.delay_hours} hours after entry`
          : 'Immediately on entry'

      default:
        return JSON.stringify(triggerConfig)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.automationDetail}>
          <div className={styles.loading}>Loading automation data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.automationDetail}>
          <div className={styles.error}>
            Error: {error}
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/automations')}
            >
              Back to Automations
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!automation) {
    return (
      <AdminLayout>
        <div className={styles.automationDetail}>
          <div className={styles.notFound}>
            Automation not found
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/automations')}
            >
              Back to Automations
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.automationDetail}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <h2>{automation.name}</h2>
            <div className={styles.meta}>
              <button
                className={`${styles.statusToggle} ${automation.is_active ? styles.statusActive : styles.statusInactive}`}
                onClick={handleToggleStatus}
                disabled={statusUpdateLoading}
              >
                {statusUpdateLoading ? (
                  <span className={styles.statusLoading}>...</span>
                ) : (
                  automation.is_active ? 'Active' : 'Inactive'
                )}
              </button>
              <span className={styles.type}>
                {automation.message_type === 'email' ? 'Email' :
                 automation.message_type === 'sms' ? 'SMS' : 'Push Notification'}
              </span>
              <span className={styles.trigger}>
                {getTriggerTypeLabel(automation.trigger_type)}
              </span>
            </div>
          </div>
          <div className={styles.headerActions}>
            <button
              className={styles.triggerButton}
              onClick={() => setShowTriggerModal(true)}
            >
              Test Run
            </button>
            <Link href={`/admin/marketing/automations/${id}/edit`} className={styles.editButton}>
              Edit
            </Link>
            <button
              className={styles.deleteButton}
              onClick={() => setShowDeleteModal(true)}
            >
              Delete
            </button>
          </div>
        </div>

        <div className={styles.automationInfo}>
          <div className={styles.infoSection}>
            <h3>Automation Details</h3>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Description</span>
                <span className={styles.infoValue}>
                  {automation.description || 'No description provided'}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Trigger</span>
                <span className={styles.infoValue}>
                  {getTriggerDescription(automation.trigger_type, automation.trigger_config)}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Target Segment</span>
                <span className={styles.infoValue}>
                  {automation.segment ? (
                    <Link href={`/admin/marketing/segments/${automation.segment.id}`} className={styles.segmentLink}>
                      {automation.segment.name}
                    </Link>
                  ) : (
                    'No segment selected'
                  )}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Created</span>
                <span className={styles.infoValue}>
                  {formatDate(automation.created_at)}
                </span>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Message Content</h3>

            {automation.template && (
              <div className={styles.contentItem}>
                <span className={styles.contentLabel}>Template</span>
                <span className={styles.contentValue}>
                  <Link href={`/admin/marketing/templates/${automation.template.id}`} className={styles.templateLink}>
                    {automation.template.name}
                  </Link>
                </span>
              </div>
            )}

            {automation.message_type === 'email' && automation.subject && (
              <div className={styles.contentItem}>
                <span className={styles.contentLabel}>Subject</span>
                <span className={styles.contentValue}>
                  {automation.subject}
                </span>
              </div>
            )}

            <div className={styles.contentItem}>
              <span className={styles.contentLabel}>Content</span>
              <div className={styles.contentValue}>
                <pre className={styles.contentPre}>{automation.content}</pre>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Execution Statistics</h3>

            <div className={styles.statsGrid}>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{stats.total_executions || 0}</div>
                <div className={styles.statLabel}>Total Executions</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{stats.successful_executions || 0}</div>
                <div className={styles.statLabel}>Successful</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>
                  {stats.total_executions > 0
                    ? `${Math.round(stats.success_rate)}%`
                    : '-'}
                </div>
                <div className={styles.statLabel}>Success Rate</div>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Execution Logs</h3>

            {logs.length === 0 ? (
              <div className={styles.noLogs}>
                No execution logs yet. Test the automation to generate logs.
              </div>
            ) : (
              <div className={styles.logsTable}>
                <table>
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Trigger</th>
                      <th>Customer</th>
                      <th>Status</th>
                      <th>Message</th>
                    </tr>
                  </thead>
                  <tbody>
                    {logs.map((log) => (
                      <tr key={log.id} className={log.status === 'success' ? styles.successRow : styles.failedRow}>
                        <td>{formatDate(log.sent_at)}</td>
                        <td>{log.trigger_event}</td>
                        <td>
                          {log.customers ? (
                            <Link href={`/admin/customers/${log.customers.id}`} className={styles.customerLink}>
                              {log.customers.name || log.customers.email || log.customer_id}
                            </Link>
                          ) : (
                            log.customer_id
                          )}
                        </td>
                        <td className={log.status === 'success' ? styles.successStatus : styles.failedStatus}>
                          {log.status}
                        </td>
                        <td>{log.message || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <Modal onClose={() => setShowDeleteModal(false)}>
            <div className={styles.deleteModal}>
              <h3>Delete Automation</h3>
              <p>
                Are you sure you want to delete the automation "{automation.name}"?
                This action cannot be undone.
              </p>
              {deleteError && (
                <div className={styles.deleteError}>
                  Error: {deleteError}
                </div>
              )}
              <div className={styles.deleteActions}>
                <button
                  className={styles.cancelDeleteButton}
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleteLoading}
                >
                  Cancel
                </button>
                <button
                  className={styles.confirmDeleteButton}
                  onClick={handleDeleteAutomation}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? 'Deleting...' : 'Delete Automation'}
                </button>
              </div>
            </div>
          </Modal>
        )}

        {/* Trigger Confirmation Modal */}
        {showTriggerModal && (
          <Modal onClose={() => !triggerLoading && setShowTriggerModal(false)}>
            <div className={styles.triggerModal}>
              <h3>Test Run Automation</h3>
              <p>
                This will run the automation in test mode, sending messages to a few test customers.
                Do you want to continue?
              </p>

              {triggerError && (
                <div className={styles.triggerError}>
                  Error: {triggerError}
                </div>
              )}

              {triggerSuccess && (
                <div className={styles.triggerSuccess}>
                  {triggerSuccess}
                </div>
              )}

              <div className={styles.triggerActions}>
                <button
                  className={styles.cancelTriggerButton}
                  onClick={() => setShowTriggerModal(false)}
                  disabled={triggerLoading}
                >
                  Cancel
                </button>
                <button
                  className={styles.confirmTriggerButton}
                  onClick={handleTriggerAutomation}
                  disabled={triggerLoading}
                >
                  {triggerLoading ? 'Running...' : 'Run Test'}
                </button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </AdminLayout>
  )
}
