# Google Cloud Email Setup Guide
## For OceanSoulSparkles Website

## 🎯 Overview
Your website now has a complete Google Cloud email integration with a tiered approach:
1. **Primary**: Google Workspace SMTP (for business email)
2. **Secondary**: Gmail SMTP (for personal Gmail accounts)  
3. **Fallback**: OneSignal (existing system)

## 📧 Email Services Priority
The system automatically chooses the best available service:
- If Google Workspace is configured → Use Workspace
- Else if Gmail is configured → Use Gmail
- Else fallback to OneSignal

## 🚀 Quick Setup Options

### Option A: Gmail SMTP (Easiest - Personal Gmail)

**Prerequisites:**
- Gmail account with 2-Factor Authentication enabled
- Gmail App Password generated

**Steps:**
1. **Enable 2FA on your Gmail:**
   - Go to https://myaccount.google.com/security
   - Turn on 2-Step Verification if not already enabled

2. **Generate App Password:**
   - In Google Account Security settings
   - Go to "2-Step Verification" → "App passwords"
   - Select "Mail" as the app type
   - Copy the 16-character password (format: abcd-efgh-ijkl-mnop)

3. **Update Environment Variables:**
   Replace these values in your `.env.local` file:
   ```bash
   GMAIL_SMTP_USER=<EMAIL>
   GMAIL_SMTP_APP_PASSWORD=abcd-efgh-ijkl-mnop
   GMAIL_FROM_EMAIL=<EMAIL>
   ```

### Option B: Google Workspace (Recommended for Production)

**Prerequisites:**
- Google Workspace account (G Suite)
- Custom domain email (e.g., <EMAIL>)
- Admin access to Google Workspace

**Steps:**
1. **Set up business email:**
   - Create email account in Google Workspace Admin Console
   - Recommended: <EMAIL>.<NAME_EMAIL>

2. **Generate App Password:**
   - Sign in to Google Account for the business email
   - Enable 2FA if not already enabled
   - Generate app password for "Mail" application

3. **Update Environment Variables:**
   Uncomment and update these values in `.env.local`:
   ```bash
   WORKSPACE_SMTP_USER=<EMAIL>
   WORKSPACE_SMTP_APP_PASSWORD=your-workspace-app-password
   WORKSPACE_FROM_EMAIL=<EMAIL>
   ```

## 🧪 Testing Your Configuration

### Via Admin Panel (Recommended)
1. Navigate to: http://localhost:3000/admin/settings
2. Go to "Email Settings" tab
3. Fill in your Google Cloud email credentials
4. Click "Test Gmail Connection" or "Test Workspace Connection"
5. Look for ✅ Green checkmark = Success

### Via Code Testing
Create a test file to verify email functionality:

```javascript
// test-email.js
const { sendEmail } = require('./lib/google-cloud-email');

async function testEmail() {
  try {
    const result = await sendEmail({
      to: '<EMAIL>',
      subject: 'Test Email from Ocean Soul Sparkles',
      text: 'This is a test email to verify Google Cloud integration.',
      html: '<h2>Test Email</h2><p>Google Cloud email is working!</p>'
    });
    
    console.log('Email sent successfully:', result);
  } catch (error) {
    console.error('Email failed:', error);
  }
}

testEmail();
```

## 🔧 Email Functions Available

### 1. User Registration
- Welcome emails for new users
- Account verification emails

### 2. Booking System
- Booking confirmation emails
- Reminder emails (24 hours before)
- Cancellation confirmations

### 3. Shop Notifications
- Order confirmations
- Payment receipts
- Shipping notifications

### 4. Admin Notifications
- New user registrations
- New bookings
- Error alerts

## 📋 Email Templates

The system uses these templates (in `/lib/email-templates.js`):
- **Welcome Email**: New user registration
- **Booking Confirmation**: Booking details and receipt
- **Booking Reminder**: 24-hour advance reminder
- **Payment Confirmation**: Order and payment details
- **Admin Notification**: Internal alerts

## 🛠️ Admin Panel Features

### Email Settings Tab
- ✅ Enable/disable email notifications
- ✅ Configure Gmail SMTP settings
- ✅ Configure Google Workspace settings
- ✅ Test connections with one click
- ✅ Check email service status
- ✅ Legacy SMTP fallback configuration

### Connection Testing
- **Gmail Connection Test**: Verifies Gmail SMTP credentials
- **Workspace Connection Test**: Verifies Google Workspace SMTP
- **Email Service Status**: Shows which services are available
- **Legacy SMTP Test**: Tests alternative SMTP configuration

## 🚨 Troubleshooting

### Common Issues:

**1. "Gmail SMTP credentials not configured"**
- Ensure GMAIL_SMTP_USER and GMAIL_SMTP_APP_PASSWORD are set
- Check that app password is exactly 16 characters
- Verify 2FA is enabled on Gmail account

**2. "Authentication failed"**
- App password might be incorrect
- Gmail account might not have 2FA enabled
- Check if account is locked or has security restrictions

**3. "Connection timeout"**
- Check internet connection
- Verify SMTP port (587 for TLS, 465 for SSL)
- Check firewall settings

**4. "Email not received"**
- Check spam/junk folder
- Verify recipient email address
- Check Gmail sent folder to confirm sending

### Debug Steps:
1. Check browser console for error messages
2. Verify environment variables are loaded
3. Test connection in admin panel
4. Check server logs for detailed error messages

## 🔒 Security Best Practices

### Environment Variables
- ✅ Never commit `.env.local` to version control
- ✅ Use app passwords, not main account passwords
- ✅ Rotate app passwords periodically
- ✅ Use different credentials for development/production

### Email Security
- ✅ Enable 2FA on all Google accounts
- ✅ Use business email for production
- ✅ Monitor email sending limits
- ✅ Validate recipient email addresses

## 📊 Monitoring & Analytics

### Email Service Status
The system provides real-time status monitoring:
- ✅ Primary service availability
- ✅ Fallback service status
- ✅ Service health checks
- ✅ Configuration validation

### Usage Tracking
Monitor email performance:
- Delivery success rates
- Response times
- Error rates
- Service reliability

## 🚀 Production Deployment

### Pre-Deployment Checklist
- [ ] Google Workspace configured with business domain
- [ ] App passwords generated and tested
- [ ] DNS records configured for domain
- [ ] Environment variables updated in production
- [ ] Email templates customized
- [ ] Connection tests passed

### Environment Variables for Production
Update these in your production environment:
```bash
# Google Workspace (Production)
WORKSPACE_SMTP_USER=<EMAIL>
WORKSPACE_SMTP_APP_PASSWORD=production-app-password
WORKSPACE_FROM_NAME=Ocean Soul Sparkles
WORKSPACE_FROM_EMAIL=<EMAIL>

# Site URL (Production)
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
```

## 📞 Support

### If you encounter issues:
1. Check this guide first
2. Test connections in admin panel
3. Review server logs
4. Contact technical support with:
   - Error messages
   - Steps to reproduce
   - Configuration details (without passwords)

### Useful Resources:
- Google Workspace Admin Console: https://admin.google.com/
- Gmail Security Settings: https://myaccount.google.com/security
- Google Cloud Console: https://console.cloud.google.com/

---

**🎉 Once configured, your email system will:**
- ✅ Send professional emails from your domain
- ✅ Automatically handle user registrations
- ✅ Confirm bookings and send reminders
- ✅ Process shop orders and payments
- ✅ Provide reliable email delivery
- ✅ Fall back gracefully if primary service fails

**Next Step**: Update your credentials in `.env.local` and test the connection in the admin panel!
