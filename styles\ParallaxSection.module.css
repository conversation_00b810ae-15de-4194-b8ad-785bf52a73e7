.parallaxSection {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.parallaxBackground {
  position: absolute;
  top: -50px; /* Extra space to allow for parallax movement */
  left: 0;
  width: 100%;
  height: calc(100% + 100px); /* Extra space to allow for parallax movement */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  will-change: transform; /* Optimize for animations */
  z-index: 1;
}

.parallaxOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.parallaxContent {
  position: relative;
  z-index: 3;
  width: 100%;
  max-width: var(--max-width);
  padding: 0 var(--spacing-lg);
  color: var(--text-inverse);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .parallaxBackground {
    /* Reduce parallax effect on mobile for better performance */
    top: -30px;
    height: calc(100% + 60px);
  }
}

@media (max-width: 480px) {
  .parallaxBackground {
    /* Further reduce parallax effect on small mobile devices */
    top: -20px;
    height: calc(100% + 40px);
  }
}
