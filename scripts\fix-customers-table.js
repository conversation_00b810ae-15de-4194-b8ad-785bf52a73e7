// Script to update the customer database schema
// Run with: node scripts/fix-customers-table.js

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Get Supabase credentials from environment
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service key is missing from .env.local file');
  process.exit(1);
}

// Create Supabase admin client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function runSQL() {
  try {
    const sqlFilePath = path.join(process.cwd(), 'fix_customers_table.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('Executing SQL script to fix customers table...');
    
    // Split the script into individual statements
    const statements = sqlScript.split(';').filter(stmt => stmt.trim() !== '');
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        const { error } = await supabaseAdmin.rpc('exec_sql', { 
          sql_statement: statement.trim() 
        });
        
        if (error) {
          console.error(`Error executing SQL: ${error.message}`);
          console.error('Statement:', statement);
        }
      }
    }
    
    console.log('SQL script executed successfully');
    
    // Verify the changes
    const { data, error } = await supabaseAdmin
      .from('customers')
      .select('*')
      .limit(1);
      
    if (error) {
      console.error('Error verifying customers table:', error.message);
    } else {
      console.log('Customers table structure has been updated');
      console.log('Sample customer:', data);
    }
    
  } catch (err) {
    console.error('Error running SQL script:', err);
  }
}

// Run the main function
runSQL().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
