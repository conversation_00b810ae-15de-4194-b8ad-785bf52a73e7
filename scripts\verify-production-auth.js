#!/usr/bin/env node

/**
 * Production Environment Authentication Verification Script
 * Verifies that all admin API endpoints have proper authentication in production
 */

import { config } from 'dotenv'
import fs from 'fs'
import path from 'path'

// Load environment variables
config({ path: '.env.local' })

const ADMIN_API_ENDPOINTS = [
  '/api/admin/users',
  '/api/admin/users/applications',
  '/api/admin/users/applications/[id]/review',
  '/api/admin/users/applications/[id]/delete',
  '/api/admin/users/applications/[id]/cancel',
  '/api/admin/users/[id]/status',
  '/api/admin/users/create',
  '/api/admin/users/delete'
]

async function verifyProductionAuth() {
  console.log('🔒 Production Authentication Verification\n')

  let allPassed = true
  const results = []

  // Check environment configuration
  console.log('1. Environment Configuration:')
  const nodeEnv = process.env.NODE_ENV
  const authBypass = process.env.ENABLE_AUTH_BYPASS
  
  console.log(`   NODE_ENV: ${nodeEnv}`)
  console.log(`   ENABLE_AUTH_BYPASS: ${authBypass}`)
  
  if (nodeEnv === 'production' && authBypass === 'true') {
    console.log('   ⚠️  WARNING: Auth bypass is enabled in production!')
    allPassed = false
  } else {
    console.log('   ✅ Environment configuration looks good')
  }

  // Check API endpoint authentication
  console.log('\n2. API Endpoint Authentication:')
  
  for (const endpoint of ADMIN_API_ENDPOINTS) {
    const result = await checkEndpointAuth(endpoint)
    results.push(result)
    
    const status = result.hasAuth ? '✅' : '❌'
    console.log(`   ${status} ${endpoint}: ${result.message}`)
    
    if (!result.hasAuth) {
      allPassed = false
    }
  }

  // Check for legacy authentication usage
  console.log('\n3. Legacy Authentication Usage:')
  const legacyUsage = await checkLegacyAuthUsage()
  
  if (legacyUsage.length > 0) {
    console.log('   ⚠️  Found legacy authentication usage:')
    legacyUsage.forEach(file => {
      console.log(`      - ${file}`)
    })
  } else {
    console.log('   ✅ No legacy authentication usage found')
  }

  // Summary
  console.log('\n📊 Summary:')
  const passedCount = results.filter(r => r.hasAuth).length
  const totalCount = results.length
  
  console.log(`   Endpoints with proper auth: ${passedCount}/${totalCount}`)
  console.log(`   Overall status: ${allPassed ? '✅ PASS' : '❌ FAIL'}`)

  if (!allPassed) {
    console.log('\n🔧 Recommended Actions:')
    console.log('   1. Fix authentication issues in failing endpoints')
    console.log('   2. Ensure ENABLE_AUTH_BYPASS is false in production')
    console.log('   3. Update legacy authentication usage')
    console.log('   4. Test authentication flow end-to-end')
  }

  return allPassed
}

async function checkEndpointAuth(endpoint) {
  try {
    // Convert endpoint pattern to file path
    const filePath = endpointToFilePath(endpoint)
    
    if (!fs.existsSync(filePath)) {
      return {
        hasAuth: false,
        message: 'File not found'
      }
    }

    const content = fs.readFileSync(filePath, 'utf8')
    
    // Check for proper authentication import and usage
    const hasAuthImport = content.includes('authenticateAdminRequest') || 
                         content.includes('withAdminAuth')
    
    const hasAuthCheck = content.includes('authResult.authorized') ||
                        content.includes('authorized: false') ||
                        content.includes('Authentication required')
    
    const hasProductionAuth = !content.includes('TODO: Add proper authentication for production')
    
    if (hasAuthImport && hasAuthCheck && hasProductionAuth) {
      return {
        hasAuth: true,
        message: 'Proper authentication implemented'
      }
    } else if (!hasProductionAuth) {
      return {
        hasAuth: false,
        message: 'TODO comment found - authentication not implemented'
      }
    } else if (!hasAuthImport) {
      return {
        hasAuth: false,
        message: 'Missing authentication import'
      }
    } else {
      return {
        hasAuth: false,
        message: 'Authentication check not found'
      }
    }
  } catch (error) {
    return {
      hasAuth: false,
      message: `Error checking file: ${error.message}`
    }
  }
}

function endpointToFilePath(endpoint) {
  // Convert API endpoint to file path
  let filePath = endpoint.replace('/api/', 'pages/api/')
  
  // Handle dynamic routes
  filePath = filePath.replace(/\[([^\]]+)\]/g, '[$1]')
  
  // Add .js extension if not present
  if (!filePath.endsWith('.js')) {
    filePath += '.js'
  }
  
  return filePath
}

async function checkLegacyAuthUsage() {
  const legacyFiles = []
  
  try {
    const pagesApiDir = 'pages/api'
    const files = getAllJsFiles(pagesApiDir)
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8')
      
      if (content.includes('Using legacy authenticateAdminRequest function')) {
        legacyFiles.push(file)
      }
    }
  } catch (error) {
    console.warn('Error checking legacy auth usage:', error.message)
  }
  
  return legacyFiles
}

function getAllJsFiles(dir) {
  const files = []
  
  function scanDir(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item)
      const stats = fs.statSync(itemPath)
      
      if (stats.isDirectory()) {
        scanDir(itemPath)
      } else if (item.endsWith('.js')) {
        files.push(itemPath)
      }
    }
  }
  
  scanDir(dir)
  return files
}

// Run the verification
verifyProductionAuth()
  .then(success => {
    if (success) {
      console.log('\n✅ Production authentication verification passed!')
      process.exit(0)
    } else {
      console.log('\n❌ Production authentication verification failed!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Verification script error:', error)
    process.exit(1)
  })
