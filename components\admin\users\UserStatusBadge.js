import styles from '@/styles/admin/UserStatusBadge.module.css'

export default function UserStatusBadge({ status, size = 'medium' }) {
  const getStatusConfig = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return {
          label: 'Active',
          className: styles.statusActive,
          icon: '✓'
        }
      case 'inactive':
        return {
          label: 'Inactive',
          className: styles.statusInactive,
          icon: '○'
        }
      case 'deactivated':
        return {
          label: 'Deactivated',
          className: styles.statusDeactivated,
          icon: '✕'
        }
      default:
        return {
          label: status || 'Unknown',
          className: styles.statusDefault,
          icon: '?'
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <span 
      className={`${styles.statusBadge} ${config.className} ${styles[size]}`}
      title={`User status: ${config.label}`}
    >
      <span className={styles.statusIcon}>{config.icon}</span>
      <span className={styles.statusLabel}>{config.label}</span>
    </span>
  )
}
