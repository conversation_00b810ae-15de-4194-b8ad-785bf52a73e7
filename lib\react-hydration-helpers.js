/**
 * React 18 Hydration Helper
 *
 * This utility provides methods to help address React 18 hydration issues,
 * particularly the "Class constructor D cannot be invoked without 'new'" error
 * that can occur during the hydration process in Next.js applications.
 */
import React, { useState, useEffect } from 'react';

/**
 * Helper for components that may have hydration mismatches
 * Use this for components where server rendering might not match client rendering
 *
 * @param {boolean} condition Optional condition to determine if component should use hydration safety
 * @returns {Object} Props to spread onto a component (suppressHydrationWarning)
 *
 * Example usage:
 *   <div {...useHydrationSafety()}>Content that might cause hydration issues</div>
 */
const isClassComponent = (component) => {
  return typeof component === 'function' &&
         !!component.prototype?.isReactComponent;
};

export const patchedCreateElement = (originalCreateElement) => {
  return function(type, props, ...children) {
    if (isClassComponent(type)) {
      // Validate component constructor
      if (typeof type !== 'function' || !type.toString().startsWith('class')) {
        console.warn('[React Patch] Invalid class component:', type.name);
        return originalCreateElement('div', {
          className: 'hydration-fallback',
          suppressHydrationWarning: true
        });
      }

      try {
        const instance = new type(props);
        return originalCreateElement(instance.render.bind(instance), props, ...children);
      } catch (error) {
        console.error('[React Patch] Fallback for:', type.name, error);
        return originalCreateElement('div', {
          className: 'hydration-fallback',
          suppressHydrationWarning: true
        });
      }
    }
    return originalCreateElement(type, props, ...children);
  };
};

export function useHydrationSafety(condition = true) {
  if (typeof window === 'undefined' || !condition) {
    return {};
  }
  return { suppressHydrationWarning: true };
}

/**
 * Fixes common React 18 hydration warning messages by suppressing them
 * Call this from within a useEffect with an empty dependency array
 */
export function suppressHydrationWarnings() {
  if (typeof window === 'undefined') return;

  // Store original console methods
  const originalConsoleError = console.error;

  // Replace console.error with filtered version
  console.error = (...args) => {
    const message = args[0] || '';

    // Skip hydration warnings and authentication session errors
    if (
      typeof message === 'string' && (
        message.includes('Hydration failed') ||
        message.includes('does not match server-rendered HTML') ||
        message.includes('Text content does not match') ||
        message.includes('Class constructor') ||
        message.includes('cannot be invoked without') ||
        message.includes('Expected server HTML to contain') ||
        message.includes('Warning: Did not expect server HTML to contain') ||
        message.includes('Warning: An error occurred during hydration') ||
        message.includes('Auth session missing!') ||
        message.includes('AuthSessionMissingError') ||
        message.includes('The message port closed before a response was received')
      )
    ) {
      return;
    }

    // Pass through other errors
    originalConsoleError.apply(console, args);
  };
}

/**
 * Patch React to fix "Class constructor D cannot be invoked without 'new'" error
 * This is a common issue with React 18 hydration and certain libraries
 */
export function patchReactForClassConstructorError() {
  if (typeof window === 'undefined' || !React) return;

  // Skip if already patched
  if (window.__REACT_PATCHED_FOR_CLASS_CONSTRUCTOR__) return;

  try {
    // Store original createElement
    const originalCreateElement = React.createElement;

    // Replace createElement with patched version
    React.createElement = function patchedCreateElement(type, props, ...children) {
      // Check if we're dealing with a class constructor
      if (typeof type === 'function' &&
          !type.prototype?.isReactComponent && // Not a React component prototype
          type.toString().includes('class') && // Is a class (by string inspection)
          props !== null &&
          typeof props === 'object') {

        try {
          // Try to safely instantiate the class
          const instance = new type(props);
          if (instance && typeof instance.render === 'function') {
            // If it has a render method, use that instead
            return originalCreateElement(instance.render.bind(instance), props, ...children);
          }
        } catch (e) {
          // If instantiation fails, fall back to original behavior
          console.warn('[React Patch] Failed to instantiate class component:', e);
        }
      }

      // Default behavior
      return originalCreateElement.apply(React, [type, props, ...children]);
    };

    // Mark as patched
    window.__REACT_PATCHED_FOR_CLASS_CONSTRUCTOR__ = true;
    console.log('[React Patch] Successfully patched React.createElement for class constructor issues');
  } catch (error) {
    console.error('[React Patch] Failed to patch React:', error);
  }
}

/**
 * Check if the current environment is client-side only
 * Useful for conditionally rendering components that should only run on the client
 *
 * @returns {boolean} True if code is executing in browser, false on server
 */
export function isClientSide() {
  return typeof window !== 'undefined';
}

/**
 * For components that should only render on the client to avoid hydration issues
 *
 * @param {React.Component} Component The component to render only on client side
 * @param {React.Component} FallbackComponent Optional fallback for server-side rendering
 * @returns {React.Component} A component that only renders on the client
 *
 * Example usage:
 *   const ClientOnlyComponent = withClientSideRendering(MyComponent);
 */
export function withClientSideRendering(Component, FallbackComponent = () => null) {
  // Create a wrapper component that only renders on client-side
  function ClientSideOnly(props) {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
      // Set mounted state after component mounts
      setIsMounted(true);
    }, []);

    // Use empty div with same className for better hydration
    if (!isMounted) {
      // Extract className from props if available for better styling consistency
      const { className = '' } = props;
      return <div className={`client-side-placeholder ${className}`} aria-hidden="true"><FallbackComponent {...props} /></div>;
    }

    return <Component {...props} />;
  }

  // Set display name for debugging
  const displayName = Component.displayName || Component.name || 'Component';
  ClientSideOnly.displayName = `ClientOnly(${displayName})`;

  return ClientSideOnly;
}

/**
 * Safely initialize hydration fixes
 * Call this once in your app's main component useEffect
 */
export function initializeHydrationFixes() {
  if (!isClientSide()) return;

  // Apply all fixes
  suppressHydrationWarnings();
  patchReactForClassConstructorError();

  // Mark as fixed
  window.__REACT_HYDRATION_FIXED__ = true;
}

export default {
  useHydrationSafety,
  suppressHydrationWarnings,
  patchReactForClassConstructorError,
  isClientSide,
  withClientSideRendering,
  initializeHydrationFixes
};
