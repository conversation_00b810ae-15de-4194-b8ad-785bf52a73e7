/**
 * Auth Token Manager
 *
 * A centralized utility for managing authentication tokens
 * with a simplified, consistent approach to reduce complexity
 * and prevent authentication issues.
 *
 * This version uses Supabase directly for authentication.
 */

import { supabase } from './supabase';

// Constants - Unified token storage keys to match admin-auth.js expectations
const TOKEN_STORAGE_KEY = 'oss_auth_token_cache';
const COOKIE_TOKEN_KEY = 'oss_auth_token'; // This matches what admin-auth.js looks for
const SUPABASE_TOKEN_KEY = 'sb-ndlgbcsbidyhxbpqzgqp-auth-token';
const TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before expiry

// Track token refresh state to prevent multiple simultaneous refreshes
let isRefreshing = false;
let refreshPromise = null;

/**
 * Get the current auth token from Supabase session or storage
 * Enhanced with better error handling and production-ready token management
 *
 * @returns {Promise<string|null>} The auth token or null if not available
 */
export const getAuthToken = async () => {
  try {
    // First try to get token from storage for performance
    const cachedToken = getTokenFromStorage();
    if (cachedToken) {
      console.log('[AuthTokenManager] Using cached token');
      return cachedToken;
    }

    // In production, if no cached token, user likely needs to log in
    if (process.env.NODE_ENV === 'production') {
      console.log('[AuthTokenManager] Production mode: no cached token found, user needs to authenticate');
      return null;
    }

    // In development, try to get from current Supabase session
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.warn('[AuthTokenManager] Error getting session:', error);
        return null; // Don't try to refresh in production-like scenarios
      }

      if (data?.session?.access_token) {
        console.log('[AuthTokenManager] Got token from current session');
        // Store the token for future use and also set cookie for server-side access
        storeToken(data.session.access_token);
        setCookieToken(data.session.access_token);
        return data.session.access_token;
      }

      // If no current session in development, try to refresh
      console.log('[AuthTokenManager] No current session, attempting refresh');
      return await refreshAuthToken();
    } catch (sessionError) {
      console.warn('[AuthTokenManager] Session access failed:', sessionError);
      return null;
    }
  } catch (error) {
    console.error('[AuthTokenManager] Error in getAuthToken:', error);
    return null;
  }
};

/**
 * Refresh the authentication token with proper state management
 *
 * @returns {Promise<string|null>} The refreshed token or null if refresh failed
 */
export const refreshAuthToken = async () => {
  // If already refreshing, wait for the existing promise
  if (isRefreshing && refreshPromise) {
    console.log('[AuthTokenManager] Token refresh already in progress, waiting...');
    return refreshPromise;
  }

  // Set refreshing state and create promise
  isRefreshing = true;
  refreshPromise = performTokenRefresh();

  try {
    const result = await refreshPromise;
    return result;
  } finally {
    isRefreshing = false;
    refreshPromise = null;
  }
};

/**
 * Perform the actual token refresh
 *
 * @returns {Promise<string|null>} The refreshed token or null if refresh failed
 */
const performTokenRefresh = async () => {
  try {
    console.log('[AuthTokenManager] Starting token refresh');
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('[AuthTokenManager] Token refresh failed:', error);
      return null;
    }

    if (data?.session?.access_token) {
      console.log('[AuthTokenManager] Token refreshed successfully');
      // Store the refreshed token and set cookie
      storeToken(data.session.access_token);
      setCookieToken(data.session.access_token);
      return data.session.access_token;
    }

    console.warn('[AuthTokenManager] Token refresh returned no token');
    return null;
  } catch (error) {
    console.error('[AuthTokenManager] Error during token refresh:', error);
    return null;
  }
};

/**
 * Store a token in sessionStorage with expiration
 *
 * @param {string} token - The auth token to store
 */
export const storeToken = (token) => {
  if (!token || typeof window === 'undefined' || !window.sessionStorage) {
    return;
  }

  try {
    const tokenData = {
      token,
      expiry: Date.now() + (60 * 60 * 1000) - TOKEN_EXPIRY_BUFFER, // 1 hour minus buffer
      refreshed: Date.now()
    };

    sessionStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokenData));
    console.log('[AuthTokenManager] Token stored in sessionStorage');
  } catch (error) {
    console.warn('[AuthTokenManager] Failed to store auth token:', error);
  }
};

/**
 * Set auth token as cookie for server-side access
 * This ensures admin-auth.js can find the token in cookies
 *
 * @param {string} token - The auth token to store as cookie
 */
export const setCookieToken = (token) => {
  if (!token || typeof window === 'undefined') {
    return;
  }

  try {
    // Set cookie with appropriate security settings
    const isProduction = window.location.protocol === 'https:';
    const cookieOptions = [
      'path=/',
      'SameSite=Lax',
      `max-age=${60 * 60}` // 1 hour
    ];

    if (isProduction) {
      cookieOptions.push('Secure');
    }

    // Set both cookie variants for maximum compatibility
    document.cookie = [`${COOKIE_TOKEN_KEY}=${token}`, ...cookieOptions].join('; ');
    document.cookie = [`sb_auth_token=${token}`, ...cookieOptions].join('; ');

    console.log('[AuthTokenManager] Token set as cookies for server-side access');
  } catch (error) {
    console.warn('[AuthTokenManager] Failed to set auth token cookie:', error);
  }
};

/**
 * Store token in multiple locations for compatibility with documented architecture
 * This ensures the token is available where the server-side auth expects it
 *
 * @param {string} token - The auth token to store
 */
export const storeTokenInMultipleLocations = (token) => {
  if (!token || typeof window === 'undefined') {
    return;
  }

  console.log('[Auth Token Manager] Storing token in multiple locations for compatibility...');

  // Store in sessionStorage (primary location)
  storeToken(token);

  // Store in localStorage for compatibility with documented architecture
  try {
    localStorage.setItem(COOKIE_TOKEN_KEY, token);
    localStorage.setItem(SUPABASE_TOKEN_KEY, token);
    console.log('[Auth Token Manager] Stored token in localStorage');
  } catch (error) {
    console.warn('[Auth Token Manager] Failed to store token in localStorage:', error);
  }

// Store in cookies for server-side authentication compatibility
 try {
   // Set cookie with appropriate security settings
  const isProduction = window.location.protocol === 'https:';
    const cookieOptions = [
      `${COOKIE_TOKEN_KEY}=${token}`,
      'path=/',
      'SameSite=Lax',
      `max-age=${60 * 60}` // 1 hour
    ];

    if (isProduction) {
      cookieOptions.push('Secure');
    }

    document.cookie = cookieOptions.join('; ');
    console.log('[Auth Token Manager] Stored token in cookies');
  } catch (error) {
    console.warn('[Auth Token Manager] Failed to store token in cookies:', error);
  }
};

/**
 * Get a token from storage (checks multiple locations as per documented architecture)
 *
 * @returns {string|null} The auth token or null if not available/valid
 */
export const getTokenFromStorage = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  // First try sessionStorage (primary location)
  try {
    const cachedToken = sessionStorage.getItem(TOKEN_STORAGE_KEY);
    if (cachedToken) {
      const tokenData = JSON.parse(cachedToken);
      if (tokenData && tokenData.token && tokenData.expiry > Date.now()) {
        console.log('[Auth Token Manager] Found valid token in sessionStorage');
        return tokenData.token;
      }
    }
  } catch (error) {
    console.warn('[Auth Token Manager] Error accessing token from sessionStorage:', error);
  }

  // Try localStorage (compatibility with documented architecture)
  try {
    const localToken = localStorage.getItem(COOKIE_TOKEN_KEY) || localStorage.getItem(SUPABASE_TOKEN_KEY);
    if (localToken) {
      console.log('[Auth Token Manager] Found token in localStorage');

      // Check if it's a JSON object and extract the access_token
      try {
        const parsedToken = JSON.parse(localToken);
        if (parsedToken && parsedToken.access_token) {
          console.log('[Auth Token Manager] Extracted access_token from JSON object');
          return parsedToken.access_token;
        }
      } catch (parseError) {
        // Not JSON, use as is
        console.log('[Auth Token Manager] Using token as string');
      }

      return localToken;
    }
  } catch (error) {
    console.warn('[Auth Token Manager] Error accessing token from localStorage:', error);
  }

  // Try cookies (server-side compatibility)
  try {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === COOKIE_TOKEN_KEY && value) {
        console.log('[Auth Token Manager] Found token in cookies');
        return value;
      }
    }
  } catch (error) {
    console.warn('[Auth Token Manager] Error accessing token from cookies:', error);
  }

  return null;
};

/**
 * Clear the auth token from all storage locations
 */
export const clearToken = () => {
  if (typeof window === 'undefined') {
    return;
  }

  console.log('[Auth Token Manager] Clearing tokens from all locations...');

  // Clear from sessionStorage
  try {
    sessionStorage.removeItem(TOKEN_STORAGE_KEY);
    console.log('[Auth Token Manager] Cleared token from sessionStorage');
  } catch (error) {
    console.warn('[Auth Token Manager] Error clearing token from sessionStorage:', error);
  }

  // Clear from localStorage
  try {
    localStorage.removeItem(COOKIE_TOKEN_KEY);
    localStorage.removeItem(SUPABASE_TOKEN_KEY);
    console.log('[Auth Token Manager] Cleared token from localStorage');
  } catch (error) {
    console.warn('[Auth Token Manager] Error clearing token from localStorage:', error);
  }

  // Clear from cookies
  try {
    document.cookie = `${COOKIE_TOKEN_KEY}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    console.log('[Auth Token Manager] Cleared token from cookies');
  } catch (error) {
    console.warn('[Auth Token Manager] Error clearing token from cookies:', error);
  }
};

/**
 * Add auth headers to a request options object
 *
 * @param {Object} options - The request options
 * @returns {Promise<Object>} The updated options with auth headers
 */
export const addAuthHeaders = async (options = {}) => {
  const token = await getAuthToken();

  const headers = {
    ...options.headers,
    'Content-Type': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return {
    ...options,
    headers,
    credentials: 'include', // Always include credentials for cookies
  };
};

/**
 * Verify token function for backward compatibility with existing API endpoints
 * This function provides the same interface as expected by legacy API files
 * but internally uses the correct authentication pattern from admin-auth.js
 *
 * @param {Object} req - HTTP request object
 * @returns {Promise<Object>} - { valid, user, error }
 */
export const verifyToken = async (req) => {
  try {
    // Import the correct authentication function
    const { authenticateAdminRequest } = await import('./admin-auth');

    // Use the correct authentication function
    const authResult = await authenticateAdminRequest(req);

    // Transform the result to match the expected interface
    return {
      valid: authResult.authorized,
      user: authResult.user ? {
        ...authResult.user,
        role: authResult.role
      } : null,
      error: authResult.error?.message || null
    };
  } catch (error) {
    console.error('[Auth Token Manager] verifyToken error:', error);
    return {
      valid: false,
      user: null,
      error: error.message || 'Authentication failed'
    };
  }
};

export default {
  getAuthToken,
  storeToken,
  storeTokenInMultipleLocations,
  getTokenFromStorage,
  clearToken,
  addAuthHeaders,
  verifyToken
};
