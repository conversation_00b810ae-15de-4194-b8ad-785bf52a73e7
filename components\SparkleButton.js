import { useState, useRef } from 'react';
import Link from 'next/link';
import GlitterExplosion from './GlitterExplosion';
import styles from '@/styles/SparkleButton.module.css';

/**
 * SparkleButton component that triggers a glitter explosion on click
 *
 * @param {Object} props - Component props
 * @param {string} props.href - Link destination
 * @param {React.ReactNode} props.children - Button content
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element}
 */
const SparkleButton = ({
  href,
  children,
  className = '',
  ...props
}) => {
  const [exploding, setExploding] = useState(false);
  const buttonRef = useRef(null);

  const handleClick = (e) => {
    // Don't trigger explosion if already exploding
    if (exploding) return;

    // Trigger explosion
    setExploding(true);

    // Reset after animation completes
    setTimeout(() => {
      setExploding(false);
    }, 1000);
  };

  return (
    <div className={styles.container} ref={buttonRef}>
      <Link
        href={href}
        className={`${styles.button} ${className}`}
        onClick={handleClick}
        {...props}
      >
        {children}
      </Link>

      {exploding && (
        <GlitterExplosion
          active={exploding}
          particleCount={150}
          colors={['#FFD700', '#4ECDC4', '#FF6B6B', '#FFE66D', '#1A73E8', '#FF1493', '#00FFFF', '#FF00FF']}
          duration={1500}
          onComplete={() => setExploding(false)}
        />
      )}
    </div>
  );
};

export default SparkleButton;
