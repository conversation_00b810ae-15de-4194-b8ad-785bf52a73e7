.customerSearch {
  width: 100%;
  margin-bottom: 30px;
}

.searchHeader {
  margin-bottom: 16px;
}

.searchInputContainer {
  display: flex;
  gap: 8px;
}

.searchInput {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.searchInput:focus {
  outline: none;
  border-color: #6a0dad;
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
}

.filtersToggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 4px;
  color: #495057;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filtersToggle:hover {
  background-color: #e9ecef;
}

.filtersPanel {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.filterGroup {
  margin-bottom: 16px;
}

.filterGroup:last-child {
  margin-bottom: 0;
}

.filterGroupTitle {
  font-size: 0.95rem;
  color: #495057;
  margin-top: 0;
  margin-bottom: 8px;
  font-weight: 500;
}

.tagFilters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tagFilter {
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 0.85rem;
  background-color: transparent;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s;
}

.tagFilter:hover {
  opacity: 0.8;
}

.tagFilter.active {
  color: white;
}

.checkboxFilters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.checkboxFilter {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #495057;
}

.rangeFilter {
  width: 100%;
}

.selectFilter {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  color: #495057;
  font-size: 0.9rem;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-size: 0.9rem;
}

.noResults {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  font-size: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #ced4da;
}

.customersList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.customerCard {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.customerCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.customerInfo {
  flex: 1;
}

.customerName {
  font-size: 1.1rem;
  margin: 0 0 8px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.vipBadge {
  display: inline-block;
  padding: 2px 6px;
  background-color: #ffd700;
  color: #333;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.customerContact {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
  color: #6c757d;
  font-size: 0.9rem;
}

.customerEmail::before {
  content: '✉️';
  margin-right: 4px;
  font-size: 0.9rem;
}

.customerPhone::before {
  content: '📱';
  margin-right: 4px;
  font-size: 0.9rem;
}

.customerTags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
}

.tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  color: white;
}

.customerStats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 0.85rem;
  color: #6c757d;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 4px;
}

.statLabel {
  font-weight: 500;
}

.customerActions {
  display: flex;
  gap: 8px;
}

.selectButton,
.viewButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.selectButton {
  background-color: #6a0dad;
  color: white;
  border: none;
}

.selectButton:hover {
  background-color: #5a0b9d;
}

.viewButton {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
}

.viewButton:hover {
  background-color: #e9ecef;
}

@media (max-width: 768px) {
  .customerCard {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .customerActions {
    margin-top: 16px;
    width: 100%;
  }
  
  .selectButton,
  .viewButton {
    width: 100%;
    text-align: center;
  }
}
