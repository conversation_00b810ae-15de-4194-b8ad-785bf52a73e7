# Square Payment Testing Guide - DOM CLEANUP & AVS FIXES

## 🚨 **Critical Issues Resolved**

**Problem 1**: Square sandbox payments failing with `ADDRESS_VERIFICATION_FAILURE` (AVS_REJECTED)
**Problem 2**: Duplicate card input sections appearing in payment form
**Problem 3**: React DOM errors - `NotFoundError: Failed to execute 'removeChild' on 'Node'`
**Problem 4**: Square SDK iframe cleanup causing "message port closed" errors
**Root Causes**:
- Missing billing address collection and AVS configuration for sandbox environment
- Square SDK `includeInputLabels` causing duplicate card forms
- Improper DOM cleanup during React component unmounting
- Race conditions between Square SDK and React DOM management
**Solution**: Comprehensive Square payment form with safe DOM cleanup, billing address collection, and duplicate prevention

---

## 🔧 **Fixes Implemented**

### **1. Safe DOM Cleanup & Management**
- ✅ **React DOM Error Prevention**: Fixed `removeChild` errors during component unmounting
- ✅ **Square SDK Cleanup**: Proper destruction of Square payment forms before DOM cleanup
- ✅ **Iframe Management**: Safe removal of Square-created iframe elements
- ✅ **Container Cleanup**: Defensive checks before attempting DOM node removal
- ✅ **Race Condition Prevention**: Proper timing and sequencing of cleanup operations
- ✅ **Error Boundary Protection**: Fallback UI for render errors with emergency cleanup

### **2. Enhanced Square Payment Form**
- ✅ **Billing Address Collection**: Added billing address form for sandbox environment
- ✅ **AVS Configuration**: Automatic detection of sandbox vs production environment
- ✅ **Token Enhancement**: Include billing address in payment tokenization
- ✅ **User Experience**: Clear instructions for test environment requirements
- ✅ **Duplicate Prevention**: Fixed duplicate card input sections
- ✅ **Container Management**: Proper cleanup and initialization of Square forms

### **3. Improved Error Handling**
- ✅ **ADDRESS_VERIFICATION_FAILURE**: Specific error messages for AVS failures
- ✅ **Payment Status Checking**: Handle FAILED payments with detailed AVS/CVV status
- ✅ **DOM Error Recovery**: Graceful handling of React DOM cleanup errors
- ✅ **Component Error Boundaries**: Fallback UI when payment form encounters errors
- ✅ **Sandbox vs Production**: Different error messages for test vs live environments
- ✅ **Enhanced Logging**: Detailed console logs for debugging AVS issues

### **4. Sandbox Configuration**
- ✅ **Verification Bypass**: Sandbox-specific payment request options
- ✅ **Test Card Support**: Optimized for Square's test card requirements
- ✅ **Address Validation**: Pre-filled valid test addresses for AVS compliance

---

## 🧪 **Testing Instructions**

### **Phase 1: Verify Single Card Form (No Duplicates)**
1. **Access POS Terminal**: Navigate to `https://www.oceansoulsparkles.com.au/admin/pos`
2. **Select Service**: Choose "Airbrush Face & Body Painting" ($350 AUD)
3. **Choose Credit Card**: Select "Credit Card" payment method
4. **Verify Single Form**: Should see ONLY ONE card input section (card number, expiry, CVV)
5. **Check Billing Address**: Billing address form should appear below (in dev mode)
6. **No Duplicates**: Confirm there are no duplicate card input fields

### **Phase 2: Test with Valid Address**
1. **Use Test Card**: Enter `4111 1111 1111 1111` (Visa)
2. **Expiry**: `12/25`
3. **CVV**: `123`
4. **Keep Default Address**: Use the pre-filled billing address
5. **Process Payment**: Click "Charge $350.00"
6. **Expected Result**: ✅ Payment should succeed

### **Phase 3: Test Address Variations**
Try these test addresses to verify AVS handling:

#### **Valid Test Addresses**
```
Address 1: 1455 Market St, Suite 600, San Francisco, CA, 94103, US
Address 2: 500 Yale Ave N, Seattle, WA, 98109, US
Address 3: 410 Terry Ave N, Seattle, WA, 98109, US
```

#### **Test Cards for Different Scenarios**
```
✅ Success: 4111 1111 1111 1111 (Visa)
✅ Success: 5555 5555 5555 4444 (Mastercard)
❌ Decline: 4000 0000 0000 0002 (Declined)
❌ Insufficient: 4000 0000 0000 9995 (Insufficient funds)
```

---

## 🔍 **Expected Console Logs**

### **Successful Payment Flow (No DOM Errors)**
```javascript
✅ 🧹 Starting safe Square form cleanup...
✅ POSSquarePayment component mounted
✅ Container found via: {domQuery: true, reactRef: true, testId: true, element: div#pos-square-card-container}
✅ Square environment configuration: {environment: "sandbox", nodeEnv: "production", squareEnv: "sandbox"}
✅ Sandbox mode: Enabling billing address collection for AVS compatibility
✅ Using custom billing address form (not Square includeInputLabels to avoid duplication)
✅ Square card object created, attempting to attach...
✅ Safely clearing existing content from container to prevent duplication
✅ ✅ Safely removed existing child 1/1
✅ Square card form attached successfully!
✅ Including billing address for AVS verification: {addressLines: ["1455 Market St", "Suite 600"], city: "San Francisco", state: "CA", postalCode: "94103", country: "US"}
✅ Payment tokenized successfully with AVS data
✅ Square payment successful: N0tb6BIAojlpgwv4Po1TgnlSJVVZY
```

### **AVS Failure Handling**
```javascript
⚠️ Square payment response: {id: "xyz123", status: "FAILED", cardDetails: {avs_status: "AVS_REJECTED", cvv_status: "CVV_ACCEPTED"}}
⚠️ Payment failed with details: {avsStatus: "AVS_REJECTED", cvvStatus: "CVV_ACCEPTED", cardDetails: {...}}
❌ Address verification failed in sandbox mode. This is normal for test cards. Use the pre-filled billing address or try a different test card.
```

---

## 🛠️ **Troubleshooting Guide**

### **If React DOM Errors Occur**
1. **Check Console**: Look for "🧹 Starting safe Square form cleanup..." messages
2. **Verify Cleanup**: Ensure "Square payment form destroyed successfully" appears
3. **Hard Refresh**: Press Ctrl+F5 (or Cmd+Shift+R) to clear cached JavaScript
4. **Check Error Boundary**: Look for fallback UI if component crashes
5. **Monitor Cleanup**: Watch for "✅ Safely removed existing child" messages

### **If Duplicate Card Forms Appear**
1. **Hard Refresh**: Press Ctrl+F5 (or Cmd+Shift+R) to clear cached JavaScript
2. **Check Console**: Look for "Payment form already initialized, skipping duplicate initialization"
3. **Clear Browser Cache**: Clear all cached data for the site
4. **Check for Multiple Scripts**: Look for duplicate Square SDK script tags in Network tab

### **If Billing Address Form Doesn't Appear**
1. **Check Environment**: Verify `SQUARE_ENVIRONMENT=sandbox` in environment variables
2. **Check Console**: Look for "Sandbox mode: Enabling billing address collection" message
3. **Refresh Page**: Clear browser cache and reload the POS terminal

### **If AVS Still Fails**
1. **Use Exact Test Address**: Copy the pre-filled address exactly as shown
2. **Try Different Test Card**: Use `5555 5555 5555 4444` (Mastercard)
3. **Check Console Logs**: Look for tokenization logs with billing address data

### **If Payment Status is FAILED**
1. **Check Payment Response**: Look for `avsStatus` and `cvvStatus` in console
2. **Verify Address Format**: Ensure address matches Square's expected format
3. **Try Production Test**: If available, test with production environment

---

## 📊 **Success Metrics**

### **Before Fix**
```
❌ ADDRESS_VERIFICATION_FAILURE error
❌ Payment status: FAILED (AVS_REJECTED)
❌ 168-hour delay before cancellation
❌ No billing address collection
❌ Duplicate card input sections
❌ Square SDK includeInputLabels causing UI conflicts
❌ React DOM errors: "Failed to execute 'removeChild' on 'Node'"
❌ "message port closed" errors from Square SDK iframes
❌ Component crashes requiring ErrorBoundary recovery
```

### **After Fix**
```
✅ Single card input section (no duplicates)
✅ Billing address form appears in sandbox
✅ Payment tokenization includes address data
✅ Enhanced error messages for AVS failures
✅ Successful payment processing with valid addresses
✅ Proper container cleanup and initialization
✅ No React DOM removeChild errors
✅ Safe Square SDK iframe cleanup
✅ Error boundary protection with fallback UI
✅ Defensive DOM node removal with proper checks
```

---

## 🚀 **Production Considerations**

### **Sandbox vs Production Behavior**
- **Sandbox**: Billing address form appears, AVS failures expected with some test cards
- **Production**: Billing address collection may be optional, real AVS verification applies

### **Real Card Testing**
When testing with real cards in production:
1. **Use Real Addresses**: Billing address must match cardholder's statement
2. **Verify ZIP Codes**: Postal codes are critical for AVS verification
3. **International Cards**: Different AVS rules may apply for non-US cards

---

## 📝 **Next Steps**

1. **Test All Payment Scenarios**: Verify both successful and failed payments
2. **Monitor Production Logs**: Watch for AVS-related issues in live environment
3. **User Training**: Ensure staff understand billing address requirements
4. **Documentation Update**: Update POS user manual with new billing address requirements

---

## 🆘 **Emergency Rollback**

If critical issues persist, the billing address requirement can be temporarily disabled:

```javascript
// In POSSquarePayment.js, comment out this line:
// setShowBillingAddress(true)

// This will hide the billing address form but may cause AVS failures
```

**⚠️ Warning**: Disabling billing address collection may result in continued AVS failures in sandbox environment.

---

## 📞 **Support Information**

**Square Sandbox Documentation**: https://developer.squareup.com/docs/testing/test-values
**AVS Response Codes**: https://developer.squareup.com/docs/payments-api/take-payments#address-verification-system-avs
**Test Card Numbers**: https://developer.squareup.com/docs/testing/test-values#test-card-numbers

The comprehensive AVS fixes should resolve the ADDRESS_VERIFICATION_FAILURE issues and provide a smooth payment experience in both sandbox and production environments.
