import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for payment statistics
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response with payment statistics
 */
export default async function handler(req, res) {
  // Authenticate request using our robust auth module
  const { authorized, error } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({ error: 'Unauthorized access' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get total payments count
    const { count: total, error: totalError } = await supabaseAdmin
      .from('payments')
      .select('*', { count: 'exact', head: true });

    if (totalError) throw totalError;

    // Get completed payments count
    const { count: completed, error: completedError } = await supabaseAdmin
      .from('payments')
      .select('*', { count: 'exact', head: true })
      .in('payment_status', ['completed', 'succeeded']);

    if (completedError) throw completedError;

    // Get pending payments count
    const { count: pending, error: pendingError } = await supabaseAdmin
      .from('payments')
      .select('*', { count: 'exact', head: true })
      .in('payment_status', ['pending', 'processing']);

    if (pendingError) throw pendingError;

    // Get failed payments count
    const { count: failed, error: failedError } = await supabaseAdmin
      .from('payments')
      .select('*', { count: 'exact', head: true })
      .in('payment_status', ['failed', 'canceled', 'refunded']);

    if (failedError) throw failedError;

    // Calculate the total amount processed
    const { data: amounts, error: amountsError } = await supabaseAdmin
      .from('payments')
      .select('amount')
      .in('payment_status', ['completed', 'succeeded']);

    if (amountsError) throw amountsError;

    const totalAmount = amounts.reduce(
      (sum, payment) => sum + (payment.amount || 0),
      0
    );

    return res.status(200).json({
      total,
      completed,
      pending,
      failed,
      totalAmount,
    });
  } catch (error) {
    console.error('Payment stats error:', error);
    return res.status(500).json({
      error: 'Failed to fetch payment statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
