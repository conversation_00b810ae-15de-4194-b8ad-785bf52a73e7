-- User Profile Management System Migration
-- This migration adds comprehensive user profile management features including:
-- - Role-specific permissions and settings
-- - Commission rates for artists and braiders
-- - Custom permission matrices
-- - Profile visibility controls

-- Create user permissions table for granular access control
CREATE TABLE IF NOT EXISTS public.user_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  permission_name TEXT NOT NULL,
  permission_value BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, permission_name)
);

-- Create role permissions template table
CREATE TABLE IF NOT EXISTS public.role_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_name TEXT NOT NULL CHECK (role_name IN ('dev', 'admin', 'artist', 'braider', 'user')),
  permission_name TEXT NOT NULL,
  default_value BOOLEAN DEFAULT FALSE,
  description TEXT,
  category TEXT DEFAULT 'general',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(role_name, permission_name)
);

-- Create commission rates table for artists and braiders
CREATE TABLE IF NOT EXISTS public.user_commission_rates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  service_category TEXT,
  commission_percentage DECIMAL(5, 2) CHECK (commission_percentage >= 0 AND commission_percentage <= 100),
  base_rate DECIMAL(10, 2),
  bonus_rate DECIMAL(10, 2) DEFAULT 0,
  effective_from DATE DEFAULT CURRENT_DATE,
  effective_until DATE,
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_by UUID REFERENCES auth.users,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user profile settings table for customization
CREATE TABLE IF NOT EXISTS public.user_profile_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  setting_name TEXT NOT NULL,
  setting_value JSONB,
  setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json', 'array')),
  is_visible_to_admin BOOLEAN DEFAULT TRUE,
  is_editable_by_user BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, setting_name)
);

-- Create dashboard widget permissions table
CREATE TABLE IF NOT EXISTS public.user_dashboard_widgets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  widget_name TEXT NOT NULL,
  is_enabled BOOLEAN DEFAULT TRUE,
  widget_order INTEGER DEFAULT 0,
  widget_config JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, widget_name)
);

-- Enable RLS on all new tables
ALTER TABLE public.user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_commission_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profile_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_dashboard_widgets ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_permissions
CREATE POLICY "Users can view their own permissions" ON public.user_permissions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Devs can view all permissions" ON public.user_permissions
  FOR SELECT USING (get_user_role(auth.uid()) = 'dev');

CREATE POLICY "Devs can manage all permissions" ON public.user_permissions
  FOR ALL USING (get_user_role(auth.uid()) = 'dev');

-- Create RLS policies for role_permissions
CREATE POLICY "All authenticated users can view role permissions" ON public.role_permissions
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Only devs can manage role permissions" ON public.role_permissions
  FOR ALL USING (get_user_role(auth.uid()) = 'dev');

-- Create RLS policies for user_commission_rates
CREATE POLICY "Users can view their own commission rates" ON public.user_commission_rates
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and devs can view all commission rates" ON public.user_commission_rates
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can manage commission rates" ON public.user_commission_rates
  FOR ALL USING (is_admin_or_dev(auth.uid()));

-- Create RLS policies for user_profile_settings
CREATE POLICY "Users can view their own profile settings" ON public.user_profile_settings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Devs can view all profile settings" ON public.user_profile_settings
  FOR SELECT USING (get_user_role(auth.uid()) = 'dev');

CREATE POLICY "Users can update their editable settings" ON public.user_profile_settings
  FOR UPDATE USING (auth.uid() = user_id AND is_editable_by_user = true);

CREATE POLICY "Devs can manage all profile settings" ON public.user_profile_settings
  FOR ALL USING (get_user_role(auth.uid()) = 'dev');

-- Create RLS policies for user_dashboard_widgets
CREATE POLICY "Users can manage their own dashboard widgets" ON public.user_dashboard_widgets
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Devs can view all dashboard widgets" ON public.user_dashboard_widgets
  FOR SELECT USING (get_user_role(auth.uid()) = 'dev');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON public.user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_name ON public.user_permissions(permission_name);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_name ON public.role_permissions(role_name);
CREATE INDEX IF NOT EXISTS idx_role_permissions_category ON public.role_permissions(category);
CREATE INDEX IF NOT EXISTS idx_user_commission_rates_user_id ON public.user_commission_rates(user_id);
CREATE INDEX IF NOT EXISTS idx_user_commission_rates_active ON public.user_commission_rates(is_active);
CREATE INDEX IF NOT EXISTS idx_user_profile_settings_user_id ON public.user_profile_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_dashboard_widgets_user_id ON public.user_dashboard_widgets(user_id);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_user_permissions_updated_at
  BEFORE UPDATE ON public.user_permissions
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at
  BEFORE UPDATE ON public.role_permissions
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_commission_rates_updated_at
  BEFORE UPDATE ON public.user_commission_rates
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_profile_settings_updated_at
  BEFORE UPDATE ON public.user_profile_settings
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_dashboard_widgets_updated_at
  BEFORE UPDATE ON public.user_dashboard_widgets
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default role permissions
INSERT INTO public.role_permissions (role_name, permission_name, default_value, description, category) VALUES
-- DEV permissions (unrestricted access)
('dev', 'admin_panel_access', true, 'Access to admin panel', 'access'),
('dev', 'user_management', true, 'Manage users and roles', 'users'),
('dev', 'user_profiles_management', true, 'Manage user profiles and permissions', 'users'),
('dev', 'commission_management', true, 'Manage commission rates', 'finance'),
('dev', 'system_settings', true, 'Access system settings', 'system'),
('dev', 'diagnostics_access', true, 'Access diagnostic tools', 'system'),
('dev', 'marketing_access', true, 'Access marketing tools', 'marketing'),
('dev', 'analytics_access', true, 'Access analytics', 'analytics'),
('dev', 'inventory_management', true, 'Manage inventory', 'inventory'),
('dev', 'booking_management', true, 'Manage bookings', 'bookings'),
('dev', 'customer_management', true, 'Manage customers', 'customers'),
('dev', 'pos_access', true, 'Access POS terminal', 'sales'),
('dev', 'payment_management', true, 'Manage payments', 'finance'),
('dev', 'gallery_management', true, 'Manage gallery', 'content'),

-- Admin permissions (full business access)
('admin', 'admin_panel_access', true, 'Access to admin panel', 'access'),
('admin', 'user_management', true, 'Manage users and roles', 'users'),
('admin', 'commission_management', true, 'Manage commission rates', 'finance'),
('admin', 'marketing_access', true, 'Access marketing tools', 'marketing'),
('admin', 'analytics_access', true, 'Access analytics', 'analytics'),
('admin', 'inventory_management', true, 'Manage inventory', 'inventory'),
('admin', 'booking_management', true, 'Manage bookings', 'bookings'),
('admin', 'customer_management', true, 'Manage customers', 'customers'),
('admin', 'pos_access', true, 'Access POS terminal', 'sales'),
('admin', 'payment_management', true, 'Manage payments', 'finance'),
('admin', 'gallery_management', true, 'Manage gallery', 'content'),

-- Artist permissions (service-focused access)
('artist', 'admin_panel_access', true, 'Access to admin panel', 'access'),
('artist', 'booking_management', true, 'Manage own bookings', 'bookings'),
('artist', 'customer_management', true, 'View customer information', 'customers'),
('artist', 'pos_access', true, 'Access POS terminal', 'sales'),
('artist', 'gallery_management', false, 'Manage gallery', 'content'),
('artist', 'commission_view', true, 'View own commission rates', 'finance'),

-- Braider permissions (service-focused access)
('braider', 'admin_panel_access', true, 'Access to admin panel', 'access'),
('braider', 'booking_management', true, 'Manage own bookings', 'bookings'),
('braider', 'customer_management', true, 'View customer information', 'customers'),
('braider', 'pos_access', true, 'Access POS terminal', 'sales'),
('braider', 'gallery_management', false, 'Manage gallery', 'content'),
('braider', 'commission_view', true, 'View own commission rates', 'finance'),

-- User permissions (basic access)
('user', 'admin_panel_access', false, 'Access to admin panel', 'access')
ON CONFLICT (role_name, permission_name) DO NOTHING;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_permissions TO authenticated;
GRANT SELECT ON public.role_permissions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_commission_rates TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_profile_settings TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_dashboard_widgets TO authenticated;
