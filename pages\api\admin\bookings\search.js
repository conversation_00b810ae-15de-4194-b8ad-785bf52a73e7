import { supabaseAdmin } from '@/lib/supabase';
import authTokenManager from '@/lib/auth-token-manager';

/**
 * Enhanced Booking Search API
 * 
 * GET /api/admin/bookings/search
 * 
 * Query Parameters:
 * - search: Text search across customer name, email, booking reference
 * - status: Filter by booking status
 * - service: Filter by service ID
 * - customer: Filter by customer ID
 * - startDate: Filter bookings starting from this date
 * - endDate: Filter bookings ending before this date
 * - location: Filter by location text
 * - limit: Number of results to return (default: 50, max: 200)
 * - offset: Number of results to skip for pagination (default: 0)
 * - sortBy: Field to sort by (default: start_time)
 * - sortOrder: Sort order - asc or desc (default: desc)
 */
export default async function handler(req, res) {
  // Generate request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Enhanced booking search request started`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log(`[${requestId}] Method ${req.method} not allowed`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const authResult = await authTokenManager.verifyToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract and validate query parameters
    const {
      search = '',
      status = 'all',
      service = 'all',
      customer = 'all',
      startDate,
      endDate,
      location = '',
      limit = '50',
      offset = '0',
      sortBy = 'start_time',
      sortOrder = 'desc'
    } = req.query;

    // Validate and sanitize parameters
    const parsedLimit = Math.min(parseInt(limit) || 50, 200);
    const parsedOffset = Math.max(parseInt(offset) || 0, 0);
    const validSortFields = ['start_time', 'end_time', 'status', 'created_at', 'customer_name', 'service_name'];
    const validSortBy = validSortFields.includes(sortBy) ? sortBy : 'start_time';
    const validSortOrder = ['asc', 'desc'].includes(sortOrder) ? sortOrder : 'desc';

    console.log(`[${requestId}] Search parameters:`, {
      search: search.substring(0, 50),
      status,
      service,
      customer,
      startDate,
      endDate,
      location: location.substring(0, 50),
      limit: parsedLimit,
      offset: parsedOffset,
      sortBy: validSortBy,
      sortOrder: validSortOrder
    });

    // Build the base query
    let query = supabaseAdmin
      .from('bookings')
      .select(`
        id,
        customer_id,
        service_id,
        start_time,
        end_time,
        status,
        location,
        notes,
        booking_reference,
        estimated_revenue,
        actual_revenue,
        priority_level,
        internal_notes,
        customer_notes,
        created_at,
        updated_at,
        customers:customer_id (
          id,
          name,
          email,
          phone
        ),
        services:service_id (
          id,
          name,
          color,
          price,
          duration
        )
      `);

    // Apply text search filter
    if (search.trim()) {
      // Search across customer name, email, booking reference, and notes
      query = query.or(`
        customers.name.ilike.%${search}%,
        customers.email.ilike.%${search}%,
        booking_reference.ilike.%${search}%,
        notes.ilike.%${search}%,
        internal_notes.ilike.%${search}%,
        customer_notes.ilike.%${search}%
      `);
    }

    // Apply status filter
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    // Apply service filter
    if (service !== 'all') {
      query = query.eq('service_id', service);
    }

    // Apply customer filter
    if (customer !== 'all') {
      query = query.eq('customer_id', customer);
    }

    // Apply date range filters
    if (startDate) {
      query = query.gte('start_time', new Date(startDate).toISOString());
    }

    if (endDate) {
      query = query.lte('start_time', new Date(endDate).toISOString());
    }

    // Apply location filter
    if (location.trim()) {
      query = query.ilike('location', `%${location}%`);
    }

    // Apply sorting
    const ascending = validSortOrder === 'asc';
    if (validSortBy === 'customer_name') {
      query = query.order('customers(name)', { ascending });
    } else if (validSortBy === 'service_name') {
      query = query.order('services(name)', { ascending });
    } else {
      query = query.order(validSortBy, { ascending });
    }

    // Apply pagination
    query = query.range(parsedOffset, parsedOffset + parsedLimit - 1);

    console.log(`[${requestId}] Executing search query...`);

    // Execute the query
    const { data: bookings, error, count } = await query;

    if (error) {
      console.error(`[${requestId}] Database error:`, error);
      throw error;
    }

    console.log(`[${requestId}] Found ${bookings?.length || 0} bookings`);

    // Get total count for pagination (separate query for performance)
    let totalCount = null;
    if (parsedOffset === 0) {
      // Only get count on first page to avoid performance issues
      let countQuery = supabaseAdmin
        .from('bookings')
        .select('id', { count: 'exact', head: true });

      // Apply the same filters for count
      if (search.trim()) {
        countQuery = countQuery.or(`
          customers.name.ilike.%${search}%,
          customers.email.ilike.%${search}%,
          booking_reference.ilike.%${search}%,
          notes.ilike.%${search}%,
          internal_notes.ilike.%${search}%,
          customer_notes.ilike.%${search}%
        `);
      }

      if (status !== 'all') {
        countQuery = countQuery.eq('status', status);
      }

      if (service !== 'all') {
        countQuery = countQuery.eq('service_id', service);
      }

      if (customer !== 'all') {
        countQuery = countQuery.eq('customer_id', customer);
      }

      if (startDate) {
        countQuery = countQuery.gte('start_time', new Date(startDate).toISOString());
      }

      if (endDate) {
        countQuery = countQuery.lte('start_time', new Date(endDate).toISOString());
      }

      if (location.trim()) {
        countQuery = countQuery.ilike('location', `%${location}%`);
      }

      const { count: totalCountResult } = await countQuery;
      totalCount = totalCountResult;
    }

    // Transform the data for frontend consumption
    const transformedBookings = bookings.map(booking => ({
      id: booking.id,
      customer_id: booking.customer_id,
      service_id: booking.service_id,
      start_time: booking.start_time,
      end_time: booking.end_time,
      status: booking.status,
      location: booking.location,
      notes: booking.notes,
      booking_reference: booking.booking_reference,
      estimated_revenue: booking.estimated_revenue,
      actual_revenue: booking.actual_revenue,
      priority_level: booking.priority_level,
      internal_notes: booking.internal_notes,
      customer_notes: booking.customer_notes,
      created_at: booking.created_at,
      updated_at: booking.updated_at,
      // Flatten customer and service data
      customerName: booking.customers?.name || 'Unknown Customer',
      customerEmail: booking.customers?.email || '',
      customerPhone: booking.customers?.phone || '',
      serviceName: booking.services?.name || 'Unknown Service',
      serviceColor: booking.services?.color || '#3788d8',
      servicePrice: booking.services?.price || 0,
      serviceDuration: booking.services?.duration || 60,
      // Add computed fields
      revenue: booking.actual_revenue || booking.estimated_revenue || booking.services?.price || 0,
      duration: booking.services?.duration || 60
    }));

    // Prepare response
    const response = {
      bookings: transformedBookings,
      pagination: {
        limit: parsedLimit,
        offset: parsedOffset,
        total: totalCount,
        hasMore: bookings.length === parsedLimit
      },
      filters: {
        search,
        status,
        service,
        customer,
        startDate,
        endDate,
        location
      },
      sorting: {
        sortBy: validSortBy,
        sortOrder: validSortOrder
      }
    };

    console.log(`[${requestId}] Search completed successfully`);
    return res.status(200).json(response);

  } catch (error) {
    console.error(`[${requestId}] Error in booking search:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while searching bookings'
    });
  }
}
