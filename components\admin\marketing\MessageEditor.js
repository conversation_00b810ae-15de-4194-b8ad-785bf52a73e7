import { useState, useEffect } from 'react'
import styles from '@/styles/admin/marketing/MessageEditor.module.css'

export default function MessageEditor({
  initialMessage = null,
  campaignId,
  onSave,
  onCancel,
  onDelete,
  onSend,
  readOnly = false
}) {
  const [subject, setSubject] = useState('')
  const [content, setContent] = useState('')
  const [messageType, setMessageType] = useState('email')
  const [scheduledDate, setScheduledDate] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showPersonalizationMenu, setShowPersonalizationMenu] = useState(false)
  const [previewCustomer, setPreviewCustomer] = useState({
    name: '<PERSON>',
    first_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '0412 345 678',
    city: 'Sydney',
    state: 'NSW'
  })

  // Initialize form with initial message data
  useEffect(() => {
    if (initialMessage) {
      setSubject(initialMessage.subject || '')
      setContent(initialMessage.content || '')
      setMessageType(initialMessage.message_type || 'email')
      setScheduledDate(initialMessage.scheduled_date ? new Date(initialMessage.scheduled_date).toISOString().slice(0, 16) : '')
    }
  }, [initialMessage])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Validate form
      if (!subject) {
        throw new Error('Subject is required')
      }

      if (!content) {
        throw new Error('Content is required')
      }

      // Call onSave callback
      await onSave({
        subject,
        content,
        message_type: messageType,
        scheduled_date: scheduledDate ? new Date(scheduledDate).toISOString() : null
      })
    } catch (error) {
      console.error('Error saving message:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle delete
  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this message?')) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      await onDelete()
    } catch (error) {
      console.error('Error deleting message:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle send
  const handleSend = async (testMode = false) => {
    const confirmMessage = testMode
      ? 'This will send a test message to a few customers. Continue?'
      : 'This will send the message to all customers in the segment. This action cannot be undone. Continue?'

    if (!window.confirm(confirmMessage)) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      await onSend(testMode)
    } catch (error) {
      console.error('Error sending message:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Insert personalization token at cursor position
  const insertPersonalizationToken = (token) => {
    const textarea = document.getElementById('message-content')
    if (!textarea) return

    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    const textBefore = content.substring(0, startPos)
    const textAfter = content.substring(endPos, content.length)
    
    setContent(textBefore + token + textAfter)
    setShowPersonalizationMenu(false)
    
    // Focus back on textarea and set cursor position after the inserted token
    setTimeout(() => {
      textarea.focus()
      textarea.selectionStart = startPos + token.length
      textarea.selectionEnd = startPos + token.length
    }, 0)
  }

  // Preview personalized content
  const previewPersonalizedContent = (text) => {
    if (!text) return ''
    
    return text
      .replace(/\{name\}/g, previewCustomer.name)
      .replace(/\{first_name\}/g, previewCustomer.first_name)
      .replace(/\{email\}/g, previewCustomer.email)
      .replace(/\{phone\}/g, previewCustomer.phone)
      .replace(/\{city\}/g, previewCustomer.city)
      .replace(/\{state\}/g, previewCustomer.state)
  }

  return (
    <div className={styles.messageEditor}>
      {error && (
        <div className={styles.error}>
          Error: {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className={styles.formGroup}>
          <label htmlFor="message-type">Message Type *</label>
          <select
            id="message-type"
            value={messageType}
            onChange={(e) => setMessageType(e.target.value)}
            disabled={loading || readOnly}
            className={styles.select}
          >
            <option value="email">Email</option>
            <option value="sms">SMS</option>
            <option value="push">Push Notification</option>
          </select>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="message-subject">Subject *</label>
          <input
            type="text"
            id="message-subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Enter message subject"
            disabled={loading || readOnly}
            className={styles.input}
            required
          />
        </div>

        <div className={styles.formGroup}>
          <div className={styles.contentHeader}>
            <label htmlFor="message-content">Content *</label>
            <div className={styles.personalizationDropdown}>
              <button
                type="button"
                onClick={() => setShowPersonalizationMenu(!showPersonalizationMenu)}
                disabled={loading || readOnly}
                className={styles.personalizationButton}
              >
                Add Personalization
              </button>
              {showPersonalizationMenu && (
                <div className={styles.personalizationMenu}>
                  <button type="button" onClick={() => insertPersonalizationToken('{name}')}>
                    Full Name
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{first_name}')}>
                    First Name
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{email}')}>
                    Email
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{phone}')}>
                    Phone
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{city}')}>
                    City
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{state}')}>
                    State
                  </button>
                </div>
              )}
            </div>
          </div>
          <textarea
            id="message-content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Enter message content"
            disabled={loading || readOnly}
            className={styles.textarea}
            rows={10}
            required
          />
        </div>

        {messageType !== 'push' && (
          <div className={styles.formGroup}>
            <label htmlFor="scheduled-date">Schedule Send (optional)</label>
            <input
              type="datetime-local"
              id="scheduled-date"
              value={scheduledDate}
              onChange={(e) => setScheduledDate(e.target.value)}
              disabled={loading || readOnly}
              className={styles.input}
              min={new Date().toISOString().slice(0, 16)}
            />
            <div className={styles.helpText}>
              Leave blank to send manually
            </div>
          </div>
        )}

        {!readOnly && (
          <div className={styles.formActions}>
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className={styles.cancelButton}
            >
              Cancel
            </button>
            {initialMessage && initialMessage.status !== 'sent' && (
              <button
                type="button"
                onClick={handleDelete}
                disabled={loading}
                className={styles.deleteButton}
              >
                Delete
              </button>
            )}
            <button
              type="submit"
              disabled={loading}
              className={styles.saveButton}
            >
              {loading ? 'Saving...' : 'Save Message'}
            </button>
          </div>
        )}
      </form>

      {initialMessage && initialMessage.status !== 'sent' && !readOnly && (
        <div className={styles.sendActions}>
          <button
            type="button"
            onClick={() => handleSend(true)}
            disabled={loading}
            className={styles.testButton}
          >
            Send Test
          </button>
          <button
            type="button"
            onClick={() => handleSend(false)}
            disabled={loading}
            className={styles.sendButton}
          >
            Send to All
          </button>
        </div>
      )}

      {(subject || content) && (
        <div className={styles.preview}>
          <h3>Preview</h3>
          {subject && (
            <div className={styles.previewSubject}>
              <strong>Subject:</strong> {previewPersonalizedContent(subject)}
            </div>
          )}
          {content && (
            <div className={styles.previewContent}>
              <strong>Content:</strong>
              <div className={styles.previewContentText}>
                {previewPersonalizedContent(content).split('\n').map((line, i) => (
                  <p key={i}>{line}</p>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
