.bookingFormContainer {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.formTitle {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formRow {
  display: flex;
  gap: 20px;
}

.formRow .formGroup {
  flex: 1;
}

.formGroup label {
  font-weight: 500;
  color: #444;
  font-size: 0.95rem;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  border-color: #6a0dad;
  outline: none;
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
}

.formGroup textarea {
  min-height: 100px;
  resize: vertical;
}

.newCustomerForm {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.linkButton {
  background: none;
  border: none;
  color: #6a0dad;
  font-size: 0.9rem;
  padding: 5px 0;
  cursor: pointer;
  text-align: left;
  text-decoration: underline;
  margin-top: 5px;
}

.linkButton:hover {
  color: #5a0b9d;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 10px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancelButton,
.saveButton {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background-color: #e9e9e9;
}

.saveButton {
  background-color: #6a0dad;
  color: white;
  border: none;
}

.saveButton:hover {
  background-color: #5a0b9d;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.95rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .bookingFormContainer {
    padding: 15px;
  }
  
  .formRow {
    flex-direction: column;
    gap: 15px;
  }
  
  .formActions {
    flex-direction: column-reverse;
    gap: 10px;
  }
  
  .cancelButton,
  .saveButton {
    width: 100%;
  }
}
