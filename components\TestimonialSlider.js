import { useState, useEffect } from 'react';
import styles from '@/styles/TestimonialSlider.module.css';

/**
 * TestimonialSlider component for displaying testimonials in a carousel
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {Array} props.testimonials - Array of testimonial objects with quote and author
 * @returns {JSX.Element}
 */
const TestimonialSlider = ({ 
  title = 'Our Happy Clients',
  testimonials = [],
  ...props
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      goToNext();
    }, 8000); // Change testimonial every 8 seconds
    
    return () => clearInterval(interval);
  }, [activeIndex]);

  const goToPrev = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    setActiveIndex((prevIndex) => {
      const newIndex = prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1;
      return newIndex;
    });
    
    setTimeout(() => setIsAnimating(false), 600);
  };

  const goToNext = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    setActiveIndex((prevIndex) => {
      const newIndex = prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1;
      return newIndex;
    });
    
    setTimeout(() => setIsAnimating(false), 600);
  };

  const goToSlide = (index) => {
    if (isAnimating || index === activeIndex) return;
    
    setIsAnimating(true);
    setActiveIndex(index);
    setTimeout(() => setIsAnimating(false), 600);
  };

  return (
    <section className={styles.testimonialSection} {...props}>
      <h2 className={styles.testimonialTitle}>{title}</h2>
      <div className={styles.divider}></div>
      
      <div className={styles.testimonialSliderContainer}>
        <div className={styles.testimonialSlider}>
          {testimonials.map((testimonial, index) => (
            <div 
              key={index} 
              className={`${styles.testimonialSlide} ${index === activeIndex ? styles.active : ''}`}
            >
              <blockquote className={styles.testimonialQuote}>
                {testimonial.quote}
              </blockquote>
              <p className={styles.testimonialAuthor}>
                {testimonial.author}
              </p>
            </div>
          ))}
        </div>
        
        <div className={styles.testimonialControls}>
          <button 
            className={styles.testimonialNavButton}
            onClick={goToPrev}
            aria-label="Previous testimonial"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          
          <div className={styles.testimonialDots}>
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`${styles.testimonialDot} ${index === activeIndex ? styles.activeDot : ''}`}
                onClick={() => goToSlide(index)}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
          
          <button 
            className={styles.testimonialNavButton}
            onClick={goToNext}
            aria-label="Next testimonial"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSlider;
