.commissionManagement {
  padding: 30px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.headerContent h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.headerContent p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.addButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.addButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.addForm {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin-bottom: 30px;
  overflow: hidden;
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
  border-bottom: 1px solid #e9ecef;
}

.formHeader h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #f0f0f0;
  color: #333;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 25px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formGroup label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.formGroup select,
.formGroup input,
.formGroup textarea {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.formGroup select:focus,
.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.cancelButton,
.saveButton {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancelButton {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #e9ecef;
}

.cancelButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.saveButton {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: 2px solid transparent;
}

.saveButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.ratesTable {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tableHeader {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr 1fr 1fr;
  background: #f8f9fa;
  padding: 20px 25px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
}

.tableRow {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr 1fr 1fr;
  padding: 20px 25px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.tableRow:hover {
  background: #f8f9ff;
}

.userCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.userName {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.userCategory {
  color: #666;
  font-size: 0.8rem;
}

.percentageCell {
  font-weight: 600;
  color: #6e8efb;
  font-size: 1rem;
}

.rateCell {
  font-weight: 600;
  color: #28a745;
  font-size: 0.95rem;
}

.earningsCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.earningsTotal {
  font-weight: 700;
  color: #333;
  font-size: 1rem;
}

.earningsBreakdown {
  color: #666;
  font-size: 0.8rem;
}

.statusBadge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusBadge.active {
  background: #d4edda;
  color: #155724;
}

.statusBadge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.actionsCell {
  display: flex;
  gap: 8px;
}

.editButton,
.deleteButton {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton {
  background: #007bff;
  color: white;
}

.editButton:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.deleteButton {
  background: #dc3545;
  color: white;
}

.deleteButton:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.emptyState h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.3rem;
}

.emptyState p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .tableHeader,
  .tableRow {
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .commissionManagement {
    padding: 20px;
  }
  
  .header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .ratesTable {
    overflow-x: auto;
  }
  
  .tableHeader,
  .tableRow {
    min-width: 800px;
  }
}

@media (max-width: 480px) {
  .commissionManagement {
    padding: 15px;
  }
  
  .formHeader {
    padding: 15px 20px;
  }
  
  .formGrid {
    padding: 20px;
  }
}
