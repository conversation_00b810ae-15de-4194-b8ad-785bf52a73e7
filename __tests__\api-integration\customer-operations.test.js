/**
 * Integration tests for Customer API operations (update, delete, GDPR)
 * 
 * These tests use a real Supabase test database instead of mocks.
 * They require proper environment variables in .env.test
 */

import {
  createTestUser,
  createTestCustomer,
  signInTestUser,
  cleanupTestData,
  makeAuthenticatedRequest,
  testSupabase
} from '../utils/api-test-helpers';

// Test data to clean up
const testData = {
  customerIds: [],
  userIds: []
};

// Base URL for API requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

describe('Customer Operations API Integration Tests', () => {
  let adminUser;
  let adminSession;
  let staffUser;
  let staffSession;
  
  // Set up test users before all tests
  beforeAll(async () => {
    // Create admin user
    adminUser = await createTestUser('admin');
    testData.userIds.push(adminUser.id);
    
    // Create staff user
    staffUser = await createTestUser('staff');
    testData.userIds.push(staffUser.id);
    
    // Sign in as admin
    const adminAuth = await signInTestUser({
      email: adminUser.email,
      password: adminUser.password
    });
    adminSession = adminAuth.session;
    
    // Sign in as staff
    const staffAuth = await signInTestUser({
      email: staffUser.email,
      password: staffUser.password
    });
    staffSession = staffAuth.session;
  }, 30000); // Increase timeout for user creation
  
  // Clean up test data after all tests
  afterAll(async () => {
    await cleanupTestData(testData);
  }, 10000);
  
  describe('PUT /api/customers/:id', () => {
    let testCustomer;
    
    beforeEach(async () => {
      // Create fresh test customer for each test
      testCustomer = await createTestCustomer();
      testData.customerIds.push(testCustomer.id);
    });
    
    it('should update a customer', async () => {
      // Updated customer data
      const updatedData = {
        name: 'Updated Customer Name',
        email: testCustomer.email, // Keep same email
        phone: '0499887766',
        city: 'Adelaide',
        state: 'SA',
        notes: 'Updated customer notes'
      };
      
      // Make request to update customer
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        method: 'PUT',
        body: updatedData,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.id).toBe(testCustomer.id);
      expect(response.data.name).toBe(updatedData.name);
      expect(response.data.city).toBe(updatedData.city);
      expect(response.data.state).toBe(updatedData.state);
      expect(response.data.notes).toBe(updatedData.notes);
    });
    
    it('should validate required fields during update', async () => {
      // Missing required fields
      const invalidData = {
        name: '', // Empty name
        email: '', // Empty email
        phone: '0499887766'
      };
      
      // Make request with invalid data
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        method: 'PUT',
        body: invalidData,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(400);
      expect(response.data.error).toBeDefined();
    });
    
    it('should prevent duplicate email during update', async () => {
      // Create another customer with different email
      const anotherCustomer = await createTestCustomer();
      testData.customerIds.push(anotherCustomer.id);
      
      // Try to update with another customer's email
      const duplicateData = {
        name: 'Updated Name',
        email: anotherCustomer.email, // Duplicate email
        phone: '0499887766'
      };
      
      // Make request with duplicate email
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        method: 'PUT',
        body: duplicateData,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(409);
      expect(response.data.error).toBeDefined();
    });
    
    it('should allow staff users to update customers', async () => {
      // Updated customer data
      const updatedData = {
        name: 'Staff Updated Name',
        email: testCustomer.email,
        phone: '0499887766'
      };
      
      // Make request as staff user
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        method: 'PUT',
        body: updatedData,
        session: staffSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.name).toBe(updatedData.name);
    });
  });
  
  describe('DELETE /api/customers/:id', () => {
    let testCustomer;
    
    beforeEach(async () => {
      // Create fresh test customer for each test
      testCustomer = await createTestCustomer();
      testData.customerIds.push(testCustomer.id);
    });
    
    it('should allow admin users to delete customers', async () => {
      // Make request to delete customer
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        method: 'DELETE',
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      
      // Verify customer was deleted
      const { data, error } = await testSupabase
        .from('customers')
        .select('*')
        .eq('id', testCustomer.id)
        .single();
      
      expect(data).toBeNull();
    });
    
    it('should prevent staff users from deleting customers', async () => {
      // Make request as staff user
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        method: 'DELETE',
        session: staffSession
      });
      
      // Check response
      expect(response.status).toBe(403);
      expect(response.data.error).toBeDefined();
      
      // Verify customer was not deleted
      const { data } = await testSupabase
        .from('customers')
        .select('*')
        .eq('id', testCustomer.id)
        .single();
      
      expect(data).not.toBeNull();
    });
    
    it('should return 404 for non-existent customer', async () => {
      // Make request with invalid ID
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/00000000-0000-0000-0000-000000000000`,
        method: 'DELETE',
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(404);
      expect(response.data.error).toBeDefined();
    });
  });
  
  describe('POST /api/customers/:id/gdpr-delete', () => {
    let testCustomer;
    
    beforeEach(async () => {
      // Create fresh test customer for each test
      testCustomer = await createTestCustomer();
      testData.customerIds.push(testCustomer.id);
    });
    
    it('should anonymize customer data (admin only)', async () => {
      // Make request to anonymize customer
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}/gdpr-delete`,
        method: 'POST',
        body: { reason: 'Customer request for GDPR compliance' },
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.message).toBeDefined();
      
      // Verify customer was anonymized
      const { data } = await testSupabase
        .from('customers')
        .select('*')
        .eq('id', testCustomer.id)
        .single();
      
      expect(data).not.toBeNull();
      expect(data.name).toBe('Anonymized User');
      expect(data.email).toContain('anonymized-');
      expect(data.phone).toBeNull();
      expect(data.address).toBeNull();
      expect(data.city).toBeNull();
      expect(data.state).toBeNull();
      expect(data.postal_code).toBeNull();
      expect(data.marketing_consent).toBe(false);
    });
    
    it('should prevent staff users from anonymizing customers', async () => {
      // Make request as staff user
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}/gdpr-delete`,
        method: 'POST',
        body: { reason: 'Customer request' },
        session: staffSession
      });
      
      // Check response
      expect(response.status).toBe(403);
      expect(response.data.error).toBeDefined();
      
      // Verify customer was not anonymized
      const { data } = await testSupabase
        .from('customers')
        .select('*')
        .eq('id', testCustomer.id)
        .single();
      
      expect(data.name).toBe(testCustomer.name);
      expect(data.email).toBe(testCustomer.email);
    });
  });
});
