<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Visibility Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .visibility-status {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        .visibility-item {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .visible { background-color: #d4edda; color: #155724; }
        .hidden { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Service Visibility Persistence Test</h1>
        <p>This test verifies that service visibility settings are properly saved and loaded.</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testVisibilityPersistence()">Test Visibility Persistence</button>
            <button onclick="loadAllServices()">Load All Services</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="info status">Click "Test Visibility Persistence" to start testing...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Services with Visibility Status</h3>
            <div id="services-list">
                <div class="info status">Click "Load All Services" to see current visibility settings...</div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="${result.type} status">[${result.timestamp}] ${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResults();
        }

        async function testVisibilityPersistence() {
            addResult('🧪 Starting visibility persistence test...', 'info');
            
            try {
                // Step 1: Get a service to test with
                const servicesResponse = await fetch('/api/admin/services/index');
                if (!servicesResponse.ok) {
                    throw new Error(`Failed to fetch services: ${servicesResponse.status}`);
                }
                
                const servicesData = await servicesResponse.json();
                const services = servicesData.services || [];
                
                if (services.length === 0) {
                    addResult('❌ No services found to test with', 'error');
                    return;
                }
                
                const testService = services[0];
                addResult(`📋 Testing with service: "${testService.name}"`, 'info');
                
                // Step 2: Get current service details
                const serviceResponse = await fetch(`/api/admin/services/${testService.id}`);
                if (!serviceResponse.ok) {
                    throw new Error(`Failed to fetch service details: ${serviceResponse.status}`);
                }
                
                const serviceData = await serviceResponse.json();
                const service = serviceData.service;
                
                addResult(`📊 Current visibility: Public=${service.visible_on_public}, POS=${service.visible_on_pos}, Events=${service.visible_on_events}`, 'info');
                
                // Step 3: Toggle visibility settings
                const newVisibility = {
                    visible_on_public: !service.visible_on_public,
                    visible_on_pos: !service.visible_on_pos,
                    visible_on_events: !service.visible_on_events
                };
                
                addResult(`🔄 Changing visibility to: Public=${newVisibility.visible_on_public}, POS=${newVisibility.visible_on_pos}, Events=${newVisibility.visible_on_events}`, 'info');
                
                // Step 4: Update the service
                const updateData = {
                    ...service,
                    ...newVisibility
                };
                
                const updateResponse = await fetch(`/api/admin/services/${testService.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                if (!updateResponse.ok) {
                    throw new Error(`Failed to update service: ${updateResponse.status}`);
                }
                
                addResult('✅ Service updated successfully', 'success');
                
                // Step 5: Fetch the service again to verify persistence
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
                
                const verifyResponse = await fetch(`/api/admin/services/${testService.id}`);
                if (!verifyResponse.ok) {
                    throw new Error(`Failed to verify service: ${verifyResponse.status}`);
                }
                
                const verifyData = await verifyResponse.json();
                const verifiedService = verifyData.service;
                
                addResult(`🔍 Verified visibility: Public=${verifiedService.visible_on_public}, POS=${verifiedService.visible_on_pos}, Events=${verifiedService.visible_on_events}`, 'info');
                
                // Step 6: Check if the values persisted correctly
                const publicMatch = verifiedService.visible_on_public === newVisibility.visible_on_public;
                const posMatch = verifiedService.visible_on_pos === newVisibility.visible_on_pos;
                const eventsMatch = verifiedService.visible_on_events === newVisibility.visible_on_events;
                
                if (publicMatch && posMatch && eventsMatch) {
                    addResult('🎉 SUCCESS: All visibility settings persisted correctly!', 'success');
                } else {
                    addResult('❌ FAILURE: Visibility settings did not persist correctly', 'error');
                    if (!publicMatch) addResult(`  - Public visibility mismatch: expected ${newVisibility.visible_on_public}, got ${verifiedService.visible_on_public}`, 'error');
                    if (!posMatch) addResult(`  - POS visibility mismatch: expected ${newVisibility.visible_on_pos}, got ${verifiedService.visible_on_pos}`, 'error');
                    if (!eventsMatch) addResult(`  - Events visibility mismatch: expected ${newVisibility.visible_on_events}, got ${verifiedService.visible_on_events}`, 'error');
                }
                
                // Step 7: Restore original settings
                const restoreResponse = await fetch(`/api/admin/services/${testService.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ...verifiedService,
                        visible_on_public: service.visible_on_public,
                        visible_on_pos: service.visible_on_pos,
                        visible_on_events: service.visible_on_events
                    })
                });
                
                if (restoreResponse.ok) {
                    addResult('🔄 Original settings restored', 'info');
                } else {
                    addResult('⚠️ Warning: Could not restore original settings', 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        async function loadAllServices() {
            try {
                const response = await fetch('/api/admin/services/index');
                if (!response.ok) {
                    throw new Error(`Failed to fetch services: ${response.status}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                displayServices(services);
                
            } catch (error) {
                document.getElementById('services-list').innerHTML = 
                    `<div class="error status">Error loading services: ${error.message}</div>`;
            }
        }

        function displayServices(services) {
            const container = document.getElementById('services-list');
            
            if (services.length === 0) {
                container.innerHTML = '<div class="warning status">No services found</div>';
                return;
            }
            
            const serviceCards = services.slice(0, 10).map(service => {
                return `
                    <div class="service-card">
                        <h4>${service.name}</h4>
                        <div class="visibility-status">
                            <div class="visibility-item ${service.visible_on_public ? 'visible' : 'hidden'}">
                                Public: ${service.visible_on_public ? 'Visible' : 'Hidden'}
                            </div>
                            <div class="visibility-item ${service.visible_on_pos ? 'visible' : 'hidden'}">
                                POS: ${service.visible_on_pos ? 'Visible' : 'Hidden'}
                            </div>
                            <div class="visibility-item ${service.visible_on_events ? 'visible' : 'hidden'}">
                                Events: ${service.visible_on_events ? 'Visible' : 'Hidden'}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                ${serviceCards}
                ${services.length > 10 ? `<p><em>Showing first 10 of ${services.length} services</em></p>` : ''}
            `;
        }

        // Auto-load services on page load
        window.addEventListener('load', () => {
            setTimeout(loadAllServices, 1000);
        });
    </script>
</body>
</html>
