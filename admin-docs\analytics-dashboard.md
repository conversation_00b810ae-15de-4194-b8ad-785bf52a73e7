# Analytics Dashboard

This document provides detailed implementation instructions for the OceanSoulSparkles admin panel analytics dashboard.

## Overview

The analytics dashboard provides business owners with key metrics and insights about their business performance. It includes revenue tracking, customer acquisition statistics, service popularity analytics, and customizable date ranges for all reports.

## Features

- Key business metrics (revenue, bookings, customer acquisition)
- Service popularity analytics
- Customer retention statistics
- Product sales analysis
- Customizable date ranges for all reports
- Visual data representation (charts and graphs)
- Exportable reports

## Database Functions

Create the following database functions in Supabase:

```sql
-- Function to get revenue by date range
CREATE OR REPLACE FUNCTION public.get_revenue_by_date_range(
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ
) RETURNS TABLE (
  date DATE,
  revenue DECIMAL(10, 2)
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(payment_date) AS date,
    SUM(amount) AS revenue
  FROM 
    public.payments
  WHERE 
    payment_date >= start_date AND
    payment_date <= end_date AND
    payment_status IN ('completed', 'succeeded')
  GROUP BY 
    DATE(payment_date)
  ORDER BY 
    date;
END;
$$;

-- Function to get bookings by date range
CREATE OR REPLACE FUNCTION public.get_bookings_by_date_range(
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ
) RETURNS TABLE (
  date DATE,
  booking_count INTEGER
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(created_at) AS date,
    COUNT(*) AS booking_count
  FROM 
    public.bookings
  WHERE 
    created_at >= start_date AND
    created_at <= end_date
  GROUP BY 
    DATE(created_at)
  ORDER BY 
    date;
END;
$$;

-- Function to get new customers by date range
CREATE OR REPLACE FUNCTION public.get_new_customers_by_date_range(
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ
) RETURNS TABLE (
  date DATE,
  customer_count INTEGER
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(created_at) AS date,
    COUNT(*) AS customer_count
  FROM 
    public.customers
  WHERE 
    created_at >= start_date AND
    created_at <= end_date
  GROUP BY 
    DATE(created_at)
  ORDER BY 
    date;
END;
$$;

-- Function to get service popularity
CREATE OR REPLACE FUNCTION public.get_service_popularity(
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ
) RETURNS TABLE (
  service_id UUID,
  service_name TEXT,
  booking_count INTEGER
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id AS service_id,
    s.name AS service_name,
    COUNT(b.id) AS booking_count
  FROM 
    public.services s
  LEFT JOIN 
    public.bookings b ON s.id = b.service_id
  WHERE 
    b.created_at >= start_date AND
    b.created_at <= end_date
  GROUP BY 
    s.id, s.name
  ORDER BY 
    booking_count DESC;
END;
$$;

-- Function to get product sales
CREATE OR REPLACE FUNCTION public.get_product_sales(
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ
) RETURNS TABLE (
  product_id UUID,
  product_name TEXT,
  quantity_sold INTEGER,
  revenue DECIMAL(10, 2)
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id AS product_id,
    p.name AS product_name,
    SUM(oi.quantity) AS quantity_sold,
    SUM(oi.quantity * oi.price) AS revenue
  FROM 
    public.products p
  JOIN 
    public.order_items oi ON p.id = oi.product_id
  JOIN 
    public.orders o ON oi.order_id = o.id
  WHERE 
    o.created_at >= start_date AND
    o.created_at <= end_date AND
    o.status != 'cancelled'
  GROUP BY 
    p.id, p.name
  ORDER BY 
    revenue DESC;
END;
$$;
```

## Implementation Steps

### 1. Create Analytics API Endpoints

Create the following API endpoints:

```javascript
// pages/api/analytics/revenue.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { start_date, end_date, interval } = req.query

  // Validate required parameters
  if (!start_date || !end_date) {
    return res.status(400).json({ error: 'Start date and end date are required' })
  }

  try {
    let data
    
    if (interval === 'daily') {
      // Get daily revenue
      const { data: revenueData, error } = await supabase.rpc(
        'get_revenue_by_date_range',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = revenueData
    } else if (interval === 'weekly') {
      // Get weekly revenue
      const { data: revenueData, error } = await supabase.rpc(
        'get_revenue_by_week',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = revenueData
    } else if (interval === 'monthly') {
      // Get monthly revenue
      const { data: revenueData, error } = await supabase.rpc(
        'get_revenue_by_month',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = revenueData
    } else {
      // Default to daily
      const { data: revenueData, error } = await supabase.rpc(
        'get_revenue_by_date_range',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = revenueData
    }
    
    return res.status(200).json(data)
  } catch (error) {
    console.error('Error fetching revenue data:', error)
    return res.status(500).json({ error: 'Failed to fetch revenue data' })
  }
}
```

Create similar API endpoints for other analytics:

```javascript
// pages/api/analytics/bookings.js
// pages/api/analytics/customers.js
// pages/api/analytics/services.js
// pages/api/analytics/products.js
// pages/api/analytics/summary.js
```

### 2. Create Analytics Dashboard Component

Create a component for the analytics dashboard:

```javascript
// components/admin/AnalyticsDashboard.js
import { useState, useEffect } from 'react'
import { Line, Bar, Pie } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js'
import styles from '@/styles/admin/AnalyticsDashboard.module.css'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

export default function AnalyticsDashboard() {
  const [dateRange, setDateRange] = useState('month') // 'week', 'month', 'year', 'custom'
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')
  const [interval, setInterval] = useState('daily') // 'daily', 'weekly', 'monthly'
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  // Analytics data
  const [revenueData, setRevenueData] = useState(null)
  const [bookingsData, setBookingsData] = useState(null)
  const [customersData, setCustomersData] = useState(null)
  const [servicesData, setServicesData] = useState(null)
  const [productsData, setProductsData] = useState(null)
  const [summary, setSummary] = useState(null)

  // Calculate date range based on selection
  const getDateRange = () => {
    const endDate = new Date()
    let startDate = new Date()
    
    if (dateRange === 'week') {
      startDate.setDate(startDate.getDate() - 7)
    } else if (dateRange === 'month') {
      startDate.setMonth(startDate.getMonth() - 1)
    } else if (dateRange === 'year') {
      startDate.setFullYear(startDate.getFullYear() - 1)
    } else if (dateRange === 'custom') {
      return {
        startDate: new Date(customStartDate),
        endDate: new Date(customEndDate)
      }
    }
    
    return { startDate, endDate }
  }

  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      setError(null)

      try {
        const { startDate, endDate } = getDateRange()
        
        // Format dates for API
        const formattedStartDate = startDate.toISOString()
        const formattedEndDate = endDate.toISOString()
        
        // Fetch revenue data
        const revenueResponse = await fetch(
          `/api/analytics/revenue?start_date=${formattedStartDate}&end_date=${formattedEndDate}&interval=${interval}`
        )
        
        if (!revenueResponse.ok) {
          throw new Error('Failed to fetch revenue data')
        }
        
        const revenueData = await revenueResponse.json()
        setRevenueData(revenueData)
        
        // Fetch bookings data
        const bookingsResponse = await fetch(
          `/api/analytics/bookings?start_date=${formattedStartDate}&end_date=${formattedEndDate}&interval=${interval}`
        )
        
        if (!bookingsResponse.ok) {
          throw new Error('Failed to fetch bookings data')
        }
        
        const bookingsData = await bookingsResponse.json()
        setBookingsData(bookingsData)
        
        // Fetch customers data
        const customersResponse = await fetch(
          `/api/analytics/customers?start_date=${formattedStartDate}&end_date=${formattedEndDate}&interval=${interval}`
        )
        
        if (!customersResponse.ok) {
          throw new Error('Failed to fetch customers data')
        }
        
        const customersData = await customersResponse.json()
        setCustomersData(customersData)
        
        // Fetch services data
        const servicesResponse = await fetch(
          `/api/analytics/services?start_date=${formattedStartDate}&end_date=${formattedEndDate}`
        )
        
        if (!servicesResponse.ok) {
          throw new Error('Failed to fetch services data')
        }
        
        const servicesData = await servicesResponse.json()
        setServicesData(servicesData)
        
        // Fetch products data
        const productsResponse = await fetch(
          `/api/analytics/products?start_date=${formattedStartDate}&end_date=${formattedEndDate}`
        )
        
        if (!productsResponse.ok) {
          throw new Error('Failed to fetch products data')
        }
        
        const productsData = await productsResponse.json()
        setProductsData(productsData)
        
        // Fetch summary data
        const summaryResponse = await fetch(
          `/api/analytics/summary?start_date=${formattedStartDate}&end_date=${formattedEndDate}`
        )
        
        if (!summaryResponse.ok) {
          throw new Error('Failed to fetch summary data')
        }
        
        const summaryData = await summaryResponse.json()
        setSummary(summaryData)
      } catch (error) {
        console.error('Error fetching analytics data:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [dateRange, customStartDate, customEndDate, interval])

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range)
  }

  // Handle interval change
  const handleIntervalChange = (newInterval) => {
    setInterval(newInterval)
  }

  // Prepare chart data for revenue
  const prepareRevenueChartData = () => {
    if (!revenueData) return null
    
    return {
      labels: revenueData.map(item => item.date),
      datasets: [
        {
          label: 'Revenue',
          data: revenueData.map(item => item.revenue),
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1
        }
      ]
    }
  }

  // Prepare chart data for bookings
  const prepareBookingsChartData = () => {
    if (!bookingsData) return null
    
    return {
      labels: bookingsData.map(item => item.date),
      datasets: [
        {
          label: 'Bookings',
          data: bookingsData.map(item => item.booking_count),
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }
      ]
    }
  }

  // Prepare chart data for services
  const prepareServicesChartData = () => {
    if (!servicesData) return null
    
    return {
      labels: servicesData.map(item => item.service_name),
      datasets: [
        {
          label: 'Service Bookings',
          data: servicesData.map(item => item.booking_count),
          backgroundColor: [
            'rgba(255, 99, 132, 0.5)',
            'rgba(54, 162, 235, 0.5)',
            'rgba(255, 206, 86, 0.5)',
            'rgba(75, 192, 192, 0.5)',
            'rgba(153, 102, 255, 0.5)',
            'rgba(255, 159, 64, 0.5)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }
      ]
    }
  }

  if (loading) {
    return <div className={styles.loading}>Loading analytics data...</div>
  }

  if (error) {
    return <div className={styles.error}>{error}</div>
  }

  return (
    <div className={styles.analyticsDashboard}>
      <div className={styles.controls}>
        <div className={styles.dateRangeSelector}>
          <button
            className={`${styles.rangeButton} ${dateRange === 'week' ? styles.active : ''}`}
            onClick={() => handleDateRangeChange('week')}
          >
            Last 7 Days
          </button>
          <button
            className={`${styles.rangeButton} ${dateRange === 'month' ? styles.active : ''}`}
            onClick={() => handleDateRangeChange('month')}
          >
            Last 30 Days
          </button>
          <button
            className={`${styles.rangeButton} ${dateRange === 'year' ? styles.active : ''}`}
            onClick={() => handleDateRangeChange('year')}
          >
            Last Year
          </button>
          <button
            className={`${styles.rangeButton} ${dateRange === 'custom' ? styles.active : ''}`}
            onClick={() => handleDateRangeChange('custom')}
          >
            Custom
          </button>
        </div>

        {dateRange === 'custom' && (
          <div className={styles.customDateRange}>
            <input
              type="date"
              value={customStartDate}
              onChange={(e) => setCustomStartDate(e.target.value)}
              className={styles.dateInput}
            />
            <span>to</span>
            <input
              type="date"
              value={customEndDate}
              onChange={(e) => setCustomEndDate(e.target.value)}
              className={styles.dateInput}
            />
          </div>
        )}

        <div className={styles.intervalSelector}>
          <button
            className={`${styles.intervalButton} ${interval === 'daily' ? styles.active : ''}`}
            onClick={() => handleIntervalChange('daily')}
          >
            Daily
          </button>
          <button
            className={`${styles.intervalButton} ${interval === 'weekly' ? styles.active : ''}`}
            onClick={() => handleIntervalChange('weekly')}
          >
            Weekly
          </button>
          <button
            className={`${styles.intervalButton} ${interval === 'monthly' ? styles.active : ''}`}
            onClick={() => handleIntervalChange('monthly')}
          >
            Monthly
          </button>
        </div>
      </div>

      {summary && (
        <div className={styles.summaryCards}>
          <div className={styles.summaryCard}>
            <h3>Total Revenue</h3>
            <p className={styles.amount}>${summary.total_revenue.toFixed(2)}</p>
          </div>
          <div className={styles.summaryCard}>
            <h3>Total Bookings</h3>
            <p className={styles.amount}>{summary.total_bookings}</p>
          </div>
          <div className={styles.summaryCard}>
            <h3>New Customers</h3>
            <p className={styles.amount}>{summary.new_customers}</p>
          </div>
          <div className={styles.summaryCard}>
            <h3>Products Sold</h3>
            <p className={styles.amount}>{summary.products_sold}</p>
          </div>
        </div>
      )}

      <div className={styles.chartContainer}>
        <div className={styles.chart}>
          <h3>Revenue</h3>
          {revenueData && revenueData.length > 0 ? (
            <Line data={prepareRevenueChartData()} />
          ) : (
            <div className={styles.noData}>No revenue data available</div>
          )}
        </div>

        <div className={styles.chart}>
          <h3>Bookings</h3>
          {bookingsData && bookingsData.length > 0 ? (
            <Bar data={prepareBookingsChartData()} />
          ) : (
            <div className={styles.noData}>No bookings data available</div>
          )}
        </div>

        <div className={styles.chart}>
          <h3>Service Popularity</h3>
          {servicesData && servicesData.length > 0 ? (
            <Pie data={prepareServicesChartData()} />
          ) : (
            <div className={styles.noData}>No service data available</div>
          )}
        </div>
      </div>

      <div className={styles.exportSection}>
        <button className={styles.exportButton}>
          Export Report (CSV)
        </button>
        <button className={styles.exportButton}>
          Export Report (PDF)
        </button>
      </div>
    </div>
  )
}
```

### 3. Create Report Export Functionality

Create API endpoints for exporting reports:

```javascript
// pages/api/analytics/export.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'
import { Parser } from 'json2csv'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { 
    start_date, 
    end_date, 
    report_type, 
    format 
  } = req.query

  // Validate required parameters
  if (!start_date || !end_date || !report_type) {
    return res.status(400).json({ error: 'Start date, end date, and report type are required' })
  }

  try {
    let data
    let fields
    let filename

    // Fetch data based on report type
    if (report_type === 'revenue') {
      const { data: revenueData, error } = await supabase.rpc(
        'get_revenue_by_date_range',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = revenueData
      fields = ['date', 'revenue']
      filename = 'revenue_report'
    } else if (report_type === 'bookings') {
      const { data: bookingsData, error } = await supabase.rpc(
        'get_bookings_by_date_range',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = bookingsData
      fields = ['date', 'booking_count']
      filename = 'bookings_report'
    } else if (report_type === 'services') {
      const { data: servicesData, error } = await supabase.rpc(
        'get_service_popularity',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = servicesData
      fields = ['service_id', 'service_name', 'booking_count']
      filename = 'services_report'
    } else if (report_type === 'products') {
      const { data: productsData, error } = await supabase.rpc(
        'get_product_sales',
        { start_date, end_date }
      )
      
      if (error) throw error
      data = productsData
      fields = ['product_id', 'product_name', 'quantity_sold', 'revenue']
      filename = 'products_report'
    } else {
      return res.status(400).json({ error: 'Invalid report type' })
    }

    // Format and return data
    if (format === 'csv') {
      const json2csvParser = new Parser({ fields })
      const csv = json2csvParser.parse(data)
      
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename=${filename}.csv`)
      return res.status(200).send(csv)
    } else {
      // Default to JSON
      return res.status(200).json(data)
    }
  } catch (error) {
    console.error('Error exporting report:', error)
    return res.status(500).json({ error: 'Failed to export report' })
  }
}
```

## Testing

1. Test analytics data retrieval
2. Test chart rendering
3. Test date range selection
4. Test interval selection
5. Test report export functionality

## Security Considerations

- Implement proper authentication and authorization
- Validate all input data
- Protect against SQL injection
- Implement rate limiting for API endpoints
- Ensure sensitive business data is only accessible to authorized users
