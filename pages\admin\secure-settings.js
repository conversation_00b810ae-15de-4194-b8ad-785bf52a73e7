import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'

export default function SecureSettings() {
  const router = useRouter()
  const { user, hasAdminAccess } = useAuth()

  const [settings, setSettings] = useState({})
  const [categories, setCategories] = useState({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [testing, setTesting] = useState({})
  const [testResults, setTestResults] = useState({})
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [editMode, setEditMode] = useState({})
  const [formData, setFormData] = useState({})

  // Load settings on component mount
  useEffect(() => {
    if (hasAdminAccess) {
      loadSettings()
    }
  }, [hasAdminAccess])

  const loadSettings = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/admin/secure-settings')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load settings')
      }

      setSettings(data.settings)
      setCategories(data.categories)
      
      // Initialize form data with current values
      const initialFormData = {}
      Object.entries(data.categories).forEach(([categoryKey, category]) => {
        Object.entries(category.settings).forEach(([settingKey, config]) => {
          const currentValue = data.settings[categoryKey]?.[settingKey]?.value || config.default || ''
          initialFormData[settingKey] = currentValue
        })
      })
      setFormData(initialFormData)

    } catch (error) {
      console.error('Error loading settings:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (settingKey, value) => {
    setFormData(prev => ({
      ...prev,
      [settingKey]: value
    }))
  }

  const toggleEditMode = (settingKey) => {
    setEditMode(prev => ({
      ...prev,
      [settingKey]: !prev[settingKey]
    }))
  }

  const saveSettings = async (testFirst = false) => {
    try {
      setSaving(true)
      setError('')
      setSuccess('')

      // Only send changed settings
      const changedSettings = {}
      Object.entries(formData).forEach(([key, value]) => {
        const currentValue = getCurrentSettingValue(key)
        if (value !== currentValue) {
          changedSettings[key] = value
        }
      })

      if (Object.keys(changedSettings).length === 0) {
        setSuccess('No changes to save')
        return
      }

      const response = await fetch('/api/admin/secure-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          settings: changedSettings,
          testBeforeSave: testFirst
        })
      })

      const data = await response.json()

      if (!response.ok) {
        if (data.testResults) {
          setTestResults(data.testResults)
        }
        throw new Error(data.message || 'Failed to save settings')
      }

      setSuccess(`Settings saved successfully! Updated: ${data.updatedKeys.join(', ')}`)
      
      if (data.testResults) {
        setTestResults(data.testResults)
      }

      // Reload settings to get updated masked values
      await loadSettings()
      
      // Clear edit modes
      setEditMode({})

    } catch (error) {
      console.error('Error saving settings:', error)
      setError(error.message)
    } finally {
      setSaving(false)
    }
  }

  const testService = async (serviceType) => {
    try {
      setTesting(prev => ({ ...prev, [serviceType]: true }))
      setError('')

      // Get credentials for the service
      const credentials = getServiceCredentials(serviceType)
      
      const response = await fetch('/api/admin/secure-settings/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          serviceType,
          credentials,
          testEmail: user.email // Use admin's email for testing
        })
      })

      const data = await response.json()

      setTestResults(prev => ({
        ...prev,
        [serviceType]: data.result
      }))

      if (!data.success) {
        setError(`${serviceType} test failed: ${data.result.error}`)
      }

    } catch (error) {
      console.error(`Error testing ${serviceType}:`, error)
      setError(`Error testing ${serviceType}: ${error.message}`)
      setTestResults(prev => ({
        ...prev,
        [serviceType]: { success: false, error: error.message }
      }))
    } finally {
      setTesting(prev => ({ ...prev, [serviceType]: false }))
    }
  }

  const getCurrentSettingValue = (settingKey) => {
    for (const [categoryKey, categoryData] of Object.entries(settings)) {
      if (categoryData[settingKey]) {
        return categoryData[settingKey].value
      }
    }
    return ''
  }

  const getServiceCredentials = (serviceType) => {
    const credentials = {}
    
    switch (serviceType) {
      case 'gmail':
        credentials.gmail_smtp_user = formData.gmail_smtp_user
        credentials.gmail_smtp_password = formData.gmail_smtp_password
        break
      case 'workspace':
        credentials.workspace_smtp_user = formData.workspace_smtp_user
        credentials.workspace_smtp_password = formData.workspace_smtp_password
        break
      case 'onesignal':
        credentials.onesignal_app_id = formData.onesignal_app_id
        credentials.onesignal_rest_api_key = formData.onesignal_rest_api_key
        break
    }
    
    return credentials
  }

  if (loading) {
    return (
      <ProtectedRoute adminOnly>
        <AdminLayout title="Secure Settings">
          <div className="flex justify-center items-center h-64">
            <div className="text-lg">Loading secure settings...</div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="Secure Settings">
        <Head>
          <title>Secure Settings - Ocean Soul Sparkles Admin</title>
        </Head>

      <div className="max-w-6xl mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🔐 Secure Settings</h1>
          <p className="text-gray-600">
            Manage encrypted API credentials and service configurations. 
            All sensitive data is encrypted in the database.
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-red-800 font-medium">Error</div>
            <div className="text-red-700">{error}</div>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-green-800 font-medium">Success</div>
            <div className="text-green-700">{success}</div>
          </div>
        )}

        <div className="grid gap-8">
          {Object.entries(categories).map(([categoryKey, category]) => (
            <SettingCategory
              key={categoryKey}
              categoryKey={categoryKey}
              category={category}
              settings={settings[categoryKey] || {}}
              formData={formData}
              editMode={editMode}
              testing={testing}
              testResults={testResults}
              onInputChange={handleInputChange}
              onToggleEdit={toggleEditMode}
              onTestService={testService}
            />
          ))}
        </div>

        <div className="mt-8 flex gap-4 justify-end">
          <button
            onClick={() => saveSettings(false)}
            disabled={saving}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
          <button
            onClick={() => saveSettings(true)}
            disabled={saving}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {saving ? 'Testing & Saving...' : 'Test & Save'}
          </button>
        </div>
      </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}

// Setting Category Component
function SettingCategory({ 
  categoryKey, 
  category, 
  settings, 
  formData, 
  editMode, 
  testing, 
  testResults,
  onInputChange, 
  onToggleEdit, 
  onTestService 
}) {
  const canTest = ['gmail', 'workspace', 'onesignal'].includes(categoryKey)
  const isTestable = canTest && hasRequiredCredentials(categoryKey, formData)
  const testResult = testResults[categoryKey]

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <span>{category.icon}</span>
              {category.title}
            </h2>
            <p className="text-gray-600 mt-1">{category.description}</p>
          </div>
          {canTest && (
            <div className="flex items-center gap-2">
              {testResult && (
                <div className={`px-3 py-1 rounded-full text-sm ${
                  testResult.success 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {testResult.success ? '✅ Working' : '❌ Failed'}
                </div>
              )}
              <button
                onClick={() => onTestService(categoryKey)}
                disabled={!isTestable || testing[categoryKey]}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {testing[categoryKey] ? 'Testing...' : 'Test Service'}
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="p-6">
        <div className="grid gap-4">
          {Object.entries(category.settings).map(([settingKey, config]) => (
            <SettingField
              key={settingKey}
              settingKey={settingKey}
              config={config}
              setting={settings[settingKey]}
              value={formData[settingKey] || ''}
              editMode={editMode[settingKey]}
              onInputChange={onInputChange}
              onToggleEdit={onToggleEdit}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

// Setting Field Component  
function SettingField({ settingKey, config, setting, value, editMode, onInputChange, onToggleEdit }) {
  const displayValue = editMode ? value : (setting?.masked ? setting.value : value)
  const hasValue = setting?.hasValue || !!value

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
      <div>
        <label className="block text-sm font-medium text-gray-700">
          {config.label}
          {config.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        {config.help && (
          <p className="text-xs text-gray-500 mt-1">{config.help}</p>
        )}
      </div>

      <div className="md:col-span-2">
        {editMode ? (
          <div className="flex gap-2">
            {config.type === 'select' ? (
              <select
                value={value}
                onChange={(e) => onInputChange(settingKey, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select an option</option>
                {config.options?.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            ) : config.type === 'boolean' ? (
              <select
                value={value}
                onChange={(e) => onInputChange(settingKey, e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="true">Enabled</option>
                <option value="false">Disabled</option>
              </select>
            ) : (
              <input
                type={config.type === 'password' ? 'password' : config.type || 'text'}
                value={value}
                onChange={(e) => onInputChange(settingKey, e.target.value)}
                placeholder={config.placeholder}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            )}
            <button
              onClick={() => onToggleEdit(settingKey)}
              className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              ✓
            </button>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <div className="flex-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg">
              {hasValue ? (
                <span className="text-gray-900">{displayValue}</span>
              ) : (
                <span className="text-gray-400 italic">Not configured</span>
              )}
            </div>
            <button
              onClick={() => onToggleEdit(settingKey)}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              ✏️
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

function hasRequiredCredentials(serviceType, formData) {
  switch (serviceType) {
    case 'gmail':
      return formData.gmail_smtp_user && formData.gmail_smtp_password
    case 'workspace':
      return formData.workspace_smtp_user && formData.workspace_smtp_password
    case 'onesignal':
      return formData.onesignal_app_id && formData.onesignal_rest_api_key
    default:
      return false
  }
}
