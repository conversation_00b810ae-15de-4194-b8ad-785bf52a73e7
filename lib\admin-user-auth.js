/**
 * Admin User Authentication Module
 *
 * This module provides authentication functions specifically for the user management section
 * of the admin dashboard. It's designed to be more robust and handle edge cases better.
 */

import supabase, { getCurrentUserWithToken } from './supabase';

/**
 * Authenticate an admin request for user management
 *
 * @param {Object} req - HTTP request object
 * @returns {Promise<Object>} Authentication result with user and role
 */
export async function authenticateAdminUserRequest(req) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Starting admin user authentication`);

  // Set a reasonable timeout to prevent hanging
  const timeoutDuration = 10000; // 10 seconds
  let authTimeout = null;

  try {
    // Create a timeout promise
    const timeoutPromise = new Promise((resolve) => {
      authTimeout = setTimeout(() => {
        console.warn(`[${requestId}] Authentication timeout after ${timeoutDuration}ms`);
        resolve({
          authorized: false,
          error: new Error(`Authentication timeout after ${timeoutDuration}ms`)
        });
      }, timeoutDuration);
    });

    // Extract token from request
    const token = extractToken(req, requestId);

    if (!token) {
      console.warn(`[${requestId}] No authentication token found`);
      clearTimeout(authTimeout);
      return {
        authorized: false,
        error: new Error('No authentication token provided')
      };
    }

    // Verify the token
    const authPromise = verifyAdminToken(token, requestId);

    // Race the promises
    const result = await Promise.race([authPromise, timeoutPromise]);

    // Clear the timeout
    if (authTimeout) {
      clearTimeout(authTimeout);
      authTimeout = null;
    }

    return result;
  } catch (error) {
    // Clear the timeout if it exists
    if (authTimeout) {
      clearTimeout(authTimeout);
      authTimeout = null;
    }

    console.error(`[${requestId}] Authentication error:`, error);
    return {
      authorized: false,
      error: new Error(`Authentication error: ${error.message}`)
    };
  }
}

/**
 * Extract token from request
 *
 * @param {Object} req - HTTP request object
 * @param {string} requestId - Request ID for logging
 * @returns {string|null} The extracted token or null
 */
function extractToken(req, requestId) {
  // Check Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader) {
    if (authHeader.startsWith('Bearer ')) {
      console.log(`[${requestId}] Using token from Authorization header`);
      return authHeader.substring(7);
    }
    console.log(`[${requestId}] Using non-Bearer token from Authorization header`);
    return authHeader;
  }

  // Check X-Auth-Token header
  if (req.headers['x-auth-token']) {
    console.log(`[${requestId}] Using token from X-Auth-Token header`);
    return req.headers['x-auth-token'];
  }

  // No token found
  return null;
}

/**
 * Verify admin token
 *
 * @param {string} token - JWT token
 * @param {string} requestId - Request ID for logging
 * @returns {Promise<Object>} Verification result
 */
async function verifyAdminToken(token, requestId) {
  try {
    console.log(`[${requestId}] Verifying admin token`);

    // Get the user from the token
    const { user, role, error } = await getCurrentUserWithToken(token);

    if (error) {
      console.warn(`[${requestId}] Token verification failed:`, error.message);
      return {
        authorized: false,
        error: new Error(`Invalid token: ${error.message}`)
      };
    }

    if (!user) {
      console.warn(`[${requestId}] No user found in token`);
      return {
        authorized: false,
        error: new Error('No user found in token')
      };
    }

    // Check if user has admin role
    if (role !== 'admin') {
      console.warn(`[${requestId}] User does not have admin role: ${role}`);
      return {
        authorized: false,
        user,
        role,
        error: new Error('Admin role required')
      };
    }

    console.log(`[${requestId}] Admin authentication successful for user: ${user.email}`);
    return {
      authorized: true,
      user,
      role
    };
  } catch (error) {
    console.error(`[${requestId}] Error verifying admin token:`, error);
    return {
      authorized: false,
      error: new Error(`Token verification error: ${error.message}`)
    };
  }
}

export default {
  authenticateAdminUserRequest
};
