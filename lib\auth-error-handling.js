/**
 * Auth Error Handling Utils
 * 
 * Enhanced error handling for authentication issues to prevent white screens
 * and provide better error messages for users.
 */

/**
 * Handle and normalize authentication errors
 * 
 * @param {Error} error - The original error
 * @param {string} context - Context where the error occurred
 * @returns {Object} - Normalized error object with friendly message
 */
export function handleAuthError(error, context = 'authentication') {
  console.error(`Auth error in ${context}:`, error);
  
  // Default error structure
  const normalizedError = {
    message: 'Authentication failed. Please try again.',
    originalError: error,
    code: 'unknown_error',
    context
  };
  
  if (!error) {
    return normalizedError;
  }
  
  // Extract message from different error formats
  if (typeof error === 'string') {
    normalizedError.message = error;
  } else if (error.message) {
    normalizedError.message = error.message;
  } else if (error.error_description) {
    normalizedError.message = error.error_description;
  } else if (error.error) {
    normalizedError.message = typeof error.error === 'string' 
      ? error.error 
      : (error.error.message || 'Authentication failed');
  }
  
  // Map error codes to user-friendly messages
  if (error.code || error.statusCode || error.status) {
    const code = error.code || error.statusCode || error.status;
    normalizedError.code = code;
    
    // Map specific error codes to friendly messages
    switch (code) {
      case 'auth/invalid-email':
      case 'invalid_email':
      case 401:
        normalizedError.message = 'Invalid email or password. Please try again.';
        break;
        
      case 'auth/user-disabled':
      case 'user_disabled':
        normalizedError.message = 'This account has been disabled. Please contact support.';
        break;
        
      case 'auth/user-not-found':
      case 'user_not_found':
        normalizedError.message = 'No account found with this email. Please check your email or sign up.';
        break;
        
      case 'auth/wrong-password':
      case 'invalid_password':
        normalizedError.message = 'Incorrect password. Please try again.';
        break;
        
      case 'auth/too-many-requests':
      case 'too_many_requests':
      case 429:
        normalizedError.message = 'Too many login attempts. Please try again later or reset your password.';
        break;
        
      case 'auth/network-request-failed':
      case 'network_error':
      case 'failed_fetch':
        normalizedError.message = 'Network error. Please check your internet connection and try again.';
        break;
        
      case 'auth/expired-action-code':
      case 'expired_token':
      case 'token_expired':
        normalizedError.message = 'Your login session has expired. Please sign in again.';
        break;
        
      case 'auth/invalid-action-code':
      case 'invalid_token':
        normalizedError.message = 'Invalid authentication token. Please sign in again.';
        break;
        
      case 500:
      case 502:
      case 503:
      case 504:
        normalizedError.message = 'Server error. Please try again later.';
        break;
    }
  }
  
  return normalizedError;
}

/**
 * Check if authentication error is recoverable
 * 
 * @param {Object} error - The error object
 * @returns {boolean} - True if error is recoverable
 */
export function isRecoverableAuthError(error) {
  if (!error) return false;
  
  const recoverable = [
    'network_error',
    'failed_fetch',
    'token_expired',
    'token_refreshing_failed',
    'timeout',
    500, 502, 503, 504
  ];
  
  const code = error.code || error.statusCode || error.status;
  
  return recoverable.includes(code) || 
         (error.message && (
           error.message.includes('network') || 
           error.message.includes('timeout') || 
           error.message.includes('failed to fetch') ||
           error.message.includes('expired')
         ));
}

/**
 * Attempt to recover from authentication error
 * 
 * @param {Object} error - The error object
 * @returns {Promise<boolean>} - True if recovery successful
 */
export async function recoverFromAuthError(error) {
  console.log('Attempting to recover from auth error:', error);
  
  if (!isRecoverableAuthError(error)) {
    console.log('Error is not recoverable:', error);
    return false;
  }
  
  try {
    // Clear cached tokens that might be expired
    if (typeof localStorage !== 'undefined') {
      const authKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('supabase.auth')) {
          authKeys.push(key);
        }
      }
      
      if (authKeys.length > 0) {
        console.log('Clearing expired auth tokens:', authKeys);
        authKeys.forEach(key => localStorage.removeItem(key));
      }
    }
    
    // Attempt to refresh session if supabase client is available
    if (typeof window !== 'undefined' && window.supabase && window.supabase.auth) {
      const { data, error: refreshError } = await window.supabase.auth.getSession();
      
      if (refreshError) {
        console.error('Failed to refresh session:', refreshError);
        return false;
      }
      
      if (data && data.session) {
        console.log('Successfully refreshed session');
        return true;
      }
    }
    
    return false;
  } catch (recoveryError) {
    console.error('Error during auth recovery:', recoveryError);
    return false;
  }
}

/**
 * Get safe error message for users
 * 
 * @param {Error|Object|string} error - The error
 * @param {string} fallback - Fallback message
 * @returns {string} - User-friendly error message
 */
export function getSafeErrorMessage(error, fallback = 'An error occurred. Please try again.') {
  if (!error) return fallback;
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error.message) {
    // Filter out sensitive information
    const message = error.message;
    
    // Check for sensitive information patterns
    if (message.includes('Internal Server Error') || 
        message.includes('syntax error') || 
        message.includes('violates') ||
        message.includes('constraint')) {
      return fallback;
    }
    
    return message;
  }
  
  if (error.error_description) {
    return error.error_description;
  }
  
  if (error.error && typeof error.error === 'string') {
    return error.error;
  }
  
  return fallback;
}
