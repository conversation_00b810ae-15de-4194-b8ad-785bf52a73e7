#!/usr/bin/env python3
"""
Create Supabase-ready customer import CSV from Google Contacts analysis
Converts the analyzed Square customer data into the exact format needed for Supabase import
"""

import json
import csv
import re
from datetime import datetime
import uuid

class SupabaseImportGenerator:
    def __init__(self):
        self.customers = []
        self.processed_customers = []
        self.statistics = {
            'total_processed': 0,
            'valid_for_import': 0,
            'skipped_no_email': 0,
            'skipped_invalid_data': 0,
            'marketing_consent_count': 0
        }
    
    def generate_import_csv(self):
        """Main function to generate Supabase import CSV"""
        print("🚀 Generating Supabase customer import CSV...")
        
        try:
            # Load analyzed Square customer data
            self.load_square_customers()
            
            # Process customers for Supabase format
            self.process_customers()
            
            # Generate CSV file
            self.create_csv_file()
            
            # Generate SQL insert statements
            self.create_sql_file()
            
            # Print summary
            self.print_summary()
            
        except Exception as e:
            print(f"❌ Error generating import: {e}")
            raise
    
    def load_square_customers(self):
        """Load the analyzed Square customer data"""
        print("📖 Loading Square customer data...")
        
        try:
            with open('square_customers_detailed.json', 'r') as f:
                self.customers = json.load(f)
            print(f"📊 Loaded {len(self.customers)} Square customers")
        except FileNotFoundError:
            print("❌ square_customers_detailed.json not found. Run analyze_contacts.py first.")
            raise
    
    def process_customers(self):
        """Process customers into Supabase format"""
        print("🔄 Processing customers for Supabase import...")
        
        for customer in self.customers:
            self.statistics['total_processed'] += 1
            
            try:
                processed = self.process_single_customer(customer)
                if processed:
                    self.processed_customers.append(processed)
                    self.statistics['valid_for_import'] += 1
                    
                    if processed.get('marketing_consent'):
                        self.statistics['marketing_consent_count'] += 1
                        
            except Exception as e:
                print(f"⚠️  Error processing customer {customer.get('name', 'Unknown')}: {e}")
                self.statistics['skipped_invalid_data'] += 1
    
    def process_single_customer(self, customer):
        """Process a single customer record"""
        # Skip if no email (required field)
        email = customer.get('email', '').strip().lower()
        if not email:
            self.statistics['skipped_no_email'] += 1
            return None
        
        # Extract custom fields
        custom_fields = customer.get('custom_fields', {})
        
        # Build customer record
        processed = {
            'id': str(uuid.uuid4()),  # Generate UUID for Supabase
            'name': self.clean_name(customer.get('name', '')),
            'email': email,
            'phone': self.clean_phone(customer.get('phone', '')),
            'address': self.extract_address_street(customer.get('address', '')),
            'city': self.extract_address_city(customer.get('address', '')),
            'state': self.extract_address_state(customer.get('address', '')),
            'postal_code': self.extract_address_postal(customer.get('address', '')),
            'country': 'Australia',
            'notes': self.build_notes(customer, custom_fields),
            'marketing_consent': self.determine_marketing_consent(custom_fields),
            'created_at': self.parse_date(custom_fields.get('Created At')),
            'updated_at': datetime.now().isoformat()
        }
        
        # Remove empty values
        return {k: v for k, v in processed.items() if v is not None and v != ''}
    
    def clean_name(self, name):
        """Clean and format customer name"""
        if not name or name.strip() == '':
            return 'Unknown Customer'
        
        # Remove extra whitespace and capitalize properly
        cleaned = ' '.join(name.strip().split())
        return cleaned.title() if cleaned.islower() else cleaned
    
    def clean_phone(self, phone):
        """Clean and standardize phone number to Australian format"""
        if not phone:
            return None
        
        # Remove all non-digit characters except +
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # Handle different Australian phone formats
        if clean_phone.startswith('+61'):
            return clean_phone
        elif clean_phone.startswith('61') and len(clean_phone) == 11:
            return '+' + clean_phone
        elif clean_phone.startswith('04') and len(clean_phone) == 10:
            return '+61' + clean_phone[1:]
        elif clean_phone.startswith('0') and len(clean_phone) == 10:
            return '+61' + clean_phone[1:]
        else:
            # Return original if can't parse
            return phone
    
    def extract_address_street(self, address):
        """Extract street address from formatted address"""
        if not address:
            return None
        
        # Split by newlines and take first line as street
        lines = address.split('\n')
        if lines:
            street = lines[0].strip()
            # Remove postal code and state from street if present
            street = re.sub(r'\s+\d{4}$', '', street)  # Remove trailing postal code
            return street if street else None
        return None
    
    def extract_address_city(self, address):
        """Extract city from formatted address"""
        if not address:
            return None
        
        # Look for city in address patterns
        # Pattern: "City, State PostalCode" or "City State PostalCode"
        match = re.search(r'([A-Za-z\s]+)(?:,\s*)?(?:VIC|NSW|QLD|SA|WA|TAS|NT|ACT)\s*\d{4}', address)
        if match:
            city = match.group(1).strip().rstrip(',')
            return city if city else None
        
        # Fallback: look for common city names
        lines = address.split('\n')
        for line in lines:
            if any(city in line.lower() for city in ['melbourne', 'sydney', 'brisbane', 'perth', 'adelaide']):
                # Extract city name
                words = line.split()
                for word in words:
                    if word.lower() in ['melbourne', 'sydney', 'brisbane', 'perth', 'adelaide']:
                        return word.title()
        
        return None
    
    def extract_address_state(self, address):
        """Extract state from formatted address"""
        if not address:
            return None
        
        # Look for Australian state abbreviations
        states = ['VIC', 'NSW', 'QLD', 'SA', 'WA', 'TAS', 'NT', 'ACT']
        for state in states:
            if state in address.upper():
                return state
        
        # Look for full state names
        state_names = {
            'VICTORIA': 'VIC',
            'NEW SOUTH WALES': 'NSW',
            'QUEENSLAND': 'QLD',
            'SOUTH AUSTRALIA': 'SA',
            'WESTERN AUSTRALIA': 'WA',
            'TASMANIA': 'TAS',
            'NORTHERN TERRITORY': 'NT',
            'AUSTRALIAN CAPITAL TERRITORY': 'ACT'
        }
        
        for full_name, abbrev in state_names.items():
            if full_name in address.upper():
                return abbrev
        
        return None
    
    def extract_address_postal(self, address):
        """Extract postal code from formatted address"""
        if not address:
            return None
        
        # Look for 4-digit postal codes
        match = re.search(r'\b(\d{4})\b', address)
        if match:
            postal = match.group(1)
            # Validate Australian postal code ranges
            postal_int = int(postal)
            if 1000 <= postal_int <= 9999:
                return postal
        
        return None
    
    def build_notes(self, customer, custom_fields):
        """Build comprehensive notes field"""
        notes = []
        
        # Add Square Customer ID
        square_id = customer.get('square_customer_id')
        if square_id:
            notes.append(f"Square Customer ID: {square_id}")
        
        # Add transaction data
        transaction_count = custom_fields.get('Transaction Count')
        if transaction_count:
            notes.append(f"Transaction Count: {transaction_count}")
        
        total_spend = custom_fields.get('Total Spend')
        if total_spend:
            notes.append(f"Total Spend: ${total_spend}")
        
        # Add visit dates
        first_visit = custom_fields.get('First Visit')
        if first_visit:
            notes.append(f"First Visit: {first_visit}")
        
        last_visit = custom_fields.get('Last Visit SQUARE')
        if last_visit:
            notes.append(f"Last Square Visit: {last_visit}")
        
        # Add creation source
        creation_source = custom_fields.get('Creation Source')
        if creation_source:
            notes.append(f"Acquisition Source: {creation_source}")
        
        # Add subscription status
        email_status = custom_fields.get('Email subscriber status')
        if email_status:
            notes.append(f"Email Subscription: {email_status}")
        
        sms_status = custom_fields.get('SMS subscriber status')
        if sms_status:
            notes.append(f"SMS Subscription: {sms_status}")
        
        # Add memo if present (contains merge information)
        memo = custom_fields.get('Memo')
        if memo:
            notes.append(f"Memo: {memo}")
        
        # Add import metadata
        notes.append(f"Imported from Google Contacts: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return '\n\n'.join(notes) if notes else None
    
    def determine_marketing_consent(self, custom_fields):
        """Determine marketing consent from subscription status"""
        email_status = custom_fields.get('Email subscriber status', '').lower()
        return email_status == 'subscribed'
    
    def parse_date(self, date_string):
        """Parse date string to ISO format"""
        if not date_string:
            return None
        
        try:
            # Try different date formats
            formats = [
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(date_string, fmt)
                    return dt.isoformat()
                except ValueError:
                    continue
            
            # If no format matches, return None
            return None
            
        except Exception:
            return None

    def create_csv_file(self):
        """Create CSV file for Supabase import"""
        print("📄 Creating CSV file for Supabase import...")

        if not self.processed_customers:
            print("⚠️  No customers to export")
            return

        # Define CSV headers matching Supabase customers table
        headers = [
            'id',
            'name',
            'email',
            'phone',
            'address',
            'city',
            'state',
            'postal_code',
            'country',
            'notes',
            'marketing_consent',
            'created_at',
            'updated_at'
        ]

        filename = 'supabase_customers_import.csv'

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

            for customer in self.processed_customers:
                # Ensure all headers are present
                row = {header: customer.get(header, '') for header in headers}
                writer.writerow(row)

        print(f"✅ Created {filename} with {len(self.processed_customers)} customers")

    def create_sql_file(self):
        """Create SQL INSERT statements for direct database import"""
        print("📄 Creating SQL file for direct import...")

        if not self.processed_customers:
            print("⚠️  No customers to export")
            return

        filename = 'supabase_customers_import.sql'

        with open(filename, 'w', encoding='utf-8') as sqlfile:
            sqlfile.write("-- Supabase Customer Import SQL\n")
            sqlfile.write("-- Generated from Google Contacts export\n")
            sqlfile.write(f"-- Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            sqlfile.write("-- Insert customers with Square Customer IDs\n")
            sqlfile.write("INSERT INTO public.customers (\n")
            sqlfile.write("  id, name, email, phone, address, city, state, postal_code, country,\n")
            sqlfile.write("  notes, marketing_consent, created_at, updated_at\n")
            sqlfile.write(") VALUES\n")

            for i, customer in enumerate(self.processed_customers):
                # Escape single quotes in text fields
                def escape_sql(value):
                    if value is None:
                        return 'NULL'
                    if isinstance(value, bool):
                        return 'TRUE' if value else 'FALSE'
                    if isinstance(value, str):
                        return "'" + value.replace("'", "''") + "'"
                    return str(value)

                values = [
                    escape_sql(customer.get('id')),
                    escape_sql(customer.get('name')),
                    escape_sql(customer.get('email')),
                    escape_sql(customer.get('phone')),
                    escape_sql(customer.get('address')),
                    escape_sql(customer.get('city')),
                    escape_sql(customer.get('state')),
                    escape_sql(customer.get('postal_code')),
                    escape_sql(customer.get('country')),
                    escape_sql(customer.get('notes')),
                    escape_sql(customer.get('marketing_consent')),
                    escape_sql(customer.get('created_at')),
                    escape_sql(customer.get('updated_at'))
                ]

                sqlfile.write(f"  ({', '.join(values)})")

                if i < len(self.processed_customers) - 1:
                    sqlfile.write(",\n")
                else:
                    sqlfile.write(";\n")

            sqlfile.write("\n-- Update customer statistics after import\n")
            sqlfile.write("-- You may want to run analytics updates after this import\n")

        print(f"✅ Created {filename} with {len(self.processed_customers)} INSERT statements")

    def print_summary(self):
        """Print processing summary"""
        print('\n📊 SUPABASE IMPORT GENERATION SUMMARY')
        print('=' * 60)
        print(f"📋 Total customers processed: {self.statistics['total_processed']}")
        print(f"✅ Valid for import: {self.statistics['valid_for_import']}")
        print(f"📧 With marketing consent: {self.statistics['marketing_consent_count']}")
        print(f"⚠️  Skipped (no email): {self.statistics['skipped_no_email']}")
        print(f"❌ Skipped (invalid data): {self.statistics['skipped_invalid_data']}")

        print('\n📁 FILES GENERATED:')
        print('• supabase_customers_import.csv - CSV file for bulk import')
        print('• supabase_customers_import.sql - SQL INSERT statements')

        print('\n🚀 IMPORT INSTRUCTIONS:')
        print('1. Review the generated files for data accuracy')
        print('2. Backup your existing customers table')
        print('3. Choose import method:')
        print('   a) CSV: Use Supabase dashboard import feature')
        print('   b) SQL: Run the SQL file in Supabase SQL Editor')
        print('4. Verify import success and data integrity')
        print('5. Update any customer analytics/statistics')

        print('\n⚠️  IMPORTANT NOTES:')
        print('• All customers have Square Customer IDs for payment integration')
        print('• Marketing consent is based on email subscription status')
        print('• Phone numbers are standardized to Australian format')
        print('• Addresses are parsed into structured components')
        print('• Transaction history is preserved in notes field')

        if self.statistics['skipped_no_email'] > 0:
            print(f'\n📧 EMAIL MISSING: {self.statistics["skipped_no_email"]} customers skipped due to missing email addresses')
            print('   Consider manual review of these records if needed')

if __name__ == '__main__':
    generator = SupabaseImportGenerator()
    generator.generate_import_csv()
