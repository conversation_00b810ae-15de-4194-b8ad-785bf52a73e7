import { useState, useEffect, useCallback } from 'react';
import { getAuthToken } from '@/lib/auth-token-manager';
import styles from '@/styles/admin/ServiceList.module.css';
import ErrorBoundary from '../ErrorBoundary';
import { initializeConsoleCapture, testErrorCapture } from '@/lib/console-error-capture';

export default function ServiceList({ refreshKey = 0, onSelectService }) {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    visibility: '', // New visibility filter
  });
  const [selectedServices, setSelectedServices] = useState(new Set());
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Fetch services when search, sort, filters, or refreshKey changes
  const fetchServices = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(`/api/admin/services?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }

      const data = await response.json();

      console.log('🔍 ServiceList - Raw API response:', data);
      console.log('🔍 ServiceList - Services array:', data.services);

      // Log first service to see structure
      if (data.services && data.services.length > 0) {
        console.log('🔍 ServiceList - First service structure:', data.services[0]);
        console.log('🔍 ServiceList - First service pricing_tiers:', data.services[0].pricing_tiers);
      }

      // Apply EXACT working pattern from /services page - convert ALL data to primitive strings
      // This is the proven method that prevents React Error #130
      // BUT preserve pricing_tiers array for editing functionality
      const safeServices = (data.services || []).map(service => {
        // Convert every single field to a string, exactly like the working /services page
        return {
          id: String(service.id || ''),
          name: String(service.name || ''),
          description: String(service.description || ''),
          price: String(service.price || ''),
          duration: String(service.duration || ''),
          category: String(service.category || ''),
          status: String(service.status || 'active'),
          image_url: String(service.image_url || ''),
          color: String(service.color || ''),
          featured: String(service.featured || 'false'), // Convert boolean to string
          created_at: String(service.created_at || ''),
          updated_at: String(service.updated_at || ''),
          // CRITICAL: Preserve pricing_tiers array for editing functionality
          pricing_tiers: Array.isArray(service.pricing_tiers) ? service.pricing_tiers : [],
          // Add visibility fields for enhanced dashboard
          visible_on_public: Boolean(service.visible_on_public),
          visible_on_pos: Boolean(service.visible_on_pos),
          visible_on_events: Boolean(service.visible_on_events),
          category_id: String(service.category_id || ''),
          // Also preserve other complex fields that might be needed
          meta_title: String(service.meta_title || ''),
          meta_description: String(service.meta_description || ''),
          booking_requirements: String(service.booking_requirements || ''),
          availability_notes: String(service.availability_notes || ''),
          gallery_images: Array.isArray(service.gallery_images) ? service.gallery_images : []
        };
      });

      setServices(safeServices);
    } catch (error) {
      console.error('Error fetching services:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [sortBy, sortOrder, debouncedSearch, filters]);

  useEffect(() => {
    // Initialize console error capture for debugging React Error #130
    initializeConsoleCapture();
    console.log('🔧 ServiceList: Console error capture initialized');

    fetchServices();
  }, [fetchServices, refreshKey]);



  // Handle sort column click
  const handleSort = (column) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to ascending order for new column
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null;

    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    );
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };



  // Bulk operations
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedServices(new Set(services.map(s => s.id)));
    } else {
      setSelectedServices(new Set());
    }
  };

  const handleSelectService = (serviceId, checked) => {
    const newSelected = new Set(selectedServices);
    if (checked) {
      newSelected.add(serviceId);
    } else {
      newSelected.delete(serviceId);
    }
    setSelectedServices(newSelected);
  };

  const bulkUpdateStatus = async (status) => {
    if (selectedServices.size === 0) {
      alert('Please select services to update');
      return;
    }

    setBulkActionLoading(true);
    try {
      const token = await getAuthToken();
      const promises = Array.from(selectedServices).map(serviceId =>
        fetch(`/api/admin/services/${serviceId}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token || ''}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ status })
        })
      );

      await Promise.all(promises);
      setSelectedServices(new Set());
      fetchServices();
    } catch (error) {
      console.error('Error in bulk update:', error);
      alert('Failed to update services. Please try again.');
    } finally {
      setBulkActionLoading(false);
    }
  };



  // Safe formatting functions removed - not used in this component

  return (
    <ErrorBoundary debugProps={{ componentName: 'ServiceList', servicesCount: services.length }}>
      <div className={styles.serviceListContainer}>
      <div className={styles.filters}>
        <div className={styles.searchBox}>
          <input
            type="text"
            placeholder="Search services..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filterControls}>
          <div className={styles.filterItem}>
            <label>Category:</label>
            <select
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="">All Categories</option>
              <option value="face-painting">Face Painting</option>
              <option value="body-art">Body Art</option>
              <option value="glitter">Glitter</option>
              <option value="braiding">Braiding</option>
              <option value="entertainment">Entertainment</option>
            </select>
          </div>

          <div className={styles.filterItem}>
            <label>Status:</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className={styles.filterItem}>
            <label>Visibility:</label>
            <select
              name="visibility"
              value={filters.visibility}
              onChange={handleFilterChange}
            >
              <option value="">All Services</option>
              <option value="public">Public Book-Online</option>
              <option value="pos">POS Terminal</option>
              <option value="events">Events Booking</option>
              <option value="hidden">Hidden Services</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedServices.size > 0 && (
          <div className={styles.bulkActions}>
            <div className={styles.bulkInfo}>
              {selectedServices.size} service{selectedServices.size > 1 ? 's' : ''} selected
            </div>
            <div className={styles.bulkButtons}>
              <button
                onClick={() => bulkUpdateStatus('active')}
                disabled={bulkActionLoading}
                className={styles.bulkButton}
              >
                Activate Selected
              </button>
              <button
                onClick={() => bulkUpdateStatus('inactive')}
                disabled={bulkActionLoading}
                className={styles.bulkButton}
              >
                Deactivate Selected
              </button>

              <button
                onClick={() => setSelectedServices(new Set())}
                className={styles.bulkButtonCancel}
              >
                Clear Selection
              </button>
            </div>
          </div>
        )}
      </div>

      {loading && (
        <div className={styles.loadingSpinner}>Loading services...</div>
      )}

      {error && (
        <div className={styles.errorMessage}>
          Error: {error}
          <button onClick={fetchServices}>Try Again</button>
        </div>
      )}

      {!loading && !error && services.length === 0 && (
        <div className={styles.noResults}>
          No services found. Try adjusting your search or filters.
        </div>
      )}

      {!loading && !error && services.length > 0 && (
        <div className={styles.tableContainer}>
          <table className={styles.serviceTable}>
            <thead>
              <tr>
                <th className={styles.checkboxColumn}>
                  <input
                    type="checkbox"
                    checked={selectedServices.size === services.length && services.length > 0}
                    onChange={handleSelectAll}
                    title="Select all services"
                  />
                </th>
                <th>Image</th>
                <th onClick={() => handleSort('name')}>
                  Service Name {renderSortIndicator('name')}
                </th>
                <th onClick={() => handleSort('price')}>
                  Price {renderSortIndicator('price')}
                </th>
                <th onClick={() => handleSort('duration')}>
                  Duration {renderSortIndicator('duration')}
                </th>
                <th onClick={() => handleSort('category')}>
                  Category {renderSortIndicator('category')}
                </th>
                <th onClick={() => handleSort('status')}>
                  Status {renderSortIndicator('status')}
                </th>
                <th>Visibility</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {services.map((service) => {
                try {
                  return (
                    <tr key={service.id || Math.random()}>
                      <td className={styles.checkboxColumn}>
                        <input
                          type="checkbox"
                          checked={selectedServices.has(service.id)}
                          onChange={(e) => handleSelectService(service.id, e.target.checked)}
                        />
                      </td>
                      <td className={styles.imageCell}>
                        {String(service.image_url || '') ? (
                          <img
                            src={String(service.image_url || '')}
                            alt={String(service.name || 'Service')}
                            width="50"
                            height="50"
                            style={{ objectFit: 'cover' }}
                            onError={(e) => {
                              // Prevent infinite error loops
                              if (e.target.src !== '/images/placeholder.svg') {
                                e.target.onerror = null;
                                e.target.src = '/images/placeholder.svg';
                              } else {
                                // If placeholder also fails, hide the image and show CSS fallback
                                e.target.style.display = 'none';
                                e.target.nextElementSibling.style.display = 'flex';
                              }
                            }}
                          />
                        ) : (
                          <div className={styles.noImage}>No Image</div>
                        )}
                        {/* CSS-based fallback for when both image and placeholder fail */}
                        <div
                          className={styles.imageFallback}
                          style={{ display: 'none' }}
                        >
                          🎨
                        </div>
                      </td>
                      <td>{String(service.name || '')}</td>
                      <td>
                        {service.pricing_tiers && service.pricing_tiers.length > 0 ? (
                          <div className={styles.pricingTiers}>
                            <div className={styles.mainPrice}>
                              {service.pricing_tiers.find(tier => tier.is_default === 'true') ?
                                `A$${service.pricing_tiers.find(tier => tier.is_default === 'true').price}` :
                                `A$${service.pricing_tiers[0].price}`
                              }
                            </div>
                            <div className={styles.tierCount}>
                              {service.pricing_tiers.length} tier{service.pricing_tiers.length > 1 ? 's' : ''}
                            </div>
                          </div>
                        ) : (
                          `A$${String(service.price || '')}`
                        )}
                      </td>
                      <td>
                        {service.pricing_tiers && service.pricing_tiers.length > 0 ? (
                          <div className={styles.durationRange}>
                            {Math.min(...service.pricing_tiers.map(tier => parseInt(tier.duration) || 0))} -
                            {Math.max(...service.pricing_tiers.map(tier => parseInt(tier.duration) || 0))} min
                          </div>
                        ) : (
                          `${String(service.duration || '')} min`
                        )}
                      </td>
                      <td>{String(service.category || '')}</td>
                      <td>
                        <span
                          className={`${styles.statusBadge} ${
                            String(service.status || '') === 'active'
                              ? styles.statusActive
                              : styles.statusInactive
                          }`}
                        >
                          {String(service.status || '')}
                        </span>
                      </td>
                      <td className={styles.visibilityColumn}>
                        <div className={styles.visibilityIndicators}>
                          <span
                            className={`${styles.visibilityBadge} ${
                              service.visible_on_public ? styles.visibilityVisible : styles.visibilityHidden
                            }`}
                            title={`Public Book-Online: ${service.visible_on_public ? 'Visible' : 'Hidden'}`}
                          >
                            🌐 {service.visible_on_public ? '✓' : '✗'}
                          </span>
                          <span
                            className={`${styles.visibilityBadge} ${
                              service.visible_on_pos ? styles.visibilityVisible : styles.visibilityHidden
                            }`}
                            title={`POS Terminal: ${service.visible_on_pos ? 'Visible' : 'Hidden'}`}
                          >
                            🏪 {service.visible_on_pos ? '✓' : '✗'}
                          </span>
                          <span
                            className={`${styles.visibilityBadge} ${
                              service.visible_on_events ? styles.visibilityVisible : styles.visibilityHidden
                            }`}
                            title={`Events Booking: ${service.visible_on_events ? 'Visible' : 'Hidden'}`}
                          >
                            🎉 {service.visible_on_events ? '✓' : '✗'}
                          </span>
                        </div>
                      </td>
                      <td className={styles.actions}>
                        <button
                          className={styles.editButton}
                          onClick={() => {
                            console.log('🎯 Edit button clicked for service:', service.name);
                            console.log('📊 Original service data:', JSON.stringify(service, null, 2));

                            try {
                              // Create a safe service object with only serializable data
                              const safeService = {
                              id: String(service.id || ''),
                              name: String(service.name || ''),
                              description: String(service.description || ''),
                              duration: String(service.duration || '60'),
                              price: String(service.price || ''),
                              color: String(service.color || '#6a0dad'),
                              category: String(service.category || ''),
                              image_url: String(service.image_url || ''),
                              status: String(service.status || 'active'),
                              featured: Boolean(service.featured),
                              meta_title: String(service.meta_title || ''),
                              meta_description: String(service.meta_description || ''),
                              booking_requirements: String(service.booking_requirements || ''),
                              availability_notes: String(service.availability_notes || ''),
                              // Convert dates to ISO strings safely
                              created_at: (() => {
                                try {
                                  return service.created_at ? new Date(service.created_at).toISOString() : '';
                                } catch (e) {
                                  console.warn('Invalid created_at date:', service.created_at);
                                  return '';
                                }
                              })(),
                              updated_at: (() => {
                                try {
                                  return service.updated_at ? new Date(service.updated_at).toISOString() : '';
                                } catch (e) {
                                  console.warn('Invalid updated_at date:', service.updated_at);
                                  return '';
                                }
                              })(),
                              // Handle pricing tiers safely
                              pricing_tiers: Array.isArray(service.pricing_tiers)
                                ? service.pricing_tiers.map(tier => ({
                                    id: String(tier.id || ''),
                                    name: String(tier.name || ''),
                                    description: String(tier.description || ''),
                                    duration: String(tier.duration || '60'),
                                    price: String(tier.price || '0'),
                                    is_default: Boolean(tier.is_default),
                                    sort_order: Number(tier.sort_order) || 0
                                  }))
                                : [],
                              // Handle gallery images safely
                              gallery_images: Array.isArray(service.gallery_images)
                                ? service.gallery_images.map(img => String(img))
                                : []
                            };
                              console.log('✅ Safe service object created successfully');
                              console.log('🔍 Safe service data:', JSON.stringify(safeService, null, 2));
                              console.log('🚀 Calling onSelectService with safe data...');

                              onSelectService(safeService);

                              console.log('✅ onSelectService called successfully');
                            } catch (error) {
                              console.error('🚨 CRITICAL ERROR in Edit button handler:', error);
                              console.error('📊 Error stack:', error.stack);
                              console.error('📋 Original service data:', service);
                              alert('Error opening service editor. Please check the console for details and refresh the page.');
                            }
                          }}
                        >
                          Edit
                        </button>
                      </td>
                    </tr>
                  );
                } catch (error) {
                  console.error('Error rendering service row:', error, 'Service:', service);
                  return (
                    <tr key={service.id || Math.random()}>
                      <td colSpan="9" style={{ color: 'red', padding: '10px' }}>
                        Error displaying service data. Please refresh the page.
                      </td>
                    </tr>
                  );
                }
              })}
            </tbody>
          </table>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
}
