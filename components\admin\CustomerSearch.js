import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { debounce } from 'lodash';
import { authenticatedGet } from '@/lib/unified-auth-fetch';
import styles from '@/styles/admin/CustomerSearch.module.css';

/**
 * Advanced customer search component
 *
 * @param {Object} props - Component props
 * @param {Function} props.onSelectCustomer - Function to call when a customer is selected
 * @returns {JSX.Element}
 */
export default function CustomerSearch({ onSelectCustomer }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    tags: [],
    isVip: false,
    hasBookings: false,
    lastBookingDays: 0,
  });
  const [availableTags, setAvailableTags] = useState([]);
  const [showFilters, setShowFilters] = useState(false);

  // Fetch available tags via API route
  useEffect(() => {
    const fetchTags = async () => {
      try {
        console.log('[CustomerSearch] Fetching customer tags via API...');
        const response = await authenticatedGet('/api/admin/customer-tags');

        if (response.ok) {
          const data = await response.json();
          setAvailableTags(data.tags || []);
          console.log('[CustomerSearch] Tags loaded:', data.tags?.length || 0);
        } else {
          console.warn('[CustomerSearch] Failed to fetch tags:', response.status);
        }
      } catch (error) {
        console.error('[CustomerSearch] Error fetching tags:', error);
      }
    };

    fetchTags();
  }, []);

  // Debounced search function using API route
  const debouncedSearch = useCallback(
    debounce(async (term, filters) => {
      if (!term && !filters.tags.length && !filters.isVip && !filters.hasBookings && !filters.lastBookingDays) {
        setCustomers([]);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('[CustomerSearch] Searching customers via API...', { term, filters });

        // Build query parameters for API call
        const queryParams = new URLSearchParams();

        if (term) {
          queryParams.append('search', term);
        }

        if (filters.isVip) {
          queryParams.append('vip', 'true');
        }

        if (filters.hasBookings) {
          queryParams.append('has_bookings', 'true');
        }

        if (filters.lastBookingDays > 0) {
          queryParams.append('last_booking_days', filters.lastBookingDays.toString());
        }

        if (filters.tags.length > 0) {
          queryParams.append('tags', filters.tags.join(','));
        }

        // Make API call
        const response = await authenticatedGet(`/api/admin/customers?${queryParams.toString()}`);

        if (!response.ok) {
          throw new Error(`Search failed: ${response.status}`);
        }

        const data = await response.json();
        console.log('[CustomerSearch] Search results:', data.customers?.length || 0);

        // Use the customers from API response (they should already be processed)
        setCustomers(data.customers || []);
      } catch (error) {
        console.error('[CustomerSearch] Error searching customers:', error);
        setError('Failed to search customers');
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  // Trigger search when search term or filters change
  useEffect(() => {
    debouncedSearch(searchTerm, filters);

    // Cleanup function
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, filters, debouncedSearch]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle filter changes
  const handleFilterChange = (name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle tag filter toggle
  const handleTagToggle = (tagId) => {
    setFilters(prev => {
      const currentTags = [...prev.tags];
      const tagIndex = currentTags.indexOf(tagId);

      if (tagIndex === -1) {
        // Add tag
        return {
          ...prev,
          tags: [...currentTags, tagId]
        };
      } else {
        // Remove tag
        currentTags.splice(tagIndex, 1);
        return {
          ...prev,
          tags: currentTags
        };
      }
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <div className={styles.customerSearch}>
      <div className={styles.searchHeader}>
        <div className={styles.searchInputContainer}>
          <input
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Search customers by name, email, or phone..."
            className={styles.searchInput}
          />
          <button
            className={styles.filtersToggle}
            onClick={() => setShowFilters(!showFilters)}
            aria-label="Toggle filters"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
            Filters
          </button>
        </div>
      </div>

      {showFilters && (
        <div className={styles.filtersPanel}>
          <div className={styles.filterGroup}>
            <h4 className={styles.filterGroupTitle}>Customer Tags</h4>
            <div className={styles.tagFilters}>
              {availableTags.map(tag => (
                <button
                  key={tag.id}
                  className={`${styles.tagFilter} ${filters.tags.includes(tag.id) ? styles.active : ''}`}
                  style={{
                    borderColor: tag.color,
                    backgroundColor: filters.tags.includes(tag.id) ? tag.color : 'transparent',
                    color: filters.tags.includes(tag.id) ? '#fff' : tag.color
                  }}
                  onClick={() => handleTagToggle(tag.id)}
                >
                  {tag.name}
                </button>
              ))}
            </div>
          </div>

          <div className={styles.filterGroup}>
            <h4 className={styles.filterGroupTitle}>Customer Status</h4>
            <div className={styles.checkboxFilters}>
              <label className={styles.checkboxFilter}>
                <input
                  type="checkbox"
                  checked={filters.isVip}
                  onChange={(e) => handleFilterChange('isVip', e.target.checked)}
                />
                <span>VIP Customers</span>
              </label>
              <label className={styles.checkboxFilter}>
                <input
                  type="checkbox"
                  checked={filters.hasBookings}
                  onChange={(e) => handleFilterChange('hasBookings', e.target.checked)}
                />
                <span>Has Bookings</span>
              </label>
            </div>
          </div>

          <div className={styles.filterGroup}>
            <h4 className={styles.filterGroupTitle}>Last Booking</h4>
            <div className={styles.rangeFilter}>
              <select
                value={filters.lastBookingDays}
                onChange={(e) => handleFilterChange('lastBookingDays', parseInt(e.target.value))}
                className={styles.selectFilter}
              >
                <option value="0">Any time</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="180">Last 6 months</option>
                <option value="365">Last year</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className={styles.error}>{error}</div>
      )}

      {loading ? (
        <div className={styles.loading}>Searching...</div>
      ) : customers.length === 0 ? (
        searchTerm || filters.tags.length > 0 || filters.isVip || filters.hasBookings || filters.lastBookingDays > 0 ? (
          <div className={styles.noResults}>No customers found matching your search criteria</div>
        ) : null
      ) : (
        <div className={styles.customersList}>
          {customers.map(customer => (
            <div key={customer.id} className={styles.customerCard}>
              <div className={styles.customerInfo}>
                <h3 className={styles.customerName}>
                  {customer.name}
                  {customer.vip && <span className={styles.vipBadge}>VIP</span>}
                </h3>
                <div className={styles.customerContact}>
                  <div className={styles.customerEmail}>{customer.email}</div>
                  {customer.phone && (
                    <div className={styles.customerPhone}>{customer.phone}</div>
                  )}
                </div>
                {customer.tags.length > 0 && (
                  <div className={styles.customerTags}>
                    {customer.tags.map(tag => (
                      <span
                        key={tag.id}
                        className={styles.tag}
                        style={{ backgroundColor: tag.color }}
                      >
                        {tag.name}
                      </span>
                    ))}
                  </div>
                )}
                <div className={styles.customerStats}>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Bookings:</span>
                    <span className={styles.statValue}>{customer.booking_count || 0}</span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Last Booking:</span>
                    <span className={styles.statValue}>{formatDate(customer.last_booking_date)}</span>
                  </div>
                </div>
              </div>
              <div className={styles.customerActions}>
                {onSelectCustomer ? (
                  <button
                    className={styles.selectButton}
                    onClick={() => onSelectCustomer(customer)}
                  >
                    Select
                  </button>
                ) : (
                  <Link
                    href={`/admin/customers/${customer.id}`}
                    className={styles.viewButton}
                  >
                    View Profile
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
